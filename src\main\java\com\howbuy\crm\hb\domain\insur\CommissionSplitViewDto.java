package com.howbuy.crm.hb.domain.insur;

import com.google.common.collect.Lists;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (创新产品预约佣金拆单视图对象)
 * <AUTHOR>
 * @date 2024/9/26 15:33
 * @since JDK 1.8
 */

/**
 * 创新产品预约佣金拆单视图
 */
@Data
public class CommissionSplitViewDto {

    /**
    * 佣金明细表ID
    */
    private BigDecimal commissionId;

    /**
     * 保单号
     */
    private String insurId;


    /**
     * 应收日期
     */
    private String gatheringDt;
    /**
     * 公司佣金率
     */
    private BigDecimal commissionRatio;
    /**
     * 币种
     */
    private String currency;

    /**
    * 应收佣金-内
    */
    private BigDecimal gatherCommIn;

    /**
    * 应收佣金-外
    */
    private BigDecimal gatherCommOut;



    /**
     * 开票日期
     */
    private String ticketDt;


    /**
    * 实收佣金
    */
    private BigDecimal realCommission;


    /**
     * 结算日期
     */
    private String accountDt;

    /**
     * 结算外币
     */
    private String settleCurrency;


    /**
     * 结算汇率
     */
    private BigDecimal settleRate;


    /**
    * 应收佣金-内(结算外币)
    */
    private BigDecimal settleGatherCommIn;

    /**
    * 应收佣金-外(结算外币)
    */
    private BigDecimal settleGatherCommOut;

    /**
    * 实收佣金(结算外币)
    */
    private BigDecimal settleRealCommission;


    /**
     * 拆单明细列表
     */
    private List<SplitDetailViewDto> splitDetailDtoList= Lists.newArrayList();


    @Data
    public static class SplitDetailViewDto{

        /**
         * 拆分明细ID
         */
        private BigDecimal splitId;

        /**
         * 序号
         */
        private Integer seqNo;

        /**
         * 保单号
         */
        private String insurId;

        /**
         * 应收日期
         */
        private String gatheringDt;
        /**
         * 公司佣金率
         * 标记： 允许编辑
         */
        private BigDecimal commissionRatio;
        /**
         * 币种
         */
        private String currency;

        /**
         * 应收佣金-内
         */
        private BigDecimal gatherCommIn;

        /**
         * 应收佣金-外
         *  标记： 允许编辑
         */
        private BigDecimal gatherCommOut;



        /**
         * 开票日期
         *  标记： 允许编辑
         */
        private String ticketDt;


        /**
         * 实收佣金
         *  标记： 允许编辑
         */
        private BigDecimal realCommission;


        /**
         * 结算日期
         *  标记： 允许编辑
         */
        private String accountDt;

        /**
         * 结算外币
         *  标记： 允许编辑
         */
        private String settleCurrency;


        /**
         * 结算汇率
         *  标记： 允许编辑
         */
        private BigDecimal settleRate;


        /**
         * 应收佣金-内(结算外币)
         */
        private BigDecimal settleGatherCommIn;

        /**
         * 应收佣金-外(结算外币)
         *  标记： 允许编辑
         */
        private BigDecimal settleGatherCommOut;

        /**
         * 实收佣金(结算外币)
         *  标记： 允许编辑
         */
        private BigDecimal settleRealCommission;

    }


}