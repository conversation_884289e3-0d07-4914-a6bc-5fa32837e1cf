package com.howbuy.crm.hb.enums;


import crm.howbuy.base.constants.StaticVar;

/**
 * 资料管理审核状态-审核级别枚举
 * <AUTHOR> on 2022/3/14 16:37
 */
public enum  CounterCheckStatusLevelEnum {

    /** 分部待审核 */
    WAIT_CHECK(StaticVar.CURSTAT_WAIT_CHECK, "1"),
    /** 分部已审核 */
    DEPT_PASS(StaticVar.CURSTAT_DEPT_PASS, "3");

    /** 审核状态 */
    private String checkStat;
    /** 审核级别 */
    private String checkLevel;

    CounterCheckStatusLevelEnum(String checkStat, String checkLevel) {
        this.checkStat = checkStat;
        this.checkLevel = checkLevel;
    }

    /**
     * 根据checkStat获取checkLevel
     * @param checkStat
     * @return
     */
    public static String getCheckLevel(String checkStat) {
        CounterCheckStatusLevelEnum checkStatusLevelEnum = getEnum(checkStat);
        return checkStatusLevelEnum == null ? null : checkStatusLevelEnum.getCheckLevel();
    }

    /**
     * 根据checkStat直接返回 整个枚举类型
     * @param checkStat
     * @return
     */
    public static CounterCheckStatusLevelEnum getEnum(String checkStat) {
        for (CounterCheckStatusLevelEnum checkStatusLevelEnum : values()) {
            if (checkStatusLevelEnum.checkStat.equals(checkStat)) {
                return checkStatusLevelEnum;
            }
        }
        return null;
    }

    public String getCheckStat() {
        return checkStat;
    }

    public String getCheckLevel() {
        return checkLevel;
    }
}
