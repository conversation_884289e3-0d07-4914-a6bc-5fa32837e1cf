package com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * <AUTHOR>
 * @description: (直销交易记录实体)
 * @date 2023/3/1 16:07
 * @since JDK 1.8
 */
@Data
public class ZxTradeRecord implements Serializable {

    private String appSerialNo;

    private String tradeDt;

    private String conscustNo;

    private String custname;

    private String fundCode;

    private String fundName;

    private String tradeType;

    // 申请金额
    private BigDecimal appamt;

    private BigDecimal transferPrice;

    private BigDecimal ackvol;

    private BigDecimal ackamt;

    private BigDecimal fee;

    private BigDecimal nav;

    private String modifier;

    private String moddt;

    private String divMode;



}
