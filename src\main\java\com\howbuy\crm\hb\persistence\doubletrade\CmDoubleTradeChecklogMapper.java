package com.howbuy.crm.hb.persistence.doubletrade;

import java.util.List;
import java.util.Map;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleTradeChecklog;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmDoubleTradeChecklogMapper {
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleTradeChecklog> listCmDoubleTradeChecklog(Map<String, String> param);
	
	/**
	 * 新增数据对象
	 * @param cmDoubleTradeChecklog
	 */
	void insertCmDoubleTradeChecklog(CmDoubleTradeChecklog cmDoubleTradeChecklog);
}
