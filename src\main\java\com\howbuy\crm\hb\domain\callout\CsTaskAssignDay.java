package com.howbuy.crm.hb.domain.callout;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 客服当天任务实体类
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class CsTaskAssignDay implements Serializable {
	private static final long serialVersionUID = 1L;
	private long taskId;
	private long waitId;
	private String userId;
	private int distributeMode;
	private String distributeDate;
	private int handleFlag;
	/** 预约时间 */
	private String orderDate;
	/** 处理时间 */
	private String handleDate;
	/** 呼出状态 */
	private int calloutStatus; 

}
