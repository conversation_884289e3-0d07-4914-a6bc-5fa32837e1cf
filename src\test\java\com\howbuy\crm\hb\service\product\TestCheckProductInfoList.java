package com.howbuy.crm.hb.service.product;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.BeforeClass;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.Test;
import com.howbuy.crm.hb.domain.product.ProductCheckInfo;
import com.howbuy.crm.hb.domain.product.ProductManagerInfo;
import com.howbuy.crm.hb.persistence.product.ProductInfoCheckMapper;
import com.howbuy.crm.hb.persistence.product.ProductInfoManagerMapper;
import com.howbuy.crm.hb.service.product.impl.ProductInfoCheckServiceImpl;
import lombok.SneakyThrows;


/**
 * 单元测试ProductInfoCheckServiceImpl.checkProductInfoList：产品审核
 * <AUTHOR>
 */
@PowerMockIgnore("javax.management.*")
@Test
public class TestCheckProductInfoList extends PowerMockTestCase {

    @InjectMocks
    private ProductInfoCheckServiceImpl serviceMock;
    
    private static Map<String, Object> paramMap = new HashMap<String, Object>(8);
    
    private static ProductManagerInfo productManagerInfo = new ProductManagerInfo();

    @Mock
    private ProductInfoCheckMapper productInfoCheckMapperMock;
    
    @Mock
    private ProductInfoManagerMapper productInfoManagerMapperMock;

    private static String updFlag = "editorSucc";
    
    @BeforeClass
	public static  void init() {
    	
	}
    
    /**
     * 1.审核不通过
     */
   @Test
   @SneakyThrows
    public void testCheckRefuse() {
	   final ProductInfoCheckServiceImpl spy = PowerMockito.spy(serviceMock);
	   
	   //有操作权限
	   paramMap.put("director", true);
	   //退回
	   paramMap.put("checkFlag", "2");
	   paramMap.put("checker", "111");
	   paramMap.put("checkId", "333");
	   ProductCheckInfo updCheck = new ProductCheckInfo();
	   updCheck.setCreator("111");
	   //mock审核记录
	   mockProductCheckInfo(spy,updCheck);

	   ArgumentCaptor<Map> stringCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<ProductCheckInfo> productCheckInfoCaptor = ArgumentCaptor.forClass(ProductCheckInfo.class);
	   updFlag = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ProductInfoCheckServiceImpl.class, "checkProductInfoList")[0], spy,
			   paramMap);
	   //验证查询审核记录1次
       Mockito.verify(productInfoCheckMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
       //验证更新审核记录1次
       Mockito.verify(productInfoCheckMapperMock, new Times(1)).updateProductInfo(productCheckInfoCaptor.capture());
       
       Assert.assertTrue(updFlag.equals("editorSucc"));
    }
   
   
   /**
    * mock 审核记录
    * @param spy 测试目标类
    * @param productCheckInfo
    */
   @SneakyThrows
   private void mockProductCheckInfo(ProductInfoCheckServiceImpl spy,ProductCheckInfo productCheckInfo) {
	   List<ProductCheckInfo> proList = null; 
	   if( productCheckInfo != null ){
		   proList = new ArrayList<ProductCheckInfo>();
		   proList.add(productCheckInfo);
	   }
       MemberModifier.field(ProductInfoCheckServiceImpl.class, "productInfoCheckMapper").set(spy, productInfoCheckMapperMock);

       PowerMockito.when(productInfoCheckMapperMock.getProductInfo(Mockito.any()))
               .thenReturn(proList);
   }
   
   /**
    * mock 基金信息记录
    * @param spy 测试目标类
    * @param productCheckInfo
    */
   @SneakyThrows
   private void mockProductManagerInfo(ProductInfoCheckServiceImpl spy,ProductManagerInfo productManagerInfo) {
	   List<ProductManagerInfo> productManagerInfoList = null; 
	   if( productManagerInfo != null ){
		   productManagerInfoList = new ArrayList<ProductManagerInfo>();
		   productManagerInfoList.add(productManagerInfo);
	   }
       MemberModifier.field(ProductInfoCheckServiceImpl.class, "productInfoManagerMapper").set(spy, productInfoManagerMapperMock);

       PowerMockito.when(productInfoManagerMapperMock.getProductInfo(Mockito.any()))
               .thenReturn(productManagerInfoList);
   }
   
   /**
    * 2.审核通过、审核记录的操作类型等于新增   && 查询到的基金信息为空
    */
   @Test
   @SneakyThrows
   public void testCheckPassAndInsertNull() {
       final ProductInfoCheckServiceImpl spy = PowerMockito.spy(serviceMock);
	   
       //有操作权限
	   paramMap.put("director", true);
	   //审核通过
	   paramMap.put("checkFlag", "1");
	   paramMap.put("checker", "111");
	   paramMap.put("checkId", "333");
	   ProductCheckInfo updCheck = new ProductCheckInfo();
	   updCheck.setId(1);
	   updCheck.setCreator("111");
	   //新增
	   updCheck.setOperationType("1");
	   //mock审核记录
	   mockProductCheckInfo(spy,updCheck);
	   //mock基金信息 为空
	   mockProductManagerInfo(spy,null);

	   ArgumentCaptor<Map> stringCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<ProductCheckInfo> productCheckInfoCaptor = ArgumentCaptor.forClass(ProductCheckInfo.class);
	   ArgumentCaptor<ProductManagerInfo> productManagerInfoCaptor = ArgumentCaptor.forClass(ProductManagerInfo.class);
	   updFlag = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ProductInfoCheckServiceImpl.class, "checkProductInfoList")[0], spy,
			   paramMap);
	   //验证查询审核记录1次
       Mockito.verify(productInfoCheckMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
       //验证更新审核记录1次
       Mockito.verify(productInfoCheckMapperMock, new Times(1)).updateProductInfo(productCheckInfoCaptor.capture());
       //验证查询基金信息1次
       Mockito.verify(productInfoManagerMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
       //验证插入基金信息1次
       Mockito.verify(productInfoManagerMapperMock, new Times(1)).insertProductInfo(productManagerInfoCaptor.capture());
       
       Assert.assertTrue(updFlag.equals("editorSucc"));
   }
   
   
  /**
   * 3.审核通过、审核记录的操作类型等于新增   && 查询到的基金信息不为空
   */
  @Test
  @SneakyThrows
  public void testCheckPassAndQryNotNull() {
	  final ProductInfoCheckServiceImpl spy = PowerMockito.spy(serviceMock);
	   
       //有操作权限
	   paramMap.put("director", true);
	   //审核通过
	   paramMap.put("checkFlag", "1");
	   paramMap.put("checker", "111");
	   paramMap.put("checkId", "333");
	   ProductCheckInfo updCheck = new ProductCheckInfo();
	   updCheck.setId(1);
	   updCheck.setCreator("111");
	   //新增
	   updCheck.setOperationType("1");
	   //mock审核记录
	   mockProductCheckInfo(spy,updCheck);
	   //mock基金信息 不为空
	   mockProductManagerInfo(spy,productManagerInfo);

	   ArgumentCaptor<Map> stringCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<ProductCheckInfo> productCheckInfoCaptor = ArgumentCaptor.forClass(ProductCheckInfo.class);
	   ArgumentCaptor<ProductManagerInfo> productManagerInfoCaptor = ArgumentCaptor.forClass(ProductManagerInfo.class);
	   updFlag = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ProductInfoCheckServiceImpl.class, "checkProductInfoList")[0], spy,
			   paramMap);
	   //验证查询审核记录1次
      Mockito.verify(productInfoCheckMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
      //验证更新审核记录1次
      Mockito.verify(productInfoCheckMapperMock, new Times(1)).updateProductInfo(productCheckInfoCaptor.capture());
      //验证查询基金信息1次
      Mockito.verify(productInfoManagerMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
      //验证插入基金信息0次
      Mockito.verify(productInfoManagerMapperMock, new Times(0)).insertProductInfo(productManagerInfoCaptor.capture());
      
      Assert.assertTrue(updFlag.equals("editorSucc"));
  }
  
  
  
  /**
   * 4.审核通过、审核记录的操作类型等于修改   && 查询到的基金信息不为空
   */
  @Test
  @SneakyThrows
  public void testCheckPassAndUpNotNull() {
	  final ProductInfoCheckServiceImpl spy = PowerMockito.spy(serviceMock);
	   
      //有操作权限
	   paramMap.put("director", true);
	   //审核通过
	   paramMap.put("checkFlag", "1");
	   paramMap.put("checker", "111");
	   paramMap.put("checkId", "333");
	   ProductCheckInfo updCheck = new ProductCheckInfo();
	   updCheck.setId(1);
	   updCheck.setCreator("111");
	   //操作类型:修改
	   updCheck.setOperationType("2");
	   //mock审核记录
	   mockProductCheckInfo(spy,updCheck);
	   //mock基金信息 不为空
	   mockProductManagerInfo(spy,productManagerInfo);

	   ArgumentCaptor<Map> stringCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<ProductCheckInfo> productCheckInfoCaptor = ArgumentCaptor.forClass(ProductCheckInfo.class);
	   ArgumentCaptor<ProductManagerInfo> productManagerInfoCaptor = ArgumentCaptor.forClass(ProductManagerInfo.class);
	   updFlag = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ProductInfoCheckServiceImpl.class, "checkProductInfoList")[0], spy,
			   paramMap);
	   //验证查询审核记录1次
	   Mockito.verify(productInfoCheckMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
	   //验证更新审核记录1次
	   Mockito.verify(productInfoCheckMapperMock, new Times(1)).updateProductInfo(productCheckInfoCaptor.capture());
	   //验证查询基金信息1次
	   Mockito.verify(productInfoManagerMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
	   //验证插入基金信息0次
	   Mockito.verify(productInfoManagerMapperMock, new Times(0)).insertProductInfo(productManagerInfoCaptor.capture());
	   //验证更新基金信息1次
	   Mockito.verify(productInfoManagerMapperMock, new Times(1)).updateProductInfo(productManagerInfoCaptor.capture());
	     
	   Assert.assertTrue(updFlag.equals("editorSucc"));
  }
  
  
  /**
   * 5.审核通过、审核记录的操作类型等于修改   && 查询到的基金信息为空
   */
  @Test
  @SneakyThrows
  public void testCheckPassAndUpNull() {
	  final ProductInfoCheckServiceImpl spy = PowerMockito.spy(serviceMock);
	   
      //有操作权限
	   paramMap.put("director", true);
	   //审核通过
	   paramMap.put("checkFlag", "1");
	   paramMap.put("checker", "111");
	   paramMap.put("checkId", "333");
	   ProductCheckInfo updCheck = new ProductCheckInfo();
	   updCheck.setId(1);
	   updCheck.setCreator("111");
	   //操作类型:修改
	   updCheck.setOperationType("2");
	   //mock审核记录
	   mockProductCheckInfo(spy,updCheck);
	   //mock基金信息 不为空
	   mockProductManagerInfo(spy,null);

	   ArgumentCaptor<Map> stringCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<ProductCheckInfo> productCheckInfoCaptor = ArgumentCaptor.forClass(ProductCheckInfo.class);
	   ArgumentCaptor<ProductManagerInfo> productManagerInfoCaptor = ArgumentCaptor.forClass(ProductManagerInfo.class);
	   updFlag = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ProductInfoCheckServiceImpl.class, "checkProductInfoList")[0], spy,
			   paramMap);
	   //验证查询审核记录1次
	   Mockito.verify(productInfoCheckMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
	   //验证更新审核记录1次
	   Mockito.verify(productInfoCheckMapperMock, new Times(1)).updateProductInfo(productCheckInfoCaptor.capture());
	   //验证查询基金信息1次
	   Mockito.verify(productInfoManagerMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
	   //验证插入基金信息1次
	   Mockito.verify(productInfoManagerMapperMock, new Times(1)).insertProductInfo(productManagerInfoCaptor.capture());
	   //验证更新基金信息0次
	   Mockito.verify(productInfoManagerMapperMock, new Times(0)).updateProductInfo(productManagerInfoCaptor.capture());
	     
	   Assert.assertTrue(updFlag.equals("editorSucc"));
  }

  /**
   * 6.没有审核权限
   */
  @Test
  @SneakyThrows
  public void testCreEqualsChe() {
	  final ProductInfoCheckServiceImpl spy = PowerMockito.spy(serviceMock);
	   
       //没有操作权限
	   paramMap.put("director", false);
	   paramMap.put("checker", "111");
	   ProductCheckInfo updCheck = new ProductCheckInfo();
	   updCheck.setCreator("111");
	   //mock审核记录
	   mockProductCheckInfo(spy,updCheck);

	   ArgumentCaptor<Map> stringCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<ProductCheckInfo> productCheckInfoCaptor = ArgumentCaptor.forClass(ProductCheckInfo.class);
	   ArgumentCaptor<ProductManagerInfo> productManagerInfoCaptor = ArgumentCaptor.forClass(ProductManagerInfo.class);
	   updFlag = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ProductInfoCheckServiceImpl.class, "checkProductInfoList")[0], spy,
			   paramMap);
	   //验证查询审核记录1次
	   Mockito.verify(productInfoCheckMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
	   //验证更新审核记录0次
	   Mockito.verify(productInfoCheckMapperMock, new Times(0)).updateProductInfo(productCheckInfoCaptor.capture());
	     
	   Assert.assertTrue(updFlag.equals("editorErr"));
  }
  
  
  /**
   * 7.没有审核权限
   */
  @Test
  @SneakyThrows
  public void testNotPermission() {
	  final ProductInfoCheckServiceImpl spy = PowerMockito.spy(serviceMock);
	   
       //没有操作权限
	   paramMap.put("director", false);
	   paramMap.put("checker", "111");
	   ProductCheckInfo updCheck = new ProductCheckInfo();
	   updCheck.setCreator("111");
	   //mock审核记录
	   mockProductCheckInfo(spy,updCheck);

	   ArgumentCaptor<Map> stringCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<ProductCheckInfo> productCheckInfoCaptor = ArgumentCaptor.forClass(ProductCheckInfo.class);
	   ArgumentCaptor<ProductManagerInfo> productManagerInfoCaptor = ArgumentCaptor.forClass(ProductManagerInfo.class);
	   updFlag = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ProductInfoCheckServiceImpl.class, "checkProductInfoList")[0], spy,
			   paramMap);
	   //验证查询审核记录1次
	   Mockito.verify(productInfoCheckMapperMock, new Times(1)).getProductInfo(stringCaptor.capture());
	   //验证更新审核记录0次
	   Mockito.verify(productInfoCheckMapperMock, new Times(0)).updateProductInfo(productCheckInfoCaptor.capture());
	     
	   Assert.assertTrue(updFlag.equals("editorErr"));
  }
}
