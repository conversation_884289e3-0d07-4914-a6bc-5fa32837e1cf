package com.howbuy.crm.hb.persistence.insur;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.insur.CmBxProductChannel;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxProductChannelMapper {


     /**
      * 新增数据对象
      * @param cmBxProductChannel
      */
	void insertCmBxProductChannel(CmBxProductChannel cmBxProductChannel);
	
	/**
	 * 插入日志表
	 * @param map
	 */
	void insertCmBxProductChannelLog(Map<String,String> map);
	
	/**
	 * 根据产品代码删除对应的产品和合作渠道对应关系表
	 * @param map
	 */
	void delCmBxProductChannelByFundcode(Map<String,String> map);
	
	/**
	 * 查询列表
	 * @param param
	 * @return
	 */
	List<CmBxProductChannel> listCmBxProductChannel(Map<String, String> param);
	
}
