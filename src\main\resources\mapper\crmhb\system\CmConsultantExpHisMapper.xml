<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpHisMapper">
	<insert id="insertCmConsultantExpHis" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpHisPO">
		INSERT INTO CM_CONSULTANT_EXP_HIS (
		<trim suffix="" suffixOverrides=",">
			<if test="userid != null"> userid, </if>
			<if test="userno != null"> userno, </if>
			<if test="provcode != null"> provcode, </if>
			<if test="citycode != null"> citycode, </if>
			<if test="gender != null"> gender, </if>
			<if test="birthday != null"> birthday, </if>
			<if test="edulevel != null"> edulevel, </if>
			<if test="worktype != null"> worktype, </if>
			<if test="workstate != null"> workstate, </if>
			<if test="curmonthlevel != null"> curmonthlevel, </if>
			<if test="startdt != null"> startdt, </if>
			<if test="startlevel != null"> startlevel, </if>
			<if test="salary != null"> salary, </if>
			<if test="probationenddt != null"> probationenddt, </if>
			<if test="regulardt != null"> regulardt, </if>
			<if test="regularlevel != null"> regularlevel, </if>
			<if test="regularsalary != null"> regularsalary, </if>
			<if test="quitdt != null"> quitdt, </if>
			<if test="quitlevel != null"> quitlevel, </if>
			<if test="quitsalary != null"> quitsalary, </if>
			<if test="quitreason != null"> quitreason, </if>
			<if test="servingage != null"> servingage, </if>
			<if test="checkflag != null"> checkflag, </if>
			<if test="checkor != null"> checkor, </if>
			<if test="creator != null"> creator, </if>
			credt,
			<if test="modor != null"> modor, </if>
			<if test="jjcardno !=null"> jjcardno, </if>
			<if test="attachtype !=null"> attachtype, </if>
			<if test="background !=null"> background, </if>
			<if test="source !=null"> source, </if>
			<if test="beforepositiontype !=null"> beforepositiontype, </if>
			<if test="beforepositionage !=null"> beforepositionage, </if>
			<if test="recruit !=null"> recruit, </if>
			<if test="recommend !=null"> recommend, </if>
			<if test="recommenduserno !=null"> recommenduserno, </if>
			<if test="recommendtype !=null"> recommendtype, </if>
			<if test="remark !=null"> remark, </if>
			<if test="teamcode !=null"> teamcode, </if>
			<if test="outletcode !=null"> outletcode, </if>
			<if test="email !=null"> email, </if>
			<if test="consname !=null"> consname, </if>
			<if test="curmonthsalary !=null"> curmonthsalary, </if>
			<if test="probationresult3m !=null"> probationresult3m, </if>
			<if test="probationresult6m !=null"> probationresult6m, </if>
			<if test="nexttestdate !=null"> nexttestdate, </if>
			<if test="subpositions !=null"> subpositions, </if>
			<if test="quitInfo !=null"> quitinfo, </if>
			<if test="promotedate !=null"> promotedate, </if>
			<if test="bxcommissionway !=null"> bxcommissionway, </if>
			<if test="beisenid !=null"> beisenid, </if>
			<if test="probationResult12m !=null"> probation_result_12m, </if>
			<if test="centerOrg !=null"> center_org, </if>
			<if test="adjustServingMonth !=null"> adjust_serving_month, </if>
			<if test="adjustManageServingMonth !=null"> adjust_manage_serving_month, </if>
			<if test="dt3m !=null"> DT3M, </if>
			<if test="dt12m !=null"> DT12M, </if>
			<if test="nextTestPeriod !=null"> NEXT_TEST_PERIOD, </if>
			<if test="probationSalary3m !=null"> PROBATION_SALARY_3M, </if>
			<if test="salary12m !=null"> SALARY_12M, </if>
			<if test="joinRank !=null"> JOIN_RANK, </if>
			<if test="probationRank3m !=null"> PROBATION_RANK_3M, </if>
			<if test="regularRank !=null"> REGULAR_RANK, </if>
			<if test="rank12m !=null"> RANK_12M, </if>
			<if test="joinSsb !=null"> JOIN_SSB, </if>
			<if test="probationSsb3m !=null"> PROBATION_SSB_3M, </if>
			<if test="regularSsb !=null"> REGULAR_SSB, </if>
			<if test="ssb12m !=null"> SSB_12M, </if>
			<if test="probationLevel3m !=null"> PROBATIONLEVEL_3M, </if>
			<if test="testLevel12m !=null"> TESTLEVEL_12M, </if>
			<if test="dt3mFlag !=null"> DT3M_FLAG, </if>
			<if test="dt12mFlag !=null"> DT12M_FLAG, </if>
			<if test="regulardtFlag !=null"> REGULARDT_FLAG, </if>
			<if test="nexttestdateFlag !=null"> NEXTTESTDATE_FLAG, </if>
			<if test="countyCode !=null"> COUNTY_CODE, </if>
			<if test="inTransitRemark !=null"> IN_TRANSIT_REMARK, </if>
		</trim>
		) values (
		<trim suffix="" suffixOverrides=",">
			<if test="userid != null"> #{userid}, </if>
			<if test="userno != null"> #{userno}, </if>
			<if test="provcode != null"> #{provcode}, </if>
			<if test="citycode != null"> #{citycode}, </if>
			<if test="gender != null"> #{gender}, </if>
			<if test="birthday != null"> #{birthday}, </if>
			<if test="edulevel != null"> #{edulevel}, </if>
			<if test="worktype != null"> #{worktype}, </if>
			<if test="workstate != null"> #{workstate}, </if>
			<if test="curmonthlevel != null"> #{curmonthlevel}, </if>
			<if test="startdt != null"> #{startdt}, </if>
			<if test="startlevel != null"> #{startlevel}, </if>
			<if test="salary != null"> #{salary}, </if>
			<if test="probationenddt != null"> #{probationenddt}, </if>
			<if test="regulardt != null"> #{regulardt}, </if>
			<if test="regularlevel != null"> #{regularlevel}, </if>
			<if test="regularsalary != null"> #{regularsalary}, </if>
			<if test="quitdt != null"> #{quitdt}, </if>
			<if test="quitlevel != null"> #{quitlevel}, </if>
			<if test="quitsalary != null"> #{quitsalary}, </if>
			<if test="quitreason != null"> #{quitreason}, </if>
			<if test="servingage != null"> #{servingage}, </if>
			<if test="checkflag != null"> #{checkflag}, </if>
			<if test="checkor != null"> #{checkor}, </if>
			<if test="creator != null"> #{creator}, </if>
			sysdate,
			<if test="modor != null"> #{modor}, </if>
			<if test="jjcardno !=null"> #{jjcardno}, </if>
			<if test="attachtype !=null"> #{attachtype}, </if>
			<if test="background !=null"> #{background}, </if>
			<if test="source !=null"> #{source}, </if>
			<if test="beforepositiontype !=null"> #{beforepositiontype}, </if>
			<if test="beforepositionage !=null"> #{beforepositionage}, </if>
			<if test="recruit !=null"> #{recruit}, </if>
			<if test="recommend !=null"> #{recommend}, </if>
			<if test="recommenduserno !=null"> #{recommenduserno}, </if>
			<if test="recommendtype !=null"> #{recommendtype}, </if>
			<if test="remark !=null"> #{remark}, </if>
			<if test="teamcode !=null"> #{teamcode}, </if>
			<if test="outletcode !=null"> #{outletcode}, </if>
			<if test="email !=null"> #{email}, </if>
			<if test="consname !=null"> #{consname}, </if>
			<if test="curmonthsalary !=null"> #{curmonthsalary}, </if>
			<if test="probationresult3m !=null"> #{probationresult3m}, </if>
			<if test="probationresult6m !=null"> #{probationresult6m}, </if>
			<if test="nexttestdate !=null"> #{nexttestdate}, </if>
			<if test="subpositions !=null"> #{subpositions}, </if>
			<if test="quitInfo !=null"> #{quitInfo}, </if>
			<if test="promotedate !=null"> #{promotedate}, </if>
			<if test="bxcommissionway !=null"> #{bxcommissionway}, </if>
			<if test="beisenid !=null"> #{beisenid}, </if>
			<if test="probationResult12m !=null"> #{probationResult12m}, </if>
			<if test="centerOrg !=null"> #{centerOrg}, </if>
			<if test="adjustServingMonth !=null"> #{adjustServingMonth}, </if>
			<if test="adjustManageServingMonth !=null"> #{adjustManageServingMonth}, </if>
			<if test="dt3m !=null"> #{dt3m}, </if>
			<if test="dt12m !=null"> #{dt12m}, </if>
			<if test="nextTestPeriod !=null"> #{nextTestPeriod}, </if>
			<if test="probationSalary3m !=null"> #{probationSalary3m}, </if>
			<if test="salary12m !=null"> #{salary12m}, </if>
			<if test="joinRank !=null"> #{joinRank}, </if>
			<if test="probationRank3m !=null"> #{probationRank3m}, </if>
			<if test="regularRank !=null"> #{regularRank}, </if>
			<if test="rank12m !=null"> #{rank12m}, </if>
			<if test="joinSsb !=null"> #{joinSsb}, </if>
			<if test="probationSsb3m !=null"> #{probationSsb3m}, </if>
			<if test="regularSsb !=null"> #{regularSsb}, </if>
			<if test="ssb12m !=null"> #{ssb12m}, </if>
			<if test="probationLevel3m !=null"> #{probationLevel3m}, </if>
			<if test="testLevel12m !=null"> #{testLevel12m}, </if>
			<if test="dt3mFlag !=null"> #{dt3mFlag}, </if>
			<if test="dt12mFlag !=null"> #{dt12mFlag}, </if>
			<if test="regulardtFlag !=null"> #{regulardtFlag}, </if>
			<if test="nexttestdateFlag !=null"> #{nexttestdateFlag}, </if>
			<if test="countyCode !=null"> #{countyCode}, </if>
			<if test="inTransitRemark !=null"> #{inTransitRemark}, </if>
		</trim>
		)
	</insert>

	<select id="listCmConsultantExpHisByIds" parameterType="List" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExpHisPO">
		select *
		from (
			SELECT T1.USERID as conscode,
			T1.CONSNAME,
			T1.EMAIL,
			T1.OUTLETCODE,
			T1.TEAMCODE,
			T1.USERID,
			T1.USERNO,
			T1.PROVCODE,
			T1.CITYCODE,
			T1.GENDER,
			T1.BIRTHDAY,
			T1.EDULEVEL,
			T1.WORKTYPE,
			T1.WORKSTATE,
			T1.CURMONTHLEVEL,
			T1.STARTDT,
			T1.STARTLEVEL,
			T1.SALARY,
			T1.PROBATIONENDDT,
			T1.REGULARDT,
			T1.REGULARLEVEL,
			T1.REGULARSALARY,
			T1.QUITDT,
			T1.QUITLEVEL,
			T1.QUITSALARY,
			T1.QUITREASON,
			T1.SERVINGAGE,
			T1.CHECKFLAG,
			T1.CHECKOR,
			T1.CREATOR,
			T1.MODOR,
			t1.jjcardno,
			t1.attachtype,
			t1.background,
			t1.source,
			t1.beforepositiontype,
			t1.beforepositionage,
			t1.recruit,
			t1.recommend,
			t1.recommenduserno,
			t1.recommendtype,
			t1.remark,
			T1.SUBPOSITIONS,
			T1.NEXTTESTDATE,
			T1.PROBATIONRESULT3M,
			T1.PROBATIONRESULT6M,
			T1.CURMONTHSALARY,
			T1.QUITINFO,
			T1.PROMOTEDATE,
			T1.BXCOMMISSIONWAY,
			T1.BEISENID,
			T1.center_org as centerOrg,
			T1.PROBATION_RESULT_12M as probationResult12M,
			T1.ADJUST_SERVING_MONTH as adjustServingMonth,
			T1.adjust_manage_serving_month as adjustManageServingMonth,
			T1.DT3M as dt3m,
			T1.DT12M as dt12m,
			T1.NEXT_TEST_PERIOD as nextTestPeriod,
			T1.PROBATION_SALARY_3M as probationSalary3m,
			T1.SALARY_12M as salary12m,
			T1.JOIN_RANK as joinRank,
			T1.PROBATION_RANK_3M as probationRank3m,
			T1.REGULAR_RANK as regularRank,
			T1.RANK_12M as rank12m,
			T1.JOIN_SSB as joinSsb,
			T1.PROBATION_SSB_3M as probationSsb3m,
			T1.REGULAR_SSB as regularSsb,
			T1.SSB_12M as ssb12m,
			T1.PROBATIONLEVEL_3M as probationLevel3m,
			T1.TESTLEVEL_12M as testLevel12m,
			T1.COUNTY_CODE countyCode,
			T1.IN_TRANSIT_REMARK as inTransitRemark,
			row_number() over(partition by t1.userid order by t1.credt desc) rn
			FROM CM_CONSULTANT_EXP_HIS T1
			where 1=1
			<if test="userIds != null and userIds.size()>0 ">
				and t1.userid in
				<foreach collection="userIds" item="id" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
		) t2 where rn=2
	</select>


	<delete id="deleteExpHis" parameterType="String">
		DELETE from CM_CONSULTANT_EXP_HIS
		where userid = #{userid}
	</delete>
</mapper>