package com.howbuy.crm.hb.persistence.insur;

import java.util.List;
import java.util.Map;
import com.howbuy.crm.hb.domain.insur.CmBxProductCoeffRatio;


/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxProductCoeffRatioMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxProductCoeffRatio getCmBxProductCoeffRatio(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmBxProductCoeffRatio
      */
	void insertCmBxProductCoeffRatio(CmBxProductCoeffRatio cmBxProductCoeffRatio);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxProductCoeffRatio
	 */
	void updateCmBxProductCoeffRatio(CmBxProductCoeffRatio cmBxProductCoeffRatio);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxProductCoeffRatio> listCmBxProductCoeffRatio(Map<String, Object> param);
}
