package com.howbuy.crm.hb.manager;

import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import crm.howbuy.base.utils.StringUtil;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.write.*;
import jxl.write.biff.RowsExceededException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class ProsaleManager {
	
	/**
	 * 统计报表
	 * @param response
	 * @param map
	 * @throws Exception
	 */
	public void exportConfirmTradeList(HttpServletResponse response,Map<String,Object> map) throws Exception {
		try {
			// 设置导出参数和标题
			String excelName = URLEncoder.encode(map.get("downName").toString(), "utf-8");
			// 清空输出流
			response.reset();
			response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), "iso8859-1"));
			ServletOutputStream os = response.getOutputStream();
			// 建立excel文件
			WritableWorkbook book = Workbook.createWorkbook(os);
			WritableSheet sheet = book.createSheet("交易确认", 1);
			List<Prebookproductinfo> srList = (List<Prebookproductinfo>) map.get("rows");
			Label label = null ; 
		    label = new Label(0,0,"客户姓名");
		    sheet.addCell(label);
		    label = new Label(1,0, StringUtil.replaceNullStr(map.get("custname")));
		    sheet.addCell(label);
		    
		    label = new Label(2,0,"录入日期");
		    sheet.addCell(label);
		    label = new Label(3,0,StringUtil.replaceNullStr(map.get("credt")));
		    sheet.addCell(label);
		    
		    label = new Label(4,0,"产品名称");
		    sheet.addCell(label);
		    label = new Label(5,0,StringUtil.replaceNullStr(map.get("pname")));
		    sheet.addCell(label);
		    
		    label = new Label(6,0,"交易类型");
		    sheet.addCell(label);
		    label = new Label(7,0,StringUtil.replaceNullStr(map.get("tradeTypes")));
		    sheet.addCell(label);
		    
		    label = new Label(8,0,"是否复购");
		    sheet.addCell(label);
		    label = new Label(9,0,StringUtil.replaceNullStr(map.get("isrepeatbuy")));
		    sheet.addCell(label);
		    
		    label = new Label(0,1,"预约状态");
		    sheet.addCell(label);
		    label = new Label(1,1,StringUtil.replaceNullStr(map.get("prebookStates")));
		    sheet.addCell(label);
		    
		    label = new Label(2,1,"打款状态");
		    sheet.addCell(label);
		    label = new Label(3,1,StringUtil.replaceNullStr(map.get("payStates")));
		    sheet.addCell(label);
		    
		    label = new Label(4,1,"交易确认状态");
		    sheet.addCell(label);
		    label = new Label(5,1,StringUtil.replaceNullStr(map.get("tradestate")));
		    sheet.addCell(label);
		    
		    label = new Label(6,1,"当前所属投顾");
		    sheet.addCell(label);
		    label = new Label(7,1,StringUtil.replaceNullStr(map.get("orgCode"))+StringUtil.replaceNullStr(map.get("consCode")));
		    sheet.addCell(label);
		    
		    label = new Label(0,2,"预计交易日期");
		    sheet.addCell(label);
		    label = new Label(1,2,StringUtil.replaceNullStr(map.get("expecttradebegdt")));
		    sheet.addCell(label);
		    label = new Label(2,2,StringUtil.replaceNullStr(map.get("expecttradeenddt")));
		    sheet.addCell(label);
		    
			String[] columns = {"录入时间","预计交易日期", "客户姓名", "预约时所属投顾", "所属部门", "所属区域", "交易类型", "预约类型",
					"产品名称", "认缴金额", "购买金额(万)", "预计打款日期", "实际打款金额(万)", "实际打款日期",
					"赎回份额","预约状态","打款状态","下单状态","折扣状态",
					"上报状态","交易确认状态","销助备忘"};
			for (int i = 0; i < columns.length; i++) {
				label = new Label(i, 4, columns[i]);
				sheet.addCell(label);
				sheet.setColumnView(i, 22);
			}

			if (srList != null) {
				int x = 5;
				for (int i = 0; i < srList.size(); i++) {
					Prebookproductinfo obj = srList.get(i);
					addConfirmTradeRow(x++, obj, sheet);
				}
			}
			book.write();
			// 主体内容生成结束
			book.write(); // 写入文件
			if(book!=null){
				book.close();
			}
			os.close(); // 关闭流
			System.out.println("报表导出成功！");
		} catch (Exception ex) {
			ex.printStackTrace();
			System.out.println("报表导出异常！");
		}

	}
	
	private void addConfirmTradeRow(int x, Prebookproductinfo info, WritableSheet sheet) throws RowsExceededException, WriteException {
		Label label = null;
		NumberFormat format = NumberFormat.getNumberInstance();
		format.setMinimumFractionDigits(2);
		format.setMaximumFractionDigits(2);
		WritableCellFormat cell = new WritableCellFormat();
		cell.setAlignment(Alignment.RIGHT);
		label = new Label(0, x, info.getCredt());
		sheet.addCell(label);
		label = new Label(1, x, info.getExpecttradedt());
		sheet.addCell(label);
		label = new Label(2, x, info.getConscustname());
		sheet.addCell(label);
		label = new Label(3, x, info.getConsname());
		sheet.addCell(label);
		label = new Label(4, x, info.getOutletName());
		sheet.addCell(label);
		label = new Label(5, x, info.getUporgname());
		sheet.addCell(label);
		label = new Label(6, x, info.getTradeTypeVal());
		sheet.addCell(label);
		label = new Label(7, x, info.getPretypeval());
		sheet.addCell(label);
		label = new Label(8, x, info.getFundname());
		sheet.addCell(label);
		label = new Label(9, x, info.getTotalamt() == null ? null : info.getTotalamt().toPlainString());
		sheet.addCell(label);
		if(StringUtil.isNotNullStr(info.getBuyamt())){
			label = new Label(10, x, format.format(info.getBuyamt().divide(new BigDecimal(10000))), cell);
			sheet.addCell(label);
		}else{
			label = new Label(10, x, "");
			sheet.addCell(label);
		}
		label = new Label(11, x, info.getExpectpayamtdt());
		sheet.addCell(label);
		if(StringUtil.isNotNullStr(info.getRealpayamt())){
			label = new Label(12, x, format.format(info.getRealpayamt().divide(new BigDecimal(10000))), cell);
			sheet.addCell(label);
		}else{
			label = new Label(12, x, "");
			sheet.addCell(label);
		}
		label = new Label(13, x, info.getRealpayamtdt());
		sheet.addCell(label);
		/*label = new Label(11, x, info.getRealbuyman());
		sheet.addCell(label);*/
		if(StringUtil.isNotNullStr(info.getSellvol())){
			label = new Label(14, x, format.format(info.getSellvol()),cell);
			sheet.addCell(label);
		}else{
			label = new Label(14, x, "");
			sheet.addCell(label);
		}
		label = new Label(15, x, info.getPrebookstateval());
		sheet.addCell(label);
		label = new Label(16, x, info.getPaystateval());
		sheet.addCell(label);
		label = new Label(17, x, info.getOrderstateval());
		sheet.addCell(label);
		label = new Label(18, x, info.getDiscountstateval());
		sheet.addCell(label);
		//新增 海外订单 上报状态
		label = new Label(19, x, info.getSubmitStatus());
		sheet.addCell(label);
		label = new Label(20, x, info.getTradestateval());
		sheet.addCell(label);
		label = new Label(21, x, info.getNotes());
		sheet.addCell(label);
	}
}
