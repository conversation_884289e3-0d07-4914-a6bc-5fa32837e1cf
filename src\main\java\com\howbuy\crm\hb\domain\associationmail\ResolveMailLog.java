package com.howbuy.crm.hb.domain.associationmail;

import lombok.Data;

import java.io.Serializable;

/**
 * 邮件解析日志
 * <AUTHOR> on 2021/6/15 11:18
 */
@Data
public class ResolveMailLog implements Serializable {

    /** 主键 */
    private String id;

    /** 创建人 */
    private String creator;

    /** 记录创建日期 */
    private String credt;

    /** 邮件总数 */
    private Integer totalNum;

    /** 符合条件邮件数量 */
    private Integer validNum;

    /** 正常解析数量 */
    private Integer normalResolveNum;

    /** 异常解析数量 */
    private Integer expResolveNum;

    /** 重复邮件数量 */
    private Integer repeatResolveNum;

    /** 耗时 */
    private Long elapsedTime;
}
