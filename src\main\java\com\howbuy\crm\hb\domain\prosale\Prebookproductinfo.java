package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class Prebookproductinfo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * //预约ID
	 */
	private BigDecimal id;
	/**
	 * //分次CALL首次预约
	 */
	private BigDecimal firstpreid;
	/**
	 * //分次CALL壳id
	 */
	private BigDecimal mainid;
	/**
	 * //预计交易日期
	 */
	private String expecttradedt;
	/**
	 * //录入时间
	 */
	private String credt;
	/**
	 * //姓名
	 */
	private String conscustname;
	/**
	 * //投顾客户号
	 */
	private String conscustno;
	private String hboneno;
	private String consname;
	/**
	 * //所属投顾
	 */
	private String creator;
	/**
	 * //交易类型
	 */
	private String tradeType;
	private String tradeTypeVal;
	private String fundcode;
	private String fundname;
	private String fundtype;
	/**
	 * //是否海外基金
	 */
	private String sfhwjj;
	/**
	 * //好买产品线
	 */
	private String hmcpx;
	/**
	 * //赎回金额
	 */
	private BigDecimal sellamt;
	/**
	 * 赎回方式
	 */
	private String redeemMode;
	/**
	 * //是否计入统计
	 */
	private String isstat;
	/**
	 * //预约类型
	 */
	private String pretype;
	private String pretypeval;
	/**
	 * //预约确认人
	 */
	private String prebookcheckman;
	/**
	 * //预约确认时间
	 */
	private String prebookchecktime;
	/**
	 * //预约状态
	 */
	private String prebookstate;
	private String prebookstateval;
	/**
	 * //是否可线上下单
	 */
	private String islineorder;
	/**
	 * //下单状态
	 */
	private String orderstate;
	private String orderstateval;
	/**
	 * //线上下单时间
	 */
	private String ordertime;
	/**
	 * //认缴金额
	 */
	private BigDecimal totalamt;
	private String totalamtval;
	/**
	 * //预约购买金额
	 */
	private BigDecimal buyamt;
	/**
	 * //预约购买金额
	 */
	private BigDecimal buyamtrmb;
	/**
	 * //打款状态
	 */
	private String paystate;
	private String paystateval;
	/**
	 * //预计打款日期
	 */
	private String expectpayamtdt;
	/**
	 * //实际打款日期
	 */
	private String realpayamtdt;
	/**
	 * //实际打款金额
	 */
	private BigDecimal realpayamt;
	/**
	 * //实际打款金额人民币
	 */
	private BigDecimal realpayamtrmb;
	/**
	 * //实际打款手续费
	 */
	private BigDecimal realfee;
	/**
	 * //实际打款手续费（人民币）
	 */
	private BigDecimal realfeeRmb;
	/**
	 * //预约手续费
	 */
	private BigDecimal fee;
	/**
	 * //支付方式
	 */
	private String paymenttype;
	/**
	 * //赎回份额
	 */
	private BigDecimal sellvol;
	/**
	 * //赎回至
	 */
	private String redeemdirection;
	/**
	 * //冷静期截止时间
	 */
	private String calmdatetime;
	/**
	 * //crm冷静期截止时间
	 */
	private String crmcalmdatetime;
	/**
	 * //资产证明状态
	 */
	private String zczmstate;
	/**
	 * //交易确认状态
	 */
	private String tradestate;
	private String tradestateval;
	/**
	 * //资源类型
	 */
	private String restype;
	/**
	 * //备注
	 */
	private String remarks;
	/**
	 * //折扣审核
	 */
	private String remarks2;
	/**
	 * //销助备忘
	 */
	private String notes;
	/**
	 * //实际预约人
	 */
	private String realcreator;
	/**
	 * //币种
	 */
	private String currency;
	/**
	 * //实际购买人
	 */
	private String realbuyman;
	/**
	 * //特殊预约类型
	 */
	private String spectradetype;
	/**
	 * //中台订单号
	 */
	private String dealno;

	/**
	 * //下单渠道
	 */
	private String txchannel;
	private String txchannelval;

	/**
	 * //中台申请金额
	 */
	private BigDecimal ztbuyamt;
	/**
	 * //中台是否到账确认 1 已确认 0 未确认
	 */
	private String ztPayConfirm;
	/**
	 * //代销直销状态：1为代销，2为直销，3为直转代
	 */
	private String sfmsjg;
	/**
	 * 是否电子成单：1是，0否
	 */
	private String sfdzcd;
	/**
	 * 是否分次call产品：1-是 0-否
	 */
	private String fccl;
	/**
	 * 是否直销（包括直转代黑名单中的）：1-是 0-否
	 */
	private String iszx;
	/**
	 * 折扣状态
	 */
	private String discountstate;
	private String discountstateval;
	/**
	 * 折扣方式值
	 */
	private String discountWayVal;
	/**
	 * 折扣类型
	 */
	private String discountType;
	private String discountTypeVal;
	/**
	 * 折扣率
	 */
	private BigDecimal discountRate;
	/**
	 * 折扣理由
	 */
	private String discountReason;
	/**
	 * 税后折扣金额
	 */
	private BigDecimal afterTaxAmt;
	/**
	 * 税后折扣金额(人民币)
	 */
	private BigDecimal afterTaxAmtRmb;
	/**
	 * 折扣方式
	 */
	private String discountWay;
	/**
	 * 折扣客户权益描述
	 */
	private String interestComment;

	/**
	 * 是否可以新增实缴预约
	 */
	private boolean canaddfccl;
	/**
	 * 是否可以复购：0-否；1-是
	 */
	private String isrepeat;
	/**
	 * 是否展示 新增实缴预约：0-不展示；1-展示
	 */
	private String isshow;
	/**
	 * 所属区域
	 */
	private String uporgname;
	/**
	 * 所属部门
	 */
	private String outletName;
	/**
	 * 所属部门编码
	 */
	private String outletCode;
	/**
	 * 所属中心名称
	 */
	private String centerOrgName;
	/**
	 * 是否已下单
	 */
	private String isOrdered;

	/**
	 * 折扣形式
	 */
	private String discountstyle;
	/**
	 * 税前折扣金额
	 */
	private BigDecimal beforetaxamt;
	/**
	 * 税前折扣金额（调整）
	 */
	private BigDecimal befTaxAmtAdjust;
	/**
	 * 员工关系
	 */
	private String staffRelation ;
	private BigDecimal sno;
	private String snoflag;
	private String idno;
	private String idnoMask;
	private String idnoCipher;
	/**
	 * //线上下单时间
	 */
	private String appdate;
	/**
	 * //线上下单时间
	 */
	private String apptime;
	/**
	 * //汇率
	 */
	private BigDecimal zjj;
	/**
	 * //分销结构
	 */
	private String disCode;
	/**
	 * 汇率日期
	 */
	private String ratedt;
	/**
	 * 第一来源
	 */
	private String firstsource;
	private String firstsourceVal;
	/**
	 * 来源类型
	 */
	private String sourcetype;
	private String sourcetypeVal;

	/**
	 *是否资金已匹配  1-是 0-否
	 */
	private String finMatched;
	
	/**
	 *是否白名单标识
	 */
	private String whiteFlag;

	/**
	 * 直销:2 代销: 1
	 */
	private String isdxflag;

	/**
	 * 是否香港:1-是，0-否
	 */
	private String sfxg;

	/**
	 * 线上签约标识 0-否;1-是;2-待确定
	 */
	private String onlineSignFlag;

	/**
	 * 签约状态 0-未签约；1-已签约
	 */
	private String signFlag;

	/**
	 * 签约日期时间
	 */
	private Date signTime;


	/**
	 * 支付方式 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
	 */
	private String paymentMode;

	/**
	 * 赎回去向 1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：基金转投
	 */
	private String ppredeemDirection;
	/**
	 * 千禧年产品标志
	 */
	private Boolean qianXiFlag;

	/**
	 * 申请金额
	 */
	private BigDecimal appAmt;

	/**
	 * 手续费标准
	 */
	private String feeRateMethod;

	/**
	 * 中台认缴
	 */
	private BigDecimal ztSubscribeAmt;

	/**
	 * 售前留痕材料状态(0-无需上传/1-未上传/2-已上传/3-审核通过/4审核不通过)
	 */
	private String legaldocStat;
	private String legaldocStatVal;
	private String hgPreCreator;


	/**
	 * 上报TA状态上报状态 0-未上报、1-上报中、2-上报成功、3-需重新上报、4-撤回上报、5-无需上报
	 */
	private String submitStatus;


	/**
	 * 香港交易账号
	 */
	private String hkTxAcctNo;

	/**
	 * 预约的架构分类。 0-海外（中台）预约    1-高端（中台）代销  2-直销的预约
	 */
	private String archType;

	/**
	 * 	售前留痕材料上传方式 0-自主上传资料、1-自动发送客户邮箱
	 */
	private String legalDocUploadMethod;

	/**
	 * 售前留痕材料上传方式-描述
	 */
	private String legalDocUploadMethodVal;

}
