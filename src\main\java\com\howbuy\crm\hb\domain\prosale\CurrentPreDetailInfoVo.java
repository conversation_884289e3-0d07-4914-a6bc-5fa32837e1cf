package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 *
 */
public class CurrentPreDetailInfoVo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	/**
	 * 预约id
	 */
	private String preId;
	/**
	 * 预计交易日期
	 */
	private String expecttradedt;
	/**
	 * 投顾客户号
	 */
	private String conscustno;
	/**
	 * 客户姓名
	 */
	private String custname;
	/**
	 * 创建人
	 */
	private String creator;
	/**
	 * 所属投顾
	 */
	private String consname;
	/**
	 * 所属部门
	 */
	private String outletName;
	/**
	 * 所属区域
	 */
	private String uporgname;
	/**
	 * 产品代码
	 */
	private String fundcode;
	/**
	 * 销售类型
	 */
	private String sfmsjg;
	/**
	 * 销售类型val:1为代销，2为直销，3为直转代
	 */
	private String sfmsjgVal;
	/**
	 * 是否分次call产品：1-是 0-否
	 */
	private String fccl;
	/**
	 * 预约类型
	 */
	private String pretype;
	/**
	 * 预约类型解析
	 */
	private String pretypeVal;
	/**
	 * 是否可线上下单
	 */
	private String islineorder;
	/**
	 * 下单状态
	 */
	private String orderstate;
	/**
	 * 下单状态解析
	 */
	private String orderstateVal;
	/**
	 * 线上下单时间
	 */
	private String ordertime;
	/**
	 * 认缴金额
	 */
	private BigDecimal totalamt;
	/**
	 * 预约购买金额
	 */
	private BigDecimal buyamt;
	/**
	 * 中台申请金额
	 */
	private BigDecimal ztbuyamt;
	/**
	 * 打款状态
	 */
	private String paystate;
	/**
	 * 打款状态
	 */
	private String paystateVal;
	/**
	 * 实际打款日期
	 */
	private String realpayamtdt;
	/**
	 * 实际打款金额
	 */
	private BigDecimal realpayamt;
	/**
	 * 实际打款手续费
	 */
	private BigDecimal realfee;
	/**
	 * 交易类型
	 */
	private String tradeType;
	/**
	 * 支付方式
	 */
	private String paymenttype;
	/**
	 * 支付方式解析
	 */
	private String paymenttypeVal;
	/**
	 *是否资金已匹配 code   1-是 0-否
	 */
	private String finMatched;

	/**
	 *是否资金已匹配 描述 1-是 0-否
	 */
	private String finMatchedDesc;
	/**
	 * 冷静期截止时间
	 */
	private String calmdatetime;
	/**
	 * 赎回份额
	 */
	private BigDecimal sellvol;
	/**
	 * 中台订单id
	 */
	private String dealno;
	/**
	 * 是否直销（包括直转代黑名单中的）：1-是 0-否
	 */
	private String iszx;
	/**
	 * 赎回至
	 */
	private String redeemdirection;
	/**
	 * 赎回至
	 */
	private String redeemdirectionVal;
	/**
	 * 折扣状态
	 */
	private String discountstate;
	/**
	 * 折扣状态解析
	 */
	private String discountstateVal;
	/**
	 * 一账通
	 */
	private String hboneno;
	/**
	 * 资产证明状态
	 */
	private String zczmstate;
	/**
	 * 证件有效期
	 */
	private String validitydt;
	/**
	 * 好买风险到期日
	 */
	private String hmfcdqr;
	/**
	 * 好臻风测到期日
	 */
	private String hzfcdqr;

	public String getPreId() {
		return preId;
	}
	public void setPreId(String preId) {
		this.preId = preId;
	}
	public String getExpecttradedt() {
		return expecttradedt;
	}
	public void setExpecttradedt(String expecttradedt) {
		this.expecttradedt = expecttradedt;
	}
	public String getConscustno() {
		return conscustno;
	}
	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	public String getCustname() {
		return custname;
	}
	public void setCustname(String custname) {
		this.custname = custname;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public String getConsname() {
		return consname;
	}
	public void setConsname(String consname) {
		this.consname = consname;
	}
	public String getOutletName() {
		return outletName;
	}
	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}
	public String getUporgname() {
		return uporgname;
	}
	public void setUporgname(String uporgname) {
		this.uporgname = uporgname;
	}
	public String getFundcode() {
		return fundcode;
	}
	public void setFundcode(String fundcode) {
		this.fundcode = fundcode;
	}
	public String getSfmsjg() {
		return sfmsjg;
	}
	public void setSfmsjg(String sfmsjg) {
		this.sfmsjg = sfmsjg;
	}
	public String getSfmsjgVal() {
		return sfmsjgVal;
	}
	public void setSfmsjgVal(String sfmsjgVal) {
		this.sfmsjgVal = sfmsjgVal;
	}
	public String getPretype() {
		return pretype;
	}
	public void setPretype(String pretype) {
		this.pretype = pretype;
	}
	public String getPretypeVal() {
		return pretypeVal;
	}
	public void setPretypeVal(String pretypeVal) {
		this.pretypeVal = pretypeVal;
	}
	public String getIslineorder() {
		return islineorder;
	}
	public void setIslineorder(String islineorder) {
		this.islineorder = islineorder;
	}
	public String getOrderstate() {
		return orderstate;
	}
	public void setOrderstate(String orderstate) {
		this.orderstate = orderstate;
	}
	public String getOrderstateVal() {
		return orderstateVal;
	}
	public void setOrderstateVal(String orderstateVal) {
		this.orderstateVal = orderstateVal;
	}
	public String getOrdertime() {
		return ordertime;
	}
	public void setOrdertime(String ordertime) {
		this.ordertime = ordertime;
	}
	public BigDecimal getTotalamt() {
		return totalamt;
	}
	public void setTotalamt(BigDecimal totalamt) {
		this.totalamt = totalamt;
	}
	public BigDecimal getBuyamt() {
		return buyamt;
	}
	public void setBuyamt(BigDecimal buyamt) {
		this.buyamt = buyamt;
	}
	public String getPaystate() {
		return paystate;
	}
	public void setPaystate(String paystate) {
		this.paystate = paystate;
	}
	public String getPaystateVal() {
		return paystateVal;
	}
	public void setPaystateVal(String paystateVal) {
		this.paystateVal = paystateVal;
	}
	public String getRealpayamtdt() {
		return realpayamtdt;
	}
	public void setRealpayamtdt(String realpayamtdt) {
		this.realpayamtdt = realpayamtdt;
	}
	public BigDecimal getRealpayamt() {
		return realpayamt;
	}
	public void setRealpayamt(BigDecimal realpayamt) {
		this.realpayamt = realpayamt;
	}
	public BigDecimal getRealfee() {
		return realfee;
	}
	public void setRealfee(BigDecimal realfee) {
		this.realfee = realfee;
	}
	public String getTradeType() {
		return tradeType;
	}
	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}
	public String getPaymenttype() {
		return paymenttype;
	}
	public void setPaymenttype(String paymenttype) {
		this.paymenttype = paymenttype;
	}
	public String getPaymenttypeVal() {
		return paymenttypeVal;
	}
	public void setPaymenttypeVal(String paymenttypeVal) {
		this.paymenttypeVal = paymenttypeVal;
	}

	public String getCalmdatetime() {
		return calmdatetime;
	}
	public void setCalmdatetime(String calmdatetime) {
		this.calmdatetime = calmdatetime;
	}
	public BigDecimal getSellvol() {
		return sellvol;
	}
	public void setSellvol(BigDecimal sellvol) {
		this.sellvol = sellvol;
	}
	public String getDealno() {
		return dealno;
	}
	public void setDealno(String dealno) {
		this.dealno = dealno;
	}
	public String getIszx() {
		return iszx;
	}
	public void setIszx(String iszx) {
		this.iszx = iszx;
	}
	public String getRedeemdirection() {
		return redeemdirection;
	}
	public void setRedeemdirection(String redeemdirection) {
		this.redeemdirection = redeemdirection;
	}
	public String getRedeemdirectionVal() {
		return redeemdirectionVal;
	}
	public void setRedeemdirectionVal(String redeemdirectionVal) {
		this.redeemdirectionVal = redeemdirectionVal;
	}
	public String getDiscountstate() {
		return discountstate;
	}
	public void setDiscountstate(String discountstate) {
		this.discountstate = discountstate;
	}
	public String getDiscountstateVal() {
		return discountstateVal;
	}
	public void setDiscountstateVal(String discountstateVal) {
		this.discountstateVal = discountstateVal;
	}
	public String getHboneno() {
		return hboneno;
	}
	public void setHboneno(String hboneno) {
		this.hboneno = hboneno;
	}
	public String getFccl() {
		return fccl;
	}
	public void setFccl(String fccl) {
		this.fccl = fccl;
	}
	public BigDecimal getZtbuyamt() {
		return ztbuyamt;
	}
	public void setZtbuyamt(BigDecimal ztbuyamt) {
		this.ztbuyamt = ztbuyamt;
	}
	public String getZczmstate() {
		return zczmstate;
	}
	public void setZczmstate(String zczmstate) {
		this.zczmstate = zczmstate;
	}
	public String getValiditydt() {
		return validitydt;
	}
	public void setValiditydt(String validitydt) {
		this.validitydt = validitydt;
	}
	public String getHmfcdqr() {
		return hmfcdqr;
	}
	public void setHmfcdqr(String hmfcdqr) {
		this.hmfcdqr = hmfcdqr;
	}
	public String getHzfcdqr() {
		return hzfcdqr;
	}
	public void setHzfcdqr(String hzfcdqr) {
		this.hzfcdqr = hzfcdqr;
	}

	public String getFinMatched() {
		return finMatched;
	}

	public void setFinMatched(String finMatched) {
		this.finMatched = finMatched;
	}

	public String getFinMatchedDesc() {
		return finMatchedDesc;
	}

	public void setFinMatchedDesc(String finMatchedDesc) {
		this.finMatchedDesc = finMatchedDesc;
	}
}
