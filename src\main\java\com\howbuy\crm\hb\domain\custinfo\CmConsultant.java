package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmConsultant implements Serializable {

    private static final long serialVersionUID = -8090330991942412012L;

    private String conscode;

    private String consname;

    private String conslevel;

    private String teamcode;

    private String teamname;

    private String character;

    private String consstatus;

    private String recstat;

    private String checkflag;

    private String creator;

    private String modifier;

    private String checker;

    private String credt;

    private String moddt;

    private String telno;

    private String mobile;

    private String deptcode;

    private String outletcode;

    private String outletname;

    private String pictureurl;

    private String email;

    private String resume;

    private String startdt;

    private String enddt;

    private String empcardno;

    private String loginflag;

    private String isseniormgr;

    private String workplace;

    private String roleId;

    private String roleName;

    private String managerrole;

    private String dialchannel;

    private String ip;

    private String isvirtual;

    private String isinside;

    private String picaddr;

    private String custno;

	private String seniormgrcode;
}
