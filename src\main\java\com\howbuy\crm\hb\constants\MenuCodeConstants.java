package com.howbuy.crm.hb.constants;

/**
 * <AUTHOR>
 * @description: 菜单编码常量
 * @date 2024/4/17 15:47
 * @since JDK 1.8
 */
public class MenuCodeConstants {

    /**
     * 配置-客户来源系数
     */
    public static final String CUST_SOURCE_COEFF_MENU_CODE = "071611";

    /**
     * 季度绩效-结果
     */
    public static final String PRP_RESULT_MENU_CODE = "071610";

    /**
     * 季度绩效-复核
     */
    public static final String PRP_CHECK_MENU_CODE = "071609";

    /**
     *季度绩效-核算
     */
    public static final String PRP_CAL_MENU_CODE = "071608";

    /**
     *调整各项系数
     */
    public static final String ADJUST_COEFF_MENU_CODE = "071607";

    /**
     *配置-订单类型系数
     */
    public static final String PRP_ORDER_COEFF_MENU_CODE = "071606";

    /**
     *配置-初始交易次数
     */
    public static final String TRADE_NUM_MENU_CODE = "071604";

    /**
     *配置-来源系数
     */
    public static final String SOURCE_COEFF_MENU_CODE = "071603";

    /**
     *配置-产品系数
     */
    public static final String PRODUCT_COEFF_MENU_CODE = "071601";

    /**
     *配置-职级薪资
     */
    public static final String ZJ_SALE_MENU_CODE = "B09010803";

    /**
     *留档-职级薪资
     */
    public static final String ZJ_SALE_ARCH_MENU_CODE = "B09010804";
    /**
     *员工状态统计报表
     */
    public static final String CONSULT_EXP_COUNT_MENU_CODE = "090301";

    /**
     *花名册
     */
    public static final String CONSULT_EXP_MENU_CODE = "090120";

    /**
     *客户企业微信添加率
     */
    public static final String WECHAT_ADD_MENU_CODE = "140802";

    /**
     *已退款预约
     */
    public static final String REFUND_PRE_MENU_CODE = "07150305";

    /**
     *个人绩效明细
     */
    public static final String CONS_DETAIL_MENU_CODE = "07150304";


    /**
     * 生日礼品记录
     */
    public static final String BIRTHDAY_GIFT_MENU_CODE = "020149";

}
