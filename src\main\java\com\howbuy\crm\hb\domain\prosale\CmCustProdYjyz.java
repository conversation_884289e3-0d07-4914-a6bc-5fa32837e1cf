package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;

/**
 * @Description: 实体类CmCustProdYjyz.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmCustProdYjyz implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String hboneno;
	
	private String custname;
	
	private String fundcode;

	private String fundname;

	private String jjrq;

	private String yjyz;
	
	private String curryjyz;

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getHboneno() {
		return this.hboneno;
	}

	public void setHboneno(String hboneno) {
		this.hboneno = hboneno;
	}

	public String getFundcode() {
		return this.fundcode;
	}

	public void setFundcode(String fundcode) {
		this.fundcode = fundcode;
	}

	public String getJjrq() {
		return this.jjrq;
	}

	public void setJjrq(String jjrq) {
		this.jjrq = jjrq;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getFundname() {
		return fundname;
	}

	public void setFundname(String fundname) {
		this.fundname = fundname;
	}

	public String getYjyz() {
		return this.yjyz;
	}

	public void setYjyz(String yjyz) {
		this.yjyz = yjyz;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getCurryjyz() {
		return curryjyz;
	}

	public void setCurryjyz(String curryjyz) {
		this.curryjyz = curryjyz;
	}
}
