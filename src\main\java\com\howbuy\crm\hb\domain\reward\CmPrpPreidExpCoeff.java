package com.howbuy.crm.hb.domain.reward;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: 预约各项系数调整
 * @reason:
 * @Date: 2020/9/18 18:12
 */
@Data
public class CmPrpPreidExpCoeff implements Serializable {

    private static final long serialVersionUID = 1L;

    private String preId;
    private String accountDt;
    private BigDecimal foldCoeff;
    private BigDecimal commissionRate;
    private BigDecimal annuallySetAward;
    private BigDecimal secondStockCoeff;
    private BigDecimal stockFeeA;
    private BigDecimal stockFeeB;
    private BigDecimal operFoldCoeff;
    /** 添加客户来源系数相关修改项 */
    private BigDecimal sourceCoeff;
    private BigDecimal zbCoeff;
    private BigDecimal manageCoeff;

    /**
     * 管理系数区副
     */
    private BigDecimal manageCoeffRegionalsubtotal;
    /**
     * 管理系数区总
     */
    private BigDecimal manageCoeffRegionaltotal;


    /**
     * 调整系数
     */
    private BigDecimal unqualifiedCoeff;
    /**
     * 折扣金额
     */
    private BigDecimal beforetaxamt;

    private String cxa;
    private String cxb;

    /**
     * 权益折扣率
     */
    private BigDecimal interestsRate;

    private String creator;
    private String createTime;
    private String modor;
    private String updateTime;

}
