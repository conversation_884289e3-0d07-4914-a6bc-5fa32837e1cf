package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Description: 实体类Vconscust.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class WaitTransferCust implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustno;
	
	private String custname;
	
	private String conscode;
	
	private String outletcode;
	
	private String orgname;
	
	private String firtrdt;
	
	private String latesttradedt;
	
	private String gdcjlabel;
	
	private String gdqzlabel;
	
	private String gdlabelval;
	
	private BigDecimal prvamt;
	
	private BigDecimal pubamt;
	
	private String zjllabel;
	
	private String khyxlabel;
	
	private String zjyxlabelval;
	
	private String visittime;
	
	private String visitsummary;
	
	private String sourcename;
	
	private String directcons;
	
	private String pubcustno;
	
	private String cjbyk;
	
	private String yzqzk;
	
	private String ybqzk;
	
	private String mcallk;
	
	private String gmk;
	
	private String slk;
	
	/**
	 * 公司来源库
	 */
	private String gslyk;
	
	/**
	 * 投顾来源库
	 */
	private String tglyk;
	
	/**
	 * 客户第一来源
	 */
	private String firstsourcetype;
	
	/**
	 * 潜客失联库
	 */
	private String qkslk;
	
	private String istwokhtj;
	
	private String istwotyjz;
	
	/**
	 * 指定承接人
	 */
	private String nextcons;
	
	/**
	 * 状态1：待审核；2：待复核
	 */
	private String state;

	/**
	 * 状态枚举值1：待审核；2：待复核
	 */
	private String stateVal;
	
	/**
	 * 划转原因
	 */
	private String reason;
    /**
     * 划转操作人
     */
	private String auditMan;

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getOutletcode() {
		return outletcode;
	}

	public void setOutletcode(String outletcode) {
		this.outletcode = outletcode;
	}

	public String getFirtrdt() {
		return firtrdt;
	}

	public void setFirtrdt(String firtrdt) {
		this.firtrdt = firtrdt;
	}

	public String getLatesttradedt() {
		return latesttradedt;
	}

	public void setLatesttradedt(String latesttradedt) {
		this.latesttradedt = latesttradedt;
	}

	public String getGdcjlabel() {
		return gdcjlabel;
	}

	public void setGdcjlabel(String gdcjlabel) {
		this.gdcjlabel = gdcjlabel;
	}

	public String getGdqzlabel() {
		return gdqzlabel;
	}

	public void setGdqzlabel(String gdqzlabel) {
		this.gdqzlabel = gdqzlabel;
	}

	public BigDecimal getPrvamt() {
		return prvamt;
	}

	public void setPrvamt(BigDecimal prvamt) {
		this.prvamt = prvamt;
	}

	public BigDecimal getPubamt() {
		return pubamt;
	}

	public void setPubamt(BigDecimal pubamt) {
		this.pubamt = pubamt;
	}

	public String getZjllabel() {
		return zjllabel;
	}

	public void setZjllabel(String zjllabel) {
		this.zjllabel = zjllabel;
	}

	public String getKhyxlabel() {
		return khyxlabel;
	}

	public void setKhyxlabel(String khyxlabel) {
		this.khyxlabel = khyxlabel;
	}

	public String getVisittime() {
		return visittime;
	}

	public void setVisittime(String visittime) {
		this.visittime = visittime;
	}

	public String getVisitsummary() {
		return visitsummary;
	}

	public void setVisitsummary(String visitsummary) {
		this.visitsummary = visitsummary;
	}

	public String getSourcename() {
		return sourcename;
	}

	public void setSourcename(String sourcename) {
		this.sourcename = sourcename;
	}

	public String getDirectcons() {
		return directcons;
	}

	public void setDirectcons(String directcons) {
		this.directcons = directcons;
	}

	public String getGdlabelval() {
		return gdlabelval;
	}

	public void setGdlabelval(String gdlabelval) {
		this.gdlabelval = gdlabelval;
	}

	public String getZjyxlabelval() {
		return zjyxlabelval;
	}

	public void setZjyxlabelval(String zjyxlabelval) {
		this.zjyxlabelval = zjyxlabelval;
	}

	public String getPubcustno() {
		return pubcustno;
	}

	public void setPubcustno(String pubcustno) {
		this.pubcustno = pubcustno;
	}

	public String getCjbyk() {
		return cjbyk;
	}

	public void setCjbyk(String cjbyk) {
		this.cjbyk = cjbyk;
	}

	public String getYzqzk() {
		return yzqzk;
	}

	public void setYzqzk(String yzqzk) {
		this.yzqzk = yzqzk;
	}

	public String getYbqzk() {
		return ybqzk;
	}

	public void setYbqzk(String ybqzk) {
		this.ybqzk = ybqzk;
	}

	public String getMcallk() {
		return mcallk;
	}

	public void setMcallk(String mcallk) {
		this.mcallk = mcallk;
	}

	public String getGmk() {
		return gmk;
	}

	public void setGmk(String gmk) {
		this.gmk = gmk;
	}

	public String getOrgname() {
		return orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}

	public String getSlk() {
		return slk;
	}

	public void setSlk(String slk) {
		this.slk = slk;
	}

	public String getGslyk() {
		return gslyk;
	}

	public void setGslyk(String gslyk) {
		this.gslyk = gslyk;
	}

	public String getTglyk() {
		return tglyk;
	}

	public void setTglyk(String tglyk) {
		this.tglyk = tglyk;
	}

	public String getFirstsourcetype() {
		return firstsourcetype;
	}

	public void setFirstsourcetype(String firstsourcetype) {
		this.firstsourcetype = firstsourcetype;
	}

	public String getQkslk() {
		return qkslk;
	}

	public void setQkslk(String qkslk) {
		this.qkslk = qkslk;
	}

	public String getIstwokhtj() {
		return istwokhtj;
	}

	public void setIstwokhtj(String istwokhtj) {
		this.istwokhtj = istwokhtj;
	}

	public String getIstwotyjz() {
		return istwotyjz;
	}

	public void setIstwotyjz(String istwotyjz) {
		this.istwotyjz = istwotyjz;
	}

	public String getNextcons() {
		return nextcons;
	}

	public void setNextcons(String nextcons) {
		this.nextcons = nextcons;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getStateVal() {
		return stateVal;
	}

	public void setStateVal(String stateVal) {
		this.stateVal = stateVal;
	}

    public String getAuditMan() {
        return auditMan;
    }

    public void setAuditMan(String auditMan) {
        this.auditMan = auditMan;
    }
}
