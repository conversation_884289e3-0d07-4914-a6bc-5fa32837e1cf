package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Description: 实体类CmReviewRecordCount.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmReviewRecordCount implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;
	private String custtype;
	private String reviewtype;
	private BigDecimal recordcount;
	public BigDecimal getId() {
		return id;
	}
	public void setId(BigDecimal id) {
		this.id = id;
	}
	public String getCusttype() {
		return custtype;
	}
	public void setCusttype(String custtype) {
		this.custtype = custtype;
	}
	public String getReviewtype() {
		return reviewtype;
	}
	public void setReviewtype(String reviewtype) {
		this.reviewtype = reviewtype;
	}
	public BigDecimal getRecordcount() {
		return recordcount;
	}
	public void setRecordcount(BigDecimal recordcount) {
		this.recordcount = recordcount;
	}
	
}
