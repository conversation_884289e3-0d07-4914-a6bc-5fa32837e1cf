/*
package com.howbuy.crm.hb.service.counter.cmcounterorderserviceimpl;

import com.howbuy.crm.hb.domain.counter.CmCounterBusiness;
import com.howbuy.crm.hb.enums.CounterCheckStatusLevelEnum;
import com.howbuy.crm.hb.service.counter.CmCounterBusinessService;
import com.howbuy.crm.hb.service.counter.impl.CmCounterOrderServiceImpl;
import com.howbuy.crm.page.framework.domain.User;
import crm.howbuy.base.constants.StaticVar;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.testng.Assert;

import static org.mockito.ArgumentMatchers.anyString;

*/
/**
 * 单元测试：根据投顾客户号和业务细分id获取当前资料的审核状态及审核级别
 * <AUTHOR>
 * @date 2022/3/24 16:50
 *//*

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest(CmCounterOrderServiceImpl.class)
public class TestGetCounterCheckStatusLevel {

    @Mock
    private CmCounterOrderServiceImpl cmCounterOrderService;
    @Mock
    private CmCounterBusinessService cmCounterBusinessService;
    public static final String TEST_CONSCUSTNO = "TEST_CONSCUSTNO";
    public static final String TEST_BDID = "TEST_BDID";

    */
/**
     * 投顾客户号为空
     *//*

    @Test
    public void testEmptyConscustno() {
        Assert.expectThrows(RuntimeException.class, () -> {
            String conscustno = null;
            String bdid = "";
            PowerMockito.when(cmCounterOrderService.getCounterCheckStatusLevel(conscustno, bdid)).thenCallRealMethod();
            cmCounterOrderService.getCounterCheckStatusLevel(conscustno, bdid);
        });
    }

    */
/**
     * 业务类型没取到
     *//*

    @Test
    public void testBusiidIsNull() {
        Assert.expectThrows(RuntimeException.class, () -> {
            mockBusiidIsNull();
            cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID);
        });
    }

    */
/**
     * 关联账户或私募定投业务
     *//*

    @Test
    public void testIsRelateFixedBusiness() {
        mockIsRelateFixedBusiness();
        CounterCheckStatusLevelEnum counterCheckStatusLevel = cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID);
        Assert.assertEquals(counterCheckStatusLevel, CounterCheckStatusLevelEnum.DEPT_PASS);
    }

    */
/**
     * 客户所属投顾的所属组织架构不属于IC/HBC
     *//*

    @Test
    public void testCustNotIcHbc() {
        mockCustNotIcHbc();
        CounterCheckStatusLevelEnum counterCheckStatusLevel = cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID);
        Assert.assertEquals(counterCheckStatusLevel, CounterCheckStatusLevelEnum.DEPT_PASS);
    }

    */
/**
     * 上传人是地区投顾助理或高端柜台业务处理
     *//*

    @Test
    public void testUploaderIsAssistantOP() {
        mockUploaderIsAssistantOP();
        CounterCheckStatusLevelEnum counterCheckStatusLevel = cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID);
        Assert.assertEquals(counterCheckStatusLevel, CounterCheckStatusLevelEnum.DEPT_PASS);
    }

    */
/**
     * 是香港业务
     *//*

    @Test
    public void testIsHKBusiness() {
        mockIsHKBusiness();
        CounterCheckStatusLevelEnum counterCheckStatusLevel = cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID);
        Assert.assertEquals(counterCheckStatusLevel, CounterCheckStatusLevelEnum.DEPT_PASS);
    }

    */
/**
     * 默认情况
     *//*

    @Test
    public void testDefaultSituation() {
        mockDefaultSituation();
        CounterCheckStatusLevelEnum counterCheckStatusLevel = cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID);
        Assert.assertEquals(counterCheckStatusLevel, CounterCheckStatusLevelEnum.WAIT_CHECK);
    }

    */
/**
     * mock 业务类型没取到
     *//*

    @SneakyThrows
    private void mockBusiidIsNull() {
        PowerMockito.when(cmCounterBusinessService.getCmCounterBusinessById(TEST_BDID)).thenReturn(null);
        PowerMockito.when(cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID)).thenCallRealMethod();
        MemberModifier.field(CmCounterOrderServiceImpl.class, "cmCounterBusinessService").set(cmCounterOrderService, cmCounterBusinessService);
    }

    */
/**
     * mock 关联账户或私募定投业务
     *//*

    @SneakyThrows
    private void mockIsRelateFixedBusiness() {
        CmCounterBusiness cmCounterBusiness = new CmCounterBusiness();
        cmCounterBusiness.setBusiid("41");
        PowerMockito.when(cmCounterBusinessService.getCmCounterBusinessById(TEST_BDID)).thenReturn(cmCounterBusiness);
        PowerMockito.when(cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID)).thenCallRealMethod();
        PowerMockito.when(cmCounterOrderService, "checkIsRelateFixedBusiness", anyString()).thenReturn(true);
        MemberModifier.field(CmCounterOrderServiceImpl.class, "cmCounterBusinessService").set(cmCounterOrderService, cmCounterBusinessService);
    }

    */
/**
     * mock 客户所属投顾的所属组织架构不属于IC/HBC
     *//*

    @SneakyThrows
    private void mockCustNotIcHbc() {
        CmCounterBusiness cmCounterBusiness = new CmCounterBusiness();
        cmCounterBusiness.setBusiid("13");
        PowerMockito.when(cmCounterBusinessService.getCmCounterBusinessById(TEST_BDID)).thenReturn(cmCounterBusiness);
        PowerMockito.when(cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID)).thenCallRealMethod();
        PowerMockito.when(cmCounterOrderService, "checkIsRelateFixedBusiness", anyString()).thenReturn(false);
        PowerMockito.when(cmCounterOrderService, "checkIsIcHbc", anyString()).thenReturn(false);
        MemberModifier.field(CmCounterOrderServiceImpl.class, "cmCounterBusinessService").set(cmCounterOrderService, cmCounterBusinessService);
    }

    */
/**
     * mock 上传人是地区投顾助理或高端柜台业务处理
     *//*

    @SneakyThrows
    private void mockUploaderIsAssistantOP() {
        CmCounterBusiness cmCounterBusiness = new CmCounterBusiness();
        cmCounterBusiness.setBusiid("13");
        PowerMockito.when(cmCounterBusinessService.getCmCounterBusinessById(TEST_BDID)).thenReturn(cmCounterBusiness);
        PowerMockito.when(cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID)).thenCallRealMethod();
        PowerMockito.when(cmCounterOrderService, "checkIsRelateFixedBusiness", anyString()).thenReturn(false);
        PowerMockito.when(cmCounterOrderService, "checkIsIcHbc", anyString()).thenReturn(true);
        PowerMockito.when(cmCounterOrderService, "checkUploaderIsAssistantOP").thenReturn(true);
        User loginUser = new User();
        loginUser.setUserId("chun.lin");
        PowerMockito.when(cmCounterOrderService, "getLoginUser").thenReturn(loginUser);
        MemberModifier.field(CmCounterOrderServiceImpl.class, "cmCounterBusinessService").set(cmCounterOrderService, cmCounterBusinessService);
    }

    */
/**
     * mock 是香港业务
     *//*

    @SneakyThrows
    private void mockIsHKBusiness() {
        CmCounterBusiness cmCounterBusiness = new CmCounterBusiness();
        cmCounterBusiness.setBusiid("13");
        cmCounterBusiness.setBusiarea(StaticVar.COUNTER_BUSIAREA_HK);
        PowerMockito.when(cmCounterBusinessService.getCmCounterBusinessById(TEST_BDID)).thenReturn(cmCounterBusiness);
        PowerMockito.when(cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID)).thenCallRealMethod();
        PowerMockito.when(cmCounterOrderService, "checkIsRelateFixedBusiness", anyString()).thenReturn(false);
        PowerMockito.when(cmCounterOrderService, "checkIsIcHbc", anyString()).thenReturn(true);
        PowerMockito.when(cmCounterOrderService, "checkUploaderIsAssistantOP").thenReturn(false);
        PowerMockito.when(cmCounterOrderService, "checkIsHKBusiness", anyString()).thenReturn(true);
        MemberModifier.field(CmCounterOrderServiceImpl.class, "cmCounterBusinessService").set(cmCounterOrderService, cmCounterBusinessService);
    }

    */
/**
     * mock 默认情况
     *//*

    @SneakyThrows
    private void mockDefaultSituation() {
        CmCounterBusiness cmCounterBusiness = new CmCounterBusiness();
        cmCounterBusiness.setBusiid("13");
        cmCounterBusiness.setBusiid(StaticVar.COUNTER_BUSIAREA_DL);
        PowerMockito.when(cmCounterBusinessService.getCmCounterBusinessById(TEST_BDID)).thenReturn(cmCounterBusiness);
        PowerMockito.when(cmCounterOrderService.getCounterCheckStatusLevel(TEST_CONSCUSTNO, TEST_BDID)).thenCallRealMethod();
        PowerMockito.when(cmCounterOrderService, "checkIsRelateFixedBusiness", anyString()).thenReturn(false);
        PowerMockito.when(cmCounterOrderService, "checkIsIcHbc", anyString()).thenReturn(true);
        PowerMockito.when(cmCounterOrderService, "checkUploaderIsAssistantOP").thenReturn(false);
        PowerMockito.when(cmCounterOrderService, "checkIsHKBusiness", anyString()).thenReturn(false);
        MemberModifier.field(CmCounterOrderServiceImpl.class, "cmCounterBusinessService").set(cmCounterOrderService, cmCounterBusinessService);
    }
}*/
