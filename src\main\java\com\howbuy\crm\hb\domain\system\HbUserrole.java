package com.howbuy.crm.hb.domain.system;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class HbUserrole implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer id;

	private String usercode;

	private String username;

	private String rolecode;

	private String rolename;

	private String recstat;

	private String creator;

	private String modifier;

	private String credt;

	private String moddt;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getUsercode() {
		return this.usercode;
	}

	public void setUsercode(String usercode) {
		this.usercode = usercode;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getRolecode() {
		return this.rolecode;
	}

	public void setRolecode(String rolecode) {
		this.rolecode = rolecode;
	}

	public String getRolename() {
		return rolename;
	}

	public void setRolename(String rolename) {
		this.rolename = rolename;
	}

	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public String toString() {
		return "HbUserrole [id=" + id + ", usercode=" + usercode + ",username=" + username + ", rolecode=" + rolecode
				+ ", rolename=" + rolename + ", recstat=" + recstat + ", creator=" + creator + ", modifier=" + modifier
				+ ", credt=" + credt + ", moddt=" + moddt + "]";
	}

}
