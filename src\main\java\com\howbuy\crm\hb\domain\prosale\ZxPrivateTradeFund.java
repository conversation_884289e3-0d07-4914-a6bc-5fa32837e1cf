package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class ZxPrivateTradeFund implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 姓名
	 */
	private String custname;
	/**
	 * 投顾客户号
	 */
	private String custno;
	private String hboneno;
	private String consname;
	/**
	 * 所属投顾
	 */
	private String conscode;
	private String fundcode;
	private String fundname;
	
	/**
	 * 所属区域
	 */
	private String uporgname;
	/**
	 * 所属部门
	 */
	private String outletName;
	
	private String outletcode;
	
	/**
	 * 当前份额
	 */
	private BigDecimal balancevol;
	/**
	 * 当前市值
	 */
	private BigDecimal balanceamt;
	
	/**
	 * 净值
	 */
	private BigDecimal nav;
	
	/**
	 * 线上下单时间
	 */
	private String navdt;
	
	/**
	 * 币种
	 */
	private String currency;
	
	/**
	 * 平衡因子
	 */
	private BigDecimal balancefactor;
}
