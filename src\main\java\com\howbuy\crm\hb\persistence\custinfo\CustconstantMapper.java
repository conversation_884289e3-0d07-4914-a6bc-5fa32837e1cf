package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.conscust.CustConsAndMgrCodeDTO;
import com.howbuy.crm.hb.domain.conscust.Custconstant;
import org.apache.ibatis.annotations.Param;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CustconstantMapper {
    /**
     * 根据投顾客户号查询投顾客户维护关系
     * @param param
     * @return
     */
    List<Map<String, String>> ajaxGetCustConsByConscust(Map<String, String> param);

    /**
     * 获取客户数
     * @param param
     * @return
     */
    int hadCustNum(Map<String, String> param);
    
    /**
	 * 根据投顾客户号查询最近三条拜访记录
	 * @param param
	 * @return
	 */
	public List<Map<String,String>> ajaxGetCustVisitInfo(Map<String, String> param);
	
	/**
	 * 获取组数
	 * @param param
	 * @return
	 */
	public int hadCustGroupNum(Map<String, String> param);
	
	/**
	 * 根据客户号查询客户的投顾和高管
	 * @param param
	 * @return
	 */
	public Custconstant getConscodeMgrcodeByCustNo(Map<String, String> param);

	/**
	 * @description: 根据客户号查询客户的投顾和高管
	 * @param custNo
	 * @return com.howbuy.crm.hb.domain.conscust.Custconstant
	 * @author: jin.wang03
	 * @date: 2024/9/25 9:55
	 * @since JDK 1.8
	 */
	Custconstant getConscCdeMgrCodeByCustNo(@Param("custNo") String custNo);

	/**
	 * @description: 根据客户号list查询客户的投顾和高管
	 * @param custNoList
	 * @return java.util.List<com.howbuy.crm.hb.domain.conscust.Custconstant>
	 * @author: jin.wang03
	 * @date: 2024/9/25 17:42
	 * @since JDK 1.8
	 */
	List<CustConsAndMgrCodeDTO> getCustconstantByCustNoList(@Param("custNoList") List<String> custNoList);

}
