package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.ConscustRepeat;
import com.howbuy.crm.hb.domain.custinfo.ConscustOperation;

import crm.howbuy.base.db.CommPageBean;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ConscustIcMapper {

	/**
	 * 得到单个数据对象
	 * @param param
	 * @return
	 */
	ConscustRepeat getConscustIcByRepeat(Map<String, String> param);

	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<ConscustOperation> listConscustOperationByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 根据客户号删除潜在客户
	 * @param map
	 */
	void deleteConscustIcByConscustno(Map<String,String> map);

	/**
	 * 更新操作人信息
	 * @param operationid
	 */
	void updateConscustOperation(String operationid);
}
