package com.howbuy.crm.hb.domain.wage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CrmWageQuaterDetail implements Serializable {

    private static final long serialVersionUID = -6430037841855553488L;
    private String id;
    private String yyyy;
    private String qu;
    private String depart;
    private String conscode;
    private String consname;
    private String outletcode;
    private String teamcode;
    private String highratio;
    private String pubratio;
    private String outdetail;
    private String indetail;
    private String tranfvol;
    private String longserviceprize;
    private String selfbuy;
    private String manageamt;
    private String other;
    private String reissue;
    private String deduct;
    private String totalamt;
    private String remark;
    private String creator;
    private Date creatdt;
    private String modifier;
    private Date modifydt;
    private String innovatecoeff;
    private String finalcoeff;
    private String unqualifiedcoeff;
    private String quarterpreform;
    private String stockcall;
    
    private String xjh;
    private String houseoffic;
    private String currpersonhz;
    private String currpersonhzeight;
    private String currpersonhztwo;
    private String cxa;
    private String cxb;
    private String mgmsal;
    private String needsal;
    
    private String userno;
    private String centerName;
    private String areaname;
    
}
