package com.howbuy.crm.hb.domain.reward;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/9/11 13:58
 */
public class CmPrpSourceStart implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;

	private String orgcode;
	
	private String orgname;
	
	private String sourceType;
	
	private String sourceTypeVal;
	
	private String startPoint;

	private String creator;

	private Date createTime;

	private String modor;
	
	private Date updateTime;

	public BigDecimal getId() {
		return this.id;
	}

	public void setId(BigDecimal id) {
		this.id = id;
	}

	public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	public String getOrgname() {
		return orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	public String getSourceTypeVal() {
		return sourceTypeVal;
	}

	public void setSourceTypeVal(String sourceTypeVal) {
		this.sourceTypeVal = sourceTypeVal;
	}

	public String getStartPoint() {
		return startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getModor() {
		return modor;
	}

	public void setModor(String modor) {
		this.modor = modor;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
