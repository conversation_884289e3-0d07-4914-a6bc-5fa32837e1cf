/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.hkconscust;

import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 香港客户信息VO类，用于接收CRM账户服务返回的香港客户信息数据，继承香港客户信息实体类，方便扩展属性字段
 * <AUTHOR>
 * @date 2023/12/14 13:37
 * @since JDK 1.8
 */
public class HkConscustVO extends HkConscust {

    /**
     *香港客户状态 描述翻译
     */
    private String hkCustStatus;

    /**
     * {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}
     * 香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
     */
    private String hkCustStatusCode;

    /**
     * eborkerId
     */
    private String ebrokerId;


    /**
     * 香港客户展示名（优先取中文名、其次取英文名）
     */
    private String custShowName;

    /**
     * 香港客户中文名
     */
    private String custChineseName;

    /**
     * 香港客户英文名
     */
    private String custEnName;

    /**
     * 出生日期
     */
    private String birthDt;
    /**
     *证件号码
     */
    private String idNoDigest;
    /**
     *证件号码
     */
    private String idNoMask;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 投资者类型 0-机构,1-个人,2-产品户
     */
    private String invstType;
    /**
     * 手机号码区号
     */
    private String mobileAreaCode;
    /**
     * 手机号码
     */
    private String mobileDigest;
    /**
     * 手机号码
     */
    private String mobileMask;
    /**
     * 手机号码 验证状态
     * 0:未验证 1:已验证
     */
    private String mobileVerifyStatus;
    /**
     * 邮件
     */
    private String emailDigest;
    /**
     * 邮件
     */
    private String emailMask;
    /**
     * 邮件 验证状态
     * 0:未验证 1:已验证
     */
    private String emailVerifyStatus;

    /**
     * 关联的一账通号
     */
    private String hboneNo;

    /**
     * 开户方式 0-线下 1-线上
     */
    private String openType;

    /**
     * 开户日期
     */
    private String openDate;


    /**
     * 资产证明有效期
     */
    private String assetCertExpiredDate;

    /**
     * @description:
     * @param
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/1/18 16:20
     * @since JDK 1.8
     */
    public String getUsedName() {
        // 优先使用中文名，为空 则使用英文名，如果英文名也为空，则返回""
        if (custChineseName != null && !custChineseName.isEmpty()) {
            return custChineseName;
        } else if (custEnName != null && !custEnName.isEmpty()) {
            return custEnName;
        } else {
            return "";
        }

    }







    public String getHkCustStatus() {
        return hkCustStatus;
    }

    public void setHkCustStatus(String hkCustStatus) {
        this.hkCustStatus = hkCustStatus;
    }

    public String getCustChineseName() {
        return custChineseName;
    }

    public void setCustChineseName(String custChineseName) {
        this.custChineseName = custChineseName;
    }


    public String getCustEnName() {
        return custEnName;
    }

    public void setCustEnName(String custEnName) {
        this.custEnName = custEnName;
    }

    public String getBirthDt() {
        return birthDt;
    }

    public void setBirthDt(String birthDt) {
        this.birthDt = birthDt;
    }

    public String getIdNoDigest() {
        return idNoDigest;
    }

    public void setIdNoDigest(String idNoDigest) {
        this.idNoDigest = idNoDigest;
    }

    public String getIdNoMask() {
        return idNoMask;
    }

    public void setIdNoMask(String idNoMask) {
        this.idNoMask = idNoMask;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getMobileMask() {
        return mobileMask;
    }

    public void setMobileMask(String mobileMask) {
        this.mobileMask = mobileMask;
    }

    public String getMobileVerifyStatus() {
        return mobileVerifyStatus;
    }

    public void setMobileVerifyStatus(String mobileVerifyStatus) {
        this.mobileVerifyStatus = mobileVerifyStatus;
    }

    public String getEmailDigest() {
        return emailDigest;
    }

    public void setEmailDigest(String emailDigest) {
        this.emailDigest = emailDigest;
    }

    public String getEmailMask() {
        return emailMask;
    }

    public void setEmailMask(String emailMask) {
        this.emailMask = emailMask;
    }

    public String getEmailVerifyStatus() {
        return emailVerifyStatus;
    }

    public void setEmailVerifyStatus(String emailVerifyStatus) {
        this.emailVerifyStatus = emailVerifyStatus;
    }

    public String getCustShowName() {
        return custShowName;
    }

    public void setCustShowName(String custShowName) {
        this.custShowName = custShowName;
    }

    public String getEbrokerId() {
        return ebrokerId;
    }

    public void setEbrokerId(String ebrokerId) {
        this.ebrokerId = ebrokerId;
    }



    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getOpenType() {
        return openType;
    }

    public void setOpenType(String openType) {
        this.openType = openType;
    }

    public String getOpenDate() {
        return openDate;
    }

    public void setOpenDate(String openDate) {
        this.openDate = openDate;
    }

    public String getAssetCertExpiredDate() {
        return assetCertExpiredDate;
    }

    public void setAssetCertExpiredDate(String assetCertExpiredDate) {
        this.assetCertExpiredDate = assetCertExpiredDate;
    }

    public String getHkCustStatusCode() {
        return hkCustStatusCode;
    }

    public void setHkCustStatusCode(String hkCustStatusCode) {
        this.hkCustStatusCode = hkCustStatusCode;
    }

    /**
     * 客户 是否开香港户: 香港客户号不为空 且 (客户状态为“正常” 或 客户状态为“休眠”)
     * @return
     */
    public boolean isOpHkAcct() {
        return StringUtils.isNotBlank(getHkTxAcctNo())
                && (HkAcctCustStatusEnum.NORMAL.getCode().equals(hkCustStatusCode)
                || HkAcctCustStatusEnum.DORMANT.getCode().equals(hkCustStatusCode));
    }
}