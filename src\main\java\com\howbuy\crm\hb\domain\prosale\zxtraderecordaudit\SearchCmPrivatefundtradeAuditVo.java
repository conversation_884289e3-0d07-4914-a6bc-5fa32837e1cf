package com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit;

import com.howbuy.crm.hb.domain.insur.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 直销交易记录审核-前端查询对象
 * <AUTHOR>
 * @date 2022/6/1 17:56
 */
@Data
public class SearchCmPrivatefundtradeAuditVo extends PageVo implements Serializable {

    private static final long serialVersionUID = -1660356050236181716L;

    /** 审核id列表 */
    private List<String> auditidList;

    /** 投顾客户号 */
    private String conscustno;
    /** 客户姓名 */
    private String custname;

    /** 交易类型（importZxTradeType） */
    private String tradetype;

    /** 审核状态（zxTradeAuditStatus） */
    private String auditstatus;

    /** 产品代码 */
    private String fundcode;
    /** 一账通号 */
    private String hboneno;
    /** 是否海外特殊处理（是、否） */
    private String ishaiwai;
    /** 修改人 */
    private String modifierName;
    /** 修改时间-开始 */
    private String moddtStart;
    /** 修改时间-结束 */
    private String moddtEnd;
    /** 是否香港 1-是 0-否 */
    private String sfxg;
    /** 合规修改人 */
    private String hgmodifier;
}
