<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.reward.CmPrpInitialTradeNumDOMapper">

    <sql id="Base_Column_List">
    ID, CONSCUSTNO, CONSCODE, INITIAL_NUM, IS_VALID, CREATOR, CREATE_TIME, MODOR, UPDATE_TIME,REMARK
    </sql>

    <insert id="insertCmPrpInitialTradeNum" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpInitialTradeNumDO" >
     INSERT INTO CM_PRP_INITIAL_TRADE_NUM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test='null != id'>ID,</if>
            <if test='null != conscustno'>CONSCUSTNO,</if>
            <if test='null != conscode'>CONSCODE,</if>
            <if test='null != initialNum'>INITIAL_NUM,</if>
            <if test='null != isValid'>IS_VALID,</if>
            <if test='null != remark'>REMARK,</if>
            CREATE_TIME,
            <if test='null != updateTime'>UPDATE_TIME,</if>
            <if test='null != creator'>CREATOR,</if>
            <if test='null != modor'>MODOR</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test='null != id'>#{id},</if>
            <if test='null != conscustno'>#{conscustno},</if>
            <if test='null != conscode'>#{conscode},</if>
            <if test='null != initialNum'>#{initialNum},</if>
            <if test='null != isValid'>#{isValid}, </if>
            <if test='null != remark'>#{remark}, </if>
            sysdate,
            <if test='null != updateTime'>#{updateTime},</if>
            <if test='null != creator'>#{creator},</if>
            <if test='null != modor'> #{modor}</if>
        </trim>
    </insert>

   <delete id="deleteCmPrpInitialTradeNumById" parameterType="java.lang.Long">
    delete from CM_PRP_INITIAL_TRADE_NUM where ID = #{id,jdbcType=DECIMAL}
    </delete>

    <update id="updateCmPrpInitialTradeNumById" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpInitialTradeNumDO">
         update CM_PRP_INITIAL_TRADE_NUM
        <set>
            <if test='null != conscustno'>CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},</if>
            <if test='null != conscode'>CONSCODE = #{conscode,jdbcType=VARCHAR},</if>
            <if test='null != initialNum'>INITIAL_NUM = #{initialNum,jdbcType=NUMERIC},</if>
            <if test='null != isValid'>IS_VALID = #{isValid,jdbcType=VARCHAR},</if>
            <if test='null != remark'>REMARK = #{remark,jdbcType=VARCHAR},</if>
            <if test='null != createTime'>CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},</if>
            UPDATE_TIME = sysdate,
            <if test='null != creator'>CREATOR = #{creator,jdbcType=VARCHAR},</if>
            <if test='null != modor'>MODOR = #{modor,jdbcType=VARCHAR},</if>
        </set>
        WHERE ID = #{id}
    </update>

    <select id="selectCmPrpInitialTradeNumByPage" parameterType="Map" resultType="com.howbuy.crm.hb.domain.reward.CmPrpInitialTradeNumDO" useCache="false">
            select CN.id,
               CN.CONSCUSTNO,
               CN.CONSCODE,
               CN.INITIAL_NUM,
	           CN.IS_VALID,
	           C2.CONSNAME,
	           CC.CUSTNAME as conscustname,
	           H.orgname,
	    	   CN.creator,
        to_char(CN.create_time, 'yyyy-mm-dd hh24:mi:ss') as createTime,
	    	   nvl(CN.modor,CN.creator) modor,
        to_char(nvl(CN.update_time,CN.create_time), 'yyyy-mm-dd hh24:mi:ss') as updateTime,
	    	   CN.remark
	    	from CM_PRP_INITIAL_TRADE_NUM CN
	        LEFT JOIN CM_CONSCUST CC ON CN.CONSCUSTNO =CC.CONSCUSTNO
	        LEFT JOIN CM_CONSULTANT C2 ON CN.CONSCODE = C2.CONSCODE
	        LEFT JOIN hb_organization H ON C2.OUTLETCODE = H.ORGCODE
        WHERE 1=1
        <if test="param.conscustno != null"> AND CN.CONSCUSTNO = #{param.conscustno}</if>
        <if test="param.conscustname != null">and CC.CUSTNAME like concat(concat('%',#{param.conscustname}),'%')</if>
        <if test="param.conscode == 'ALL' and param.orgcode != null">
			 and  (c2.TEAMCODE = #{param.orgcode} OR
                H.ORGCODE in
                (SELECT ORGCODE
                FROM HB_ORGANIZATION ho
                WHERE ho.STATUS = '0'
                START WITH ho.ORGCODE = #{param.orgcode}
                CONNECT BY PRIOR ORGCODE = PARENTORGCODE))
        </if>
        <if test="param.conscode != null and param.conscode != 'ALL'">and CN.CONSCODE = #{param.conscode}</if>
        order by CN.ID desc
    </select>

    <select id="selectCmPrpInitialTradeNum" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpInitialTradeNumDO"
            resultType="com.howbuy.crm.hb.domain.reward.CmPrpInitialTradeNumDO" useCache="false">
         select * from CM_PRP_INITIAL_TRADE_NUM ct where ct.ID != #{id}
         and ct.CONSCODE= #{conscode,jdbcType=VARCHAR}
         and ct.CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
    </select>

    <select id="selectCmPrpInitialTradeNumById" parameterType="java.lang.Long" resultType="com.howbuy.crm.hb.domain.reward.CmPrpInitialTradeNumDO">
    select
        <include refid="Base_Column_List"/>
    from CM_PRP_INITIAL_TRADE_NUM
    where ID = #{id,jdbcType=DECIMAL}
    </select>

    <select id="selectCmPrpInitialTradeNumListInfo" parameterType="Map" resultType="com.howbuy.crm.hb.domain.reward.CmPrpInitialTradeNumDO" useCache="false">
      select CN.id,
               CN.CONSCUSTNO,
               CN.CONSCODE,
               CN.INITIAL_NUM,
	           CN.IS_VALID,
	           C2.CONSNAME,
	           CC.CUSTNAME as conscustname,
	           H.orgname,
	    	   CN.creator,
        to_char(CN.create_time, 'yyyy-mm-dd hh24:mi:ss') as createTime,
        nvl(CN.modor,CN.creator) modor,
        to_char(nvl(CN.update_time,CN.create_time), 'yyyy-mm-dd hh24:mi:ss') as updateTime,
        CN.remark
	    	from CM_PRP_INITIAL_TRADE_NUM CN
	        LEFT JOIN CM_CONSCUST CC ON CN.CONSCUSTNO =CC.CONSCUSTNO
	        LEFT JOIN CM_CONSULTANT C2 ON CN.CONSCODE = C2.CONSCODE
	        LEFT JOIN hb_organization H ON C2.OUTLETCODE = H.ORGCODE
      <where>
            <trim prefixOverrides="AND | OR " suffixOverrides=",">
                <if test="conscustno != null">CN.CONSCUSTNO = #{conscustno}</if>
                <if test="conscustname != null">and CC.CUSTNAME like concat(concat('%',#{conscustname}),'%')</if>
                <if test="conscode == 'ALL' and orgcode != null">
			    and  (c2.TEAMCODE = #{orgcode} OR
                H.ORGCODE in
                (SELECT ORGCODE
                FROM HB_ORGANIZATION ho
                WHERE ho.STATUS = '0'
                START WITH ho.ORGCODE = #{orgcode}
                CONNECT BY PRIOR ORGCODE = PARENTORGCODE))
                </if>
                <if test="conscode != null and conscode != 'ALL'">and CN.CONSCODE = #{conscode}</if>
            </trim>
        </where>
        order by CN.ID desc
    </select>
</mapper>