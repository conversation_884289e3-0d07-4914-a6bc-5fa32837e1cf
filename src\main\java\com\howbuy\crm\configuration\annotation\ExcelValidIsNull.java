package com.howbuy.crm.configuration.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * @description: (增加数据校验注解)
 * <AUTHOR>
 * @date 2023/4/10 10:43
 * @since JDK 1.8
 */
@Target({ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelValidIsNull {
    String message() default "导入有未填入的字段";
}