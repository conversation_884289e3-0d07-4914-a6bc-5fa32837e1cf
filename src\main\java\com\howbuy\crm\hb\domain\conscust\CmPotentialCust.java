package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类PotentialCust.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmPotentialCust implements Serializable {

	private static final long serialVersionUID = 1L;

	private String pcustid;

	private String pcustlevel;

	private Integer pgrade;

	private String pcuststatus;

	private String idtype;

	private String idno;

	private String custname;

	private String provcode;

	private String citycode;

	private String edulevel;

	private String vocation;

	private String inclevel;

	private String birthday;

	private String gender;

	private String married;

	private String pincome;

	private String fincome;

	private String decisionflag;

	private String interests;

	private String familycondition;

	private String contacttime;

	private String contactmethod;

	private String sendinfoflag;

	private String recvemailflag;

	private String recvtelflag;

	private String recvmsgflag;

	private String company;

	private String risklevel;

	private String selfrisklevel;

	private String addr;

	private String postcode;

	private String addr2;

	private String postcode2;

	private String addr3;

	private String postcode3;

	private String mobile;

	private String mobile2;

	private String telno;

	private String telno2;

	private String fax;

	private String pagerno;

	private String email;

	private String email2;

	private String hometelno;

	private String officetelno;

	private String actcode;

	private String brokercode;

	private String conscode;

	private String intrcustno;

	private String source;

	private String knowchan;

	private String otherchan;

	private String otherinvest;

	private String salon;

	private Integer winvestamt;

	private String wcustlevel;

	private String financememo;

	private String beforeinvest;

	private String im;

	private String regdt;

	private String uddt;

	private String memo;

	private String subsource;

	private String conscustno;

	private String batchid;

	private String newsourceno;

	private String newsourcename;

	private String uploader;

	private String idnomask;
	private String idnodigest;
	private String idnocipher;

	private String addrmask;
	private String addrdigest;
	private String addrcipher;

	private String addr2mask;
	private String addr2digest;
	private String addr2cipher;

	private String addr3mask;
	private String addr3digest;
	private String addr3cipher;

	private String mobilemask;
	private String mobiledigest;
	private String mobilecipher;


	private String mobile2mask;
	private String mobile2digest;
	private String mobile2cipher;

	private String telnomask;
	private String telnodigest;
	private String telnocipher;

	private String telno2mask;

	private String emailmask;
	private String emaildigest;
	private String emailcipher;

	private String email2mask;
	private String email2digest;
	private String email2cipher;

	private String hometelnomask;
	private String hometelnodigest;
	private String hometelnocipher;

	private String officetelnomask;
	private String officetelnodigest;
	private String officetelnocipher;

	/**
	 * 手机区号
	 */
	private String mobileAreaCode;

	public String getMobileAreaCode() {
		return mobileAreaCode;
	}

	public void setMobileAreaCode(String mobileAreaCode) {
		this.mobileAreaCode = mobileAreaCode;
	}

	public String getPcustid() {
		return this.pcustid;
	}

	public void setPcustid(String pcustid) {
		this.pcustid = pcustid;
	}

	public String getPcustlevel() {
		return this.pcustlevel;
	}

	public void setPcustlevel(String pcustlevel) {
		this.pcustlevel = pcustlevel;
	}

	public Integer getPgrade() {
		return this.pgrade;
	}

	public void setPgrade(Integer pgrade) {
		this.pgrade = pgrade;
	}

	public String getPcuststatus() {
		return this.pcuststatus;
	}

	public void setPcuststatus(String pcuststatus) {
		this.pcuststatus = pcuststatus;
	}

	public String getIdtype() {
		return this.idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}

	public String getIdno() {
		return this.idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}

	public String getCustname() {
		return this.custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getProvcode() {
		return this.provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return this.citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public String getEdulevel() {
		return this.edulevel;
	}

	public void setEdulevel(String edulevel) {
		this.edulevel = edulevel;
	}

	public String getVocation() {
		return this.vocation;
	}

	public void setVocation(String vocation) {
		this.vocation = vocation;
	}

	public String getInclevel() {
		return this.inclevel;
	}

	public void setInclevel(String inclevel) {
		this.inclevel = inclevel;
	}

	public String getBirthday() {
		return this.birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getMarried() {
		return this.married;
	}

	public void setMarried(String married) {
		this.married = married;
	}

	public String getPincome() {
		return this.pincome;
	}

	public void setPincome(String pincome) {
		this.pincome = pincome;
	}

	public String getFincome() {
		return this.fincome;
	}

	public void setFincome(String fincome) {
		this.fincome = fincome;
	}

	public String getDecisionflag() {
		return this.decisionflag;
	}

	public void setDecisionflag(String decisionflag) {
		this.decisionflag = decisionflag;
	}

	public String getInterests() {
		return this.interests;
	}

	public void setInterests(String interests) {
		this.interests = interests;
	}

	public String getFamilycondition() {
		return this.familycondition;
	}

	public void setFamilycondition(String familycondition) {
		this.familycondition = familycondition;
	}

	public String getContacttime() {
		return this.contacttime;
	}

	public void setContacttime(String contacttime) {
		this.contacttime = contacttime;
	}

	public String getContactmethod() {
		return this.contactmethod;
	}

	public void setContactmethod(String contactmethod) {
		this.contactmethod = contactmethod;
	}

	public String getSendinfoflag() {
		return this.sendinfoflag;
	}

	public void setSendinfoflag(String sendinfoflag) {
		this.sendinfoflag = sendinfoflag;
	}

	public String getRecvemailflag() {
		return this.recvemailflag;
	}

	public void setRecvemailflag(String recvemailflag) {
		this.recvemailflag = recvemailflag;
	}

	public String getRecvtelflag() {
		return this.recvtelflag;
	}

	public void setRecvtelflag(String recvtelflag) {
		this.recvtelflag = recvtelflag;
	}

	public String getRecvmsgflag() {
		return this.recvmsgflag;
	}

	public void setRecvmsgflag(String recvmsgflag) {
		this.recvmsgflag = recvmsgflag;
	}

	public String getCompany() {
		return this.company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getRisklevel() {
		return this.risklevel;
	}

	public void setRisklevel(String risklevel) {
		this.risklevel = risklevel;
	}

	public String getSelfrisklevel() {
		return this.selfrisklevel;
	}

	public void setSelfrisklevel(String selfrisklevel) {
		this.selfrisklevel = selfrisklevel;
	}

	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getPostcode() {
		return this.postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}

	public String getAddr2() {
		return this.addr2;
	}

	public void setAddr2(String addr2) {
		this.addr2 = addr2;
	}

	public String getPostcode2() {
		return this.postcode2;
	}

	public void setPostcode2(String postcode2) {
		this.postcode2 = postcode2;
	}

	public String getAddr3() {
		return this.addr3;
	}

	public void setAddr3(String addr3) {
		this.addr3 = addr3;
	}

	public String getPostcode3() {
		return this.postcode3;
	}

	public void setPostcode3(String postcode3) {
		this.postcode3 = postcode3;
	}

	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMobile2() {
		return this.mobile2;
	}

	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}

	public String getTelno() {
		return this.telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}

	public String getTelno2() {
		return this.telno2;
	}

	public void setTelno2(String telno2) {
		this.telno2 = telno2;
	}

	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getPagerno() {
		return this.pagerno;
	}

	public void setPagerno(String pagerno) {
		this.pagerno = pagerno;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getEmail2() {
		return this.email2;
	}

	public void setEmail2(String email2) {
		this.email2 = email2;
	}

	public String getHometelno() {
		return this.hometelno;
	}

	public void setHometelno(String hometelno) {
		this.hometelno = hometelno;
	}

	public String getOfficetelno() {
		return this.officetelno;
	}

	public void setOfficetelno(String officetelno) {
		this.officetelno = officetelno;
	}

	public String getActcode() {
		return this.actcode;
	}

	public void setActcode(String actcode) {
		this.actcode = actcode;
	}

	public String getBrokercode() {
		return this.brokercode;
	}

	public void setBrokercode(String brokercode) {
		this.brokercode = brokercode;
	}

	public String getConscode() {
		return this.conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getIntrcustno() {
		return this.intrcustno;
	}

	public void setIntrcustno(String intrcustno) {
		this.intrcustno = intrcustno;
	}

	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getKnowchan() {
		return this.knowchan;
	}

	public void setKnowchan(String knowchan) {
		this.knowchan = knowchan;
	}

	public String getOtherchan() {
		return this.otherchan;
	}

	public void setOtherchan(String otherchan) {
		this.otherchan = otherchan;
	}

	public String getOtherinvest() {
		return this.otherinvest;
	}

	public void setOtherinvest(String otherinvest) {
		this.otherinvest = otherinvest;
	}

	public String getSalon() {
		return this.salon;
	}

	public void setSalon(String salon) {
		this.salon = salon;
	}

	public Integer getWinvestamt() {
		return this.winvestamt;
	}

	public void setWinvestamt(Integer winvestamt) {
		this.winvestamt = winvestamt;
	}

	public String getWcustlevel() {
		return this.wcustlevel;
	}

	public void setWcustlevel(String wcustlevel) {
		this.wcustlevel = wcustlevel;
	}

	public String getFinancememo() {
		return this.financememo;
	}

	public void setFinancememo(String financememo) {
		this.financememo = financememo;
	}

	public String getBeforeinvest() {
		return this.beforeinvest;
	}

	public void setBeforeinvest(String beforeinvest) {
		this.beforeinvest = beforeinvest;
	}

	public String getIm() {
		return this.im;
	}

	public void setIm(String im) {
		this.im = im;
	}

	public String getRegdt() {
		return this.regdt;
	}

	public void setRegdt(String regdt) {
		this.regdt = regdt;
	}

	public String getUddt() {
		return this.uddt;
	}

	public void setUddt(String uddt) {
		this.uddt = uddt;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getSubsource() {
		return subsource;
	}

	public void setSubsource(String subsource) {
		this.subsource = subsource;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getBatchid() {
		return batchid;
	}

	public void setBatchid(String batchid) {
		this.batchid = batchid;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getNewsourceno() {
		return newsourceno;
	}

	public void setNewsourceno(String newsourceno) {
		this.newsourceno = newsourceno;
	}

	public String getNewsourcename() {
		return newsourcename;
	}

	public void setNewsourcename(String newsourcename) {
		this.newsourcename = newsourcename;
	}

	public String getUploader() {
		return uploader;
	}

	public void setUploader(String uploader) {
		this.uploader = uploader;
	}

	public String getIdnomask() {
		return idnomask;
	}

	public void setIdnomask(String idnomask) {
		this.idnomask = idnomask;
	}

	public String getIdnodigest() {
		return idnodigest;
	}

	public void setIdnodigest(String idnodigest) {
		this.idnodigest = idnodigest;
	}

	public String getIdnocipher() {
		return idnocipher;
	}

	public void setIdnocipher(String idnocipher) {
		this.idnocipher = idnocipher;
	}

	public String getAddrmask() {
		return addrmask;
	}

	public void setAddrmask(String addrmask) {
		this.addrmask = addrmask;
	}

	public String getAddrdigest() {
		return addrdigest;
	}

	public void setAddrdigest(String addrdigest) {
		this.addrdigest = addrdigest;
	}

	public String getAddrcipher() {
		return addrcipher;
	}

	public void setAddrcipher(String addrcipher) {
		this.addrcipher = addrcipher;
	}

	public String getAddr2mask() {
		return addr2mask;
	}

	public void setAddr2mask(String addr2mask) {
		this.addr2mask = addr2mask;
	}

	public String getAddr2digest() {
		return addr2digest;
	}

	public void setAddr2digest(String addr2digest) {
		this.addr2digest = addr2digest;
	}

	public String getAddr2cipher() {
		return addr2cipher;
	}

	public void setAddr2cipher(String addr2cipher) {
		this.addr2cipher = addr2cipher;
	}

	public String getAddr3mask() {
		return addr3mask;
	}

	public void setAddr3mask(String addr3mask) {
		this.addr3mask = addr3mask;
	}

	public String getAddr3digest() {
		return addr3digest;
	}

	public void setAddr3digest(String addr3digest) {
		this.addr3digest = addr3digest;
	}

	public String getAddr3cipher() {
		return addr3cipher;
	}

	public void setAddr3cipher(String addr3cipher) {
		this.addr3cipher = addr3cipher;
	}

	public String getMobilemask() {
		return mobilemask;
	}

	public void setMobilemask(String mobilemask) {
		this.mobilemask = mobilemask;
	}

	public String getMobiledigest() {
		return mobiledigest;
	}

	public void setMobiledigest(String mobiledigest) {
		this.mobiledigest = mobiledigest;
	}

	public String getMobilecipher() {
		return mobilecipher;
	}

	public void setMobilecipher(String mobilecipher) {
		this.mobilecipher = mobilecipher;
	}

	public String getMobile2mask() {
		return mobile2mask;
	}

	public void setMobile2mask(String mobile2mask) {
		this.mobile2mask = mobile2mask;
	}

	public String getMobile2digest() {
		return mobile2digest;
	}

	public void setMobile2digest(String mobile2digest) {
		this.mobile2digest = mobile2digest;
	}

	public String getMobile2cipher() {
		return mobile2cipher;
	}

	public void setMobile2cipher(String mobile2cipher) {
		this.mobile2cipher = mobile2cipher;
	}

	public String getTelnomask() {
		return telnomask;
	}

	public void setTelnomask(String telnomask) {
		this.telnomask = telnomask;
	}

	public String getTelnodigest() {
		return telnodigest;
	}

	public void setTelnodigest(String telnodigest) {
		this.telnodigest = telnodigest;
	}

	public String getTelnocipher() {
		return telnocipher;
	}

	public void setTelnocipher(String telnocipher) {
		this.telnocipher = telnocipher;
	}

	public String getTelno2mask() {
		return telno2mask;
	}

	public void setTelno2mask(String telno2mask) {
		this.telno2mask = telno2mask;
	}

	public String getEmailmask() {
		return emailmask;
	}

	public void setEmailmask(String emailmask) {
		this.emailmask = emailmask;
	}

	public String getEmaildigest() {
		return emaildigest;
	}

	public void setEmaildigest(String emaildigest) {
		this.emaildigest = emaildigest;
	}

	public String getEmailcipher() {
		return emailcipher;
	}

	public void setEmailcipher(String emailcipher) {
		this.emailcipher = emailcipher;
	}

	public String getEmail2mask() {
		return email2mask;
	}

	public void setEmail2mask(String email2mask) {
		this.email2mask = email2mask;
	}

	public String getEmail2digest() {
		return email2digest;
	}

	public void setEmail2digest(String email2digest) {
		this.email2digest = email2digest;
	}

	public String getEmail2cipher() {
		return email2cipher;
	}

	public void setEmail2cipher(String email2cipher) {
		this.email2cipher = email2cipher;
	}

	public String getHometelnomask() {
		return hometelnomask;
	}

	public void setHometelnomask(String hometelnomask) {
		this.hometelnomask = hometelnomask;
	}

	public String getHometelnodigest() {
		return hometelnodigest;
	}

	public void setHometelnodigest(String hometelnodigest) {
		this.hometelnodigest = hometelnodigest;
	}

	public String getHometelnocipher() {
		return hometelnocipher;
	}

	public void setHometelnocipher(String hometelnocipher) {
		this.hometelnocipher = hometelnocipher;
	}

	public String getOfficetelnomask() {
		return officetelnomask;
	}

	public void setOfficetelnomask(String officetelnomask) {
		this.officetelnomask = officetelnomask;
	}

	public String getOfficetelnodigest() {
		return officetelnodigest;
	}

	public void setOfficetelnodigest(String officetelnodigest) {
		this.officetelnodigest = officetelnodigest;
	}

	public String getOfficetelnocipher() {
		return officetelnocipher;
	}

	public void setOfficetelnocipher(String officetelnocipher) {
		this.officetelnocipher = officetelnocipher;
	}



}
