package com.howbuy.crm.hb.persistence.conference;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.conference.CmConferenceDepartment;
import com.howbuy.crm.hb.domain.conference.CmConferenceDepartmentWithOrg;
import com.howbuy.crm.hb.domain.system.HbOrganization;


/**
 * 路演参会部门
 * <AUTHOR>
 *
 */
public interface CmConferenceDepartmentMapper {

    /**
     * 插入
     * @param cmConferenceDepartment
     */
    public void insertCmConferenceDepartment(CmConferenceDepartment cmConferenceDepartment);

    /**
     * 更新
     * @param cmConferenceDepartment
     */
    public void updateCmConferenceDepartment(CmConferenceDepartment cmConferenceDepartment);

    /**
     * 删除
     * @param conferenceid
     */
    public void deleteCmConferenceDepartment(String conferenceid);

    /**
     * 删除
     * @param param
     */
    public void deleteDepartment(Map<String, String> param);

    /**
     * 获取特定
     * @param conferenceid
     * @return
     */
    public int getDepartmentAllOrgnub(String conferenceid);

    /**
     * 查询列表
     * @param conferenceid
     * @return
     */
    public abstract List<CmConferenceDepartment> listCmConferenceDepartment(String conferenceid);

    /**
     * 获取单个
     * @param param
     * @return
     */
    public CmConferenceDepartment getDepartmentinfo(Map<String, String> param);


    /**
     * 查询一个会议下的所有部门
     * @param conferenceId
     * @return
     */
    List<CmConferenceDepartmentWithOrg> listDepartmentWithOrg(String conferenceId);

    /**
     * 查询列表
     * @param orgCodes
     * @return
     */
    List<HbOrganization> listOrgByParentList(Map<String,List> orgCodes);

    /**
     * 获取单个
     * @param param
     * @return
     */
    CmConferenceDepartment getLimitDepartment(Map<String, Object> param);

    /**
     * 查询列表
     * @param param
     * @return
     */
    List<CmConferenceDepartment> listDepartmentByparentCode(Map<String, Object> param);

    /**
     * 删除
     * @param param
     */
    void deleteDepartmentGroup(Map<String, String> param);
}
