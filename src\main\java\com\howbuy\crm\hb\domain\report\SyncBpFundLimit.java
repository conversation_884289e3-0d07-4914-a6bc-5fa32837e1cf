package com.howbuy.crm.hb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 实体类SyncBpFundLimit.java
 * <AUTHOR>
 * @version 1.0
 */
public class SyncBpFundLimit implements Serializable {

	private static final long serialVersionUID = 1L;

	private String fundLimitId;

	private String fundCode;

	private String shareClass;

	private String busiCode;

	private String invstType;

	private String custType;

	private String tradeMode;

	private BigDecimal tradeUnit;

	private BigDecimal minAppAmt;

	private BigDecimal minAppVol;

	private BigDecimal minSuppleAmt;

	private BigDecimal minSuppleVol;

	private BigDecimal maxAppAmt;

	private BigDecimal maxAppVol;

	private String recStat;

	private String checkFlag;

	private String creator;

	private String modifier;

	private String checker;

	private String creDt;

	private String modDt;

	private String impBatch;

	private Date syncDate = new Date();

	private BigDecimal minAppAmtHb;

	private BigDecimal maxSumAmt;

	private BigDecimal maxSumVol;

	public String getFundLimitId() {
		return this.fundLimitId;
	}

	public void setFundLimitId(String fundLimitId) {
		this.fundLimitId = fundLimitId;
	}

	public String getFundCode() {
		return this.fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getShareClass() {
		return this.shareClass;
	}

	public void setShareClass(String shareClass) {
		this.shareClass = shareClass;
	}

	public String getBusiCode() {
		return this.busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public String getInvstType() {
		return this.invstType;
	}

	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}

	public String getCustType() {
		return this.custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

	public String getTradeMode() {
		return this.tradeMode;
	}

	public void setTradeMode(String tradeMode) {
		this.tradeMode = tradeMode;
	}

	public BigDecimal getTradeUnit() {
		return this.tradeUnit;
	}

	public void setTradeUnit(BigDecimal tradeUnit) {
		this.tradeUnit = tradeUnit;
	}

	public BigDecimal getMinAppAmt() {
		return this.minAppAmt;
	}

	public void setMinAppAmt(BigDecimal minAppAmt) {
		this.minAppAmt = minAppAmt;
	}

	public BigDecimal getMinAppVol() {
		return this.minAppVol;
	}

	public void setMinAppVol(BigDecimal minAppVol) {
		this.minAppVol = minAppVol;
	}

	public BigDecimal getMinSuppleAmt() {
		return this.minSuppleAmt;
	}

	public void setMinSuppleAmt(BigDecimal minSuppleAmt) {
		this.minSuppleAmt = minSuppleAmt;
	}

	public BigDecimal getMinSuppleVol() {
		return this.minSuppleVol;
	}

	public void setMinSuppleVol(BigDecimal minSuppleVol) {
		this.minSuppleVol = minSuppleVol;
	}

	public BigDecimal getMaxAppAmt() {
		return this.maxAppAmt;
	}

	public void setMaxAppAmt(BigDecimal maxAppAmt) {
		this.maxAppAmt = maxAppAmt;
	}

	public BigDecimal getMaxAppVol() {
		return this.maxAppVol;
	}

	public void setMaxAppVol(BigDecimal maxAppVol) {
		this.maxAppVol = maxAppVol;
	}

	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}

	public String getCheckFlag() {
		return this.checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public String getCreDt() {
		return this.creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public String getModDt() {
		return this.modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}

	public String getImpBatch() {
		return this.impBatch;
	}

	public void setImpBatch(String impBatch) {
		this.impBatch = impBatch;
	}

	public Date getSyncDate() {
		return this.syncDate;
	}

	public void setSyncDate(Date syncDate) {
		this.syncDate = syncDate;
	}

	public BigDecimal getMinAppAmtHb() {
		return this.minAppAmtHb;
	}

	public void setMinAppAmtHb(BigDecimal minAppAmtHb) {
		this.minAppAmtHb = minAppAmtHb;
	}

	public BigDecimal getMaxSumAmt() {
		return this.maxSumAmt;
	}

	public void setMaxSumAmt(BigDecimal maxSumAmt) {
		this.maxSumAmt = maxSumAmt;
	}

	public BigDecimal getMaxSumVol() {
		return this.maxSumVol;
	}

	public void setMaxSumVol(BigDecimal maxSumVol) {
		this.maxSumVol = maxSumVol;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
