package com.howbuy.crm.hb.persistence.conference;

import com.howbuy.crm.hb.domain.conference.CmConferenceFile;
import org.apache.ibatis.annotations.Param;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/2/12 15:27
 * @since JDK 1.8
 */
public interface CmConferenceFileMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmConferenceFile record);

    int insertSelective(CmConferenceFile record);

    CmConferenceFile selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmConferenceFile record);

    int updateByPrimaryKey(CmConferenceFile record);



    /**
     * 删除会议对应的文件
     * @param conferenceId
     */
    void deleteByConferenceId(@Param("conferenceId") String conferenceId);

    /**
     * 取会议下的有效文件
     * @param conferenceId
     * @return
     */
    CmConferenceFile getFileByConferenceId(@Param("conferenceId")String conferenceId);
}