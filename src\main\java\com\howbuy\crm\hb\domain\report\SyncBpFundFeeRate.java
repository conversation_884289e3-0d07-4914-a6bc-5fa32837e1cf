package com.howbuy.crm.hb.domain.report;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 实体类SyncBpFundFeeRate.java
 * <AUTHOR>
 * @version 1.0
 */
public class SyncBpFundFeeRate implements Serializable {

	private static final long serialVersionUID = 1L;

	private String feeRateId;

	private String fundCode;

	private String shareClass;

	private String tfundCode;

	private String tshareClass;

	private String busiCode;

	private String invstType;

	private String captType;

	private String getFeeRateMethod;

	private BigDecimal constantFee;

	private BigDecimal minFeeVol;

	private BigDecimal maxFeeVol;

	private BigDecimal minFeeAmt;

	private BigDecimal maxFeeAmt;

	private Integer minFeeDays;

	private Integer maxFeeDays;

	private BigDecimal maxFee;

	private BigDecimal minFee;

	private BigDecimal feeRate;

	private String feeRateFlag;

	private BigDecimal compareProportion;

	private String compareCaptType;

	private String startDt;

	private String endDt;

	private String recStat;

	private String checkFlag;

	private String creDt;

	private String modDt;

	private String impBatch;

	private String seqNo;

	private Date syncDate = new Date();

	public String getFeeRateId() {
		return this.feeRateId;
	}

	public void setFeeRateId(String feeRateId) {
		this.feeRateId = feeRateId;
	}

	public String getFundCode() {
		return this.fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getShareClass() {
		return this.shareClass;
	}

	public void setShareClass(String shareClass) {
		this.shareClass = shareClass;
	}

	public String getTfundCode() {
		return this.tfundCode;
	}

	public void setTfundCode(String tfundCode) {
		this.tfundCode = tfundCode;
	}

	public String getTshareClass() {
		return this.tshareClass;
	}

	public void setTshareClass(String tshareClass) {
		this.tshareClass = tshareClass;
	}

	public String getBusiCode() {
		return this.busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public String getInvstType() {
		return this.invstType;
	}

	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}

	public String getCaptType() {
		return this.captType;
	}

	public void setCaptType(String captType) {
		this.captType = captType;
	}

	public String getGetFeeRateMethod() {
		return this.getFeeRateMethod;
	}

	public void setGetFeeRateMethod(String getFeeRateMethod) {
		this.getFeeRateMethod = getFeeRateMethod;
	}

	public BigDecimal getConstantFee() {
		return this.constantFee;
	}

	public void setConstantFee(BigDecimal constantFee) {
		this.constantFee = constantFee;
	}

	public BigDecimal getMinFeeVol() {
		return this.minFeeVol;
	}

	public void setMinFeeVol(BigDecimal minFeeVol) {
		this.minFeeVol = minFeeVol;
	}

	public BigDecimal getMaxFeeVol() {
		return this.maxFeeVol;
	}

	public void setMaxFeeVol(BigDecimal maxFeeVol) {
		this.maxFeeVol = maxFeeVol;
	}

	public BigDecimal getMinFeeAmt() {
		return this.minFeeAmt;
	}

	public void setMinFeeAmt(BigDecimal minFeeAmt) {
		this.minFeeAmt = minFeeAmt;
	}

	public BigDecimal getMaxFeeAmt() {
		return this.maxFeeAmt;
	}

	public void setMaxFeeAmt(BigDecimal maxFeeAmt) {
		this.maxFeeAmt = maxFeeAmt;
	}

	public Integer getMinFeeDays() {
		return this.minFeeDays;
	}

	public void setMinFeeDays(Integer minFeeDays) {
		this.minFeeDays = minFeeDays;
	}

	public Integer getMaxFeeDays() {
		return this.maxFeeDays;
	}

	public void setMaxFeeDays(Integer maxFeeDays) {
		this.maxFeeDays = maxFeeDays;
	}

	public BigDecimal getMaxFee() {
		return this.maxFee;
	}

	public void setMaxFee(BigDecimal maxFee) {
		this.maxFee = maxFee;
	}

	public BigDecimal getMinFee() {
		return this.minFee;
	}

	public void setMinFee(BigDecimal minFee) {
		this.minFee = minFee;
	}

	public BigDecimal getFeeRate() {
		return this.feeRate;
	}

	public void setFeeRate(BigDecimal feeRate) {
		this.feeRate = feeRate;
	}

	public String getFeeRateFlag() {
		return this.feeRateFlag;
	}

	public void setFeeRateFlag(String feeRateFlag) {
		this.feeRateFlag = feeRateFlag;
	}

	public BigDecimal getCompareProportion() {
		return this.compareProportion;
	}

	public void setCompareProportion(BigDecimal compareProportion) {
		this.compareProportion = compareProportion;
	}

	public String getCompareCaptType() {
		return this.compareCaptType;
	}

	public void setCompareCaptType(String compareCaptType) {
		this.compareCaptType = compareCaptType;
	}

	public String getStartDt() {
		return this.startDt;
	}

	public void setStartDt(String startDt) {
		this.startDt = startDt;
	}

	public String getEndDt() {
		return this.endDt;
	}

	public void setEndDt(String endDt) {
		this.endDt = endDt;
	}

	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}

	public String getCheckFlag() {
		return this.checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}

	public String getCreDt() {
		return this.creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public String getModDt() {
		return this.modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}

	public String getImpBatch() {
		return this.impBatch;
	}

	public void setImpBatch(String impBatch) {
		this.impBatch = impBatch;
	}

	public String getSeqNo() {
		return this.seqNo;
	}

	public void setSeqNo(String seqNo) {
		this.seqNo = seqNo;
	}

	public Date getSyncDate() {
		return this.syncDate;
	}

	public void setSyncDate(Date syncDate) {
		this.syncDate = syncDate;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
