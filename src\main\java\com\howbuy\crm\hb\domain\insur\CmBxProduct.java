package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxProduct.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxProduct implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private String fundcode;
	private String fundname;
	private String busitype;
	private String prodtype;
	private String prodproper;
	private String currency;
	private String isdel;
	/** 合作渠道，多个逗号隔开 */
	private String channcodes; 
	/** 合作渠道，多个逗号隔开 */
	private String channnames;
	/** 保险公司代码 */
	private String compcode;
	/** 保险公司名称 */
	private String compname;
	/** 附加险，多个用逗号隔开 */
	private String attrinsur;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	/** 是否TP核算:1-是;2-否 */
	private String istpcheck;
	/** 是否特殊处理:1-是;2-否 */
	private String isspecialhandle;
}
