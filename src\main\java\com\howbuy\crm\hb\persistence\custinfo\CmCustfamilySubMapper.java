package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import com.howbuy.crm.hb.domain.custinfo.CmCustfamilySub;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
@MapperScan
public interface CmCustfamilySubMapper {

    /**
     * 根据转分配客户查询对应的家庭账户
     * @param param
     * @return
     */
    List<CmCustfamilySub> listCmCustfamilySubByAssignCust(Map<String, String> param);
    
    /**
     * 得到单个数据对象
     * @param param
     * @return
     */
    CmCustfamilySub getCmCustfamilySub(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmCustfamilySub
      */
	void insertCmCustfamilySub(CmCustfamilySub cmCustfamilySub);
	
	/**
	 * 单条修改数据对象
	 * @param cmCustfamilySub
	 */
	void updateCmCustfamilySub(CmCustfamilySub cmCustfamilySub);

	/**
	 * insert
	 * @param conscustno
	 */
	void insertCmCustfamilySubHis(String conscustno);
	/**
	 * 单条删除数据对象
	 * @param conscustno
	 */
	void delCmCustfamilySub(String conscustno);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmCustfamilySub(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmCustfamilySub> listCmCustfamilySub(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmCustfamilySubCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmCustfamilySub> listCmCustfamilySubByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 查询符合条件的所有的家庭账户
	 * @param param
	 * @return
	 */
	List<String> getAllFamilyCustnosByParam(Map<String, String> param);

}
