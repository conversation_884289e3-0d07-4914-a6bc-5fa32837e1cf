package com.howbuy.crm.hb.domain.prosale.preandtradeimport;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (交易记录 校验 dto )
 * @date 2023/3/2 13:20
 * @since JDK 1.8
 */
@Data
public class CmPreAndTradeValidateDto implements Serializable {

    /**
     *预约Id
     */
    private BigDecimal preId;

    /**
     *分次call 的  mainId [认缴ID].
     */
    private BigDecimal mainCallId;
    /**
     *投顾客户号
     */
    private String custNo;
    //
    private String custName;
    //
    private String prodCode;
    //
    private String prodName;
    /**
     * 预约类型（1：纸质成单；2：电子成单）
     */
    private String preType;
    /**
     * 币种【"156":"人民币","840":"美元","344":"港元","954":"欧元","392":"日元","826":"英镑","250":"法郎","280":"马克" 】
     */
    private String currency;
    //
    private BigDecimal appAmt;
    /**
     * 支付方式 01-自划款；04-银行卡代扣；06-储蓄罐代扣
     */
    private String paymentType;
    //
    private BigDecimal appVol;
    /**
     *  赎回去向0：银行卡；1：储蓄罐
     */
    private String redeemDirection;
    //
    private String expectPayDt;
    //
    private String expectTradeDt;
    //
    private String realPayDt;

    //
    private BigDecimal realPayAmt;

    //
    private BigDecimal realPayFee;

    //
    private String ackDt;

    //
    private BigDecimal nav;

    //
    private BigDecimal ackVol;

    //
    private BigDecimal ackAmt;

    //
    private BigDecimal ackFee;

    //
    private String rateDt;

    //
    private BigDecimal balanceFactor;

    /**
     * 是否海外特殊处理 1-是；0-否
     */
    private String haiwai;

    /**
     * 是否发送短信（1-是、0-否）
     */
    private String message;

    //
    private String impSummary;

    /**
     * 交易代码 120-认购 122-申购 124-赎回 142-强赎
     */
    private String busiCode;

    /**
     * 导入类型：1-常规 2-认缴 3-实缴
     */
    private String impType;


    /**
     * 创建人
     */
    private String creator;

}
