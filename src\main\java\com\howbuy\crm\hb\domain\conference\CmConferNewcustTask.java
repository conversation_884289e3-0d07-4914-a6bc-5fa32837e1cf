package com.howbuy.crm.hb.domain.conference;

import java.util.Date;

/**
 * @description:(扫码参会新课分配任务表)
 * @return
 * @author: xufan<PERSON><PERSON>
 * @date: 2023/11/7 19:56
 * @since JDK 1.8
 */
public class CmConferNewcustTask {
    /**
    * 主键ID
    */
    private String id;

    /**
    * 审核状态 0-待审核、2-审核不通过\审核驳回、1-审核通过\待执行 3-归档 4-作废
    */
    private String auditStatus;

    /**
    * 备注
    */
    private String remark;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTimestamp;

    /**
    * 审核人
    */
    private String auditor;

    /**
    * 审核时间
    */
    private Date auditTimestamp;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTimestamp() {
        return modifyTimestamp;
    }

    public void setModifyTimestamp(Date modifyTimestamp) {
        this.modifyTimestamp = modifyTimestamp;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public Date getAuditTimestamp() {
        return auditTimestamp;
    }

    public void setAuditTimestamp(Date auditTimestamp) {
        this.auditTimestamp = auditTimestamp;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }
}