package com.howbuy.crm.hb.persistence.custinfo;


import com.howbuy.crm.hb.domain.custinfo.CmTransferCust;

import java.util.List;

public interface CmTransferCustMapper {
    int insert(CmTransferCust cmTransferCust);

    /**
     * 批量插入数据
     * @param cmTransferCustList
     * @return
     */
    int batchInsertCmTransferCust(List<CmTransferCust> cmTransferCustList);

    /**
     * 删除导入记录
     * @param modiferuser
     */
    void deleteByModiferUser(String modiferuser);

    /**
     * 查询本次修改记录
     * @param modiferuser
     * @return
     */
    List<CmTransferCust> selectByModiferUser(String modiferuser);
}