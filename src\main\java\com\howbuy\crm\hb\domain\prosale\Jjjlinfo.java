package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;

import crm.howbuy.base.utils.StringUtil;


/**
 * 基金经理信息:
* <AUTHOR>
* @date 2022/9/20
*/
public class Jjjlinfo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 经理名称
	 */
	private String managerName;

	/**
	 * 经理简介
	 */
	private String summary;

	/**
	 * 好买评分
	 */
	private String zhpf;

	/**
	 * 所在公司
	 */
	private String companyName;

	/**
	 * 履历背景
	 */
	private String jjjlly;

	/**
	 * 策略偏好
	 */
	private String sclx;

	/**
	 * 从业年限
	 */
	private String cysj;

	/**
	 * 任私募经理年限
	 */
	private String rsmnx;

	/**
	 * 管理基金数量
	 */
	private String gljjsl;

	/**
	 * 存续基金数量
	 */
	private String cxjjsl;

	/**
	 * 任私募年均回报
	 */
	private String cypjhb;

	/**
	 * 今年回报
	 */
	private String hbjn;

	/**
	 * 本基金任期
	 */
	private String bjjrq;

	/**
	 * 本基金任期回报
	 */
	private String rqhb;

	public String getManagerName() {
		return managerName;
	}

	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public String getZhpf() {
		if(StringUtil.isNotNullStr(zhpf)){
			return zhpf+"分";
		}else{
			return zhpf;
		}
	}

	public void setZhpf(String zhpf) {
		this.zhpf = zhpf;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getJjjlly() {
		return jjjlly;
	}

	public void setJjjlly(String jjjlly) {
		this.jjjlly = jjjlly;
	}

	public String getSclx() {
		return sclx;
	}

	public void setSclx(String sclx) {
		this.sclx = sclx;
	}

	public String getCysj() {
		if(StringUtil.isNotNullStr(cysj)){
			return cysj+"年";
		}else{
			return cysj;
		}
	}

	public void setCysj(String cysj) {
		this.cysj = cysj;
	}

	public String getRsmnx() {
		return rsmnx;
	}

	public void setRsmnx(String rsmnx) {
		this.rsmnx = rsmnx;
	}

	public String getGljjsl() {
		if(StringUtil.isNotNullStr(gljjsl)){
			return gljjsl+"支";
		}else{
			return gljjsl;
		}
	}

	public void setGljjsl(String gljjsl) {
		this.gljjsl = gljjsl;
	}

	public String getCxjjsl() {
		if(StringUtil.isNotNullStr(cxjjsl)){
			return cxjjsl+"支";
		}else{
			return cxjjsl;
		}
	}

	public void setCxjjsl(String cxjjsl) {
		this.cxjjsl = cxjjsl;
	}

	public String getCypjhb() {
		if(StringUtil.isNotNullStr(cypjhb)){
			return cypjhb+"%";
		}else{
			return cypjhb;
		}
	}

	public void setCypjhb(String cypjhb) {
		this.cypjhb = cypjhb;
	}

	public String getHbjn() {
		if(StringUtil.isNotNullStr(hbjn)){
			return hbjn+"%";
		}else{
			return hbjn;
		}
	}

	public void setHbjn(String hbjn) {
		this.hbjn = hbjn;
	}

	public String getBjjrq() {
		return bjjrq;
	}

	public void setBjjrq(String bjjrq) {
		this.bjjrq = bjjrq;
	}

	public String getRqhb() {
		if(StringUtil.isNotNullStr(rqhb)){
			return rqhb+"%";
		}else{
			return rqhb;
		}
	}

	public void setRqhb(String rqhb) {
		this.rqhb = rqhb;
	}
}
	