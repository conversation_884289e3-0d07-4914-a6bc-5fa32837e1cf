package com.howbuy.crm.hb.domain.custinfo;

import com.howbuy.crm.hb.domain.insur.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:(实际投顾维护查询vo)
 * @author: xfc
 * @date: 2023/4/4 23:02
 * @since JDK 1.8
 */
@Data
public class CmRealConsultantVo extends PageVo implements Serializable {
    /**
     * 投顾客户编号
     */
    private String conscustno;

    /**
     * 一账通账号
     */
    private String hboneNo;

    /**
     * 客户姓名
     */
    private String custname;
    /**
     * 名义投顾所属中心
     */
    private String orgcenter;
    /**
     * 名义投顾所属区域
     */
    private String orgname;
    /**
     * 名义投顾所属部门
     */
    private String orgcode;
    /**
     * 名义投顾所属部门code
     */
    private String orgcodevalue;
    /**
     * 名义投顾代码
     */
    private String conscode;
    private String conscodevalue;

    /**
     * 实际投顾所属中心
     */
    private String realorgcenter;
    /**
     * 实际投顾所属区域
     */
    private String realorgname;
    /**
     * 实际投顾所属部门
     */
    private String realorgcode;
    /**
     * 实际所属投顾部门coode
     */
    private String realorgcodevalue;

    /**
     * 实际投顾代码
     */
    private String realConscode;
    private String realConscodevalue;

    /**
     * 备注
     */
    private String remarks;

    private Date modiferTime;
}
