/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.conference;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (分配任务单前端实体类)
 * @date 2023/11/14 09:07
 * @since JDK 1.8
 */
@Data
public class CmconferNewCustTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表单id
     */
    private String id;

    /**
     * 申请人
     */
    private String applyer;

    /**
     * 申请时间
     */
    private String appDt;

    /**
     * 当前状态
     */
    private String currentStatus;

    /**
     * 备注
     */
    private String remark;

}