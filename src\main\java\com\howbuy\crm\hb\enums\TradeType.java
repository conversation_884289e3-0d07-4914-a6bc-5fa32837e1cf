/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/6/25 11:12
 * @since JDK 1.8
 */
public enum TradeType {
    TRADE_TYPE_QS("强赎", "6"),
    TRADE_TYPE_QZ("强增", "5"),
    TRADE_TYPE_QJ("强减", "3"),
    TRA<PERSON>_TYPE_GQHK("股权回款", "1"),
    TRADE_TYPE_FH("分红", "2"),
    TRADE_TYPE_FJYZC("非交易过户转出", "7"),
    TRADE_TYPE_FJYZR("非交易过户转入", "8"),
    TRADE_TYPE_JJQS("基金清算", "9");

    private String typeName;
    private String value;

    TradeType(String typeName, String value) {
        this.typeName = typeName;
        this.value = value;
    }

    public String getTypeName() {
        return typeName;
    }

    public String getValue() {
        return value;
    }

    /**
     * @description:(根据传入的value的值获取文字描述)
     * @param value
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2023/6/25 11:15
     * @since JDK 1.8
     */
    public static String getTypeNameByValue(String value) {
        for (TradeType type : TradeType.values()) {
            if (type.getValue().equals(value)) {
                return type.getTypeName();
            }
        }
        return null; // 如果没有找到匹配的值，则返回null或抛出异常
    }


}