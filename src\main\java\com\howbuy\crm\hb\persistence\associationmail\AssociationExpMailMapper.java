package com.howbuy.crm.hb.persistence.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationExpMail;
import com.howbuy.crm.hb.domain.associationmail.AssociationExpOverRideMail;
import com.howbuy.crm.hb.domain.associationmail.AssociationMail;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 协会邮件-异常解析
 * <AUTHOR> on 2021/6/3 14:10
 */
public interface AssociationExpMailMapper {

    /**
     * 分页查询邮件列表
     * @param param 查询参数
     * @param pageBean 分页参数
     * @return
     */
    List<AssociationExpMail> listAssociationExpMailByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 查询邮件列表
     * @param param 查询参数
     * @return
     */
    List<AssociationExpMail> listAssociationExpMail(@Param("param") Map<String, String> param);

    /**
     * 根据主键获取异常解析邮件
     * @param id
     * @return
     */
    AssociationExpMail findAssociationExpMailById(String id);

    /**
     * 修改异常解析邮件的信息
     * @param associationExpMail
     * @return
     */
    void updateAssociationExpMail(AssociationExpMail associationExpMail);

    /**
     * 更新至正常表
     * @param associationMail
     */
    void updateToNormalMail(AssociationMail associationMail);

    /**
     * 根据重复邮件的信息找出被重复的正常邮件
     * @param custName 客户姓名
     * @param orgName 机构名称
     * @param investorAccount 投资者账号
     * @param managerRegno 管理人登记编码
     * @return
     */
    AssociationMail findNormalMailFromRepeatMail(@Param("custName") String custName, @Param("orgName") String orgName,
                                                 @Param("investorAccount") String investorAccount, @Param("managerRegno") String managerRegno);

    /**
     * 批量修改处理状态
     * @param list 待修改记录id的集合
     * @param handleStatus 处理状态
     * @param modifier 修改人
     * @param remark 备注
     */
    void batchUpdateHandleStatusRemark(@Param("list") List<String> list, @Param("handleStatus") String handleStatus,
                                       @Param("modifier") String modifier, @Param("remark") String remark);


    /**
     * 根据异常表id查询覆盖历史表的记录
     * @param expid
     * @return
     */
    List<AssociationExpOverRideMail> findOverRideMailFromRepeatMail(String expid);
}
