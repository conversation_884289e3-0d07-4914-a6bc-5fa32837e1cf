package com.howbuy.crm.hb.domain.doubletrade;

import java.io.Serializable;

/**
 * @Description: 实体类CmDoubleRemark.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmDoubleRemark implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String tid;

	private String remark;

	private String deptFlag;

	private String creator;

	private String creDt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getDeptFlag() {
		return deptFlag;
	}

	public void setDeptFlag(String deptFlag) {
		this.deptFlag = deptFlag;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreDt() {
		return creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
