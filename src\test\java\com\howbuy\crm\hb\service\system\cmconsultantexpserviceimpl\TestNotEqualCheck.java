package com.howbuy.crm.hb.service.system.cmconsultantexpserviceimpl;

import com.howbuy.crm.hb.service.system.impl.CmConsultantExpServiceImpl;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.Test;

import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2024/11/27 16:59
 * @since JDK 1.8
 */
@PowerMockIgnore("javax.management.*")
@PrepareForTest({CmConsultantExpServiceImpl.class})
public class TestNotEqualCheck extends PowerMockTestCase {

    @InjectMocks
    private CmConsultantExpServiceImpl spy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        spy = PowerMockito.spy(new CmConsultantExpServiceImpl());
    }

    @Test
    public void testNotEqualCheck_NewValueEmpty_OldValueNotEmpty_ShouldReturnTrue() {
        // Arrange
        String oldValue = "old";
        String newValue = "";

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testNotEqualCheck_NewValueNotEmpty_OldValueEmpty_ShouldReturnFalse() {
        // Arrange
        String oldValue = "";
        String newValue = "new";

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testNotEqualCheck_BothValuesEqual_ShouldReturnFalse() {
        // Arrange
        String oldValue = "same";
        String newValue = "same";

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertFalse(result);
    }

    @Test
    public void testNotEqualCheck_BothValuesEmpty_ShouldReturnFalse() {
        // Arrange
        String oldValue = "";
        String newValue = "";

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertFalse(result);
    }

    @Test
    public void testNotEqualCheck_NewValueNotEmpty_OldValueDifferent_ShouldReturnTrue() {
        // Arrange
        String oldValue = "old";
        String newValue = "new";

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertTrue(result);
    }
}