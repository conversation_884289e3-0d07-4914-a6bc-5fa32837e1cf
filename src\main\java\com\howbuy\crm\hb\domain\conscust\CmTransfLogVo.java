package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

public class CmTransfLogVo implements Serializable{
	private static final long serialVersionUID = 1L;
    /**
     * 申请日期
     */
    private String appdt;

    /**
     * 申请中心
     */
    private String appupcentername;

    /**
     * 申请区域
     */
    private String appuporgname;

    /**
     * 申请部门
     */
    private String apporgname;

    /**
     * 申请投顾
     */
    private String appconsname;

    /**
     * 客户姓名
     */
    private String custname;

    /**
     * 投顾客户号
     */
    private String conscustno;

    /**
     * 原投顾
     */
    private String oldconsname;

    /**
     * 原投顾是否分总
     */
    private String oldconsCodeIsFz;
    
    
    /**
     * 原中心
     */
    private String oldupcentername;

    /**
     * 原区域
     */
    private String olduporgname;

    /**
     * 原部门
     */
    private String oldorgname;

    /**
     * 潜在/成交客户
     */
    private String custstatus;

    /**
     * 申请原因
     */
    private String transfcause;

    /**
     * 不符合条件
     */
    private String miscondition;

    /**
     * 处理状态
     */
    private String opstatus;

    /**
     * 处理方式
     */
    private String optype;

    /**
     * 备注
     */
    private String memo;
    
    /**
     * 处理后客户成交状态
     */
    private String transstatus;

	public String getAppdt() {
		return appdt;
	}

	public void setAppdt(String appdt) {
		this.appdt = appdt;
	}

	public String getAppupcentername() {
		return appupcentername;
	}

	public void setAppupcentername(String appupcentername) {
		this.appupcentername = appupcentername;
	}

	public String getAppuporgname() {
		return appuporgname;
	}

	public void setAppuporgname(String appuporgname) {
		this.appuporgname = appuporgname;
	}

	public String getApporgname() {
		return apporgname;
	}

	public void setApporgname(String apporgname) {
		this.apporgname = apporgname;
	}

	public String getAppconsname() {
		return appconsname;
	}

	public void setAppconsname(String appconsname) {
		this.appconsname = appconsname;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getOldconsname() {
		return oldconsname;
	}

	public void setOldconsname(String oldconsname) {
		this.oldconsname = oldconsname;
	}

	public String getOldconsCodeIsFz() {
		return oldconsCodeIsFz;
	}

	public void setOldconsCodeIsFz(String oldconsCodeIsFz) {
		this.oldconsCodeIsFz = oldconsCodeIsFz;
	}

	public String getOldupcentername() {
		return oldupcentername;
	}

	public void setOldupcentername(String oldupcentername) {
		this.oldupcentername = oldupcentername;
	}

	public String getOlduporgname() {
		return olduporgname;
	}

	public void setOlduporgname(String olduporgname) {
		this.olduporgname = olduporgname;
	}

	public String getOldorgname() {
		return oldorgname;
	}

	public void setOldorgname(String oldorgname) {
		this.oldorgname = oldorgname;
	}

	public String getCuststatus() {
		return custstatus;
	}

	public void setCuststatus(String custstatus) {
		this.custstatus = custstatus;
	}

	public String getTransfcause() {
		return transfcause;
	}

	public void setTransfcause(String transfcause) {
		this.transfcause = transfcause;
	}

	public String getMiscondition() {
		return miscondition;
	}

	public void setMiscondition(String miscondition) {
		this.miscondition = miscondition;
	}

	public String getOpstatus() {
		return opstatus;
	}

	public void setOpstatus(String opstatus) {
		this.opstatus = opstatus;
	}

	public String getOptype() {
		return optype;
	}

	public void setOptype(String optype) {
		this.optype = optype;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getTransstatus() {
		return transstatus;
	}

	public void setTransstatus(String transstatus) {
		this.transstatus = transstatus;
	}

}
