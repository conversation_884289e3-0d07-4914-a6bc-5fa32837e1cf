package com.howbuy.crm.hb.domain;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 汇总金额查询返回PO
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class CountBalancePo {

    /**
     * 总条数
     */
    private Integer totalCount = 0;

    /**
     * 总金额
     */
    private BigDecimal totalBalance = BigDecimal.ZERO;


    /**
     * 业务汇总 groupBy 的值
     */
    private String groupInfo;


}
