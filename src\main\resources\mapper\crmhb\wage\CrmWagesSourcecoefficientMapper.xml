<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CrmWageSourcecoefficientMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>



    <insert id="insertCrmWageSourcecoefficient" parameterType="CrmWageSourcecoefficient">
        INSERT INTO CM_WAGE_SOURCECOEFFICIENT (
        <trim suffix="" suffixOverrides=",">
            id,
            <if test="fundType != null"> fund_Type, </if>
            <if test="sourceType != null"> source_type, </if>
            <if test="orgcode != null"> orgcode, </if>
            <if test="buyCnt != null"> buy_cnt, </if>
            <if test="sourcecoefficient != null"> sourcecoefficient, </if>
            <if test="startdt != null"> startdt, </if>
            <if test="enddt != null"> enddt, </if>
            creator,
            credt
        </trim>
        ) values (
        <trim suffix="" suffixOverrides=",">
            seq_SALARY_PRODUCTCOEFFICIENT.Nextval,
            <if test="fundType != null"> #{fundType}, </if>
            <if test="sourceType != null"> #{sourceType}, </if>
            <if test="orgcode != null"> #{orgcode}, </if>
            <if test="buyCnt != null"> #{buyCnt}, </if>
            <if test="sourcecoefficient != null"> #{sourcecoefficient}, </if>
            <if test="startdt != null"> #{startdt}, </if>
            <if test="enddt != null"> #{enddt}, </if>
            #{creator},
            sysdate
        </trim>
        )
    </insert>


    <update id="updateCrmWageSourcecoefficient" parameterType="CrmWageSourcecoefficient">
        UPDATE CM_WAGE_SOURCECOEFFICIENT
        <set>
            <if test="buyCnt != null"> buy_cnt = #{buyCnt}, </if>
            <if test="sourcecoefficient != null"> sourcecoefficient = #{sourcecoefficient}, </if>
            <if test="startdt != null"> startdt = #{startdt}, </if>
            <if test="enddt != null"> enddt = #{enddt}, </if>
            <if test="enddt == null"> enddt = null, </if>
            modifier = #{modifier},
            moddt = sysdate
        </set>
        where id = #{id}
    </update>


    <delete id="delCrmWageSourcecoefficient" parameterType="String">
	    DELETE  from CM_WAGE_SOURCECOEFFICIENT
	    where id = #{id}
	  </delete>

    <select id="listCrmWageSourcecoefficient" parameterType="Map" resultType="CrmWageSourcecoefficient" useCache="false">
        SELECT t1.* ,t3.ORGNAME
        FROM CM_WAGE_SOURCECOEFFICIENT t1
        left join HB_ORGANIZATION t3
        ON t1.ORGCODE = t3.ORGCODE
        where  1=1
        <if test="fundType != null"> AND fundType = #{fundType} </if>
        <if test="orgcode != null"> AND t1.orgcode in (select orgcode from  hb_organization  connect by prior orgcode = parentorgcode start with orgcode =#{orgcode})</if>
        <if test="sourceType != null ">AND source_type = #{sourceType}</if>
        order by id desc
    </select>

    <select id="listCmrWageSourcecoefficientByPage" parameterType="Map" resultType="CrmWageSourcecoefficient" useCache="false">
        SELECT t1.* ,t3.ORGNAME
        FROM CM_WAGE_SOURCECOEFFICIENT t1
        left join HB_ORGANIZATION t3
        ON t1.ORGCODE = t3.ORGCODE
        where  1=1
        <if test="param.fundType != null"> AND fund_TYPE = #{param.fundType} </if>
        <if test="param.orgcode != null"> AND t1.orgcode in (select orgcode from  hb_organization  connect by prior orgcode = parentorgcode start with orgcode =#{param.orgcode})</if>
        <if test="param.sourceType != null ">AND source_type = #{param.sourceType}</if>
        order by id desc
    </select>


</mapper>



