package com.howbuy.crm.hb.domain.carryover;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: bean文件
 * @ClassName: 创建日期                         修改人员   	   版本	 	     修改内容
 * -------------------------------------------------
 * 2020-09-18 14:10   yu.zhang      1.0    	初始化创建
 * <p>
 * 修改记录:
 * @since JDK1.8
 */
@Data
public class CmCarryOverTradeInfo implements Serializable {
	
	private static final long serialVersionUID = 1L;
    private String carryid;
    private String conscustno;
    private String fundcode;
    private String fundname;
    private String tradedt;
    private BigDecimal carryovershare;
    private BigDecimal subshare;
    private String status;
    private String statusStr;
    private Date creatdt;
    private String creator;
    private String modifydt;
    private String modifier;
    private String custname;
    private String redemptionappserialno;
    private String buyappserialno;
    private String isdel;

}
