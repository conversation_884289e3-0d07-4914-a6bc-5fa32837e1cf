package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.CmOrgCust;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/6/1 10:43
 */
public interface CmOrgCustMapper {

    List<CmOrgCust> listOrgCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    String getAllLinkman(@Param("custNo") String custNo);

    List<CmOrgCust> selectExportCust(Map<String,String> paramSql);
}
