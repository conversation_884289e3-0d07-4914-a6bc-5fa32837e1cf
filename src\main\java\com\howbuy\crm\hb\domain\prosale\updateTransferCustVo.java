package com.howbuy.crm.hb.domain.prosale;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:(合并客户， 更新 份额转让 客户信息，)
 * @param
 * @return 
 * @author: haoran.zhang
 * @date: 2023/8/17 15:30
 * @since JDK 1.8
 */
@Data
public class updateTransferCustVo implements Serializable {

	/** 
	 * (变量说明描述)	
	 *
	 * long Prebookmanycallinfo.java serialVersionUID
	 */
	private static final long serialVersionUID = 1L;


	/**
	 * 保留的客户号，最新的客户号
	 */
	private String custNo;

	/**
	 * 待删除的  客户号列表
	 */
	private List<String> deleteCustNoList;

	/**
	 *最新的客户号--> 投顾
	 */
	private String consCode;

	/**
	 *最新的客户号--> 投顾所属部门
	 */
	private String orgCode;

	/**
	 *最新的客户号--> 投顾所在区域
	 */
	private String area;
}
