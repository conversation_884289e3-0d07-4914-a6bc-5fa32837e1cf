package com.howbuy.crm.hb.domain.custinfo;


import lombok.Data;

import java.io.Serializable;


/**
 * @version 1.0
 * @Description: 实体类CM_WECHAT_TRANSCUST_INFO 投顾客户企业微信划转记录表
 * @created 2022年10月21日17:11:36
 */
@Data
public class CmWechatTransCustInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    //主键
    private String id;

    //旧投顾账号
    private String oldConsCode;

    //新投顾账号
    private String consCode;

    //划转客户号
    private String conscustNo;

    //划转客户一账通
    private String hboneNo;

    //划转客户企业微信ID
    private String externaluserId;

    //企业微信离职划转结果码 0成功 其他失败
    private String returnCode;

    /**
     * 分配原因 StaticVar.TRANSF_REASON_*
     */
    private String assignReason;

    //创建人
    private String creator;

    //创建时间
    private String createtime;

    //更新人
    private String modor;

    //更新时间
    private String updatetime;

}
