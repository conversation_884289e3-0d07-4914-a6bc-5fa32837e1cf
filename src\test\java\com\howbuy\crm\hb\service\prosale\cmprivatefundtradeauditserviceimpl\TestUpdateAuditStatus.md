## 测试的类
> com.howbuy.crm.hb.service.prosale.impl.CmPrivatefundtradeAuditServiceImpl
## 测试的方法
> updateAuditStatus(CmPrivatefundtradeAudit cmPrivatefundtradeAudit)

## 分支伪代码
```java
if (auditid为空) {
    直接返回，提示"请先选择需要审核的数据"
}
if (auditstatus为空) {
    直接返回，提示"审核状态不能为空"
}
if (该审核记录不是待审核状态) {
    直接返回，提示"该记录目前不是待审核状态，请刷新当前页面
}
if (审核操作为"审核通过") {
    if (验证持仓信息成功) {
        修改审核状态为“审核通过”
        返回“审核成功”
    } else {
        返回审核失败
    }
} else {
    修改审核状态为“审核不通过”
    返回“审核成功”
}
```

## 测试案例
##### 1、testEmptyAuditid：auditid为空
##### 2、testEmptyAuditstatus：auditstatus为空
##### 3、testNotWaitCheck：该审核记录不是待审核状态
##### 4、testValidateFundtradeSuccess：审核通过，验证持仓信息成功
##### 5、testValidateFundtradeFail：审核通过，验证持仓信息失败
##### 6、testAuditNotPass：审核操作为"审核不通过"
