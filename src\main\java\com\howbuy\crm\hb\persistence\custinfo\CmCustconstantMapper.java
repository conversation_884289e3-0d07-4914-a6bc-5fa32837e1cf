package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.CmBirthdayGiftInfo;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmCustconstantMapper {
	
	/**
	 * 得到单个数据对象
	 * @param param
	 * @return
	 */
	CmCustconstant getCmCustconstant(Map<String, String> param);
    
    
	/**
	 * 单条修改数据对象
	 * @param cmCustconstant
	 */
	void updateCmCustconstant(CmCustconstant cmCustconstant);
	
	/**
	 * 根据客户号和投顾号查询分配记录
	 * @param param
	 * @return
	 */
	List<CmCustconstant>  listCustConstantByCustAndConscode(Map<String, String> param);

	/**
	 * 根据客户号查询对应的投顾信息
	 *
	 * @param idList
	 * @return
	 */
	List<CmBirthdayGiftInfo> listCustConstantByCustNo(@Param("idList") List<String> idList);

	/**
	 * 根据机构编码查询对应的投顾信息
	 *
	 * @param orgCode
	 * @return
	 */
	List<String> listSubConsCodeByOrgCode(@Param("orgCode") String orgCode);

	/**
	 * 根据机构编码查询对应的投顾信息
	 *
	 * @param orgCode
	 * @return
	 */
	List<String> listSubConsCodeByOther(@Param("orgCode") String orgCode);

	/**
	 * 根据机构编码查询对应的投顾信息
	 *
	 * @param hboneNo
	 * @param consCode
	 * @return
	 */
	String getWechatAddTime(@Param("hboneNo") String hboneNo, @Param("consCode") String consCode);
    
}
