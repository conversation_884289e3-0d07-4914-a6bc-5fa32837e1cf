package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.custinfo.WaitTransferCust;

import crm.howbuy.base.db.CommPageBean;


/**
 * 
 * <AUTHOR>
 *
 */
public interface WaitTransferCustMapper {
	
	/**
	 * 插入申请划转表
	 * @param param
	 * @return
	 */
	public int insertWaitTransferCust(Map<String,String> param);
	
	/**
	 * 删除申请划转中的客户
	 * @param param
	 */
	void delWaitTransferCust(Map<String,String> param);
	
	/**
	 * 删除申请划转中的客户
	 * @param param
	 */
	void delWaitTransferCusts(Map<String,String> param);
	
	/**
	 * 更新状态
	 * @param param
	 */
	void changeWaitTransferCusts(Map<String,String> param);
	
	/**
	 * 插入处理划转客户信息表
	 * @param param
	 * @return
	 */
	public int insertDealTransferCust(Map<String,String> param);
	
	/**
	 * 批量处理
	 * @param list
	 * @return
	 */
	public int batchDealTransferCust(List<Map<String,String>> list);
	
	/**
	 * 根据客户查询是否存在申请划转客户表中的客户
	 * @param param
	 * @return
	 */
	int getWaitTransCountByCustnos(Map<String,String> param);
	
	/**
	 * 根据客户号查询这些客户中没有全部打三个标的人数
	 * @param param
	 * @return
	 */
	int getNoAllLabelsCountByCusts(Map<String,String> param);
	
	/**
	 * 根据客户号查询这些客户中是成交客户的人数
	 * @param param
	 * @return
	 */
	int getCjkhCountByCusts(Map<String,String> param);
	
	/**
	 * 根据条件查询申请划转客户信息
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<WaitTransferCust> listWaitTransferCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 查询列表
	 * @param param
	 * @return
	 */
	List<WaitTransferCust> listWaitTransferCustByCustnos(Map<String,String> param);
	
	/**
	 * 根据部门获取父部门的owner和owner类型
	 * @param param
	 * @return
	 */
	List<Map<String,String>> getParentOrgcodeOwner(Map<String,String> param);
	
	/**
	 * 查询传入的投顾是否正常投顾并且非虚拟投顾
	 * @param param
	 * @return
	 */
	int getNormalConsCount(Map<String,String> param);
}
