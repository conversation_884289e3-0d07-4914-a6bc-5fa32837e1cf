/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.outersevice;

import com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade;
import com.howbuy.crm.account.client.request.beisen.CmSyncBeisenRequest;
import com.howbuy.crm.account.client.response.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/15 10:46
 * @since JDK 1.8
 */
@Service
public class CmSyncBeisenOuterService {
    @Autowired
    private CmSyncBeisenFacade cmSyncBeisenFacade;

    /**
     * @description:(通过userno同步北森数据)
     * @param request
     * @return java.util.List<java.lang.String> 返回异常userno集合
     * @author: shijie.wang
     * @date: 2024/11/15 9:57
     * @since JDK 1.8
     */
    public List<String> syncConsultantExpByBeisenUserNoFacade(CmSyncBeisenRequest request){
        return getResponse(cmSyncBeisenFacade.syncConsultantExpByBeisenUserNoFacade(request));
    }

    /**
     * @description:(通过userno集合同步北森数据)
     * @param requestList
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/12/10 10:09
     * @since JDK 1.8
     */
    public List<String> syncConsultantExpByBeisenUserNosFacade(List<CmSyncBeisenRequest> requestList){
        return getResponse(cmSyncBeisenFacade.syncConsultantExpByBeisenUserNosFacade(requestList));
    }

    /**
     * @description:(定时调度北森同步花名册)
     * @param
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/15 10:13
     * @since JDK 1.8
     */
    public List<String> syncConsultantExpByCmBeisenDateTimeFacade(String startDate, String endDate){
        return getResponse(cmSyncBeisenFacade.syncConsultantExpByCmBeisenDateTimeFacade(startDate, endDate));
    }

    /**
     * @description:(返回结果)
     * @param response
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/12/10 10:07
     * @since JDK 1.8
     */
    private List<String> getResponse(Response<List<String>> response){
        if(null != response && response.isSuccess()){
            return response.getData();
        }
        return Collections.emptyList();
    }

}