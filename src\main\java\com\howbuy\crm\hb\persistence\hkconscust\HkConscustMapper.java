package com.howbuy.crm.hb.persistence.hkconscust;

import com.howbuy.crm.hb.domain.conscust.ConscustVO;
import com.howbuy.crm.hb.domain.hkconscust.HkConscust;
import com.howbuy.crm.hb.domain.hkconscust.SearchHkConscustVo;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 香港客户维护表Mapper
 * <AUTHOR>
 * @date 2022/4/20 9:14
 */
public interface HkConscustMapper {

    /**
     * 分页查询香港客户列表
     * @param param
     * @param pageBean
     * @return
     */
    List<HkConscust> listHkConscustByPage(@Param("param") SearchHkConscustVo param, @Param("page") CommPageBean pageBean);

    /**
     * 根据主键获取香港客户信息
     * @param id
     * @return
     */
    HkConscust findHkConscustById(String id);

    /**
     * 根据香港客户id获取香港客户信息
     * @param hkcustid
     * @return
     */
    HkConscust findHkConscustByHkcustid(String hkcustid);


    /**
     * @description: 根据客户号列表查询香港客户信息
     * @param custNoList 客户号列表
     * @return java.util.List<com.howbuy.crm.hb.domain.hkconscust.HkConscust> 香港客户信息列表
     * @author: jin.wang03
     * @date: 2023/12/12 19:37
     * @since JDK 1.8
     */
    List<HkConscust> listHkConscustByCustNoList(@Param("custNoList") List<String> custNoList);

    /**
     * @description: 根据香港客户号列表查询投顾客户信息
     * @param hkTxAcctNoList 客户号列表
     * @return java.util.List<com.howbuy.crm.hb.domain.hkconscust.HkConscust> 香港客户信息列表
     * @author: jin.wang03
     * @date: 2023/12/12 19:37
     * @since JDK 1.8
     */
    List<ConscustVO> listHkConscustByHkTxAcctNoList(@Param("hkTxAcctNoList") List<String> hkTxAcctNoList);

    /**
     * @description: 根据香港客户号列表查询 关联的投顾客户号，以及该投顾客户号绑定的一账通号
     * @param hkTxAcctNoList
     * @return java.util.List<com.howbuy.crm.hb.domain.conscust.ConscustVO>
     * @author: jin.wang03
     * @date: 2024/1/15 16:57
     * @since JDK 1.8
     */
    List<ConscustVO> listConsCustNoAndHboneNoByHkTxAcctNoList(@Param("hkTxAcctNoList") List<String> hkTxAcctNoList);
}
