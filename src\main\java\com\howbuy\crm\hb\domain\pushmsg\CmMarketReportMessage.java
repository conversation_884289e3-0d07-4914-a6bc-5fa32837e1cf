package com.howbuy.crm.hb.domain.pushmsg;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 营销喜报管理
 */
@Data
public class CmMarketReportMessage {
    /**
     * ID
     */
    private BigDecimal id;

    /**
     * 预约ID
     */
    private Long preId;

    /**
     * 所属投顾
     */
    private String consCode;

    /**
     * 投顾名称
     */
    private String consName;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 产品代码
     */
    private String prodCode;

    /**
     * 产品名称
     */
    private String prodName;

    /**
     * 好买产品线( 0:现金管理工具;1:债券型;2:类固定收益;3:对冲;4:股票型;5:PE、VC;6:房地产基金;7:其他;8:海外)
     */
    private String hbType;

    /**
     * 策略分类101	中国股票 102	香港股票 103	美国股票 104	其他 201	中国债券 202	海外债券 203	非标产品 204	中国货币市场产品 205	其他 301	多策略 302	多空仓型 303	管理期货 304	宏观策略 305	市场中性 306	套利型 307	其他 401	中国股权 402	其他 501	FOF 502	结构化产品 503	其他 601	其他
     */
    private String strategyClassify;

    /**
     * 币种
     */
    private String currency;

    /**
     * 用户打款金额
     */
    private BigDecimal payAmt;

    /**
     * 用户打款金额-人民币
     */
    private BigDecimal payAmtRmb;

    /**
     * 客户打款时间
     */
    private Date payTime;

    /**
     * 是否发送（0-未发送、1-已发送）
     */
    private String isSend;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 产品分销渠道
     */
    private String preDisCode;

    /**
     * 预约的架构分类。 0-海外（中台）预约    1-高端（中台）代销  2-直销的预约
     */
    private String archType;


}