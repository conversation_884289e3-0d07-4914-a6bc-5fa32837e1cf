package com.howbuy.crm.hb.domain.callout;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 实体类CsVisitNewest.java
 * <AUTHOR>
 * @version 1.0
 */
public class CsVisitNewest implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String conscustNo;

	private String commContent;
	
	private String consBookingId;
	
	private String visitType;
	
	private String visitClassify;

	private String modifyFlag;
	
	private String creator;

	private Date creDt;

	private String modifier;

	private Date modDt;
	
	private Date stimeStamp;

	private String hisFlag;

	private String hisId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getCommContent() {
		return commContent;
	}

	public void setCommContent(String commContent) {
		this.commContent = commContent;
	}

	public String getConsBookingId() {
		return consBookingId;
	}

	public void setConsBookingId(String consBookingId) {
		this.consBookingId = consBookingId;
	}

	public String getVisitType() {
		return visitType;
	}

	public void setVisitType(String visitType) {
		this.visitType = visitType;
	}

	public String getVisitClassify() {
		return visitClassify;
	}

	public void setVisitClassify(String visitClassify) {
		this.visitClassify = visitClassify;
	}

	public String getModifyFlag() {
		return modifyFlag;
	}

	public void setModifyFlag(String modifyFlag) {
		this.modifyFlag = modifyFlag;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreDt() {
		return creDt;
	}

	public void setCreDt(Date creDt) {
		this.creDt = creDt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public Date getModDt() {
		return modDt;
	}

	public void setModDt(Date modDt) {
		this.modDt = modDt;
	}

	public Date getStimeStamp() {
		return stimeStamp;
	}

	public void setStimeStamp(Date stimeStamp) {
		this.stimeStamp = stimeStamp;
	}

	public String getHisFlag() {
		return hisFlag;
	}

	public void setHisFlag(String hisFlag) {
		this.hisFlag = hisFlag;
	}

	public String getHisId() {
		return hisId;
	}

	public void setHisId(String hisId) {
		this.hisId = hisId;
	}


}
