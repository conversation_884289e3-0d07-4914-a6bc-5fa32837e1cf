## 测试的类
> com.howbuy.crm.hb.service.system.impl.ClAsignActiveConfigServiceImpl
## 测试的方法 
> updateClAsignActiveConfig(ClAsignActiveConfig vo)

## 分支伪代码
``` java
验证过程 {
    if (类型等于接管) {
        if(存在同一管理层、同一投顾的数据){
        	返回 存在重复数据，请在原数据上进行修改！
        }else{
        	返回 success
        }
    }
    if (类型不等于接管) {
       if(存在有重叠日期的数据){
        	返回 存在计算日期重叠的数据！
        }else{
        	返回 success
        }
    }    
}


## 测试案例
1、类型等于接管，存在同一管理层、同一投顾的数据
### test01
2、类型等于接管，不存在同一管理层、同一投顾的数据
### test02
3、类型等于非接管，存在重叠日期
### test03
4、类型等于非接管，不存在重叠日期
### test04
