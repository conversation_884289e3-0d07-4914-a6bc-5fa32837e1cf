package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CrmDealOrderBean implements Serializable {
	/**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 7757051939821034685L;
    /**
     * 客户订单号
     */
    private String dealNo;
    /**
     * 分销代码
     */
    private String disCode;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 银行账号
     */
    private String bankAcct;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 银行代码
     */
    private String paymentType;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 申请金额
     */
    private BigDecimal appAmt;
    /**
     * 申请份额
     */
    private BigDecimal appVol;
    /**
     * 申请日期时间
     */
    private Date appDtm;
    /**
     * 付款状态
     */
    private String payStatus;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * TA交易日期
     */
    private String taTradeDt;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 申请手续费（预约中的手续费）
     */
    private BigDecimal prefee;
    /**
     * 中台业务码
     */
    private String mBusiCode;
    /**
     * 赎回去向
     */
    private String redeemDirection;
    /**
     * 销售类型: 1-直销; 2-代销
     */
    private String scaleType;
    /**
     * 分红方式
     */
    private String divMode;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;
    /**
     * 备注字段
     */
    private String memo;
    /**
     * 净值
     */
    private BigDecimal nav;
    
    private String clrq;

    /**
     * 上报TA日期
     */
    private String submitTaDt;

    private String ackDt;

	private String isVolTansfer;
	
	/**
	 * 是否定投，1是0否
	 */
	private String highFundInvPlanFlag;

    /**
     * 业务类型名称
     */
    private String busiTypeName;
    /**
     * 产品简称
     */
    private String productAttr;
    /**
     * 好买香港代销标识 0-否; 1-是
     */
    private String hkSaleFlag;


    /**
     * 到账证明-pdf文件地址
     */
	private String arrivalProofPdfFilePath;

    /**
     *中台是否配置为 ：有限合伙产品
     */
	private boolean limitedCoopProd;

    /**
     * 是否分期产品
     */
	private String sffqcl;

    /**
     * 母基金代码
     */
	private String mjjdm;
}
