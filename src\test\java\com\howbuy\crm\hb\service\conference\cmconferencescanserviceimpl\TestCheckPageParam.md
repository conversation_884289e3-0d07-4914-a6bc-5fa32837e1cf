## 测试的类
> com.howbuy.crm.hb.service.conference.impl.CmConferenceScanServiceImpl
## 测试的方法 
> checkPageParam(Map<String, String> pageParam)

## 分支伪代码
``` java
验证过程 {
    if (手机号码1已存在) {
        返回 校验不通过，并给出提示信息（手机号码1已存在）
    }
    if (手机号码1不存在，数据有误) {
       返回 校验不通过，并给出提示信息（手机号码1不存在，数据有误）
    }    
    if (外部接口调用失败) {
        返回 校验不通过，并给出提示信息（外部接口调用失败）
    }
    if (手机号码2（15021286907）已存在，请修改后再试) {
        返回 校验不通过，并给出提示信息（手机号码2（15021286907）已存在，请修改后再试）
    }
    if (电话号码已存在，请修改后再试) {
        返回 校验不通过，并给出提示信息（电话号码已存在，请修改后再试）
    }
    if (电子邮箱已存在，请修改后再试) {
        返回 校验不通过，并给出提示信息（电子邮箱已存在，请修改后再试）
    }
    
    返回 校验成功 
}


## 测试案例
1、手机号码1已存在
### test01
2、手机号码1不存在，数据有误
### test02
3、外部接口调用失败
### test03
4、手机号码2（15021286907）已存在，请修改后再试
### test04
5、电话号码已存在，请修改后再试
### test05
6、电子邮箱已存在，请修改后再试
### test06
7、正常数据，校验成功
### test07