package com.howbuy.crm.hb.domain.conscust;

import com.howbuy.crm.hb.domain.custinfo.Conscust;

import java.util.Objects;

/**
 * 
 * <AUTHOR>
 *
 */
public class ConscustVO extends Conscust {
	private String conscode;

	/**
	 * 关联的香港客户号
	 */
	private String hkTxAcctNo;

	/**
	 * {@link com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum}
	 * 香港客户状态-   客户状态 0-正常 1-注销 2-休眠 3-注册4-开户申请成功
	 */
	private String hkCustStatus;

	@Override
	public String getConscode() {
		return conscode;
	}

	@Override
	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getHkTxAcctNo() {
		return hkTxAcctNo;
	}

	public void setHkTxAcctNo(String hkTxAcctNo) {
		this.hkTxAcctNo = hkTxAcctNo;
	}

	public String getHkCustStatus() {
		return hkCustStatus;
	}

	public void setHkCustStatus(String hkCustStatus) {
		this.hkCustStatus = hkCustStatus;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof ConscustVO)) {
			return false;
		}
		if (!super.equals(o)) {
			return false;
		}
		ConscustVO that = (ConscustVO) o;
		return Objects.equals(conscode, that.conscode) && Objects.equals(hkTxAcctNo, that.hkTxAcctNo);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), conscode, hkTxAcctNo);
	}
}
