<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.reward.CmPrpCxCoeffMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>

	<select id="listPrpCxCoeffByPage" parameterType="map" resultType="com.howbuy.crm.hb.domain.reward.CmPrpCxCoeff" useCache="false">
		SELECT a.id,
			   a.cx_type     as cxType,
			   a.source_type as sourceType,
			   a.ej_coeff    as ejCoeff,
			   a.gq_coeff    as gqCoeff,
			   a.start_dt    as startDt,
			   a.end_dt      as endDt,
			   a.creator,
			   a.create_time as createTime,
			   a.modor,
			   a.update_time as updateTime
		  FROM CM_PRP_CX_COEFF a
		 order by a.create_time desc
	</select>

	<select id="selectByPrimaryKey" parameterType="long" resultType="com.howbuy.crm.hb.domain.reward.CmPrpCxCoeff" useCache="false">
        SELECT
		   a.id,
		   a.cx_type     as cxType,
		   a.source_type as sourceType,
		   a.ej_coeff    as ejCoeff,
		   a.gq_coeff    as gqCoeff,
		   a.start_dt    as startDt,
		   a.end_dt      as endDt,
		   a.creator,
		   a.create_time as createTime,
		   a.modor,
		   a.update_time as updateTime
		FROM CM_PRP_CX_COEFF a
        where a.id = #{id}
    </select>

	<select id="selectAlreadyCount" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpCxCoeff" resultType="int" useCache="false">
		select count(*)
		from CM_PRP_CX_COEFF
		where
		<if test="endDt == null or endDt == ''">
			nvl(end_dt,'20991231') >= #{startDt}
		</if>
		<if test="endDt != null and endDt != ''">
			start_dt &lt;= #{endDt}
			and nvl(end_dt,'20991231') >= #{startDt}
		</if>
		and source_type = #{sourceType}
		and cx_type = #{cxType}
		<if test="id != null">
			and id != #{id}
		</if>
	</select>

	<insert id="insert" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpCxCoeff">
		insert into CM_PRP_CX_COEFF
		  (ID,
		   CX_TYPE,
		   SOURCE_TYPE,
		   EJ_COEFF,
		   GQ_COEFF,
		   START_DT,
		   END_DT,
		   CREATOR,
		   CREATE_TIME)
		values
		   (#{id},
			#{cxType,jdbcType=VARCHAR},
			#{sourceType,jdbcType=VARCHAR},
			#{ejCoeff,jdbcType=DECIMAL},
			#{gqCoeff,jdbcType=DECIMAL},
			#{startDt,jdbcType=VARCHAR},
			#{endDt,jdbcType=VARCHAR},
			#{creator,jdbcType=VARCHAR},
			sysdate)
    </insert>

	<update id="update" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpCxCoeff">
		update CM_PRP_CX_COEFF
		<set>
			<if test="ejCoeff != null">
				EJ_COEFF = #{ejCoeff,jdbcType=DECIMAL},
			</if>
			<if test="gqCoeff != null">
				GQ_COEFF = #{gqCoeff,jdbcType=DECIMAL},
			</if>
			<if test="startDt != null">
				start_dt = #{startDt},
			</if>
			<if test="endDt != null">
				end_dt = #{endDt},
			</if>
			MODOR = #{modor,jdbcType=VARCHAR},
			UPDATE_TIME = sysdate
		</set>
		where id = #{id}
	</update>

	<delete id="deleteById" parameterType="long">
        delete CM_PRP_CX_COEFF where id = #{id}
    </delete>

</mapper>