package com.howbuy.crm.hb.domain.pushmsg;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmPushMsg implements Serializable {

	private static final long serialVersionUID = 1L;
	private String pushid;
	private String title;
	private String msgtype;
	private String msgtypeval;
	private String msgcontent;
	private String conscode;
	private String orgcode;
	private String creator;
	private Date credt;
	private String modifier;
	private Date moddt;
	private String pushflag;
	private String pushflagval;
	private Date pushdt;
	private String pushdtval;
	private Date expectpushdt;
	private String expectpushdtval;
	private String msgstyle;
	private String msgstyleval;
	private String msgchannel;
	private String msgchannelval;
	/**
	 * 任务类型 1-企微任务 2-卡片消息
	 */
	private String taskType;
	/**
	 * 客户号
	 */
	private String conscustNo;
	/**
	 * 企微任务ID
	 */
	private String taskId;
	/**
	 * 客户姓名
	 */
	private String custName;

	/**
	 * 消息链接
	 */
	private String msgLink;

	public String getPushid() {
		return pushid;
	}
	public void setPushid(String pushid) {
		this.pushid = pushid;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getMsgtype() {
		return msgtype;
	}
	public void setMsgtype(String msgtype) {
		this.msgtype = msgtype;
	}
	public String getMsgcontent() {
		return msgcontent;
	}
	public void setMsgcontent(String msgcontent) {
		this.msgcontent = msgcontent;
	}
	public String getConscode() {
		return conscode;
	}
	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	public String getOrgcode() {
		return orgcode;
	}
	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public Date getCredt() {
		return credt;
	}
	public void setCredt(Date credt) {
		this.credt = credt;
	}
	public String getModifier() {
		return modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	public Date getModdt() {
		return moddt;
	}
	public void setModdt(Date moddt) {
		this.moddt = moddt;
	}
	public String getPushflag() {
		return pushflag;
	}
	public void setPushflag(String pushflag) {
		this.pushflag = pushflag;
	}
	public Date getPushdt() {
		return pushdt;
	}
	public void setPushdt(Date pushdt) {
		this.pushdt = pushdt;
	}
	public Date getExpectpushdt() {
		return expectpushdt;
	}
	public void setExpectpushdt(Date expectpushdt) {
		this.expectpushdt = expectpushdt;
	}
	public String getMsgtypeval() {
		return msgtypeval;
	}
	public void setMsgtypeval(String msgtypeval) {
		this.msgtypeval = msgtypeval;
	}
	public String getPushflagval() {
		return pushflagval;
	}
	public void setPushflagval(String pushflagval) {
		this.pushflagval = pushflagval;
	}
	public String getPushdtval() {
		return pushdtval;
	}
	public void setPushdtval(String pushdtval) {
		this.pushdtval = pushdtval;
	}
	public String getExpectpushdtval() {
		return expectpushdtval;
	}
	public void setExpectpushdtval(String expectpushdtval) {
		this.expectpushdtval = expectpushdtval;
	}

	public String getMsgstyle() {
		return msgstyle;
	}

	public void setMsgstyle(String msgstyle) {
		this.msgstyle = msgstyle;
	}

	public String getMsgstyleval() {
		return msgstyleval;
	}

	public void setMsgstyleval(String msgstyleval) {
		this.msgstyleval = msgstyleval;
	}

	public String getMsgchannel() {
		return msgchannel;
	}

	public void setMsgchannel(String msgchannel) {
		this.msgchannel = msgchannel;
	}

	public String getMsgchannelval() {
		return msgchannelval;
	}

	public void setMsgchannelval(String msgchannelval) {
		this.msgchannelval = msgchannelval;
	}

	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getMsgLink() {
		return msgLink;
	}

	public void setMsgLink(String msgLink) {
		this.msgLink = msgLink;
	}
}
