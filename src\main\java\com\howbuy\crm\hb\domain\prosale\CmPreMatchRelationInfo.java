package com.howbuy.crm.hb.domain.prosale;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @Description: 销控-->客户存入 vs 预约单关联关系
 * <AUTHOR>
 * @version 1.0
 * @created  2021-11-1 15:44:03
 */
@Data
public class CmPreMatchRelationInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/***************************prebookInfo信息  begin **************************************/
	/**
	 * 预约Id
	 */
	private BigDecimal preId;

	/**
	 * 预计交易日期
	 */
	private String expectTradeDt;

	/**
	 * 一账通账号
	 */
	private String hboneNo;


	/**
	 * 产品代码
	 */
	private String prodCode;

	/**
	 * 产品名称
	 */
	private String prodName;

	/**
	 * 客户号
	 */
	private String consCustNo;
	/**
	 * 客户名称
	 */
	private String custName;
	/**
	 * 投顾编号
	 */
	private String consCode;

	/**
	 * 投顾-名称
	 */
	private String consName;

	/**
	 * 所属部门-code
	 */
	private String orgCode;

	/**
	 * 所属部门-名称
	 */
	private String orgName;


	/**
	 * 预约金额
	 */
	private BigDecimal buyAmt;

	/**
	 * 打款金额
	 */
	private BigDecimal realPayAmt;

	/***************************prebookInfo信息  end **************************************/


	/***************************deposit信息  begin **************************************/
	/** 资金存入SID */
	private String depositSid;

	/**
	 * 资金存入 一账通账号
	 */
	private String depositHboneNo;

	/** 入账日期yyyyMMdd */
	private String vouchDt;

	/** 发生金额 元 */
	private String occurBalance;
	/** 对手方账户：卡号 */
	private String thatBankAcct;
	/** 对手方账户名称 */
	private String thatBankAcctName;
	/** 本方卡号 */
	private String thisBankAcct;
	/** 摘要 */
	private String summaryInfo;

	/** 资金存入匹配状态 0-交易未匹配；1-交易部分匹配；2-交易已匹配； */
	private String depositMatchStatus;

	/***************************deposit信息  end **************************************/


}
