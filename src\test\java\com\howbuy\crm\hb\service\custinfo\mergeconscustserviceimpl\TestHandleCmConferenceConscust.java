package com.howbuy.crm.hb.service.custinfo.mergeconscustserviceimpl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.SneakyThrows;

import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.slf4j.Logger;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.hb.domain.conference.CmConferenceConscust;
import com.howbuy.crm.hb.persistence.conference.CmConferenceConscustMapper;
import com.howbuy.crm.hb.service.conference.CmConferenceConscustService;
import com.howbuy.crm.hb.service.custinfo.impl.MergeConscustServiceImpl;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.prosale.request.CmPreSaleControlVo;

import crm.howbuy.base.utils.DateTimeUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ReflectionUtils;

import static org.mockito.Mockito.*;

/**
 * 单元测试：新增扫码签到客户信息
 * <AUTHOR>
 * @date 2022/7/12 15:06
 */
@PrepareForTest({MergeConscustServiceImpl.class, LoggerFactory.class})
@PowerMockIgnore("javax.management.*")
@Test
public class TestHandleCmConferenceConscust extends PowerMockTestCase {

	@InjectMocks
    private MergeConscustServiceImpl serviceMock;
    
	@Mock
    private Logger log;
	
	@Mock
	private CmConferenceConscustService cmConferenceConscustServiceMock;
	
	@Mock
	private CmConferenceConscustMapper cmConferenceConscustMapperMock;
		
	public static final String conscustno = "1";
	private List<String> mergeConscustes = new ArrayList<String>(8);
	public static final String userid = "sys";

    @SneakyThrows
    @BeforeMethod
	public void setUp()  {
    	PowerMockito.mockStatic(LoggerFactory.class);
        log = mock(Logger.class);
        PowerMockito.when(LoggerFactory.getLogger(MergeConscustServiceImpl.class)).thenReturn(log);
        MemberModifier.field(MergeConscustServiceImpl.class, "log").set(serviceMock, log);
	}
    
    /**
     * 1.一个会议id对应一个客户且是保留客户      &&    一个会议id对应一个客户且不是保留客户
     */
   @Test
   @SneakyThrows
   public void testOne() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("1");
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("222");
	   cmConferenceConscust.setConscustno("2");
	   list.add(cmConferenceConscust);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       verify(log, times(1)).info("该会议id对应一个客户号为，会议id为：{}","111");
       verify(log, times(1)).info("该会议id对应一个客户号为，会议id为：{}","222");
       verify(log, times(1)).info("该会议id对应一个客户号，且不为保留客户：conferenceid -> {},conscustno -> {}" ,"222","2");
       //验证更新参会客户号1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
   }
   
   /**
    * mock客户参会记录列表
    * @param spy 测试目标类
    * @param list 客户参会记录列表
    */
   @SneakyThrows
   private void mockListCmConferenceConscust(MergeConscustServiceImpl spy, List<CmConferenceConscust> list) {
       MemberModifier.field(MergeConscustServiceImpl.class, "cmConferenceConscustService").set(spy, cmConferenceConscustServiceMock);
       PowerMockito.when(cmConferenceConscustServiceMock.listCmConferenceConscustByConscustnoList(Mockito.any())).thenReturn(list);
   }


   /**
    * 2.一个会议id对应多个客户且包含保留客户，且第一条是保留客户
    */
  @Test
  @SneakyThrows
  public void testTwo() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("1");
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("2");
	   list.add(cmConferenceConscust);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
      Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
      verify(log, times(1)).info("此会议id对应多个客户，且第一个客户为保留客户：conferenceid->{},conscustno->{},list->{}" ,"111",conscustno,JSON.toJSONString(list));
      verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"111","2");
      //验证更新参会客户号0次
      Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
  }
  
  
  
  /**
   * 3.一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
   */
 @Test
 @SneakyThrows
 public void testThr() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("2");
	   cmConferenceConscust.setActualnub(1);
	   cmConferenceConscust.setActualnubdt(new Date());
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("1");
	   list.add(cmConferenceConscust);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
     Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
     verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,cmConferenceConscust.getActualnub(),DateTimeUtil.getDateFormat(cmConferenceConscust.getActualnubdt(), null));
     //验证更新保留客户的到场人数和参会时间1次
     Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
     //验证删除非保留客户1次
     Mockito.verify(cmConferenceConscustMapperMock, new Times(1)).deleteConferenceConscust(mapCaptor.capture());
     verify(log, times(1)).info("一个会议对应多条包括保留客户的参会记录，删除第一条非保留客户的参会记录：conferenceid -> {},conscustno -> {}" ,"111","2");
	 //验证更新参会客户号0次
	 Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
 }
 
	 /**
	  * 4.一个会议id对应一个客户且是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条是保留客户
	  */
	@Test
	@SneakyThrows
	public void testFor() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("1");
	   cmConferenceConscust.setActualnub(1);
	   cmConferenceConscust.setActualnubdt(new Date());
	   list.add(cmConferenceConscust);
	   CmConferenceConscust cmConferenceConscust2 = new CmConferenceConscust();
	   cmConferenceConscust2.setConferenceid("222");
	   cmConferenceConscust2.setConscustno("1");
	   list.add(cmConferenceConscust2);
	   CmConferenceConscust cmConferenceConscust3 = new CmConferenceConscust();
	   cmConferenceConscust3.setConferenceid("222");
	   cmConferenceConscust3.setConscustno("2");
	   list.add(cmConferenceConscust3);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       verify(log, times(1)).info("该会议id对应一个客户号为，会议id为：{}","111");
       list.clear();
       list.add(cmConferenceConscust2);
       list.add(cmConferenceConscust3);
       verify(log, times(1)).info("此会议id对应多个客户，且第一个客户为保留客户：conferenceid->{},conscustno->{},list->{}" ,"222",conscustno,JSON.toJSONString(list));
       //验证更新保留客户的到场人数和参会时间0次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
       //验证删除非保留客户1次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(1)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"222","2");
	   //验证更新参会客户号0次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
 
	
	/**
	  * 5.一个会议id对应一个客户且是保留客户         &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
	  */
	@Test
	@SneakyThrows
	public void testFiv() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   Date date = new Date();
	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("1");
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("222");
	   cmConferenceConscust.setConscustno("2");
	   cmConferenceConscust.setActualnub(1);
	   cmConferenceConscust.setActualnubdt(date);
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("222");
	   cmConferenceConscust.setConscustno("1");
	   list.add(cmConferenceConscust);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       verify(log, times(1)).info("该会议id对应一个客户号为，会议id为：{}","111");
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,1,DateTimeUtil.getDateFormat(date, null));
       //验证更新保留客户的到场人数和参会时间1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
       //验证删除非保留客户1次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(1)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("一个会议对应多条包括保留客户的参会记录，删除第一条非保留客户的参会记录：conferenceid -> {},conscustno -> {}" ,"222","2");
	   //验证更新参会客户号0次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	/**
	  * 6.一个会议id对应一个客户且是保留客户     &&    一个会议id对应多个客户且不包含保留客户
	  */
	@Test
	@SneakyThrows
	public void testSix() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   Date date = new Date();
	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("1");
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("222");
	   cmConferenceConscust.setConscustno("2");
	   cmConferenceConscust.setActualnub(1);
	   cmConferenceConscust.setActualnubdt(date);
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("222");
	   cmConferenceConscust.setConscustno("3");
	   list.add(cmConferenceConscust);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       verify(log, times(1)).info("该会议id对应一个客户号为，会议id为：{}","111");
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,1,DateTimeUtil.getDateFormat(date, null));
       //验证删除非保留客户1次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(1)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"222","3");
       verify(log, times(1)).info("一个会议对应多条不包括保留客户的参会记录，加入待更新list：conscustno -> {},updateConscustnoList -> {}" ,"2",JSON.toJSONString(Lists.newArrayList("2")));
       //验证更新保留客户的到场人数和参会时间0次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
	   //验证更新参会客户号1次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	/**
	  * 7.一个会议id对应一个客户且不是保留客户          &&    一个会议id对应多个客户且包含保留客户，且第一条是保留客户
	  */
	@Test
	@SneakyThrows
	public void testSer() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   Date date = new Date();
	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("2");
	   list.add(cmConferenceConscust);
	   CmConferenceConscust cmConferenceConscust2 = new CmConferenceConscust();
	   cmConferenceConscust2.setConferenceid("222");
	   cmConferenceConscust2.setConscustno("1");
	   cmConferenceConscust2.setActualnub(1);
	   cmConferenceConscust2.setActualnubdt(date);
	   list.add(cmConferenceConscust2);
	   CmConferenceConscust cmConferenceConscust3 = new CmConferenceConscust();
	   cmConferenceConscust3.setConferenceid("222");
	   cmConferenceConscust3.setConscustno("2");
	   list.add(cmConferenceConscust3);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
      Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
      verify(log, times(1)).info("该会议id对应一个客户号为，会议id为：{}","111");
      verify(log, times(1)).info("该会议id对应一个客户号，且不为保留客户：conferenceid -> {},conscustno -> {}" ,"111",list.get(0).getConscustno());
      list.clear();
      list.add(cmConferenceConscust2);
      list.add(cmConferenceConscust3);
      verify(log, times(1)).info("此会议id对应多个客户，且第一个客户为保留客户：conferenceid->{},conscustno->{},list->{}" ,"222",conscustno,JSON.toJSONString(list));
      //验证删除非保留客户1次
      Mockito.verify(cmConferenceConscustMapperMock, new Times(1)).deleteConferenceConscust(mapCaptor.capture());
      verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"222","2");
      //验证更新保留客户的到场人数和参会时间0次
      Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
	   //验证更新参会客户号1次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	
	/**
	  * 8.一个会议id对应一个客户且不是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
	  */
	@Test
	@SneakyThrows
	public void testEig() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   Date date = new Date();
	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("111");
	   cmConferenceConscust.setConscustno("2");
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("222");
	   cmConferenceConscust.setConscustno("2");
	   cmConferenceConscust.setActualnub(1);
	   cmConferenceConscust.setActualnubdt(date);
	   list.add(cmConferenceConscust);
	   cmConferenceConscust = new CmConferenceConscust();
	   cmConferenceConscust.setConferenceid("222");
	   cmConferenceConscust.setConscustno("1");
	   list.add(cmConferenceConscust);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
      Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
      verify(log, times(1)).info("该会议id对应一个客户号为，会议id为：{}","111");
      verify(log, times(1)).info("该会议id对应一个客户号，且不为保留客户：conferenceid -> {},conscustno -> {}" ,"111","2");
      verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,1,DateTimeUtil.getDateFormat(date, null));
      //验证更新保留客户的到场人数和参会时间1次
      Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
      //验证删除非保留客户1次
      Mockito.verify(cmConferenceConscustMapperMock, new Times(1)).deleteConferenceConscust(mapCaptor.capture());
      verify(log, times(1)).info("一个会议对应多条包括保留客户的参会记录，删除第一条非保留客户的参会记录：conferenceid -> {},conscustno -> {}" ,"222","2");
	  //验证更新参会客户号1次
	  Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	/**
	  * 9.一个会议id对应多个客户且包含保留客户，且第一条是保留客户         &&    一个会议id对应多个客户且包含保留客户，且第一条是保留客户
	  */
	@Test
	@SneakyThrows
	public void testNin() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust1 = new CmConferenceConscust();
	   cmConferenceConscust1.setConferenceid("111");
	   cmConferenceConscust1.setConscustno("1");
	   list.add(cmConferenceConscust1);
	   CmConferenceConscust cmConferenceConscust2 = new CmConferenceConscust();
	   cmConferenceConscust2.setConferenceid("111");
	   cmConferenceConscust2.setConscustno("2");
	   list.add(cmConferenceConscust2);
	   CmConferenceConscust cmConferenceConscust3 = new CmConferenceConscust();
	   cmConferenceConscust3.setConferenceid("222");
	   cmConferenceConscust3.setConscustno("1");
	   list.add(cmConferenceConscust3);
	   CmConferenceConscust cmConferenceConscust4 = new CmConferenceConscust();
	   cmConferenceConscust4.setConferenceid("222");
	   cmConferenceConscust4.setConscustno("2");
	   list.add(cmConferenceConscust4);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       list.clear();
       list.add(cmConferenceConscust1);
       list.add(cmConferenceConscust2);
       verify(log, times(1)).info("此会议id对应多个客户，且第一个客户为保留客户：conferenceid->{},conscustno->{},list->{}" ,"111",conscustno,JSON.toJSONString(list));
       list.clear();
       list.add(cmConferenceConscust3);
       list.add(cmConferenceConscust4);
       verify(log, times(1)).info("此会议id对应多个客户，且第一个客户为保留客户：conferenceid->{},conscustno->{},list->{}" ,"222",conscustno,JSON.toJSONString(list));
       //验证更新保留客户的到场人数和参会时间0次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
       //验证删除非保留客户2次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(2)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"111","2");
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"222","2");
	   //验证更新参会客户号0次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	/**
	  * 10.一个会议id对应多个客户且包含保留客户，且第一条是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
	  */
	@Test
	@SneakyThrows
	public void testTen() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2").collect(Collectors.toList());
	   Date date = new Date();

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust1 = new CmConferenceConscust();
	   cmConferenceConscust1.setConferenceid("111");
	   cmConferenceConscust1.setConscustno("1");
	   list.add(cmConferenceConscust1);
	   CmConferenceConscust cmConferenceConscust2 = new CmConferenceConscust();
	   cmConferenceConscust2.setConferenceid("111");
	   cmConferenceConscust2.setConscustno("2");
	   list.add(cmConferenceConscust2);
	   CmConferenceConscust cmConferenceConscust3 = new CmConferenceConscust();
	   cmConferenceConscust3.setConferenceid("222");
	   cmConferenceConscust3.setConscustno("2");
	   cmConferenceConscust3.setActualnub(1);
	   cmConferenceConscust3.setActualnubdt(date);
	   list.add(cmConferenceConscust3);
	   CmConferenceConscust cmConferenceConscust4 = new CmConferenceConscust();
	   cmConferenceConscust4.setConferenceid("222");
	   cmConferenceConscust4.setConscustno("1");
	   list.add(cmConferenceConscust4);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       list.clear();
       list.add(cmConferenceConscust1);
       list.add(cmConferenceConscust2);
       verify(log, times(1)).info("此会议id对应多个客户，且第一个客户为保留客户：conferenceid->{},conscustno->{},list->{}" ,"111",conscustno,JSON.toJSONString(list));
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,1,DateTimeUtil.getDateFormat(date, null));
       //验证更新保留客户的到场人数和参会时间1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
       cmConferenceConscust4.setActualnub(1);
	   cmConferenceConscust4.setActualnubdt(date);
       verify(log, times(1)).info("更新保留客户的到场人数和参会时间：{}" ,JSON.toJSONString(cmConferenceConscust4));
       //验证删除非保留客户1次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(2)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"111","2");
       
       verify(log, times(1)).info("一个会议对应多条包括保留客户的参会记录，删除第一条非保留客户的参会记录：conferenceid -> {},conscustno -> {}" ,"222","2");

	   //验证更新参会客户号0次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	
	/**
	  * 11.一个会议id对应多个客户且包含保留客户，且第一条是保留客户        &&    一个会议id对应多个客户且不包含保留客户
	  */
	@Test
	@SneakyThrows
	public void testEle() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2","3","4").collect(Collectors.toList());
	   Date date = new Date();

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust1 = new CmConferenceConscust();
	   cmConferenceConscust1.setConferenceid("111");
	   cmConferenceConscust1.setConscustno("1");
	   list.add(cmConferenceConscust1);
	   CmConferenceConscust cmConferenceConscust2 = new CmConferenceConscust();
	   cmConferenceConscust2.setConferenceid("111");
	   cmConferenceConscust2.setConscustno("2");
	   list.add(cmConferenceConscust2);
	   CmConferenceConscust cmConferenceConscust3 = new CmConferenceConscust();
	   cmConferenceConscust3.setConferenceid("222");
	   cmConferenceConscust3.setConscustno("3");
	   cmConferenceConscust3.setActualnub(1);
	   cmConferenceConscust3.setActualnubdt(date);
	   list.add(cmConferenceConscust3);
	   CmConferenceConscust cmConferenceConscust4 = new CmConferenceConscust();
	   cmConferenceConscust4.setConferenceid("222");
	   cmConferenceConscust4.setConscustno("4");
	   list.add(cmConferenceConscust4);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       list.clear();
       list.add(cmConferenceConscust1);
       list.add(cmConferenceConscust2);
       verify(log, times(1)).info("此会议id对应多个客户，且第一个客户为保留客户：conferenceid->{},conscustno->{},list->{}" ,"111",conscustno,JSON.toJSONString(list));
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,1,DateTimeUtil.getDateFormat(date, null));
       
       //验证更新保留客户的到场人数和参会时间0次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
       
       //验证删除非保留客户2次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(2)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"111","2");
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"222","4");
       
       verify(log, times(1)).info("一个会议对应多条不包括保留客户的参会记录，加入待更新list：conscustno -> {},updateConscustnoList -> {}" ,"3",JSON.toJSONString(Lists.newArrayList("3")));
	   //验证更新参会客户号1次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	
	/**
	  * 12.一个会议id对应多个客户且包含保留客户，且第一条不是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
	  */
	@Test
	@SneakyThrows
	public void testTwe() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2","3").collect(Collectors.toList());
	   Date date = new Date();

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust1 = new CmConferenceConscust();
	   cmConferenceConscust1.setConferenceid("111");
	   cmConferenceConscust1.setConscustno("2");
	   cmConferenceConscust1.setActualnub(1);
	   cmConferenceConscust1.setActualnubdt(date);
	   list.add(cmConferenceConscust1);
	   CmConferenceConscust cmConferenceConscust2 = new CmConferenceConscust();
	   cmConferenceConscust2.setConferenceid("111");
	   cmConferenceConscust2.setConscustno("1");
	   list.add(cmConferenceConscust2);
	   CmConferenceConscust cmConferenceConscust3 = new CmConferenceConscust();
	   cmConferenceConscust3.setConferenceid("222");
	   cmConferenceConscust3.setConscustno("3");
	   cmConferenceConscust3.setActualnub(2);
	   cmConferenceConscust3.setActualnubdt(date);
	   list.add(cmConferenceConscust3);
	   CmConferenceConscust cmConferenceConscust4 = new CmConferenceConscust();
	   cmConferenceConscust4.setConferenceid("222");
	   cmConferenceConscust4.setConscustno("1");
	   list.add(cmConferenceConscust4);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,1,DateTimeUtil.getDateFormat(date, null));
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,2,DateTimeUtil.getDateFormat(date, null));
       //验证更新保留客户的到场人数和参会时间2次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(2)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
       //验证删除非保留客户2次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(2)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("一个会议对应多条包括保留客户的参会记录，删除第一条非保留客户的参会记录：conferenceid -> {},conscustno -> {}" ,"111","2");
       verify(log, times(1)).info("一个会议对应多条包括保留客户的参会记录，删除第一条非保留客户的参会记录：conferenceid -> {},conscustno -> {}" ,"222","3");

	   //验证更新参会客户号0次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(0)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}
	
	
	/**
	  * 13.一个会议id对应多个客户且包含保留客户，且第一条不是保留客户     &&    一个会议id对应多个客户且不包含保留客户
	  */
	@Test
	@SneakyThrows
	public void testThi() {
	   final MergeConscustServiceImpl spy = PowerMockito.spy(serviceMock);
	   mergeConscustes = Stream.of("1", "2","3","4").collect(Collectors.toList());
	   Date date = new Date();

	   //mock客户参会记录列表信息
	   List<CmConferenceConscust> list = new ArrayList<CmConferenceConscust>();
	   CmConferenceConscust cmConferenceConscust1 = new CmConferenceConscust();
	   cmConferenceConscust1.setConferenceid("111");
	   cmConferenceConscust1.setConscustno("2");
	   cmConferenceConscust1.setActualnub(1);
	   cmConferenceConscust1.setActualnubdt(date);
	   list.add(cmConferenceConscust1);
	   CmConferenceConscust cmConferenceConscust2 = new CmConferenceConscust();
	   cmConferenceConscust2.setConferenceid("111");
	   cmConferenceConscust2.setConscustno("1");
	   list.add(cmConferenceConscust2);
	   CmConferenceConscust cmConferenceConscust3 = new CmConferenceConscust();
	   cmConferenceConscust3.setConferenceid("222");
	   cmConferenceConscust3.setConscustno("3");
	   cmConferenceConscust3.setActualnub(2);
	   cmConferenceConscust3.setActualnubdt(date);
	   list.add(cmConferenceConscust3);
	   CmConferenceConscust cmConferenceConscust4 = new CmConferenceConscust();
	   cmConferenceConscust4.setConferenceid("222");
	   cmConferenceConscust4.setConscustno("4");
	   list.add(cmConferenceConscust4);
	   mockListCmConferenceConscust(spy,list);
	   
	   ArgumentCaptor<List<String>> listStrCaptor = ArgumentCaptor.forClass(List.class);
	   ArgumentCaptor<Map<String,String>> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   ArgumentCaptor<CmConferenceConscust> cmConferenceConscustCaptor = ArgumentCaptor.forClass(CmConferenceConscust.class);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(MergeConscustServiceImpl.class, "handleCmConferenceConscust")[0], spy,mergeConscustes, conscustno, userid);

	   //验证客户参会记录列表1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).listCmConferenceConscustByConscustnoList(listStrCaptor.capture());
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,1,DateTimeUtil.getDateFormat(date, null));
       verify(log, times(1)).info("待更新的最大的实际到场人数和参会时间：actualnub->{},actualnubdt->{}" ,2,DateTimeUtil.getDateFormat(date, null));
       //验证更新保留客户的到场人数和参会时间1次
       Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscust(cmConferenceConscustCaptor.capture());
       //验证删除非保留客户2次
       Mockito.verify(cmConferenceConscustMapperMock, new Times(2)).deleteConferenceConscust(mapCaptor.capture());
       verify(log, times(1)).info("删除被合并的客户参会会议记录：conferenceid -> {},conscustno -> {}" ,"222","4");
       verify(log, times(1)).info("一个会议对应多条包括保留客户的参会记录，删除第一条非保留客户的参会记录：conferenceid -> {},conscustno -> {}" ,"111","2");
       verify(log, times(1)).info("一个会议对应多条不包括保留客户的参会记录，加入待更新list：conscustno -> {},updateConscustnoList -> {}" ,"3",JSON.toJSONString(Lists.newArrayList("3")));
	   //验证更新参会客户号1次
	   Mockito.verify(cmConferenceConscustServiceMock, new Times(1)).updateCmConferenceConscustByConcustnos(cmConferenceConscustCaptor.capture());
	}

}