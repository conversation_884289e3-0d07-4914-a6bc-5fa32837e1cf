package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.ConscustIc;
import com.howbuy.crm.hb.domain.custinfo.ConscustOperation;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ConscustOperationMapper {

	/**
	 * 单条修改数据对象
	 * @param conscustoperation
	 */
	void updateConscustOperation(ConscustOperation conscustoperation);

	/**
	 * 删除
	 * @param operationid
	 */
	void delConscustOperation(String operationid);
}
