package com.howbuy.crm.hb.domain.prosale;

import com.howbuy.crm.hb.domain.insur.PageVo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * @Description: 销控-->待关联 预约单 查询vo
 * <AUTHOR> @version 1.0
 * @created 
 */
public class CmPreForMatchVo extends  PageVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 客户名称
	 */
	private String custName;

	/**
	 * 预计交易日期-开始
	 */
	private String expectTradeBeginDt;


	/**
	 * 预计交易日期-结束
	 */
	private String expectTradeEndDt;

	/**
	 *录入起始时间
	 */
	private String creBeginDt;

	/**
	 *录入结束时间
	 */
	private String creEndDt;

	/**
	 * 一账通账号
	 */
	private String hboneNo;

	/**
	 * 预约单Id
	 */
	private BigDecimal preId;

	/**
	 * 产品代码
	 */
	private String prodCode;

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getExpectTradeBeginDt() {
		return expectTradeBeginDt;
	}

	public void setExpectTradeBeginDt(String expectTradeBeginDt) {
		this.expectTradeBeginDt = expectTradeBeginDt;
	}

	public String getExpectTradeEndDt() {
		return expectTradeEndDt;
	}

	public void setExpectTradeEndDt(String expectTradeEndDt) {
		this.expectTradeEndDt = expectTradeEndDt;
	}

	public String getCreBeginDt() {
		return creBeginDt;
	}

	public void setCreBeginDt(String creBeginDt) {
		this.creBeginDt = creBeginDt;
	}

	public String getCreEndDt() {
		return creEndDt;
	}

	public void setCreEndDt(String creEndDt) {
		this.creEndDt = creEndDt;
	}

	public String getHboneNo() {
		return hboneNo;
	}

	public void setHboneNo(String hboneNo) {
		this.hboneNo = hboneNo;
	}

	public BigDecimal getPreId() {
		return preId;
	}

	public void setPreId(BigDecimal preId) {
		this.preId = preId;
	}

	public String getProdCode() {
		return prodCode;
	}

	public void setProdCode(String prodCode) {
		this.prodCode = prodCode;
	}
}
