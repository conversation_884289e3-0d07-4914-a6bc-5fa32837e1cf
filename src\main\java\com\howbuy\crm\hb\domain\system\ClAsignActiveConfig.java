package com.howbuy.crm.hb.domain.system;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

import com.howbuy.crm.hb.domain.insur.PageVo;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class ClAsignActiveConfig extends PageVo implements Serializable  {
	private static final long serialVersionUID = 1L;
	private String id;
	/**
	 * 配置类型
	 */
	private String type;
	private String typeval;
	/**
	 * 是否生效1是0否
	 */
	private String validflag;
	/**
	 * 管理层
	 */
	private String manager  ;
	private String managerName  ;
	private String manageOrgCode;
	
	private String oldmanager  ;
	private String oldmanagerName  ;
	private String oldmanageOrgCode;
	  
	/**
	 * 投顾
	 */
	private String conscode     ;
	private String consName     ;
	private String consOrgCode;
	
	private String oldconscode     ;
	private String oldconsName     ;
	private String oldconsOrgCode;
	/**
	 * 客户
	 */
	private String custnos  ;
	
	private String conscustno  ;
	  
	/**
	 * 激活类型
	 */
	private String activetype     ;
	/**
	 * 是否激活
	 */
	private String activeflag  ;
	  
	/**
	 * 激活日期
	 */
	private String activedt     ;
	private String activestartdt     ;
	private String activeenddt     ;
	
	/**
	 * 计算起始日期
	 */
	private String startdt;
	
	/**
	 * 计算结束日期
	 */
	private String enddt;
	/**
	 * 存量记录比例
	 */
	private BigDecimal calrate;
	
	private BigDecimal calratetwo;
	/**
	 * 备注
	 */
	private String remark;
	
	/**
	 * 新增时符合条件的客户
	 */
	private String allcustnos;
	
	private String creator;
    private Date creatdt;
    private String modor;
    private Date moddt;
    
    private String  sort;

	private String  order;

}
