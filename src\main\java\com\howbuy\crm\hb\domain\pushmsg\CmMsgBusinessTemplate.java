package com.howbuy.crm.hb.domain.pushmsg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 消息管理-业务模板
 * @reason:
 * @Date: 2021/1/14 10:34
 */
@Data
public class CmMsgBusinessTemplate implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 消息类型id
     */
    private String msgTypeId;

    /**
     * 消息子类
     */
    private String msgTypeName;

    /**
     * 消息主类
     */
    private String masterName;

    /**
     * 是否配置pc模板
     */
    private String isPcTemplate;

    /**
     * pc是否弹窗
     */
    private String pcIsPopup;

    /**
     * pc模板内容
     */
    private String pcTemplateText;

    /**
     * pc模板跳转链接
     */
    private String pcUrl;

    /**
     * 是否配置微信模板
     */
    private String isWeixinTemplate;

    /**
     * 微信模板id
     */
    private String weixinTemplateId;

    /**
     * 微信模板开头语
     */
    private String weixinStartText;

    /**
     * 微信模板结束语
     */
    private String weixinEndText;

    /**
     * 微信模板跳转链接
     */
    private String weixinUrl;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updateMan;

    /**
     * 是否删除
     */
    private String isDel;

    /**
     * 参数
     */
    private List<CmMsgBusinessTemplateParam> businessTemplateParams;

    /**
     * 微信模板标题
     */
    private String title;

    /**
     * 消息主类id
     */
    private String masterId;
}
