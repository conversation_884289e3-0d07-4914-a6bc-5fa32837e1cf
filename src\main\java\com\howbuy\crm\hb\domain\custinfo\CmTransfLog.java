package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmTransfLog implements Serializable {
    private static final long serialVersionUID = 1L;

    private BigDecimal id;
    private String appdt;
    private String appconscode;
    private String conscustno;
    private String oldconscode;
    /**
     * 划转原因
     */
    private String transfcause;
    private String opstatus;
    private String optype;
    /**
     * 客户状态：0-潜在客户；1-成交客户
     */
    private String custstatus;
    private String memo;
    /**
     * 处理后客户成交状态
     */
    private String transstatus;
    private Date createtime;
    private Date updatetime;
    private String creator;
    private String modifier;
    /**
     * 有效状态
     */
    private String isdel;
    private String appuporgname;
    private String apporgname;
    private String olduporgname;
    private String oldorgname;
    private String appconsname;
    private String oldconsname;
    private String custname;
    /**
     * 操作表外键ID
     */
    private String operationid;
    private String mobile;
    private String mobileMask;
    /** 不符合条件 */
    private String miscondition;
    /**
     * 原投顾是否分总
     */
    private String oldconsCodeIsFz;
    /**
     * 申请中心
     */
    private String appupcentername;
    /**
     * 原中心
     */
    private String oldupcentername;


}
