package com.howbuy.crm.hb.domain.system;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmHmcBlacklist implements Serializable  {
	private static final long serialVersionUID = 1L;
	private String id;
	private String ids;
	private String conscode;
	private String consname;
	private String creator;
    private Date credate;
    private String modifier;
    private Date moddate;
    private String stat;
    /** 所属区域 */
    private String uporgname;
    /** 所属部门 */
    private String orgname;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getConscode() {
		return conscode;
	}
	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public Date getCredate() {
		return credate;
	}
	public void setCredate(Date credate) {
		this.credate = credate;
	}
	public String getModifier() {
		return modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	public Date getModdate() {
		return moddate;
	}
	public void setModdate(Date moddate) {
		this.moddate = moddate;
	}
	public String getStat() {
		return stat;
	}
	public void setStat(String stat) {
		this.stat = stat;
	}
	public String getUporgname() {
		return uporgname;
	}
	public void setUporgname(String uporgname) {
		this.uporgname = uporgname;
	}
	public String getOrgname() {
		return orgname;
	}
	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}
	public String getIds() {
		return ids;
	}
	public void setIds(String ids) {
		this.ids = ids;
	}
	public String getConsname() {
		return consname;
	}
	public void setConsname(String consname) {
		this.consname = consname;
	}
	
}
