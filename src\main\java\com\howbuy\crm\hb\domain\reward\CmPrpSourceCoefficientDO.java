package com.howbuy.crm.hb.domain.reward;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 配置来源系数
 * <AUTHOR>
 */
@Data
public class CmPrpSourceCoefficientDO implements Serializable {
    /**
     * 主键id
     */
    private BigDecimal id;

    /**
     * 来源系数起点(0.2、1)
     */
    private String startPoint;

    /**
     * 交易次数(1、2、3、4、5次及以上)
     */
    private String tradeNum;

    /**
     * 来源系数
     */
    private BigDecimal sourceCoeff;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modor;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}