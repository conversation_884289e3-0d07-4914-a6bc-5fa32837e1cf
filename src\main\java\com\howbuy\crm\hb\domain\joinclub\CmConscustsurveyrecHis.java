package com.howbuy.crm.hb.domain.joinclub;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 实体类CmConscustsurveyrecHis.java
 * <AUTHOR>
 */
@Data
public class CmConscustsurveyrecHis implements Serializable {

private static final long serialVersionUID = 1L;

	private String appserialno;
	
	private String tradedt;
	
	private String appcode;
	
	private String txcode;
	
	private String txappflag;
	
	private String txchkflag;

	private String tradechan;
	
	private String regioncode;
	
	private String outletcode;
	
	private String appdt;
	
	private String apptm;
	
	private String conscustno;
	
	private String conscustname;
	
	private String surveyid;
	
	private String setid;
	
	private Double totalwp;
	
	private String pririsklevel;
	
	private String suggestionid;
	
	private String memo;
	
	private String retcode;
	
	private String retmsg;
	
	private String creator;
	
	private String checker;
	
	private Date stimestamp;
	
	private String gpsinvestlevel;
	
	private String gpsrisklevel;
	
	private String checkdt;

	private String maxcheckdt;
	
	private String checktm;
	
	private String custname;
	
	private String mobile;

	private String ip;
	
	private String idno;
	
	private String inverstamt;
	
	private String inverstterm;
	
	private String inverstaim;

	private String dataflag;
	
	private String checkadvice;

	private String singdate;

}
