package com.howbuy.crm.hb.domain.callout;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 实体类CsCommunicateVisit.java
 * <AUTHOR>
 * @version 1.0
 */
public class CsCommunicateVisit implements Serializable {

	private static final long serialVersionUID = 1L;

	private String   id;

	private String conscustNo;

	private String consultType;

	private String commIntent;

	private String investIntent;

	private String amountFlag;

	private String specialMark;

	private String deptFlag;

	private String commContent;

	private String taskId;

	private String bookingContent;

	private String callInId;

	private String visitType;

	private String nextDt;

	private String consBookingId;

	private String nextVisitContent;

	private String nextStartTime;

	private String nextEndTime;

	private String nextVisitType;

	private String visitClassify;

	private String remark;

	private String modifyFlag;

	private String creator;

	private Date creDt;

	private String modifier;

	private Date modDt;

	private Date stimeStamp;

	private String hisFlag;

	private String hisId;
	
	private List<String> conscustnoList;

	public String getId() {
		return this.id;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getConsultType() {
		return consultType;
	}

	public void setConsultType(String consultType) {
		this.consultType = consultType;
	}

	public String getCommIntent() {
		return commIntent;
	}

	public void setCommIntent(String commIntent) {
		this.commIntent = commIntent;
	}

	public String getInvestIntent() {
		return investIntent;
	}

	public void setInvestIntent(String investIntent) {
		this.investIntent = investIntent;
	}

	public String getAmountFlag() {
		return amountFlag;
	}

	public void setAmountFlag(String amountFlag) {
		this.amountFlag = amountFlag;
	}

	public String getSpecialMark() {
		return specialMark;
	}

	public void setSpecialMark(String specialMark) {
		this.specialMark = specialMark;
	}

	public String getDeptFlag() {
		return deptFlag;
	}

	public void setDeptFlag(String deptFlag) {
		this.deptFlag = deptFlag;
	}

	public String getCommContent() {
		return commContent;
	}

	public void setCommContent(String commContent) {
		this.commContent = commContent;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getBookingContent() {
		return bookingContent;
	}

	public void setBookingContent(String bookingContent) {
		this.bookingContent = bookingContent;
	}

	public String getCallInId() {
		return callInId;
	}

	public void setCallInId(String callInId) {
		this.callInId = callInId;
	}

	public String getVisitType() {
		return visitType;
	}

	public void setVisitType(String visitType) {
		this.visitType = visitType;
	}

	public String getNextDt() {
		return nextDt;
	}

	public void setNextDt(String nextDt) {
		this.nextDt = nextDt;
	}

	public String getConsBookingId() {
		return consBookingId;
	}

	public void setConsBookingId(String consBookingId) {
		this.consBookingId = consBookingId;
	}

	public String getNextVisitContent() {
		return nextVisitContent;
	}

	public void setNextVisitContent(String nextVisitContent) {
		this.nextVisitContent = nextVisitContent;
	}

	public String getNextStartTime() {
		return nextStartTime;
	}

	public void setNextStartTime(String nextStartTime) {
		this.nextStartTime = nextStartTime;
	}

	public String getNextEndTime() {
		return nextEndTime;
	}

	public void setNextEndTime(String nextEndTime) {
		this.nextEndTime = nextEndTime;
	}

	public String getNextVisitType() {
		return nextVisitType;
	}

	public void setNextVisitType(String nextVisitType) {
		this.nextVisitType = nextVisitType;
	}

	public String getVisitClassify() {
		return visitClassify;
	}

	public void setVisitClassify(String visitClassify) {
		this.visitClassify = visitClassify;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getModifyFlag() {
		return modifyFlag;
	}

	public void setModifyFlag(String modifyFlag) {
		this.modifyFlag = modifyFlag;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreDt() {
		return creDt;
	}

	public void setCreDt(Date creDt) {
		this.creDt = creDt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public Date getModDt() {
		return modDt;
	}

	public void setModDt(Date modDt) {
		this.modDt = modDt;
	}

	public Date getStimeStamp() {
		return stimeStamp;
	}

	public void setStimeStamp(Date stimeStamp) {
		this.stimeStamp = stimeStamp;
	}

	public String getHisFlag() {
		return hisFlag;
	}

	public void setHisFlag(String hisFlag) {
		this.hisFlag = hisFlag;
	}

	public String getHisId() {
		return hisId;
	}

	public void setHisId(String hisId) {
		this.hisId = hisId;
	}

	public void setId(String id) {
		this.id = id;
	}

	public List<String> getConscustnoList() {
		return conscustnoList;
	}

	public void setConscustnoList(List<String> conscustnoList) {
		this.conscustnoList = conscustnoList;
	}

}
