package com.howbuy.crm.hb.persistence.fixed;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.fixed.CmFixedAnnex;


/**
 * 
 * <AUTHOR>
 *
 */
public interface CmFixedAnnexMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmFixedAnnex getCmFixedAnnex(Map<String, Object> param);
    
     /**
      * 新增数据对象
      * @param CmFixedAnnex
      */
	void insertCmFixedAnnex(CmFixedAnnex CmFixedAnnex);
	
	/**
	 * 单条修改数据对象
	 * @param CmFixedAnnex
	 */
	void updateCmFixedAnnex(CmFixedAnnex CmFixedAnnex);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmFixedAnnex> listCmFixedAnnex(Map<String, Object> param);
	
	/**
	 * 查询已经上传附件的文件类型
	 * @param param
	 * @return
	 */
	List<CmFixedAnnex> listHasUpFileTypeByPreid(Map<String, Object> param);
}
