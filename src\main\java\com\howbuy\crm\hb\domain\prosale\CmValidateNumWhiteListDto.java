package com.howbuy.crm.hb.domain.prosale;

import com.google.common.collect.Lists;
import com.howbuy.crm.base.PreBookTradeTypeEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.prosale.dto.CmPreSaleForControlCalInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *销控--校验白名单-校验人数-dto
 * <AUTHOR>
 * @time 2022-1-7 13:46:31
 */
@Data
public class CmValidateNumWhiteListDto implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 校验数据使用的 额度控制
	 */
	private Integer usedLimit;

	/**
	 * 追加的数据  也就是 待验证的数据
	 */
	List<CmPreForWhiteValidateDto> validatePreList= Lists.newArrayList();

	/**
	 * 当前统计内的 数据
	 */
	List<CmPreSaleForControlCalInfo>  calculatePreList=Lists.newArrayList();


	public CmValidateNumWhiteListDto() {
	}

	/**
	 *
	 * @param usedLimit 金额-限额
	 * @param validatePreList  追加的数据  也就是 待验证的数据
	 * @param calculatePreList 当前统计内的 数据
	 */
	public CmValidateNumWhiteListDto(Integer usedLimit, List<CmPreForWhiteValidateDto> validatePreList, List<CmPreSaleForControlCalInfo> calculatePreList) {
		this.usedLimit = usedLimit;
		this.validatePreList = CollectionUtils.isEmpty(validatePreList)?Lists.newArrayList():validatePreList;
		this.calculatePreList = CollectionUtils.isEmpty(calculatePreList)?Lists.newArrayList():calculatePreList;
	}

	/**
	 * 验证金额
	 * @return
	 */
	public ReturnMessageDto<String> validate(){
		//本期已预约人数+本次预约人数（去重，追加不算）是否达到预约控制参数“预约人数达本期额度”
		if(usedLimit==null){
			return ReturnMessageDto.ok();
		}
		if(CollectionUtils.isEmpty(getValidatePreList())){
			return ReturnMessageDto.ok();
		}

		//统计内已有 客户列表
		Set<String> calculateCustNoSet= getCalculatePreList().stream()
				//只有申购才计入人数控制
				.filter(CmPreSaleForControlCalInfo::isNumCalculate) 
				.map(CmPreSaleForControlCalInfo::getConcustNo)
				.collect(Collectors.toSet());

		//追加 客户列表
		Set<String> appCustNoSet= getValidatePreList().stream()
				//只有申购才计入人数控制
				.filter(CmPreForWhiteValidateDto::isNumCalculate) 
				//已在统计内。不再计入
				.filter(preInfo-> !calculateCustNoSet.contains(preInfo.getConcustNo())) 
				.map(CmPreForWhiteValidateDto::getConcustNo)
				.collect(Collectors.toSet());

		int combineNum=calculateCustNoSet.size()+appCustNoSet.size();
		if(combineNum>usedLimit){
			return ReturnMessageDto.fail("添加后白名单内的预约人数超过本期总额度，请重新选择!");
		}
		return ReturnMessageDto.ok();
	}


}
