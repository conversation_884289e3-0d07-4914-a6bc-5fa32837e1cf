package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class SubCrmBalanceBean implements Serializable {

    /**
     * 分销代码
     */
    private String disCode;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 份额类型
     */
    private String fundShareClass;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 基金公司名称
     */
    private String company;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 总份额
     */
    private BigDecimal balanceVol;
    /**
     * 待确认份额
     */
    private BigDecimal unconfirmedVol;
    /**
     * 待确认金额
     */
    private BigDecimal unconfirmedAmt;
    /**
     * 可用份额
     */
    private BigDecimal availVol;
    /**
     * 市值
     */
    private BigDecimal marketValue;
    /**
     * 当前币种的市值
     */
    private BigDecimal currencyMarketValue;
    /**
     * 净值
     */
    private BigDecimal nav;
    /**
     * 产品状态
     */
    private String productStatus;
    /**
     * 净值日期
     */
    private String navDt;
    /**
     * 购买状态：1-认购，2-申购，3-不可购买
     */
    private String buyStatus;
    /**
     * 赎回状态 1-可赎回，2-不可赎回
     */
    private String redeemStatus;
    /**
     * 销售类型: 1-直销;2-代销
     */
    private String scaleType;

    /**
     * 持仓收益(人民币）
     */
    private BigDecimal currentAsset;

    /**
     * 持仓总成本(人民币）
     */
    private BigDecimal balanceCost; 

    /**
     * 日收益(人民币）
     */
    private BigDecimal dailyAsset;

    /**
     * 持仓收益（当前币种）
     */
    private BigDecimal currentAssetCurrency;

    /**
     * 持仓总成本（当前币种）
     */
    private BigDecimal balanceCostCurrency;

    /**
     * 日收益（当前币种）
     */
    private BigDecimal dailyAssetCurrency;

    /**
     * 认缴金额
     */
    private BigDecimal paidInAmt;

    /**
     * 产品存续期限(类似于5+3+2这种说明)
     */
    private String fundCXQXStr;
    /**
     * 子产品代码
     */
    private String subProductCode;
    /**
     * 结构化基金标识: 0-非结构化; 1-结构化
     */
    private String structFlag;
    /**
     * 币种
     */
    private String currency;
    /**
     * 收益日期
     */
    private String incomeDt;
    /**
     * 0-计算中；1-计算完成
     */
    private String incomeCalStat;
    /**
     * 累计已实现收益
     */
    private BigDecimal accumRealizedIncome;
    /**
     * 累计已实现收益人民币
     */
    private BigDecimal accumRealizedIncomeRmb;
    /**
     * 累计净购买金额
     */
    private BigDecimal netBuyAmount;

    /**
     * 私募股权回款
     */
    private BigDecimal cashCollection;
    /**
     * 好买产品线
     */
    private String hmcpx;

    /** 到期日期 */
    private String duedt;
    
    /** 起息日 */
	private String valueDate;
	
	/** 到期日 */
	private String dueDate;
	
	/** 业绩比较基准 */
	private String standardFixedIncomeFlag;
	
	/** 业绩比较基准 */
	private String benchmark;
	
	/** 业绩比较基准 */
	private String investmentHorizon;

    private String naProductFeeType;
    private BigDecimal receivManageFee;
    private BigDecimal receivPreformFee;
    private BigDecimal currencyMarketValueExFee;
    private BigDecimal marketValueExFee;

    private BigDecimal balanceIncomeNew;
    private BigDecimal balanceIncomeNewRmb;
    private BigDecimal accumIncomeNew;
    private BigDecimal accumIncomeNewRmb;
    private BigDecimal currencyNetBuyAmount;
    private BigDecimal currencyCashCollection;
    
}
