package com.howbuy.crm.hb.enums;
/**
 * 持仓日序图展示数据类型枚举类
 * <AUTHOR>
 *
 */
public enum ChartTypeEnum {

    /**
	 * 持仓份额
	 */
	BALANCE_VOL("balanceVol", "持仓份额", "份"),
	/**
	 * 持仓收益
	 */
	CURRENT_ASSET("currentAsset", "持仓收益", "元"),
	/**
	 * 持仓收益率
	 */
	CURRENT_INCOME_RATE("currentIncomeRate", "持仓收益率", "%"),
	/**
	 * 持仓总成本
	 */
	BALANCE_COST("balanceCost", "持仓总成本", "元"),
	/**
	 * 日收益
	 */
	DAILY_ASSET("dailyAsset", "最新收益", "元"),
	/**
	 * 累计收益
	 */
	ACCUM_INCOME("accumIncome", "累计收益", "元"),
	/**
	 * 累计收益率
	 */
	TOTAL_INCOME_RATE("totalIncomeRate", "累计收益率", "%"),
	/**
	 * 累计已实现收益
	 */
	ACCUM_REALIZED_INCOME("accumRealizedIncome", "累计已实现收益", "元"),
	/**
	 * 累计成本
	 */
	ACCUM_COST("accumCost", "累计成本", "元"),
	/**
	 * 参考市值
	 */
	BALANCE_AMT("balanceAmt", "参考市值", "元"),
	/**
	 * 持仓浮盈亏
	 */
	BALANCE_FLOAT_INCOME("balanceFloatIncome", "持仓浮盈亏", "元"),
	/**
	 * 持仓浮盈亏比率
	 */
	BALANCE_FLOAT_INCOME_RATE("balanceFloatIncomeRate", "持仓浮盈亏比率", "%"),
	/**
	 * 最新收益率
	 */
	DAY_ASSET_RATE("dayAssetRate", "最新收益率", "%"),
	/**
	 * 最新资产增长率
	 */
	DAY_INCOME_GROWTH_RATE("dayIncomeGrowthRate", "最新资产增长率", "%"),
	/**
	 * 投资总成本——新
	 */
	ACCUM_COST_NEW("accumCostNew", "投资总成本", "元"),
	/**
	 * 累计收益——新
	 */
	ACCUM_INCOME_NEW("accumIncomeNew", "累计收益", "元"),
	/**
	 * 持仓收益——新
	 */
	BALANCE_INCOME_NEW("balanceIncomeNew", "持仓收益_新", "元"),
	/**
	 * 持仓收益——新
	 */
	BALANCE_PAIDINAMT_NEW("accumPaidinAmount", "总实缴金额", "元"),
	/**
	 * 待投金额——新
	 */
	BALANCE_UNPAIDINAMT_NEW("investedAmount", "待投金额", "元"),
	/**
	 * 累计总回款
	 */
	ACCUM_COLLECTION("accumCollection", "累计总回款", "元"),
	/**
	 * 平衡因子
	 */
	BALANCE_FACTOR("balanceFactor", "平衡因子", "元"),
	/**
	 * 累计应收管理费
	 */
	RECEIV_MANAGE_FEE("receivManageFee", "累计应收管理费", "元"),
	/**
	 * 累计应收业绩报酬
	 */
	RECEIV_PREFORM_FEE("receivPreformFee", "累计应收业绩报酬", "元"),
	/**
	 * 单位持仓成本
	 */
	UNIT_BALANCE_COST_EX_FEE("unitBalanceCostExFee", "单位持仓成本", "元"),
	/**
	 * NA费前市值
	 */
	NA_BALANCE_AMT("NABalanceAmt", "NA费前市值", "元");

	/**
	 * 编码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String description;
	/**
	 * 单位
	 */
	private String unit;

	private ChartTypeEnum(String code, String description, String unit) {
		this.code = code;
		this.description = description;
		this.unit = unit;
	}

	/**
	 * 通过code获得字段描述
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		ChartTypeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code获得字段单位
	 *
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getUnit(String code) {
		ChartTypeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getUnit();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return ChartTypeEnum
	 */
	public static ChartTypeEnum getEnum(String code) {
		for(ChartTypeEnum statusEnum : ChartTypeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}
}
