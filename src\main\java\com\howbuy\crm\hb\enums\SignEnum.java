package com.howbuy.crm.hb.enums;

/**
 * @description:(签约状态枚举)
 * @return
 * @author: xufan<PERSON><PERSON>
 * @date: 2023/8/21 15:14
 * @since JDK 1.8
 */
public enum SignEnum {
    IS_SIGN("1", "已签约"),
    NOT_SIGN("0", "未签约");


    private String code;
    private String description;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    SignEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getSignDescription(String code) {
        for(SignEnum signEnum : SignEnum.values()){
            if(signEnum.getCode().equals(code)){
                return signEnum.getDescription();
            }
        }
        return null;
    }
}
