package com.howbuy.crm.hb.domain.prosale;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Description: 销控-->待关联 客户存入的 预约单列表
 * <AUTHOR>
 * @version 1.0
 * @created  2021-11-1 15:44:03
 */
@Data
public class CmPreForMatchInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/***************************prebookInfo信息  begin **************************************/
	/**
	 * 预约Id
	 */
	private BigDecimal preId;


	/**
	 * 是否已关联
	 */
	private boolean matched;

	/**
	 * 预计交易日期
	 */
	private String expectTradeDt;

	/**
	 * 一账通账号
	 */
	private String hboneNo;


	/**
	 * 产品代码
	 */
	private String prodCode;

	/**
	 * 产品名称
	 */
	private String prodName;

	/**
	 * 客户号
	 */
	private String consCustNo;
	/**
	 * 客户名称
	 */
	private String custName;
	/**
	 * 投顾编号
	 */
	private String consCode;

	/**
	 * 投顾-名称
	 */
	private String consName;

	/**
	 * 所属部门-code
	 */
	private String orgCode;

	/**
	 * 所属部门-名称
	 */
	private String orgName;


	/**
	 * 预约金额
	 */
	private BigDecimal buyAmt;

	/**
	 * 打款金额
	 */
	private BigDecimal realPayAmt;



	/**
	 * 占位状态：0-无需占位，1-未占位，2-已占位
	 */
	private String  occupyStatus;

	/**
	 * 占位类型：0-网银匹配占位，1-手工标记到账，2-人工预留
	 */
	private String  occupyType;

	/**
	 * 产品类型
	 */
	private String fundTypeStr;
}
