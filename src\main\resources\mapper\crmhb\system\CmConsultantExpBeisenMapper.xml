<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpBeisenMapper">

	<select id="queryAllCmBeisenOrgData" resultType="com.howbuy.crm.hb.domain.beisen.CmBeisenOrgDO">
		SELECT t.ORG_ID_BEISEN orgIdBeisen, t.ORG_NAME_BEISEN orgNameBeisen FROM CM_BEISEN_ORG t
	</select>

	<select id="queryAllCmBeisenOrgConfigData" resultType="com.howbuy.crm.hb.domain.beisen.CmBeisenOrgConfigPO">
		select t.ORG_ID_BEISEN orgId<PERSON><PERSON><PERSON>, t.ORG_NAME_BEISEN orgNameB<PERSON>sen, t.ORG_CODE orgCode, t.CENTER_ORG centerOrg
		from cm_beisen_org_config t
		WHERE (t.ORG_ID_BEISEN, t.START_DATE) IN (SELECT ORG_ID_BEISEN, max(START_DATE) START_DATE FROM cm_beisen_org_config GROUP BY ORG_ID_BEISEN)
	</select>

	<select id="queryCmBeisenPosLevelConfigData" resultType="com.howbuy.crm.hb.domain.beisen.CmBeisenPosLevelConfigPO">
		select t.POSITIONS_LEVEL_BEISEN positionsLevelBeisen, t.POSITIONS_LEVEL_NAME_BEISEN positionsLevelNameBeisen, t.USER_LEVEL_CRM userLevelCrm,
			   t.POSITIONS_LEVEL_CRM positionsLevelCrm, t.SUB_POSITIONS_LEVEL_CRM subPositionsLevelCrm, t.START_DATE startDate
		from CM_BEISEN_POS_LEVEL_CONFIG t
		WHERE (t.POSITIONS_LEVEL_BEISEN,t.START_DATE)
				  IN (SELECT POSITIONS_LEVEL_BEISEN, max(START_DATE) START_DATE FROM CM_BEISEN_POS_LEVEL_CONFIG GROUP BY POSITIONS_LEVEL_BEISEN)
	</select>

	<select id="queryCmBeisenUserInfoData" resultType="com.howbuy.crm.hb.domain.beisen.CmBeisenUserInfoPO">
		SELECT EXT2,EXT3,EXT4,EXT5,EXT6,EXT7,EXT8,EXT9,EXT10
			 ,EXT11,EXT12,EXT13,EXT14,EXT15,EXT16,EXT17,EXT18
			 ,EXT19,EXT20,EXT21,EXT22,EXT23,EXT24,EXT25,EXT26
			 ,EXT27,EXT28,EXT29,EXT30,EXT31,EXT32,EXT33,EXT34,EXT35
		FROM cm_beisen_userinfo t
		where 1=1
		<if test="startDate != null and startDate != ''">
			and t.ext35 >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			and t.ext35 &lt;= #{endDate}
		</if>
	</select>
</mapper>