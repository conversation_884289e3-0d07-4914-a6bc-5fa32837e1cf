package com.howbuy.crm.hb.service.prosale;

import com.howbuy.crm.base.PreBookArchTypeEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.domain.manage.UpLoadPrivateTrade;
import com.howbuy.crm.hb.service.prosale.impl.CustprivatefundServiceImpl;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.privatetrade.dto.CmCustprivatefund;
import com.howbuy.crm.privatetrade.service.CmCustFundBalanceService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.math.BigDecimal;


/**
 * 单元测试CustprivatefundServiceImpl.checktrade检查导入交易是否符合条件
 * <AUTHOR>
 * 20220620
 */
@PowerMockIgnore("javax.management.*")
@Test
public class CustprivatefundServiceImplTest extends PowerMockTestCase {

    @InjectMocks
    private CustprivatefundServiceImpl serviceMock;

    @Mock
    private CmCustFundBalanceService cmCustFundBalanceServiceMock;

    @Mock
    private PrebookBasicInfoService prebookBasicInfoServiceMock;
    
    //客户号
    private static String TEST_CONSCUSTNO="TEST_CONSCUSTNO";
    //产品号
    private static String TEST_FUNDCODE="TEST_FUNDCODE";
    
    private static BigDecimal TEST_ZORO=BigDecimal.ZERO;
    
    private static BigDecimal TEST_FIVE = new BigDecimal("5");
    
    private static BigDecimal TEST_TEN=BigDecimal.TEN;
    
    private static String TEST_TRADETYPE ="TEST_TRADETYPE";
    
    /**
     * 强行调增
     */
    private static String QXTZ = "5";
    
    /**
     * 强行调减
     */
    private static String DUMP = "3";
    
    /**
     * 强制赎回
     */
    private static String QZSH = "6";
    
    

    /**
     * 测试_没有持仓且交易类型是强增情况
     */
    @Test
    public void testQzNotfund() {
    	CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
    	UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
    	trade.setConscustno(TEST_CONSCUSTNO);
    	trade.setFundcode(TEST_FUNDCODE);
    	//传入强行调整情况
    	trade.setTradetype(QXTZ);
    	//mock根据产品和客户号没有查到持仓的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(null);
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
    	//执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertTrue(result.getReturnCode().equals(ReturnMessageDto.ok().getReturnCode()));
    	
    }

    /**
     * mock  海外产品拦截
     */
    @Test
    public void testHwfund() {
        CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
        UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
        trade.setConscustno(TEST_CONSCUSTNO);
        trade.setFundcode(TEST_FUNDCODE);
        //传入强行调整情况
        trade.setTradetype(QXTZ);
        //mock根据产品和客户号没有查到持仓的情况
//        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(null);
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.HW.getCode());
        //执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertFalse(result.isSuccess());

    }
    
    /**
     * 测试_没有持仓且交易类型是非强增情况
     */
    @Test
    public void testNotQzNotfund() {
    	CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
    	UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
    	trade.setConscustno(TEST_CONSCUSTNO);
    	trade.setFundcode(TEST_FUNDCODE);
    	//传入非强行调整情况
    	trade.setTradetype(TEST_TRADETYPE);
    	//mock根据产品和客户号没有查到持仓的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(null);
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
    	//执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertTrue("客户不持有产品".equals(result.getReturnMsg()));
    }


    /**
     * mock 持仓对象
     * @param balanceVol
     * @return
     */
    private CmCustprivatefund getMockFund(BigDecimal balanceVol){
        CmCustprivatefund fund = new CmCustprivatefund();
        fund.setBalancevol(balanceVol);
        return  fund;
    }
    
    /**
     * 测试_有持仓且非强增且持仓份额小于等于1情况
     */
    @Test
    public void testNotQzVolLtOne() {
    	CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
    	UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
    	trade.setConscustno(TEST_CONSCUSTNO);
    	trade.setFundcode(TEST_FUNDCODE);
    	//传入非强行调整情况
    	trade.setTradetype(TEST_TRADETYPE);
    	//mock根据产品和客户号查到持仓份额小于1的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(TEST_ZORO));

        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
    	//执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertTrue("客户持仓份额小于1".equals(result.getReturnMsg()));
    }
    
    /**
     * 测试_有持仓且强制调减的情况且当前持仓份额不够调减
     */
    @Test
    public void testDumpNotEnoughVol() {
    	CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
    	UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
    	trade.setConscustno(TEST_CONSCUSTNO);
    	trade.setFundcode(TEST_FUNDCODE);
    	//传入强制调减情况
    	trade.setTradetype(DUMP);
    	//传入条件份额为10
    	trade.setAckvol(TEST_TEN);
    	//mock根据产品和客户号查到持仓份额为5的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(TEST_FIVE));

        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
    	//执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertTrue("持仓份额不够强制调减份额".equals(result.getReturnMsg()));
    }
    
    /**
     * 测试_有持仓且强制调减的情况且当前持仓份额够调减
     */
    @Test
    public void testDumpEnoughVol() {
    	CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
    	UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
    	trade.setConscustno(TEST_CONSCUSTNO);
    	trade.setFundcode(TEST_FUNDCODE);
    	//传入强制调减情况
    	trade.setTradetype(DUMP);
    	//传入条件份额为5
    	trade.setAckvol(TEST_FIVE);
    	//mock根据产品和客户号查到持仓份额为10的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(TEST_TEN));

        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
    	//执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertTrue(result.getReturnCode().equals(ReturnMessageDto.ok().getReturnCode()));
    }
    
    
    
    
    /**
     * 测试_有持仓且强制赎回的情况且当前持仓份额不够赎回
     */
    @Test
    public void testQsNotEnoughVol() {
    	CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
    	UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
    	trade.setConscustno(TEST_CONSCUSTNO);
    	trade.setFundcode(TEST_FUNDCODE);
    	//传入强制赎回情况
    	trade.setTradetype(QZSH);
    	//传入条件份额为10
    	trade.setAckvol(TEST_TEN);
    	//mock根据产品和客户号查到持仓份额为5的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(TEST_FIVE));
    	//执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertTrue("持仓份额不够强制赎回份额".equals(result.getReturnMsg()));
    }
    
    /**
     * 测试_有持仓且强制赎回的情况且当前持仓份额够赎回
     */
    @Test
    public void testQsEnoughVol() {
    	CustprivatefundServiceImpl spy = PowerMockito.spy(serviceMock);
    	UpLoadPrivateTrade trade = new UpLoadPrivateTrade();
    	trade.setConscustno(TEST_CONSCUSTNO);
    	trade.setFundcode(TEST_FUNDCODE);
    	//传入强制赎回情况
    	trade.setTradetype(DUMP);
    	//传入条件份额为5
    	trade.setAckvol(TEST_FIVE);
    	//mock根据产品和客户号查到持仓份额为10的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(TEST_TEN));
    	//执行方法
        ReturnMessageDto result = (ReturnMessageDto)ReflectionUtils.invokeMethod(MemberModifier.methods(CustprivatefundServiceImpl.class, "checktrade")[0], spy, trade);
        Assert.assertTrue(result.getReturnCode().equals(ReturnMessageDto.ok().getReturnCode()));
    }

}
