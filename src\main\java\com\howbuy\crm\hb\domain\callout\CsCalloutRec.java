package com.howbuy.crm.hb.domain.callout;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * <AUTHOR>
 *
 */
@Data
public class CsCalloutRec implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 主键id */
	private String id;
	/** 投顾编码 */
	private String conscode;
	/** 坐席分机号 */
	private String extNo;
	/** 客户手机号 */
	private String mobile;
	/** 交易订单号 */
	private String contractNo;
	/** 双录订单表流水号 */
	private String tid;
	/** 外呼任务id */
	private String waitId;
	/** 拜访表主键编号 */
	private String appSerialNo;
	/** 呼出时间 */
	private String callDt;
	/** 拜访标识 */
	private String visitFlag;
	/** 手机号密文 */
	private String mobileCipher;
	/** 手机号摘要 */
	private String mobileDigest;
	/** 手机号掩码 */
	private String mobileMask; 

}
