/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.outersevice;

import com.google.common.collect.Maps;
import com.howbuy.crm.account.client.facade.consultant.BatchConsultantSimpleFacade;
import com.howbuy.crm.account.client.request.consultant.BatchConsultantSimpleRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.BatchConsultantSimpleResponse;
import com.howbuy.crm.account.client.response.consultant.ConsultantSimpleResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: (投顾的outer服务)
 * <AUTHOR>
 * @date 2025/5/22 10:23
 * @since JDK 1.8
 */
@Service
public class ConsultantOuterService {

    @Autowired
    private BatchConsultantSimpleFacade batchConsultantSimpleFacade;



    /**
     * @description:(批量查看投顾信息)
     * @param consCodeList
     * @return java.util.Map<java.lang.String,com.howbuy.crm.account.client.response.consultant.ConsultantSimpleResponse>
     * @author: haoran.zhang
     * @date: 2025/5/22 10:30
     * @since JDK 1.8
     */
    public Map<String, ConsultantSimpleResponse> batchQueryConsultantSimple(List<String> consCodeList){
        Map<String, ConsultantSimpleResponse> responseMap=Maps.newHashMap();
        if(CollectionUtils.isEmpty(consCodeList)){
            return responseMap;
        }
        List<ConsultantSimpleResponse> responseList=null;

        BatchConsultantSimpleRequest request=new BatchConsultantSimpleRequest();
        request.setConsCodeList(consCodeList);
        Response<BatchConsultantSimpleResponse> response = batchConsultantSimpleFacade.batchQueryConsultantSimple(request);
        if(response.isSuccess() && response.getData()!=null){
            responseList= response.getData().getConsultantList();
        }

        if(CollectionUtils.isEmpty(responseList)){
            return responseMap;
        }

        responseList.forEach(consultantSimpleResponse -> responseMap.put(consultantSimpleResponse.getConsCode(),consultantSimpleResponse));

        return responseMap;
    }


}