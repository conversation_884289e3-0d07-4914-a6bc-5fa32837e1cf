package com.howbuy.crm.hb.persistence.doubletrade;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleRemark;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmDoubleRemarkMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmDoubleRemark getCmDoubleRemark(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmDoubleRemark
      */
	void insertCmDoubleRemark(CmDoubleRemark cmDoubleRemark);
	
	/**
	 * 单条修改数据对象
	 * @param cmDoubleRemark
	 */
	void updateCmDoubleRemark(CmDoubleRemark cmDoubleRemark);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmDoubleRemark(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmDoubleRemark(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleRemark> listCmDoubleRemark(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmDoubleRemarkCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmDoubleRemark> listCmDoubleRemarkByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
