package com.howbuy.crm.hb.domain.insur;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: (创新产品核保修改日志记录表)
 * <AUTHOR>
 * @date 2023/3/24 21:10
 * @since JDK 1.8
 */
@Data
public class CmBxEditLog {
    private static final long serialVersionUID = 1L;
    /**
    * 预约号
    */
    private BigDecimal preid;

    /**
    * 合作渠道
    */
    private BigDecimal channel;

    /**
    * 缴费年限(年)
    */
    private BigDecimal payyears;

    /**
    * 保障期限（年）
    */
    private BigDecimal ensureyears;

    /**
    * 年缴保费(元)
    */
    private BigDecimal payamt;

    /**
    * 核保通过日期
    */
    private BigDecimal passdt;

    /**
    * 保费缴清日
    */
    private BigDecimal paydt;

    /**
    * 冷静期截止日
    */
    private BigDecimal caltime;

    /**
     *  购买id
     * @return
     */
    private BigDecimal buyid;

}