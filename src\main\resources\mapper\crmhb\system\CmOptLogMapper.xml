<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.CmOptLogMapper">
    <insert id="insert" parameterType="com.howbuy.crm.hb.domain.system.CmOptLog">
        insert into
          CM_OPT_LOG (OPT_TYPE, CREATE_TIME, OPT_MAN, ORDER_NO, CONTENT)
          VALUES (#{optType}, sysdate, #{optMan,jdbcType=VARCHAR},#{orderNo,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR})
    </insert>
</mapper>