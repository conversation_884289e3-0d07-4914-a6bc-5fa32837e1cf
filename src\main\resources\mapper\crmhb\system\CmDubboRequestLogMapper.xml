<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.system.CmDubboRequestLogMapper">
    <insert id="insertCmDubboRequestLog" parameterType="com.howbuy.crm.hb.domain.system.CmDubboRequestLog" >
	    INSERT INTO CM_DUBBO_REQUEST_LOG (
	     <trim suffix="" suffixOverrides=",">
      	      id,
      	      <if test="interfaceName != null"> interface_name, </if>
      	      <if test="methodName != null"> method_name, </if>
      	      <if test="invokerDetail != null"> invoker_detail, </if>
			  <if test="usedTime != null"> used_time, </if>
		      sys_type
	     </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
         	  SEQ_SNO.nextval,
      	      <if test="interfaceName != null"> #{interfaceName}, </if>
      	      <if test="methodName != null"> #{methodName}, </if>
      	      <if test="invokerDetail != null"> #{invokerDetail}, </if>
		      <if test="usedTime != null"> #{usedTime}, </if>
		      '4'
	     </trim>
         )
	  </insert>
</mapper>