package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.callout.HbOneCustAbnormal;
import com.howbuy.crm.hb.domain.conscust.ConscustVO;
import com.howbuy.crm.hb.domain.conscust.CustAcctAttrVo;
import com.howbuy.crm.hb.domain.custinfo.SyncCmsCustSource;

import crm.howbuy.base.db.CommPageBean;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.custinfo.Conscust;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ConscustMapper {
    /**
     * 分页查询投顾客户信息
     * @param param
     * @param pageBean
     * @return
     */
    List<Conscust> listConscustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
    
    /**
     * 分页查询投顾客户信息
     * @param param
     * @param pageBean
     * @return
     */
    List<Conscust> listReviewConscustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
    
    /**
	 * 根据menu_code 和获取是否具备操作权限、
	 * @param paramMap
	 * @return
     */
	String getHasOpenRange(Map<String, String> paramMap);


	/**
	 * 根据公募客户号查询分销号 TODO:待重构  2022-2-18 11:29:34
	 * @param custno
	 * @return
	 */
	List<Map<String,Object>> getDiscodeByPubCustno(String custno);
	/**
	 * 根据客户号list查询导出的客户
	 * @param paramMap
	 * @return
	 */
	List<Conscust> selectExportCust(Map<String, String> paramMap);
	
	/**
	 * 根据客户号list查询导出的客户
	 * @param paramMap
	 * @return
	 */
	List<Conscust> selectExportReviewCust(Map<String, String> paramMap);
	
	/**
	 * 新增投顾下载产品报告记录
	 * @param param
	 */
	void insertDownloadFundReport(Map<String, String> param);

	/**
	 * NOTICE: 历史获取客户信息逻辑，待重构
	 * @param custNo
	 * @return
	 */
	Conscust getConscustByBaseCust(@Param("custNo") String custNo);
	
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Conscust> mergeListByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	
	
	/**
	 * 查询列表
	 * @param conscust
	 * @return
	 */
	List<Conscust> listConscust(Conscust conscust);
	
	/**
	 * 查询家庭账户数量
	 * @param hboneno
	 * @return
	 */
	Integer familycodeCount(@Param("hboneno") String hboneno);
	
	
	/**
	 * 客户私募交易记录
	 * @param param
	 * @return
	 */
	List<Conscust> selTradeFundcodeAndCustno(@Param("param") Map<String, String> param);
	
	/**
	 * 客户持有私募
	 * @param param
	 * @return
	 */
	List<Conscust> selPositionsFundcodeAndCustno(@Param("param") Map<String, String> param);
	
	/**
	 * 更新客户信息
	 * @param conscust
	 */
	void updateCmconscustSelective(Conscust conscust);
	
	/**
	 * 更新客户信息
	 * @param conscust
	 */
	void updateCmconscust(Conscust conscust);

	/**
	 * 修改客户信息关联的密文数据
	 * @param conscust
	 */
	void updateCmconscustCipherSelective(Conscust conscust);
	
	/**
	 * 修改客户信息关联的密文数据
	 * @param conscust
	 */
	void updateCmconscustCipher(Conscust conscust);
	
	/**
	 * 更新预约单信息
	 * @param conscust
	 */
	void upCmPrebookproductinfoByConcustnos(Conscust conscust);
	
	/**
	 * 更新客户持有私募信息
	 * @param conscust
	 */
	void upCmCustprivatefundByConcustnos(Conscust conscust);
	
	/**
	 * 更新客户私募交易明细信息
	 * @param conscust
	 */
	void upCmCustprivatefundtradeByConcustnos(Conscust conscust);
	
	/**
	 * 更新资产配置报告信息
	 * @param conscust
	 */
	void upCmAssectSendReportByConcustnos(Conscust conscust);
	
	/**
	 * 更新资产配置报告信息
	 * @param conscust
	 */
	void upCmAssectSendByConcustnos(Conscust conscust);
	
	/**
	 * 记录合并客户日志
	 * @param param
	 */
	void saveMergeInfo(Map<String,String> param);
	
	/**
	 * 分页查询
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Map<String,Object>> listCustConstanthisByPage(@Param("p") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 查询历史
	 * @param param
	 * @return
	 */
	List<Map<String,Object>> selectExportCustConstanthis(Map<String,String> param);
	
	/**
	 * 获取历史
	 * @param param
	 * @return
	 */
	Map<String,Object> getCustConstanthis(Map<String,String> param);
	
	/**
	 * 更新历史
	 * @param param
	 */
	void updateCustConstanthis(Map<String,String> param);

	/**
	 * 根据投顾客户号和交易日期查询交易当时的投顾信息
	 * @param param
	 * @return
	 */
	Map<String,String> getConsInfoByCustNoAndTddt(Map<String, String> param);
	
	/**
	 * 查询回访需要的客户标签
	 * @param param
	 * @return
	 */
	List<Map<String,String>> selectHightCustinfoByReview(Map<String,String> param);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<Conscust> listConscustByMap(Map<String, String> param);


	/**
	 * 判断是否是财富管理中心(包括外地分公司)
	 * @param userId
	 * @return
	 */
	String getIsFundDepart(String userId);

	/**
	 * 判断是否可以看到全部来源
	 * @param userId
	 * @return
	 */
	String getAllAuth(String userId);

	/**
	 * 判断是否具备其他权限
	 * @param userId
	 * @return
	 */
	String getBigAuth(String userId);

	/**
	 * 根据投顾客户号获取一账通
	 * @param param
	 * @return
	 */
	Map<String,Object> getHboneInfo(Map<String, String> param);

	/**
	 * 补全客户
	 * @param var1
	 * @return
	 */
	List<Map<String, String>> listConsCustByConscodeMap(Map<String, String> var1);
	
	
	
	/**
	 * 根据条件查询客户
	 * @param param
	 * @return
	 */
	List<Conscust> listConscustnoByCondition(Map<String, String> param);

	/**
	 * 根据条件查询符合条件的一账通号列表（数据自动核对使用）
	 * @param param
	 * @return
	 */
	Set<String> findHbonesByCondition(Map<String, Object> param);

	/**
	 * 判断指定客户是否有高端成交标签
	 * @param conscustno
	 * @return
	 */
	boolean hasGdcjlabel(String conscustno);
	
	/**
	 * 分页查询投顾分享信息
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<SyncCmsCustSource> listSyncCmsCustSourceByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 根据条件查询投顾分享信息
	 * @param param
	 * @return
	 */
	List<SyncCmsCustSource> listSyncCmsCustSource(@Param("param") Map<String, String> param);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<Conscust> listCustInfoByCustTel(Map<String, String> param);

	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<Conscust> listConscust2(Map<String, String> param);

	/**
	 * 获取分配给异常投顾的客户信息
	 * @param param
	 * @param pageBean
	 * @return
	 */
	public abstract List<Conscust> listAbnormalCustByPage(@Param("param") Map<String, String> param,
														  @Param("page") CommPageBean pageBean);

	/**
	 * 功能描述：获取一帐通异常客户信息
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<HbOneCustAbnormal> listAbnormalHboneCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	/**
	 * 获取非异常投顾的客户数据
	 * @param param
	 * @return
	 */
	public abstract List<ConscustVO> getAbnormalTelNoEmailCustInfo(Map<String,Object> param);
	/**
	 * 功能描述：给异常客户指定
	 * @param param
	 */
	public abstract void relationCustConscode(Map<String,Object> param);
	/**
	 * 更新异常客户对于的状态位
	 * @param param
	 */
	public abstract void updateConscuststatus(Map<String,Object> param);

	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<Conscust> listAbnormalConsCust(Map<String, String> param);

	/**
	 * update
	 * @param hboneNo
	 * @param dealStatus
	 */
	void updateHboneAbnormalStatus(@Param("hboneNo") String hboneNo, @Param("dealStatus") String dealStatus);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getConscustCount(Map<String, String> param);

	/**
	 * 根据选中的客户号和登录投顾清除标签
	 * @param param
	 */
    void clearLabelCust(Map<String, Object> param);

	/**
	 * 根据选中的客户号和登录投顾清除标签  记流水
	 * @param param
	 */
	void backupClearLabelCust(Map<String, Object> param);
	
	/**
	 * 查询客户号列表：提供给线上化查询关联使用
	 * @param param
	 * @return
	 */
	List<String> queryConscustnoForCounter(Map<String, Object> param);

	/**
	 * @description: 查询关联香港账号的投顾信息
	 * @param param 查询条件
	 * @param pageBean 分页信息
	 * @return java.util.List<com.howbuy.crm.hb.domain.conscust.ConscustVO>
	 * @author: jin.wang03
	 * @date: 2023/12/13 19:41
	 * @since JDK 1.8
	 */
	List<ConscustVO> relateListByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * @description: 根据香港客户号列表查询 关联的投顾客户号，以及该投顾客户号绑定的一账通号
	 * @param custNo
	 * @return java.util.List<com.howbuy.crm.hb.domain.conscust.ConscustVO>
	 * @since JDK 1.8
	 */
	CustAcctAttrVo getKeyAcctInfo(@Param("custNo") String custNo);

	/**
	 * @description: 根据投顾客户号列表查询 关联的投顾客户号，以及该投顾客户号绑定的一账通号
	 * @param custNoList
	 * @return java.util.List<com.howbuy.crm.hb.domain.conscust.ConscustVO>
	 * @since JDK 1.8
	 */
	List<CustAcctAttrVo> getKeyAcctInfoByCustNos(@Param("custNoList") List<String> custNoList);

	/**
	 * @description: 根据香港客户号列表查询 关联的投顾客户号，以及该投顾客户号绑定的一账通号
	 * @param hkAcctNoList
	 * @return java.util.List<com.howbuy.crm.hb.domain.conscust.ConscustVO>
	 * @since JDK 1.8
	 */
	List<CustAcctAttrVo> getKeyAcctInfoByHkAcctNos(@Param("hkAcctNoList") List<String> hkAcctNoList);
}
