/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.hb.domain.beisen.AreaVO;
import com.howbuy.crm.hb.domain.beisen.CmBeisenOrgConfigPO;
import com.howbuy.crm.hb.domain.beisen.CmBeisenPosLevelConfigPO;
import com.howbuy.crm.hb.domain.system.*;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * @description: (crm北森字段映射枚举)
 * <AUTHOR>
 * @date 2024/10/29 19:56
 * @since JDK 1.8
 */
@Slf4j
public enum CrmBeisenMappingEnum {
    USER_NO("工号", beisenBO -> true, (beisenBO, exp) -> exp.setUserno(beisenBO.getCmBeisenUserInfoPO().getExt2()), null),
    BEISEN_ID("北森ID", beisenBO -> true, (beisenBO, exp) -> exp.setBeisenid(beisenBO.getCmBeisenUserInfoPO().getExt3()), null),
    CONS_NAME("姓名", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getConsnameFlag()),
            (beisenBO, exp) -> exp.setConsname(beisenBO.getCmBeisenUserInfoPO().getExt4()), sync -> sync.setConsnameFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    AREA_CODE("省市区code", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getCitycodeFlag()),
            (beisenBO, exp) -> {
                String ext5 = beisenBO.getCmBeisenUserInfoPO().getExt5();
                AreaVO cityVO = beisenBO.getCityMapByProv().get(ext5);
                if(Objects.nonNull(cityVO)){
                    exp.setCitycode(ext5);
                    exp.setProvcode(cityVO.getProvCode());
                    return;
                }
                AreaVO countryVO = beisenBO.getCountryMapByCity().get(ext5);
                if(Objects.isNull(countryVO)){
                    return;
                }
                exp.setCountyCode(ext5);
                exp.setCitycode(countryVO.getCityCode());
                exp.setProvcode(countryVO.getProvCode());
            }, sync -> sync.setCitycodeFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    EMAIL("电子邮箱", beisenBO -> true, (beisenBO, exp) -> exp.setEmail(beisenBO.getCmBeisenUserInfoPO().getExt6()), null),
    OUT_LET_CODE("部门", beisenBO -> true, (beisenBO, exp) -> {
        CmBeisenOrgConfigPO vo = getCmAndOrgConfig(beisenBO);
        if(Objects.isNull(vo)){
            return;
        }
        exp.setOutletcode(vo.getOrgCode());
    }, null),
    GENDER("性别", beisenBO -> true, (beisenBO, exp) -> {
        if("男".equals(beisenBO.getCmBeisenUserInfoPO().getExt10())){
            exp.setGender(GenderEnum.GENDER_1.getCode());
        }else if("女".equals(beisenBO.getCmBeisenUserInfoPO().getExt10())){
            exp.setGender(GenderEnum.GENDER_2.getCode());
        }else{
            //保密
            exp.setGender(GenderEnum.GENDER_3.getCode());
        }
    }, null),
    BIRTHDAY("出生日期", beisenBO -> true, (beisenBO, exp) -> exp.setBirthday(formatStr(beisenBO.getCmBeisenUserInfoPO().getExt11())), null),
    EDULEVEL("学历", beisenBO -> true, (beisenBO, exp) ->  exp.setEdulevel(CmBeisenEdulevelEnum.getByName(beisenBO.getCmBeisenUserInfoPO().getExt12())), null),
    CENTER_ORG("业务中心", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getCenterOrgFlag()),
            (beisenBO, exp) -> {
                CmBeisenOrgConfigPO vo = getCmAndOrgConfig(beisenBO);
                if(Objects.isNull(vo)){
                    return;
                }
                exp.setCenterOrg(vo.getCenterOrg());
    }, sync -> sync.setCenterOrgFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    WORK_TYPE("员工状态", beisenBO -> true, (beisenBO, exp) -> {
        if(StringUtils.isNotEmpty(beisenBO.getCmBeisenUserInfoPO().getExt24()) || "离职".equals(beisenBO.getCmBeisenUserInfoPO().getExt14())){
            //离职
            exp.setWorktype(WorkTypeEnum.WORK_TYPE_2.getCode());
        }else if("试用".equals(beisenBO.getCmBeisenUserInfoPO().getExt14()) || "正式".equals(beisenBO.getCmBeisenUserInfoPO().getExt14())){
            //在职
            exp.setWorktype(WorkTypeEnum.WORK_TYPE_1.getCode());
        }
    }, null),
    WORK_STATE("在职状态", beisenBO -> true, (beisenBO, exp) -> {
        if("正式".equals(beisenBO.getCmBeisenUserInfoPO().getExt14())){
            exp.setWorkstate(WorkStateEnum.WORK_STATE_1.getCode());
        }else if("试用".equals(beisenBO.getCmBeisenUserInfoPO().getExt14())){
            exp.setWorkstate(WorkStateEnum.WORK_STATE_2.getCode());
        }
    }, null),
    CUR_MONTH_LEVEL("当月职级", CrmBeisenMappingEnum::curMonthLevelIsSyncFlag, (beisenBO, exp) -> {
        CmBeisenPosLevelConfigPO po = beisenBO.getCmBeisenPosLevelConfigPOMap().get(beisenBO.getCmBeisenUserInfoPO().getExt16());
        if(Objects.isNull(po)){
            return;
        }
        exp.setCurmonthlevel(po.getPositionsLevelCrm());
    }, null),
    CURMONTH_SALARY("当月薪资", beisenBO -> true, (beisenBO, exp) ->{
        String basicSalary = beisenBO.getCmBeisenUserInfoPO().getExt17();
        String bonus = beisenBO.getCmBeisenUserInfoPO().getExt18();
        BigDecimal salaryBigDecimal = BigDecimal.ZERO;
        BigDecimal bonusBigDecimal = BigDecimal.ZERO;
        if(StringUtils.isEmpty(basicSalary) && StringUtils.isEmpty(bonus)){
            return;
        }else if(StringUtils.isEmpty(basicSalary)){
            bonusBigDecimal = new BigDecimal(bonus);
        }else if(StringUtils.isEmpty(bonus)){
            salaryBigDecimal = new BigDecimal(basicSalary);
        }
        BigDecimal sum = bonusBigDecimal.add(salaryBigDecimal);
        if(BigDecimal.ZERO.compareTo(sum) > 0){
            exp.setCurmonthsalary(sum);
        }
    }, null),
    SUB_POSITIONS("副职", CrmBeisenMappingEnum::curMonthLevelIsSyncFlag, (beisenBO, exp) -> {
        //需要获取crm北森职级表数据
        CmBeisenPosLevelConfigPO po = beisenBO.getCmBeisenPosLevelConfigPOMap().get(beisenBO.getCmBeisenUserInfoPO().getExt16());
        if(Objects.isNull(po)){
            return;
        }
        exp.setSubpositions(po.getSubPositionsLevelCrm());
    }, null),
    STARTDT("入职日期", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getStartdtFlag()), (beisenBO, exp) ->
        exp.setStartdt(formatStr(beisenBO.getCmBeisenUserInfoPO().getExt19()))
    , sync -> sync.setStartdtFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    START_LEVEL("入职职级", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getStartlevelFlag()), (beisenBO, exp) -> {
        CmBeisenPosLevelConfigPO po = beisenBO.getCmBeisenPosLevelConfigPOMap().get(beisenBO.getCmBeisenUserInfoPO().getExt20());
        if(Objects.isNull(po)){
            return;
        }
        exp.setStartlevel(po.getPositionsLevelCrm());
    }, sync -> sync.setStartlevelFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    JOIN_RANK("入职档级", beisenBO -> true, (beisenBO, exp) ->
        getRankAndSal(beisenBO.getZjSalMap(), exp.getStartlevel(), exp.getStartdt(), zjSal -> exp.setJoinRank(zjSal.getRank()))
    , null),
    SALARY("入职薪资", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getSalaryFlag()), (beisenBO, exp) ->{
        if(!WorkStateEnum.WORK_STATE_3.getCode().equals(exp.getWorkstate())){
            getRankAndSal(beisenBO.getZjSalMap(), exp.getStartlevel(), exp.getStartdt(), zjSal -> exp.setSalary(zjSal.getProbationsalary()));
        }
    }, sync -> sync.setSalaryFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    JOIN_SSB("入职社保基数", beisenBO -> true, (beisenBO, exp) ->
        getRankAndSal(beisenBO.getZjSalMap(), exp.getStartlevel(), exp.getStartdt(), zjSal -> exp.setJoinSsb(zjSal.getSocialInsuranceBase()))
    , null),
    DT3M("3M日期", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getDt3mFlag()), (beisenBO, exp) ->
        exp.setDt3m(calcNewDay(exp.getStartdt(), 2L))
    , sync -> sync.setDt3mFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    REGULAR_DT("转正日期", beisenBO -> isSyncFlag(beisenBO.getExp().getDt12mFlag()), (beisenBO, exp) ->
            exp.setRegulardt(formatStr(beisenBO.getCmBeisenUserInfoPO().getExt21())),
            sync -> sync.setRegulardtFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    REGULAR_LEVEL("转正职级", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getRegularlevelFlag()), (beisenBO, exp) ->{
        CmBeisenPosLevelConfigPO po = beisenBO.getCmBeisenPosLevelConfigPOMap().get(beisenBO.getCmBeisenUserInfoPO().getExt23());
        if(Objects.isNull(po)){
            return;
        }
        exp.setRegularlevel(po.getPositionsLevelCrm());
    }, sync -> sync.setRegularlevelFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    REGULAR_RANK("转正档级", beisenBO -> true, (beisenBO, exp) ->
        getRankAndSal(beisenBO.getZjSalMap(), exp.getRegularlevel(), exp.getRegulardt(), zjSal -> exp.setRegularRank(zjSal.getRank()))
    , null),
    REGULAR_SALARY("转正薪资", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getRegularsalaryFlag()), (beisenBO, exp) ->{
        if(!WorkStateEnum.WORK_STATE_3.getCode().equals(exp.getWorkstate())){
            getRankAndSal(beisenBO.getZjSalMap(), exp.getRegularlevel(), exp.getRegulardt(), zjSal -> exp.setRegularsalary(zjSal.getRegularsalary()));
        }
    }, sync -> sync.setRegularsalaryFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    REGULAR_SSB("转正社保基数", beisenBO -> true, (beisenBO, exp) ->
            getRankAndSal(beisenBO.getZjSalMap(), exp.getRegularlevel(), exp.getRegulardt(), zjSal -> exp.setRegularSsb(zjSal.getSocialInsuranceBase()))
                    , null),
    DT12M("12M日期", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getDt12mFlag()), (beisenBO, exp) ->
        exp.setDt12m(calcNewDay(exp.getStartdt(), 11L))
    , sync -> sync.setDt12mFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    QUIT_DT("离职日期", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getQuitdtFlag()), (beisenBO, exp) ->
            exp.setQuitdt(formatStr(beisenBO.getCmBeisenUserInfoPO().getExt24())),
            sync -> sync.setQuitdtFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    QUIT_LEVEL("离职职级", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getQuitlevelFlag()), (beisenBO, exp) ->
        exp.setQuitlevel(StringUtils.isEmpty(exp.getQuitdt()) ? null: exp.getCurmonthlevel()),
            sync -> sync.setQuitlevelFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    QUIT_SALARY("离职薪资", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getQuitsalaryFlag()), (beisenBO, exp) ->{
        String currLevel = exp.getQuitlevel();
        if(StringUtils.isEmpty(currLevel)){
            return;
        }
        if(WorkStateEnum.WORK_STATE_1.getCode().equals(exp.getWorkstate())){
            getRankAndSal(beisenBO.getZjSalMap(), currLevel, exp.getQuitdt(), zjSal -> exp.setQuitsalary(zjSal.getRegularsalary()));
        }else if(WorkStateEnum.WORK_STATE_2.getCode().equals(exp.getWorkstate())){
            getRankAndSal(beisenBO.getZjSalMap(), currLevel, exp.getQuitdt(), zjSal -> exp.setQuitsalary(zjSal.getProbationsalary()));
        }
    }, sync -> sync.setQuitdtFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    SERVING_AGE("好买司龄(月)", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getStartdtFlag()), (beisenBO, exp) -> {
        String year = exp.getStartdt().substring(0,4);
        String day = exp.getStartdt().substring(6,8);
        int addMonth = isCurrMonth(year, day) ? 1:0;
        Period period = Period.between(LocalDate.parse(exp.getStartdt(), DateTimeFormatter.ofPattern("yyyyMMdd")), LocalDate.now());
        exp.setServingage(new BigDecimal(period.getMonths()+addMonth));
    }, null),
    JJCARDNO("基金从业资格编码", beisenBO -> true,
            (beisenBO, exp) -> exp.setJjcardno(beisenBO.getCmBeisenUserInfoPO().getExt26()), null),
    ATTACH_TYPE("是否挂靠", beisenBO -> true, (beisenBO, exp) ->
            //1是，2否
            exp.setAttachtype(StringUtils.isEmpty(exp.getJjcardno()) ? "2":"1"), null
    ),
    BACKGROUND("上家公司", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getBackgroundFlag()),
            (beisenBO, exp) -> exp.setBackground(beisenBO.getCmBeisenUserInfoPO().getExt27()),
            sync -> sync.setBackgroundFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    SOURCE("背景来源", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getSourceFlag()),
            (beisenBO, exp) -> exp.setSource(beisenBO.getCmBeisenUserInfoPO().getExt28()),
            sync -> sync.setSourceFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    BEFORE_POSITIONAGE("上家工作月份数", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getBeforepositionageFlag()),
            (beisenBO, exp) -> exp.setBeforepositionage(beisenBO.getCmBeisenUserInfoPO().getExt29()),
            sync -> sync.setBeforepositionageFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    RECRUIT("招聘经理", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getRecruitFlag()),
            (beisenBO, exp) -> exp.setRecruit(beisenBO.getCmBeisenUserInfoPO().getExt30()),
            sync -> sync.setRecruitFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    RECOMMEND("推荐人", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getRecommendFlag()),
            (beisenBO, exp) -> exp.setRecommend(beisenBO.getCmBeisenUserInfoPO().getExt31()),
            sync -> sync.setRecommendFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    RECOMMENDUSER_NO("推荐人工号", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getRecommendusernoFlag()),
            (beisenBO, exp) -> exp.setRecommenduserno(beisenBO.getCmBeisenUserInfoPO().getExt32()),
            sync -> sync.setRecommendusernoFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag())),
    RECOMMEND_TYPE("招聘渠道", beisenBO -> isSyncFlag(beisenBO.getSyncFlag().getRecommendtypeFlag()),
            (beisenBO, exp) -> {
                HrrecommendtypeEnum typeEnum = HrrecommendtypeEnum.getEnum(beisenBO.getCmBeisenUserInfoPO().getExt34());
                if(Objects.isNull(typeEnum)){
                    return;
                }
                exp.setRecommendtype(typeEnum.getCode());
            },
            sync -> sync.setRecommendtypeFlag(HumanModifyEnum.BEISEN_MODIFY.getFlag()));

    /**
     * 名称
     */
    private String name;
    /**
     * 同步标记
     */
    private Predicate<CmConcultantExpBeisenBO> syncFlag;

    /**
     * 写入目标字段值(CmConcultantExpBeisenBO:来源， CmConsultantExp:目标)
     */
    private BiConsumer<CmConcultantExpBeisenBO, CmConsultantExp> consSetTargetValue;
    /**
     * 把字段修改成北森同步
     */
    private Consumer<CmConsultantExpModifyFlag> setModifyFlag;

    CrmBeisenMappingEnum(String name, Predicate<CmConcultantExpBeisenBO> syncFlag,
                         BiConsumer<CmConcultantExpBeisenBO, CmConsultantExp> consSetTargetValue, Consumer<CmConsultantExpModifyFlag> setModifyFlag) {
        this.name = name;
        this.syncFlag = syncFlag;
        this.consSetTargetValue = consSetTargetValue;
        this.setModifyFlag = setModifyFlag;
    }

    /**
     * @description:(同步北森数据)
     * @param beisenBO
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/31 19:54
     * @since JDK 1.8
     */
    public static void syncBeisenData(CmConcultantExpBeisenBO beisenBO) {
        for (CrmBeisenMappingEnum mappingEnum : CrmBeisenMappingEnum.values()) {
            try{
                if (mappingEnum.syncFlag.test(beisenBO)) {
                    mappingEnum.consSetTargetValue.accept(beisenBO, beisenBO.getExp());
                    if(Objects.nonNull(mappingEnum.setModifyFlag)){
                        mappingEnum.setModifyFlag.accept(beisenBO.getSyncFlag());
                    }
                }
            }catch (Exception e){
                log.error("syncBeisenData| mappingName:{}, is error:{}", mappingEnum.name,e, e.getMessage());
                throw new BusinessException(BaseConstantEnum.DEAL_FAIL.getCode(),"同步北森数据异常！");
            }
        }
    }

    /**
     * @description:(判断是否同步)
     * @param value
     * @return java.lang.Boolean
     * @author: shijie.wang
     * @date: 2024/10/30 17:27
     * @since JDK 1.8
     */
    public static Boolean isSyncFlag(String value) {
        //为空或不为1可以同步（1为人工修改）
        return StringUtils.isEmpty(value) ||  !"1".equals(value);
    }

    /**
     * @description:(获得crm和北森机构配置信息)
     * @param beisenBO
     * @return com.howbuy.crm.account.client.response.beisen.CmBeisenOrgConfigDetailVO
     * @author: shijie.wang
     * @date: 2024/10/31 10:26
     * @since JDK 1.8
     */
    public static CmBeisenOrgConfigPO getCmAndOrgConfig(CmConcultantExpBeisenBO beisenBO){
        //（分公司、区域、所属中心）北森代码
        String[] orgCodes = new String[]{beisenBO.getCmBeisenUserInfoPO().getExt9(), beisenBO.getCmBeisenUserInfoPO().getExt8(),
                beisenBO.getCmBeisenUserInfoPO().getExt7()};
        //北森_架构ID
        String beisenOrgId = null;
        //获得北森架构ID(先取分公司没有再取区域最后取所属中心)
        for(String value : orgCodes){
            if(StringUtils.isEmpty(value)){
                continue;
            }
            //北森_架构ID
            beisenOrgId = beisenBO.getBeisenOrgDOMap().get(value);
            if(StringUtils.isEmpty(beisenOrgId)){
                continue;
            }
            break;
        }
        if(StringUtils.isEmpty(beisenOrgId)){
            return null;
        }
        return beisenBO.getCmBeisenOrgConfigMap().get(beisenOrgId);
    }

    /**
     * @description:(根据当月职级判断是否更新数据)
     * @param beisenBO
     * @return java.lang.Boolean
     * @author: shijie.wang
     * @date: 2024/10/31 14:05
     * @since JDK 1.8
     */
    public static Boolean curMonthLevelIsSyncFlag(CmConcultantExpBeisenBO beisenBO){
        String[] level = beisenBO.getExp().getCurmonthlevel().split(",");
        //当月职级为多职级不同步花名册
        if(1 < level.length){
            return false;
        }
        HbConstant hbConstant = beisenBO.getHrPositionsLevelMap().get(level[0]);
        if(Objects.isNull(hbConstant)){
            return false;
        }
        String userLevel = hbConstant.getConstext2();
        //“区域总”、“分总”、“理财师”
        List<String> userLevels = Arrays.asList("1","3","5");
        if(userLevels.contains(userLevel)){
            return true;
        }
        return false;
    }

    /**
     * @description:(获得档级和薪资)
     * @param zjSalMap
     * @param level
     * @param startDt
     * @param setValue
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/4 10:01
     * @since JDK 1.8
     */
    public static void getRankAndSal(Map<String, List<CmConsultantExpZjSal>> zjSalMap, String level, String startDt,
                                     Consumer<CmConsultantExpZjSal> setValue){
        List<CmConsultantExpZjSal> list = zjSalMap.get(level);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Integer startDate = Integer.valueOf(startDt);
        Integer endDateConstant = 20991231;
        list.stream().filter(zjSal -> Integer.valueOf(zjSal.getStartdt()) <= startDate &&
                        (StringUtils.isEmpty(zjSal.getEnddt()) ? endDateConstant: Integer.valueOf(zjSal.getEnddt())) >= startDate).
                findAny().ifPresent(setValue::accept);
    }

    /**
     * 计算新日期
     * @param startDate
     * @param month
     * @return
     */
    public static String calcNewDay(String startDate, Long month){
        if(StringUtils.isEmpty(startDate)){
            return null;
        }
        String year = startDate.substring(0,4);
        String day = startDate.substring(6,8);
        //增加月份数,如果是当月计算司龄逻辑，月份加1
        Long addMonth = isCurrMonth(year, day) ? 1L : 0L;
        return LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd")).plusMonths(month+addMonth).
                with(TemporalAdjusters.lastDayOfMonth()).
                format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * @description:(是否当月计算司龄)
     * @param year
     * @param day
     * @return java.lang.Boolean
     * @author: shijie.wang
     * @date: 2024/10/31 19:31
     * @since JDK 1.8
     */
    public static Boolean isCurrMonth(String year, String day){
        return (2023 < Integer.valueOf(year) && 10<= Integer.valueOf(day)) || (2023 >= Integer.valueOf(year) && 15<= Integer.valueOf(day));
    }

    /**
     * @description:(格式化数据)
     * @param value
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/31 16:39
     * @since JDK 1.8
     */
    public static String formatStr(String value){
        return StringUtils.isEmpty(value) ? value : value.replace("-","");
    }

}