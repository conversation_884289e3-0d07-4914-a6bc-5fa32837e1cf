<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.reward.CmCustPrpSourceCoeffMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>

	<select id="listCustPrpSourceCoeffByPage" parameterType="map" resultType="com.howbuy.crm.hb.domain.reward.CmPrpCustSourceCoeff" useCache="false">
		SELECT
		a.id           ,
		a.source_type  as sourceType,
		a.start_point  as startPoint,
		a.zb_coeff     as zbCoeff,
		a.manage_coeff as manageCoeff,
		a.manage_coeff_regionalsubtotal as manageCoeffRegionalSubTotal,
		a.manage_coeff_regionaltotal as manageCoeffRegionalTotal,
		a.cxb          ,
		a.start_dt     as startDt,
		a.end_dt       as endDt,
		a.creator      ,
		a.create_time  as createTime,
		a.modor        ,
		a.update_time  as updateTime
		FROM CM_PRP_CUST_SOURCE_COEFF a
		where 1=1
		<if test="param.id != null"> AND a.id = #{param.id} </if>
		<if test="param.sourceType != null"> AND a.source_Type = #{param.sourceType} </if>
		order by a.create_time desc
	</select>
	<select id="listCustPrpSourceCoeff" parameterType="map" resultType="com.howbuy.crm.hb.domain.reward.CmPrpCustSourceCoeff" useCache="false">
		SELECT
		a.id           ,
		a.source_type  as sourceType,
		a.start_point  as startPoint,
		a.zb_coeff     as zbCoeff,
		a.manage_coeff as manageCoeff,
		a.manage_coeff_regionalsubtotal as manageCoeffRegionalSubTotal,
		a.manage_coeff_regionaltotal as manageCoeffRegionalTotal,
		a.cxb          ,
		a.start_dt     as startDt,
		a.end_dt       as endDt,
		a.creator      ,
		a.create_time  as createTime,
		a.modor        ,
		a.update_time  as updateTime
		FROM CM_PRP_CUST_SOURCE_COEFF a
		where 1=1
		<if test="sourceType != null"> AND a.source_Type = #{sourceType} </if>
		order by a.create_time desc
	</select>

	<select id="selectByPrimaryKey" parameterType="long" resultType="com.howbuy.crm.hb.domain.reward.CmPrpCustSourceCoeff" useCache="false">
        SELECT
		a.id           ,
		a.source_type  as sourceType,
		a.start_point  as startPoint,
		a.zb_coeff     as zbCoeff,
		a.manage_coeff as manageCoeff,
		a.manage_coeff_regionalsubtotal as manageCoeffRegionalSubTotal,
		a.manage_coeff_regionaltotal as manageCoeffRegionalTotal,
		a.cxb          ,
		a.start_dt     as startDt,
		a.end_dt       as endDt,
		a.creator      ,
		a.create_time  as createTime,
		a.modor        ,
		a.update_time  as updateTime
		FROM CM_PRP_CUST_SOURCE_COEFF a
        where a.id = #{id}
    </select>

	<select id="selectAlreadyCount" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpCustSourceCoeff" resultType="int" useCache="false">
		select count(*)
		from CM_PRP_CUST_SOURCE_COEFF
		where
		<if test="endDt == null or endDt == ''">
			nvl(end_dt,'20991231') >= #{startDt}
		</if>
		<if test="endDt != null and endDt != ''">
			start_dt &lt;= #{endDt}
			and nvl(end_dt,'20991231') >= #{startDt}
		</if>
		and source_type = #{sourceType}
		<if test="id != null">
			and id != #{id}
		</if>
	</select>

	<insert id="insert" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpCustSourceCoeff">
        insert into CM_PRP_CUST_SOURCE_COEFF
		  (ID,
		   SOURCE_TYPE,
		   START_POINT,
		   ZB_COEFF,
		   MANAGE_COEFF,
		   MANAGE_COEFF_REGIONALSUBTOTAL,
		   MANAGE_COEFF_REGIONALTOTAL,
		   CXB,
		   START_DT,
		   END_DT,
		   CREATOR,
		   CREATE_TIME)
		values
		   (#{id},
			#{sourceType,jdbcType=VARCHAR},
			#{startPoint,jdbcType=VARCHAR},
			#{zbCoeff,jdbcType=DECIMAL},
			#{manageCoeff,jdbcType=DECIMAL},
			#{manageCoeffRegionalSubTotal,jdbcType=DECIMAL},
			#{manageCoeffRegionalTotal,jdbcType=DECIMAL},
			#{cxb,jdbcType=VARCHAR},
			#{startDt,jdbcType=VARCHAR},
			#{endDt,jdbcType=VARCHAR},
			#{creator,jdbcType=VARCHAR},
			sysdate)
    </insert>

	<update id="update" parameterType="com.howbuy.crm.hb.domain.reward.CmPrpCustSourceCoeff">
		update CM_PRP_CUST_SOURCE_COEFF
		<set>
			<if test="startPoint != null">
				START_POINT = #{startPoint},
			</if>
			<if test="zbCoeff != null">
				ZB_COEFF = #{zbCoeff,jdbcType=DECIMAL},
			</if>
			<if test="manageCoeff != null">
				MANAGE_COEFF = #{manageCoeff,jdbcType=DECIMAL},
			</if>
			<if test="manageCoeffRegionalSubTotal != null">
				MANAGE_COEFF_REGIONALSUBTOTAL = #{manageCoeffRegionalSubTotal,jdbcType=DECIMAL},
			</if>
			<if test="manageCoeffRegionalTotal != null">
				MANAGE_COEFF_REGIONALTOTAL = #{manageCoeffRegionalTotal,jdbcType=DECIMAL},
			</if>
			<if test="cxb != null">
				cxb = #{cxb},
			</if>
			<if test="startDt != null">
				start_dt = #{startDt},
			</if>
			<if test="endDt != null">
				end_dt = #{endDt},
			</if>
			MODOR = #{modor,jdbcType=VARCHAR},
			UPDATE_TIME = sysdate
		</set>
		where id = #{id}
	</update>

	<delete id="deleteById" parameterType="long">
        delete CM_PRP_CUST_SOURCE_COEFF where id = #{id}
    </delete>

	<update id="batchUpdateCustPrpSourceCoeff" parameterType="map">
		update CM_PRP_CUST_SOURCE_COEFF
		<set>
			<if test="dto.startPoint != null">
				START_POINT = #{dto.startPoint},
			</if>
			<if test="dto.zbCoeff != null">
				ZB_COEFF = #{dto.zbCoeff,jdbcType=DECIMAL},
			</if>
			<if test="dto.manageCoeff != null">
				MANAGE_COEFF = #{dto.manageCoeff,jdbcType=DECIMAL},
			</if>
			<if test="dto.manageCoeffRegionalSubTotal != null">
				MANAGE_COEFF_REGIONALSUBTOTAL = #{dto.manageCoeffRegionalSubTotal,jdbcType=DECIMAL},
			</if>
			<if test="dto.manageCoeffRegionalTotal != null">
				MANAGE_COEFF_REGIONALTOTAL = #{dto.manageCoeffRegionalTotal,jdbcType=DECIMAL},
			</if>
			<if test="dto.cxb != null">
				cxb = #{dto.cxb},
			</if>
			MODOR = #{dto.modor,jdbcType=VARCHAR},
			UPDATE_TIME = sysdate
		</set>
		where id in (${ids})
	</update>

	<select id="getManageCoeffBySourceType" parameterType="string" resultType="double">
		<!--NOTICE : 历史逻辑。 sourceType 对应唯一一条数据。
		绩效系统0730上线，修改为：  sourceType 在 [stratDt,endDt]内唯一一条数据
		此处Exception .  临时修复： 按照 创建时间降序排序， 取第一条配置 -->
		SELECT MANAGE_COEFF
		FROM (
		SELECT a.*
		FROM CM_PRP_CUST_SOURCE_COEFF a
		WHERE a.source_Type = #{sourceType,jdbcType=VARCHAR}
		ORDER BY a.create_time DESC NULLS LAST
		)
		WHERE rownum = 1
	</select>
</mapper>