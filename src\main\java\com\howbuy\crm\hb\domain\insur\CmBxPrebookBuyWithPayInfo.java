package com.howbuy.crm.hb.domain.insur;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @Description: 保险产品购买信息 && 缴费信息
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxPrebookBuyWithPayInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/***************************prebookInfo信息  begin **************************************/
	/**
	 * 预约Id
	 */
	private BigDecimal preId;

	/**
	 * 录入时间
	 */
	private Date creDt;

	/**
	 * 合作渠道
	 */
	private String channCode;

	private String fundCode;

	/**
	 * 产品名称
	 */
	private String fundName;

	/**
	 * 产品类型
	 */
	private String prodType;

	/**
	 * 预约状态
	 */
	private String preState;

	/**
	 * 受保人年纪
	 */
	private Integer insurAge;

	/**
	 * 保单状态
	 */
	private String insurState;

	private String orgCode;

	private String consCode;

	private String creator;

	/**
	 * 投顾客户号
	 */
	private String conscustno;

	/**
	 * 投保人
	 */
	private String custName;

	/**
	 * 受保人
	 */
	private String insurName;

	/**
	 * 业务类型
	 */
	private String busiType;

	private String compcode;


	/**
	 * 保险公司名称
	 */
	private String compname;

	/**
	 * 是否变为退保 1是；0否
	 */
	private String isChangeReturn;

	/**
	 * 币种
	 */
	private String currency;


	          /*******signInfo************/
	/**
	 * 签单日期
	 */
	private String signDt;

	/**
	 * 保单号
	 */
	private String insurId;

	/**
	 *核保通过日期
	 */
	private String passDt;

	/**
	 *冷静期截止日
	 */
	private String calTime;

	          /*******signInfo************/


	/***************************prebookInfo信息  end **************************************/




	/***************************buyInfo信息  begin **************************************/

	/**
	 * 购买记录中的fundCode
	 */
	private String detailFundCode;

	/**
	 * 购买Id
	 */
	private BigDecimal buyId;
	/**
	 * 缴费年限
	 */
	private String payYears;
	/**
	 * 缴费年数
	 */
	private String payYears2;
	/**
	 * 保障年限
	 */
	private String ensureYears;
	/**
	 * 年缴保费
	 */
	private BigDecimal yearAmk;

	/**
	 * 是否主预约的记录
	 */
	private String isMain;


	/***************************buyInfo信息  end **************************************/




	/***************************endPay 缴费信息  begin **************************************/
	/**
	 * 缴款Id
	 */
	private BigDecimal endPayId;

	/**
	 * 实际缴费日期
	 */
	private String  realPayDt;
	/**
	 * 人工修改的佣金核算日期
	 */
	private String expcommissionDt;
	/**
	 * 人工修改的投顾佣金率
	 */
	private BigDecimal expcommissionRatio;

	/**
	 * 佣金核算日期
	 */
	private String commissionDt;
	/**
	 * 投顾佣金率
	 */
	private BigDecimal commissionRatio;
	/**
	 * 投顾佣金
	 */
	private BigDecimal commission;
	private String commissionStr;
	/**
	 * 投顾佣金汇率（绩效汇率）
	 */
	private BigDecimal commissionRate;

	/**
	 *
	 */
	private Integer endPayOrder;

	/**
	 *
	 */
	private String payState;

	/***************************endPay 缴费信息  end **************************************/
	
	/**
	 * 当前客户的投顾
	 */
	private String nowconscode;
	/**
	 * 实际算绩效投顾
	 */
	private String calconscode;
	private String calconsname;
	/**
	 * 保费缴清日
     */
    private String payDt;
    //投顾创新方案 1方案一 2方案二
    private String bxcommissionway;
    /**
     * 投顾佣金 方案二
     */
    private BigDecimal commissionfortwo;
    private String commissionfortwoStr;


    /**
     * 佣金总和（方案1佣金+方案2佣金） 创新绩效需求新增字段
     */
    private String totalCommission;
    /**
     * 管理系数 创新绩效需求新增字段
     */
    private String managePoint;
	/**
	 * 管理系数-区副
	 */
	private BigDecimal manageCoeffRegionalsubtotal;
	/**
	 * 管理系数-区总
	 */
	private BigDecimal manageCoeffRegionaltotal;
    /**
     * 管理奖金基数（totalCommission * managePoint） 创新绩效需求新增字段
     */
    private String pointCommission;
	/**
	 * 管理奖金基数-区副
	 */
	private BigDecimal manageCoeffRegsubtotalComm;
	/**
	 * 管理奖金基数-区总
	 */
	private BigDecimal manageCoeffRegtotalComm;
	/**
	 * 复核状态
	 */
	private String checkState;
	/**
	 * 调整核算最近修改人
	 */
	private String expModifier;
	/**
	 * 缴款金额
	 */
	private BigDecimal payAmt;
}
