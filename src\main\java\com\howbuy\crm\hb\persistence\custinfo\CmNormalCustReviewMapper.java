package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.custinfo.CmNormalCustReview;

import crm.howbuy.base.db.CommPageBean;


/**
 * 
 * <AUTHOR>
 *
 */
public interface CmNormalCustReviewMapper {
    
     /**
      * 新增数据对象
      * @param cmNormalCustReview
      */
	void insertCmNormalCustReview(CmNormalCustReview cmNormalCustReview);
	
	/**
	 * 分页查询常规回访任务
	 * @param param
	 * @param pageBean
	 * @return
	 */
    List<CmNormalCustReview> listCmNormalCustReviewByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
    
    
    /**
     * 根据ids修改数据
     * @param param
     */
	void updateCmNormalCustReviewByIds(Map<String, String> param);
	
	/**
	 * 修改单条数据
	 * @param cmNormalCustReview
	 */
	void updateCmNormalCustReview(CmNormalCustReview cmNormalCustReview);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmNormalCustReview(String id);
	
	/**
	 * 批量处理
	 * @param param
	 */
	void batchDelCmNormalCustReview(Map<String, String> param);
	
	/**
	 * 根据id单条数据对象
	 * @param id
	 * @return
	 */
	CmNormalCustReview getCmNormalCustReviewMsgById(String id);
	
	/**
	 * 修改单条数据：部分数据
	 * @param cmNormalCustReview
	 */
	void updatePartMsgById(CmNormalCustReview cmNormalCustReview);
	
	
	/**
	 *  查询常规回访任务
	 * @param param
	 * @return
	 */
    List<CmNormalCustReview> listCmNormalCustReview(Map<String, String> param);
    
    /**
     * 根据条件查询常规回访任务
     * @param param
     * @return
     */
    List<CmNormalCustReview> listCmNormalCustReviewByCondition(Map<String, String> param);
    
    /**
	 * 判断是否存在待处理和再处理的客户对应的回访类型
	 * @param param
	 * @return
	 */
	int getIsHasCustReviewCount(Map<String, String> param);
}
