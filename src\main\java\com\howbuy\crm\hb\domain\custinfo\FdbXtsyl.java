package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

import lombok.Data;

/**
 * 信托收益率实体类
 * 
 * @author: wu.long
 * date: 2019年7月15日 上午10:10:34
 * version: V1.0
 * since: jdk 1.8,tomcat 8.0
 */
@Data
public class FdbXtsyl implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 主键 */
	private BigDecimal sno;
	/** 信托计划代码 */
	private String xtdm;
	/** 起始金额（万） */
	private BigDecimal qsje;
	/** 结束金额（万） */
	private BigDecimal jsje;
	/** 收益率（百分比） */
	private String yjsy;                       
	private String creator;
	private String modifier;
	/** 记录创建日期 */
	private String credt;
	/** 记录修改日期 */
	private String moddt;                          
	private String dataSource;
	/** 操作类型01-插入02-修改 */
	private String mOptType;
	/** 增量ID */
	private BigDecimal zlid;                       
	private String checkflag;                      
	private String checker;
	private String ruleResults;
	/** 修改时间 */
	private Timestamp modifyTime;
	/** 投资起始日 */
	private String tzqsr;
	/** 基金代码 */
	private String jjdm;
	/** 预计到期日 */
	private String yjdqr;
	/** 预计投资期 */
	private String yjtzqx;
	/** 合作方 */
	private String hzf;
	/** 是否复购 */
	private String sffg;
	/** 基金名称 */
	private String jjjc;                           
	
	
	
	public BigDecimal getSno() {
		return sno;
	}
	public void setSno(BigDecimal sno) {
		this.sno = sno;
	}
	public String getXtdm() {
		return xtdm;
	}
	public void setXtdm(String xtdm) {
		this.xtdm = xtdm;
	}
	public BigDecimal getQsje() {
		return qsje;
	}
	public void setQsje(BigDecimal qsje) {
		this.qsje = qsje;
	}
	public BigDecimal getJsje() {
		return jsje;
	}
	public void setJsje(BigDecimal jsje) {
		this.jsje = jsje;
	}
	public String getYjsy() {
		return yjsy;
	}
	public void setYjsy(String yjsy) {
		this.yjsy = yjsy;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public String getModifier() {
		return modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	public String getCredt() {
		return credt;
	}
	public void setCredt(String credt) {
		this.credt = credt;
	}
	public String getModdt() {
		return moddt;
	}
	public void setModdt(String moddt) {
		this.moddt = moddt;
	}
	public String getDataSource() {
		return dataSource;
	}
	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}
	public String getmOptType() {
		return mOptType;
	}
	public void setmOptType(String mOptType) {
		this.mOptType = mOptType;
	}
	public BigDecimal getZlid() {
		return zlid;
	}
	public void setZlid(BigDecimal zlid) {
		this.zlid = zlid;
	}
	public String getCheckflag() {
		return checkflag;
	}
	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}
	public String getChecker() {
		return checker;
	}
	public void setChecker(String checker) {
		this.checker = checker;
	}
	public String getRuleResults() {
		return ruleResults;
	}
	public void setRuleResults(String ruleResults) {
		this.ruleResults = ruleResults;
	}
	public Timestamp getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(Timestamp modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getTzqsr() {
		return tzqsr;
	}
	public void setTzqsr(String tzqsr) {
		this.tzqsr = tzqsr;
	}
	public String getJjdm() {
		return jjdm;
	}
	public void setJjdm(String jjdm) {
		this.jjdm = jjdm;
	}
	public String getYjdqr() {
		return yjdqr;
	}
	public void setYjdqr(String yjdqr) {
		this.yjdqr = yjdqr;
	}
	public String getYjtzqx() {
		return yjtzqx;
	}
	public void setYjtzqx(String yjtzqx) {
		this.yjtzqx = yjtzqx;
	}
	public String getHzf() {
		return hzf;
	}
	public void setHzf(String hzf) {
		this.hzf = hzf;
	}
	public String getSffg() {
		return sffg;
	}
	public void setSffg(String sffg) {
		this.sffg = sffg;
	}
	public String getJjjc() {
		return jjjc;
	}
	public void setJjjc(String jjjc) {
		this.jjjc = jjjc;
	}
}
