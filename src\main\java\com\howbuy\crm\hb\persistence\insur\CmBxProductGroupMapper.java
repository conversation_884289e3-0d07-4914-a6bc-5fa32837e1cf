package com.howbuy.crm.hb.persistence.insur;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.insur.CmBxProductGroup;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxProductGroupMapper {
    
     /**
      * 新增数据对象
      * @param cmBxProductGroup
      */
	void insertCmBxProductGroup(CmBxProductGroup cmBxProductGroup);
	
	/**
	 * 插入
	 * @param param
	 */
	void insertCmBxProductGroupLog(Map<String, String> param);
	
	/**
	 * 单条删除数据对象
	 * @param param
	 */
	void delCmBxProductGroupByFundcode(Map<String, String> param);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxProductGroup> listCmBxProductGroup(Map<String, String> param);

	/**
	 * 列表查询
	 * @param param
	 * @return
	 */
	List<CmBxProductGroup> listCmBxProductGroupMsg(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmBxProductGroupCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmBxProductGroup> listCmBxProductGroupByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
