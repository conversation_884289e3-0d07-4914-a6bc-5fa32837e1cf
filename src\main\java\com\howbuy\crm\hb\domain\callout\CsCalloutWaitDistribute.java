package com.howbuy.crm.hb.domain.callout;

import lombok.Data;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CsCalloutWaitDistribute implements Serializable {
	private static final long serialVersionUID = 122299934030L;
	/** 等待ID */
	private long waitId;
	/** 来源ID */
	private long taskId;
	/** 来源类型 */
	private int taskType;
	/** 开始等待分配时间 */
	private String waitDate;
	/** 处理标识符 */
	private int handleState;
	/** 分配标识符 */
	private int distributeFlag;
	/** 再分配次数 */
	private int disposeNum;
	/** 在分配处理时间 */
	private String disposeDate;
	/** 子来源类型 */
	private int subTaskType;
	/** 预约标识符 */
	private int orderFlag;
	/** 客户姓名 */
	private String custName;
	/** 备注 */
	private String remark;
	/** 预约座席ID */
	private String orderUserId;
	/** 手机号 */
	private String mobile; 
	/** 邮箱 */
	private String email;
	/** 预约数据来源时间 */
	private String sourceDt;
	/** 投顾客户号 */
	private String consCustNo;
	/** 部门编号 */
	private String deptFlag;
	/** 是否发生短信通知 */
	private String custSmsFlag;
	/** 预约内容 */
	private String bookingContent;
	/** 任务详细类型 */
	private String taskTypeDetail;
	/** 发送短信编码 */
	private String cmsNo;
	/** 咨询时间 */
	private String consultDt;
	/** 咨询类型 */
	private String consultNo;
	/** 手动添加客户标识 */
	private int isAdd;
	/** 手机掩码 */
	private String mobileMask;
	/** 手机摘要 */
	private String mobileDigest;
	/** 手机密文 */
	private String mobileCipher;
	/** 邮箱掩码 */
	private String emailMask;
	/** 邮箱摘要 */
	private String emailDigest;
	/** 邮箱密文 */
	private String emailCipher;     

}
