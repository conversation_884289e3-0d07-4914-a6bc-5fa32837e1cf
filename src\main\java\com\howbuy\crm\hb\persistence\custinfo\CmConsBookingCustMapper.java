package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.custinfo.CmConsBookingCust;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;
/**
 * 
 * <AUTHOR>
 *
 */
public interface CmConsBookingCustMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmConsBookingCust getCmConsBookingCust(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmConsBookingCust
      */
	void insertCmConsBookingCust(CmConsBookingCust cmConsBookingCust);
	
	/**
	 * 单条修改数据对象
	 * @param cmConsBookingCust
	 */
	void updateCmConsBookingCust(CmConsBookingCust cmConsBookingCust);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmVisitrecNewest(String id);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmConsBookingCust(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmConsBookingCust(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmConsBookingCust> listCmConsBookingCust(Map<String, String> param);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmConsBookingCust> listCmConsBookingCustExport(Map<String, String> param);
	
	/**
	 * 查询登陆用户对象
	 * @param param
	 * @return
	 */
	String listDeptConsByLoginUser(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmConsBookingCustCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmConsBookingCust> listCmConsBookingCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
