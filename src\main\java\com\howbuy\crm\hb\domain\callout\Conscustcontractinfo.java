package com.howbuy.crm.hb.domain.callout;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 实体类Conscustcontractinfo.java
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class Conscustcontractinfo implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String conscustno;

	private String columntype;

	private String columnvalue;

	private String delfflag;

	private String recstat;

	private String creator;

	private String modifier;

	private String credt;

	private String moddt;

	private String provcode;

	private String citycode;

	private String waitid;

}
