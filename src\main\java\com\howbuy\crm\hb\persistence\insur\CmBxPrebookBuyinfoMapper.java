package com.howbuy.crm.hb.persistence.insur;

import com.howbuy.crm.hb.domain.CountBalancePo;
import com.howbuy.crm.hb.domain.insur.BxBalanceDetail;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookBuyinfo;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookEndpayList;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxPrebookBuyinfoMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxPrebookBuyinfo getCmBxPrebookBuyinfo(Map<String, Object> param);
    
     /**
      * 新增数据对象
      * @param cmBxPrebookBuyinfo
      */
	void insertCmBxPrebookBuyinfo(CmBxPrebookBuyinfo cmBxPrebookBuyinfo);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxPrebookBuyinfo
	 */
	void updateCmBxPrebookBuyinfo(CmBxPrebookBuyinfo cmBxPrebookBuyinfo);
	
	/**
	 * 单条历史修改数据对象
	 * @param cmBxPrebookBuyinfo
	 */
	void updateHisCmBxPrebookBuyinfo(CmBxPrebookBuyinfo cmBxPrebookBuyinfo);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxPrebookBuyinfo> listCmBxPrebookBuyinfo(Map<String, Object> param);

	/**
	 * 更新保单终止状态
	 * @param cmBxPrebookEndpayLists
	 * @return
	 */
	int updateCmBxPrebookBuyinfoListById(List<CmBxPrebookEndpayList> cmBxPrebookEndpayLists);

	/**
	 * 更新人工输入的核算数据
	 * @param param
	 */
	void updateChangeCalDate(Map<String, Object> param);
	
	/**
	 * 置空核算销量相关数据
	 * @param param
	 */
	void updateClearSales(Map<String, Object> param);

	/**
	 * 更新预约购买信息中的汇率字段
	 * @param id
	 * @param rate
	 * @param modifier
	 */
	void updateRate(@Param("id") BigDecimal id, @Param("rate") BigDecimal rate, @Param("modifier") String modifier);

	/**
	 * 更新预约购买信息中的投顾佣金汇率（绩效汇率）字段
	 * @param id
	 * @param rate
	 * @param modifier
	 */
	void updateCommissionRate(@Param("id") BigDecimal id, @Param("rate") BigDecimal rate, @Param("modifier") String modifier);

	/**
	 * 查询一个客户的创新产品持仓
	 * @param conscustno
	 * @return
	 */
	List<BxBalanceDetail> listBxBalance(String conscustno);


	/**
	 * 折标销量汇总查询[按预约Id汇总]
	 * @param preIdList NOT EMPTY
	 * @return
	 */
	List<CountBalancePo> getCollAmkCountInfo(@Param("preIdList") List<List<String>>  preIdList);

	/**
	 * 获取首年的佣金明细信息
	 * @param parambuyinfo
	 * @return
	 */
	CmBxPrebookBuyinfo getCmBxPrebookBuyinfoWithBxCommissionWay(Map<String, Object> parambuyinfo);
}
