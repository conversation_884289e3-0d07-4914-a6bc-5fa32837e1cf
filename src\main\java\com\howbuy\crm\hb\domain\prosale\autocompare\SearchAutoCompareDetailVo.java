package com.howbuy.crm.hb.domain.prosale.autocompare;

import lombok.Data;

/**
 * 预约数据自动核对（详情）-前端查询对象
 * <AUTHOR>
 * @date 2022/10/19 19:58
 */
@Data
public class SearchAutoCompareDetailVo {

    /** 产品代码 */
    private String productCode;
    /** 预约结束日 */
    private String appointEndDtStr;
    /** 开放日 */
    private String openDtStr;
    /** 业务类型 */
    private String convertBusiType;
    /** 产品类别 */
    private String productCategory;
    /** 实际管理人 */
    private String actualMgmtMan;
    /** 核对结果：全部、一致 true、不一致 false */
    private Boolean compareResult;

    /** 是否已经下单（为null则不过滤） */
    private Boolean isOrder;
    /** 打款状态 */
    private String payState;

    // 以下两个条件在hb进行过滤处理
    /** 所属投顾-部门 */
    private String orgCode;
    /** 所属投顾-投顾code */
    private String consCode;
}
