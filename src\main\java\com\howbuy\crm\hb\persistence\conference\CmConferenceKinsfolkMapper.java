package com.howbuy.crm.hb.persistence.conference;

import com.howbuy.crm.hb.domain.conference.CmConferenceCustKinsfolk;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmConferenceKinsfolkMapper {

	/**
	 * 删除
	 * @param kinsfolkid
	 */
    void deleteCmConferenceKinsfolk(String kinsfolkid);

    /**
     * 删除全部
     * @param param
     */
    void deleteAllCmConferenceKinsfolk(Map<String, String> param);

    /**
     * 插入
     * @param cmConferenceConscust
     */
    void insertCmConferenceKinsfolk(CmConferenceCustKinsfolk cmConferenceConscust);

    /**
     * 更新
     * @param cmConferenceConscust
     */
    void updateCmConferenceKinsfolk(CmConferenceCustKinsfolk cmConferenceConscust);

    /**
     * 列表查询
     * @param param
     * @return
     */
    List<CmConferenceCustKinsfolk> listCmConferenceKinsfolk(Map<String, String> param);

    /**
     * 计数
     * @param param
     * @return
     */
    int countCmConferenceKinsfolk(Map<String, String> param);

    /**
     * 查询列表
     * @param param
     * @return
     */
    List<CmConferenceCustKinsfolk> cmConferenceNoKinsfolkList(Map<String, String> param);
    
    /**
     * 更新
     * @param cmConferenceConscust
     */
    void updateCmConferenceKinsfolkByConcustnos(CmConferenceCustKinsfolk cmConferenceConscust);
    
}
