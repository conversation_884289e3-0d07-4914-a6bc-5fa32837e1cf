/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.lcjzsigndata;

import crm.howbuy.base.db.PageAuthVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/7/28 21:54
 * @since JDK 1.8
 */
@Data
public class CmLcjzSignQueryVo extends PageAuthV<PERSON> {
    private String consCode;
    private String conferenceName;
    private String conferenceType;
    private String orgCode;
    private List<String> conferenceTypeName;
    private Date conferenceTime;
    private String isCons;
    private String isCheckIn;
    private String latitude;
    private String beginDt;
    private String endDt;
    private String othertearm;
    private String teamcode;
    private List<String> outletcodes;
}