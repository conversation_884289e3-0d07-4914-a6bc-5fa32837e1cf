package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.custinfo.WebChatUserDto;
import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.custinfo.Conscust;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmConscustOtherMapper {

	/**
	 * 最近分配客户
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Conscust> listNewestAssignCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 最近分配客户
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Conscust> listNewestIncomingCallCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 最近分配客户
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Conscust> listNewestBirthdayCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 最近分配客户
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Conscust> listNewestContactCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 查询所有客户微信
	 * @return
	 */
    List<WebChatUserDto> loadAllWeChatInfoByPage(@Param("param") Map<String, Object> param,@Param("page") CommPageBean pageBean);

	/**
	 * 查询所有客户微信
	 * @return
	 */
	List<WebChatUserDto> loadAllWeChatInfoList(@Param("param")Map<String, String> param);

	/**
	 * 查询全部企业微信添加该客户的投顾
	 * @param externalUserId
	 * @return
	 */
    List<String> getAllFollowCons(String externalUserId);

	/**
	 * 更新单条客户投顾企微关系的状态
	 * @param webChatUserDto
	 * @return
	 */
    int saveUpdateStatus(WebChatUserDto webChatUserDto);
}
