package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;


/**
 * @Description: 实体类ConscustRepeat.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class ConscustRepeat implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustno;

	private String idtype;
	
	private String idnoDigest;
	
	private String custname;
	
	private String mobileDigest;
	
	private String invsttype;
}
