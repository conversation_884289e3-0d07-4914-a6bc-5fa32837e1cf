package com.howbuy.crm.hb.domain.product;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class ProductManagerInfo implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private String pcode;
	private String pname;
	private String hbtype;
	private String publishMan;
	private String managerMan;
	private String orgWay;
	private String saleWay;
	private String capitalDemand;
	private BigDecimal salelimitation;
	private String manLimit;
	private String checkingaddition;
	private BigDecimal deadline;
	private String expectAnnualIncome;
	private String buyFee;
	/** 2013-09-24打款开始时间 */
	private String payStartDt;
	private String payEndDt;
	private String expectFundDt;
	private String appType;
	private String investmentScope;
	private String publishScope;
	private String rateAllocate;
	private String fundmanager;
	private String opendate;
	private String foundDt;
	private String dueDt;
	private String firstCapital;
	private String mainpartner;
	private String isupside;
	private String isimportant;
	private String productState;
	private String saleState;
	private String blockDate;
	private String introduce;
	private String isend;
	private String currency;
	private String investmentArea;
	private String acctType;
	private String publishWay;
	private String investmentType;
	private String plotType;
	private String zuhe;
	private String jgh;
	private String recstat;
	private String creator;
	private String modifier;
	private String credt;
	private String moddt;
	/** 2013-07-25添加产品预约状态 */
	private String prebookState;
	/** 2013-11-07添加是否计算存量 */
	private String isCalBal;
	
	private String jjpy;
	
	private String cpjgh;
	
	/** 2016-06-02 添加包销起始时间 */
	private String uwStartDt;
	/** 2016-06-02 添加包销结束时间 */
	private String uwEndDt;
	
	private String hmfx;
	
	private int id;
	
	/**
	 * 产品类型：1为代销，2为直销，3为直转代
	 */
	private String sfmsjg;
	
	/**
	 * 评级
	 */
	private String pj;
	
	/**
	 * 评级变动
	 */
	private String pjbd;
	
}
