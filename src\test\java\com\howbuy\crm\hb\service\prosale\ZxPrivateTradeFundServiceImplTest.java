package com.howbuy.crm.hb.service.prosale;

import com.howbuy.crm.base.PreBookArchTypeEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.service.prosale.impl.ZxPrivateTradeFundServiceImpl;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.privatetrade.dto.CmCustprivatefund;
import com.howbuy.crm.privatetrade.service.CmCustFundBalanceService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.math.BigDecimal;


/**
 * 单元测试ZxPrivateTradeFundServiceImpl.dealsignQs处理单个客户和产品的强直赎回处理
 * <AUTHOR>
 * 20220511
 */
@PowerMockIgnore("javax.management.*")
@Test
public class ZxPrivateTradeFundServiceImplTest extends PowerMockTestCase {

    @InjectMocks
    private ZxPrivateTradeFundServiceImpl serviceMock;
    
    @Mock
    private CmCustFundBalanceService cmCustFundBalanceServiceMock;

    @Mock
    private PrebookBasicInfoService prebookBasicInfoServiceMock;
    
    @Mock
    private CmPrivatefundtradeAuditService cmPrivatefundtradeAuditServiceMock;
    
    //客户号
    private static String TEST_CONSCUSTNO="TEST_CONSCUSTNO";
    //产品号
    private static String TEST_FUNDCODE="TEST_FUNDCODE";
    //交易日期
    private static String TEST_TRADEDT="TEST_TRADEDT";
    
    private static BigDecimal TEST_NAV=BigDecimal.ZERO;
    
    private static String TEST_USERID ="TEST_USERID";
    
    private static String TEST_ISHAIWAI = "TEST_ISHAIWAI";
    
    private static String TEST_DISCSUMMARY = "TEST_DISCSUMMARY";
    
    

    /**
     * 测试_客户号或产品代码或交易日期或净值为空
     */
    @Test
    public void testEmptyparam() {
       ZxPrivateTradeFundServiceImpl spy = PowerMockito.spy(serviceMock);
       //产品代码传入空的请求   以触发判空校验
       String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ZxPrivateTradeFundServiceImpl.class, "dealsignQs")[0], spy,
    		   TEST_CONSCUSTNO,null,TEST_TRADEDT,TEST_NAV,TEST_USERID,TEST_ISHAIWAI,TEST_DISCSUMMARY);
       Assert.assertTrue(result.contains("参数错误！客户号、产品代码、交易日期、净值必填！"),"参数错误！客户号、产品代码、交易日期、净值必填！");
       
    }

    /**
     * mock 持仓对象
     * @param balanceVol
     * @return
     */
    private CmCustprivatefund getMockFund(BigDecimal balanceVol){
        CmCustprivatefund fund = new CmCustprivatefund();
        fund.setBalancevol(balanceVol);
        return  fund;
    }

    /**
     * 测试_根据客户号和产品代码没有查询到持仓
     */
    @Test
    public void testNotFindFund() {
       ZxPrivateTradeFundServiceImpl spy = PowerMockito.spy(serviceMock);

        //mock根据产品和客户号没有查到持仓的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(null);
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());

       String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ZxPrivateTradeFundServiceImpl.class, "dealsignQs")[0], spy,
    		   TEST_CONSCUSTNO,TEST_FUNDCODE,TEST_TRADEDT,TEST_NAV,TEST_USERID,TEST_ISHAIWAI,TEST_DISCSUMMARY);
       Assert.assertTrue(result.contains("参数错误！没有查询到持仓！"),"参数错误！没有查询到持仓！");
    }
    
    /**
     * 测试_根据客户号和产品代码没有查询到持仓,且持仓份额为0
     */
    @Test
    public void testFundVolZero() {
       ZxPrivateTradeFundServiceImpl spy = PowerMockito.spy(serviceMock);
       //mock查询到的持仓份额为0的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(BigDecimal.ZERO));
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
       String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ZxPrivateTradeFundServiceImpl.class, "dealsignQs")[0], spy,
    		   TEST_CONSCUSTNO,TEST_FUNDCODE,TEST_TRADEDT,TEST_NAV,TEST_USERID,TEST_ISHAIWAI,TEST_DISCSUMMARY);
       Assert.assertTrue(result.contains("持仓份额有问题!"),"持仓份额有问题!");
    }
    /**
     * 测试_根据客户号和产品代码没有查询到持仓,且持仓份额为1
     */
    @Test
    public void testFundVolOne() {
       ZxPrivateTradeFundServiceImpl spy = PowerMockito.spy(serviceMock);
       //mock查询到的持仓份额为1的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(BigDecimal.ONE));
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
       String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ZxPrivateTradeFundServiceImpl.class, "dealsignQs")[0], spy,
    		   TEST_CONSCUSTNO,TEST_FUNDCODE,TEST_TRADEDT,TEST_NAV,TEST_USERID,TEST_ISHAIWAI,TEST_DISCSUMMARY);
       Assert.assertTrue(result.contains("持仓份额有问题!"),"持仓份额有问题!");
    }

    /**
     * 测试拦截海外产品
     */
    @Test
    public void testHwFund() {
        ZxPrivateTradeFundServiceImpl spy = PowerMockito.spy(serviceMock);
        //mock查询到的持仓份额为10的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(BigDecimal.TEN));
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.HW.getCode());
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ZxPrivateTradeFundServiceImpl.class, "dealsignQs")[0], spy,
                TEST_CONSCUSTNO,TEST_FUNDCODE,TEST_TRADEDT,TEST_NAV,TEST_USERID,TEST_ISHAIWAI,TEST_DISCSUMMARY);
        Assert.assertTrue(result.contains("不支持海外产品操作！"),"不支持海外产品操作！");
    }
    
    /**
     * 测试_根据客户号和产品代码没有查询到持仓,且持仓份额大于1
     */
    @Test
    public void testFundVolMoreOne() {
       ZxPrivateTradeFundServiceImpl spy = PowerMockito.spy(serviceMock);
       //mock查询到的持仓份额为10的情况
        PowerMockito.when(cmCustFundBalanceServiceMock.selectCustPrivateBalance(Mockito.any(),Mockito.any())).thenReturn(getMockFund(BigDecimal.TEN));
        //mock非海外产品
        PowerMockito.when(prebookBasicInfoServiceMock.getArchType(Mockito.any(),Mockito.any())).thenReturn(PreBookArchTypeEnum.ZX.getCode());
       //mock插入直销审核成功
       PowerMockito.when(cmPrivatefundtradeAuditServiceMock.insertCmPrivatefundtradeAudit(Mockito.any())).thenReturn(ReturnMessageDto.ok());
       
       String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ZxPrivateTradeFundServiceImpl.class, "dealsignQs")[0], spy,
    		   TEST_CONSCUSTNO,TEST_FUNDCODE,TEST_TRADEDT,TEST_NAV,TEST_USERID,TEST_ISHAIWAI,TEST_DISCSUMMARY);
       Assert.assertTrue(result.contains("success"),"success");
    }
}
