/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

/**
 * @description: (人工修改标识)
 * <AUTHOR>
 * @date 2024/11/4 15:15
 * @since JDK 1.8
 */
public enum HumanModifyEnum {
    HUMAN_NOT_ODIFY("0","人工修改清除"),
    HUMAN_MODIFY("1","人工修改"),
    BEISEN_MODIFY("2","北森修改");

    /**
     * 标识
     */
    private String flag;
    /**
     * 描述
     */
    private String desc;

    HumanModifyEnum(String flag, String desc){
        this.flag = flag;
        this.desc = desc;
    }

    public String getFlag() {
        return flag;
    }

    public String getDesc() {
        return desc;
    }
}