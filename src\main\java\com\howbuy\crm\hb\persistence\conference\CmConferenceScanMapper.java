package com.howbuy.crm.hb.persistence.conference;

import com.howbuy.crm.hb.domain.conference.CmConferenceScan;
import com.howbuy.crm.hb.domain.conference.CmConferenceScanHandleVO;
import com.howbuy.crm.hb.request.ApplyDistributeRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 扫码参会人员
 * <AUTHOR> on 2021/6/17 16:24
 */
public interface CmConferenceScanMapper {


    /**
     * @param scanIds
     * @return java.util.List<com.howbuy.crm.hb.domain.conference.CmConferenceScan>
     * @description:(根据扫码参会表的id数据查询扫码参会的信息)
     * @author: xufanchao
     * @date: 2023/11/6 17:26
     * @since JDK 1.8
     */
    List<CmConferenceScan> listCmConferenceScanByScanIds(@Param("list") List<ApplyDistributeRequest> applyDistributeRequestList);

    /**
     * 根据主键获取单条扫码参会人员记录
     * @param id
     * @return
     */
    CmConferenceScan findCmConferenceScanById(String id);

    /**
     * @description:(根据投顾客户号和会议id查询扫码签到表是否有对应数据)
     * @param conferenceId 会议id
     * @param custNo 投顾客户号
     * @return com.howbuy.crm.hb.domain.conference.CmConferenceScan
     * @author: xufanchao
     * @date: 2023/11/19 00:08
     * @since JDK 1.8
     */
    CmConferenceScan getCmconferenceScanByParams(@Param("conferenceId") String conferenceId, @Param("custNo") String custNo);

    /**
     * @description:(更新数据)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/17 10:12
     * @since JDK 1.8
     */
    int updateByPrimaryKeySelective(CmConferenceScan record);

    /**
     * @description:(根据扫码签到参会的手机号摘要匹配对应的客户信息)
     * @param id
     * @return int
     * @author: xufanchao
     * @date: 2023/12/7 09:32
     * @since JDK 1.8
     */
    int getCmConscustByScanMobile(String id);

    /**
     * @description: 根据主键查询批量处理页面的扫码签到参会人员信息
     * @param ids
     * @return java.util.List<com.howbuy.crm.hb.domain.conference.CmConferenceScanHandleVO>
     * @author: jin.wang03
     * @date: 2025/3/20 11:03
     * @since JDK 1.8
     */
    List<CmConferenceScanHandleVO> listBatchHandleVOByIds(@Param("ids") List<String> ids);
}
