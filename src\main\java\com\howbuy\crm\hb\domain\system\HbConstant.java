package com.howbuy.crm.hb.domain.system;

import java.math.BigDecimal;
import java.util.Date;

/**
    * 系统常量表
    */
public class HbConstant {
    /**
    * ID
    */
    private String id;

    /**
    * 常量类别
    */
    private String typecode;

    /**
    * 类别说明
    */
    private String typedesc;

    /**
    * 编码说明
    */
    private String codedesc;

    /**
    * 常量编码
    */
    private String constcode;

    /**
    * 常量描述
    */
    private String constdesc;

    /**
    * 层次(排序)
    */
    private BigDecimal constlevel;

    /**
    * 是否有效
    */
    private String isvalid;

    /**
    * 别名
    */
    private String constalias;

    /**
    * 扩展1
    */
    private String constext1;

    /**
    * 扩展2
    */
    private String constext2;

    /**
    * 扩展3
    */
    private String constext3;

    /**
    * 创建者
    */
    private String creator;

    /**
    * 修改者
    */
    private String modifier;

    /**
    * 审核者
    */
    private String checker;

    /**
    * 创建时间
    */
    private Date credate;

    /**
    * 修改时间
    */
    private Date moddate;

    /**
    * 审核时间
    */
    private Date checkdate;

    /**
    * 变动标志
    */
    private String updateid;

    /**
    * 来源标志
    */
    private String resourceid;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTypecode() {
        return typecode;
    }

    public void setTypecode(String typecode) {
        this.typecode = typecode;
    }

    public String getTypedesc() {
        return typedesc;
    }

    public void setTypedesc(String typedesc) {
        this.typedesc = typedesc;
    }

    public String getCodedesc() {
        return codedesc;
    }

    public void setCodedesc(String codedesc) {
        this.codedesc = codedesc;
    }

    public String getConstcode() {
        return constcode;
    }

    public void setConstcode(String constcode) {
        this.constcode = constcode;
    }

    public String getConstdesc() {
        return constdesc;
    }

    public void setConstdesc(String constdesc) {
        this.constdesc = constdesc;
    }

    public BigDecimal getConstlevel() {
        return constlevel;
    }

    public void setConstlevel(BigDecimal constlevel) {
        this.constlevel = constlevel;
    }

    public String getIsvalid() {
        return isvalid;
    }

    public void setIsvalid(String isvalid) {
        this.isvalid = isvalid;
    }

    public String getConstalias() {
        return constalias;
    }

    public void setConstalias(String constalias) {
        this.constalias = constalias;
    }

    public String getConstext1() {
        return constext1;
    }

    public void setConstext1(String constext1) {
        this.constext1 = constext1;
    }

    public String getConstext2() {
        return constext2;
    }

    public void setConstext2(String constext2) {
        this.constext2 = constext2;
    }

    public String getConstext3() {
        return constext3;
    }

    public void setConstext3(String constext3) {
        this.constext3 = constext3;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public Date getCredate() {
        return credate;
    }

    public void setCredate(Date credate) {
        this.credate = credate;
    }

    public Date getModdate() {
        return moddate;
    }

    public void setModdate(Date moddate) {
        this.moddate = moddate;
    }

    public Date getCheckdate() {
        return checkdate;
    }

    public void setCheckdate(Date checkdate) {
        this.checkdate = checkdate;
    }

    public String getUpdateid() {
        return updateid;
    }

    public void setUpdateid(String updateid) {
        this.updateid = updateid;
    }

    public String getResourceid() {
        return resourceid;
    }

    public void setResourceid(String resourceid) {
        this.resourceid = resourceid;
    }
}