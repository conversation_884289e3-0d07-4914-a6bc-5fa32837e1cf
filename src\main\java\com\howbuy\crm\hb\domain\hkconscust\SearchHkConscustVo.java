package com.howbuy.crm.hb.domain.hkconscust;

import com.howbuy.crm.hb.domain.insur.PageVo;
import lombok.Data;

import java.io.Serializable;

/**
 * 香港客户维护表-前端查询对象
 * <AUTHOR>
 * @date 2022/4/20 10:06
 */
@Data
public class SearchHkConscustVo extends PageVo implements Serializable {

    private static final long serialVersionUID = 5798714053999453264L;

    /** 客户姓名 */
    private String custname;

    /** 投顾客户号 */
    private String conscustno;

    /** 客户香港id */
    private String hkcustid;

    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;
}
