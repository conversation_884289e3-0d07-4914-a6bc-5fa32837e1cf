/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/24 15:52
 * @since JDK 1.8
 */
public enum GiftOperationSourceEnum {

    /**
     * 用户创建
     */
    CUST_CREATE("1", "用户创建", "用户小程序选礼"),

    /**
     * 用户修改
     */
    CUST_UPDATE("2", "用户修改", "用户小程序修改"),

    /**
     * 投顾修改
     */
    CONS_UPDATE("3", "投顾修改", "CRM修改礼品"),
    ;

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    /**
     * CRM用于页面展示的描述
     */
    private String crmDescription;


    GiftOperationSourceEnum(String code, String description, String crmDescription) {
        this.code = code;
        this.description = description;
        this.crmDescription = crmDescription;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCrmDescription() {
        return crmDescription;
    }

    public void setCrmDescription(String crmDescription) {
        this.crmDescription = crmDescription;
    }

    /**
     * @param code
     * @return java.lang.String
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2024/9/24 15:56
     * @since JDK 1.8
     */
    public static String getDescription(String code) {
        for (GiftOperationSourceEnum signEnum : GiftOperationSourceEnum.values()) {
            if (signEnum.getCode().equals(code)) {
                return signEnum.getDescription();
            }
        }
        return null;
    }

    /**
     * @description:(请在此添加描述)
     * @param code
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/9/24 17:00
     * @since JDK 1.8
     */
    public static String getCrmDescription(String code) {
        for (GiftOperationSourceEnum signEnum : GiftOperationSourceEnum.values()) {
            if (signEnum.getCode().equals(code)) {
                return signEnum.getCrmDescription();
            }
        }
        return null;
    }

}
