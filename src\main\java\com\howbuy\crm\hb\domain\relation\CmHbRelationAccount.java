package com.howbuy.crm.hb.domain.relation;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * CM_RELATION_ACCOUNT
 * <AUTHOR>
@Data
public class CmHbRelationAccount implements Serializable {
    /**
     * 一账通
     */
    private String hboneno;

    /**
     * 关联账户名称
     */
    private String relationname;

    /**
     * 1家庭账户、2个人关联账户、3机构关联账户
     */
    private String relationtype;

    /**
     * 1家庭账户、2个人关联账户、3机构关联账户
     */
    private String relationtypestr;

    /**
     * 关联账户ID 外键
     */
    private String relationid;

    /**
     * 资料订单ID 外键
     */
    private String orderid;

    /**
     * 创建时间
     */
    private Date creatdt;

    /**
     * 创建人
     */
    private String creater;

    /**
     * 接收时间
     */
    private Date syncdt;

    /**
     * 数据状态 账户中心状态 生效，解除； CRM状态：失效，作废
     */
    private String relationstate;

    /**
     * 数据状态 账户中心状态 生效，解除； CRM状态：失效，作废
     */
    private String relationstatestr;

    /**
     * 渠道
     */
    private String chanel;

    /**
     * 投顾客户号
     */
    private String conscustno;

    /**
     * 投顾客户
     */
    private String custname;

    /**
     * 投顾编码
     */
    private String conscode;

    /**
     * 投顾姓名
     */
    private String consname;

    /**
     * 投顾部门
     */
    private String orgcode;

    private static final long serialVersionUID = 1L;
}