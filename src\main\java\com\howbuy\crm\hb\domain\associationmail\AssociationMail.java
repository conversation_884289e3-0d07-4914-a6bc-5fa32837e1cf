package com.howbuy.crm.hb.domain.associationmail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 协会邮件
 * <AUTHOR> on 2021/4/2 14:18
 */
@Data
public class AssociationMail implements Serializable {

    /** 主键 */
    private String id;

    /** 邮件日期 */
    private Date mailDate;

    /** 邮件日期（字符串格式） */
    private String mailDateStr;

    /** 来源邮箱 */
    private String sourceMail;

    /** 投顾客户代码 */
    private String conscustno;

    /** 客户名称 */
    private String custName;

    /** 机构名称 */
    private String orgName;

    /** 投资者账号 */
    private String investorAccount;

    /** 初始密码 */
    private String initPassword;

    /** 管理人登记编码 */
    private String managerRegno;

    /** 登录链接 */
    private String loginHref;

    /** 创建人 */
    private String creator;

    /** 记录创建日期 */
    private String credt;

    /** 修改人 */
    private String modifier;

    /** 修改日期 */
    private String moddt;

    /** 邮件主题 */
    private String subject;

    /** 收件人 */
    private String toMail;

    /** 删除标志（0 正常；1 删除） */
    private String delFlag;

    /** 邮件uid（线上邮件的唯一标识） */
    private String mailUid;

    /** 信息状态（0 待匹配；1 待发短信；2 待重发短信；3 已发短信；4 短信发送失败） */
    private String infoStatus;

    /** 备注 */
    private String remark;

    // 以下是关联出来的字段
    /** 一账通号 */
    private String hboneNo;

    /** 投顾号 */
    private String consCode;
    /** 投顾名称 */
    private String consName;
    /** 投顾部门 */
    private String consDeptName;
    /** 投顾区域 */
    private String consRegionName;
    /** 投顾中心 */
    private String consCenterName;

    /** 异常表的记录id */
    private String expRecordId;
}
