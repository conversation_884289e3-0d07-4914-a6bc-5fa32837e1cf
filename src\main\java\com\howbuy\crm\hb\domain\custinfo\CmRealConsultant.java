package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CmRealConsultant {
    /**
     * 投顾客户编号
     */
    private String conscustno;

    /**
     * 一账通账号
     */
    private String hboneNo;

    /**
     * 客户姓名
     */
    private String custname;

    /**
     * 名义投顾代码
     */
    private String conscode;

    /**
     * 实际投顾代码
     */
    private String realConscode;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 修改人
     */
    private String modiferuser;

    /**
     * 修改时间
     */
    private Date modiferTime;
}

