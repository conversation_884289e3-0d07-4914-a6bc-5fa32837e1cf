package com.howbuy.crm.hb.domain.conference;

import java.io.Serializable;

/**
 * (路演会议属性表)
 * <AUTHOR>
 * @ClassName: CmConferenceTheme
 * 											  
 * 创建日期    		                            修改人员   	        版本	 	     修改内容  
 * -------------------------------------------------  
 * 2017年8月14日 下午4:48:00        1.0    	初始化创建
 *
 * 修改记录:
 * @since		JDK1.6
 */
public class CmConferenceTheme implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 会议ID(外键) */
	private String conferenceid;
	
	/** 会议属性：1-单产品 2-管理人 3-产品线 */
	private String themetype;
	
	/** 会议属性编码：单产品对应产品代码，管理人对应公司编码，产品线对应产品线代码 */
	private String themetypevalue;
	
	/** 创建日期 */
	private String creatdt;
	
	/** 创建人 */
	private String creater;
	
	/** 修改日期 */
	private String modifydt;
	
	/** 修改人 */
	private String modifier;

	public String getConferenceid() {
		return conferenceid;
	}

	public void setConferenceid(String conferenceid) {
		this.conferenceid = conferenceid;
	}

	public String getThemetype() {
		return themetype;
	}

	public void setThemetype(String themetype) {
		this.themetype = themetype;
	}

	public String getThemetypevalue() {
		return themetypevalue;
	}

	public void setThemetypevalue(String themetypevalue) {
		this.themetypevalue = themetypevalue;
	}

	public String getCreatdt() {
		return creatdt;
	}

	public void setCreatdt(String creatdt) {
		this.creatdt = creatdt;
	}

	public String getCreater() {
		return creater;
	}

	public void setCreater(String creater) {
		this.creater = creater;
	}

	public String getModifydt() {
		return modifydt;
	}

	public void setModifydt(String modifydt) {
		this.modifydt = modifydt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

}
