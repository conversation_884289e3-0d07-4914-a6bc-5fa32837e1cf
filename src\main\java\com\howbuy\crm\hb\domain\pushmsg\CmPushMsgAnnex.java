package com.howbuy.crm.hb.domain.pushmsg;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmPushMsgAnnex implements Serializable {

	private static final long serialVersionUID = 1L;
	private String id;
	private String pushid;
	private String contenttype;
	private Integer fieldnum;
	private String conscode;
	private String field1;
	private String field2;
	private String field3;
	private String field4;
	private String field5;
	private String field6;
	private String field7;
	private String field8;
	private String field9;
	private String field10;
	private String field11;
	private String field12;
	private String field13;
	private String field14;
	private String field15;
	private String field16;
	private String field17;
	private String field18;
	private String field19;
	private String field20;
	private String field21;
	private String field22;
	private String field23;
	private String field24;
	private String field25;
	private String field26;
	private String field27;
	private String field28;
	private String field29;
	private String field30;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getPushid() {
		return pushid;
	}
	public void setPushid(String pushid) {
		this.pushid = pushid;
	}
	public String getContenttype() {
		return contenttype;
	}
	public void setContenttype(String contenttype) {
		this.contenttype = contenttype;
	}
	public Integer getFieldnum() {
		return fieldnum;
	}
	public void setFieldnum(Integer fieldnum) {
		this.fieldnum = fieldnum;
	}
	public String getConscode() {
		return conscode;
	}
	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	public String getField1() {
		return field1;
	}
	public void setField1(String field1) {
		this.field1 = field1;
	}
	public String getField2() {
		return field2;
	}
	public void setField2(String field2) {
		this.field2 = field2;
	}
	public String getField3() {
		return field3;
	}
	public void setField3(String field3) {
		this.field3 = field3;
	}
	public String getField4() {
		return field4;
	}
	public void setField4(String field4) {
		this.field4 = field4;
	}
	public String getField5() {
		return field5;
	}
	public void setField5(String field5) {
		this.field5 = field5;
	}
	public String getField6() {
		return field6;
	}
	public void setField6(String field6) {
		this.field6 = field6;
	}
	public String getField7() {
		return field7;
	}
	public void setField7(String field7) {
		this.field7 = field7;
	}
	public String getField8() {
		return field8;
	}
	public void setField8(String field8) {
		this.field8 = field8;
	}
	public String getField9() {
		return field9;
	}
	public void setField9(String field9) {
		this.field9 = field9;
	}
	public String getField10() {
		return field10;
	}
	public void setField10(String field10) {
		this.field10 = field10;
	}
	public String getField11() {
		return field11;
	}
	public void setField11(String field11) {
		this.field11 = field11;
	}
	public String getField12() {
		return field12;
	}
	public void setField12(String field12) {
		this.field12 = field12;
	}
	public String getField13() {
		return field13;
	}
	public void setField13(String field13) {
		this.field13 = field13;
	}
	public String getField14() {
		return field14;
	}
	public void setField14(String field14) {
		this.field14 = field14;
	}
	public String getField15() {
		return field15;
	}
	public void setField15(String field15) {
		this.field15 = field15;
	}
	public String getField16() {
		return field16;
	}
	public void setField16(String field16) {
		this.field16 = field16;
	}
	public String getField17() {
		return field17;
	}
	public void setField17(String field17) {
		this.field17 = field17;
	}
	public String getField18() {
		return field18;
	}
	public void setField18(String field18) {
		this.field18 = field18;
	}
	public String getField19() {
		return field19;
	}
	public void setField19(String field19) {
		this.field19 = field19;
	}
	public String getField20() {
		return field20;
	}
	public void setField20(String field20) {
		this.field20 = field20;
	}
	public String getField21() {
		return field21;
	}
	public void setField21(String field21) {
		this.field21 = field21;
	}
	public String getField22() {
		return field22;
	}
	public void setField22(String field22) {
		this.field22 = field22;
	}
	public String getField23() {
		return field23;
	}
	public void setField23(String field23) {
		this.field23 = field23;
	}
	public String getField24() {
		return field24;
	}
	public void setField24(String field24) {
		this.field24 = field24;
	}
	public String getField25() {
		return field25;
	}
	public void setField25(String field25) {
		this.field25 = field25;
	}
	public String getField26() {
		return field26;
	}
	public void setField26(String field26) {
		this.field26 = field26;
	}
	public String getField27() {
		return field27;
	}
	public void setField27(String field27) {
		this.field27 = field27;
	}
	public String getField28() {
		return field28;
	}
	public void setField28(String field28) {
		this.field28 = field28;
	}
	public String getField29() {
		return field29;
	}
	public void setField29(String field29) {
		this.field29 = field29;
	}
	public String getField30() {
		return field30;
	}
	public void setField30(String field30) {
		this.field30 = field30;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public Date getCreatdt() {
		return creatdt;
	}
	public void setCreatdt(Date creatdt) {
		this.creatdt = creatdt;
	}
	public String getModifier() {
		return modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	public Date getModifydt() {
		return modifydt;
	}
	public void setModifydt(Date modifydt) {
		this.modifydt = modifydt;
	}
	
}
