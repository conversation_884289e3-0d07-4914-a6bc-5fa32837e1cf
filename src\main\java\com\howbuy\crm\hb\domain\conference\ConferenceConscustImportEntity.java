package com.howbuy.crm.hb.domain.conference;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 导入参会实体
 * @date 2022/10/10 17:47
 */
@Getter
@Setter
@EqualsAndHashCode
public class ConferenceConscustImportEntity {

    /**
     * 会议ID
     */
    @ExcelProperty(value = "会议ID", index = 0)
    private String conferenceId;

    /**
     * 客户号
     */
    @ExcelProperty(value = "客户号", index = 1)
    private String conscustno;

    /**
     * 报名时客户状态
     */
    @ExcelProperty(value = "报名时客户状态", index = 2)
    private String custStatus;

    /**
     * 预约参会人数
     */
    @ExcelProperty(value = "预约参会人数", index = 3)
    private String appointNum;

    /**
     * 实到人数
     */
    @ExcelProperty(value = "实到人数", index = 4)
    private String actualNum;

    /**
     * 签到时间
     */
    @ExcelProperty(value = "签到时间", index = 5)
    private String signDate;
}
