package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.custinfo.CmCustfamily;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmCustfamilyMapper {

	/**
	 * 判断是否是家庭账户的成员
	 * @param param
	 * @return
	 */
	int getIsHasFamilyCustCount(Map<String, String> param);
	
	/**
	 * 得到单个数据对象
	 * @param param
	 * @return
	 */
    CmCustfamily getCmCustfamily(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmCustfamily
      */
	void insertCmCustfamily(CmCustfamily cmCustfamily);
	
	/**
	 * 单条修改数据对象
	 * @param cmCustfamily
	 */
	void updateCmCustfamily(CmCustfamily cmCustfamily);
	
	/**
	 * update
	 * @param param
	 */
	void updateCmCustfamilyCheckFlag(Map<String, String> param);

	/**
	 * insert
	 * @param conscustno
	 */
	void insertCmCustfamilyhis(String conscustno);
	/**
	 * 单条删除数据对象
	 * @param conscustno
	 */
	void delCmCustfamily(String conscustno);

	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmCustfamily(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmCustfamily> listCmCustfamily(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmCustfamilyCount(Map<String, String> param);
	
	/**
	 * 查询新增的家庭账号成员是否在其他家庭账号中出现过
	 * @param param
	 * @return
	 */
	int getCmCustfamilyCountByCusts(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmCustfamily> listCmCustfamilyByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 根据主账号查询所有辅账号信息
	 * @param param
	 * @return
	 */
	List<CmCustfamily> listCmCustfamilySubByMasterCust(Map<String, String> param);
	
	/**
	 * 根据主账号查询所有账号信息
	 * @param param
	 * @return
	 */
	List<CmCustfamily> listCmCustfamilyAllByMasterCust(Map<String, String> param);
	
	/**
	 * 根据家庭成员查询所有账号信息
	 * @param param
	 * @return
	 */
	List<CmCustfamily> listCmCustfamilyAllByAllCust(Map<String, String> param);
	
	/**
	 * 根据家庭成员查询所有账号信息
	 * @param param
	 * @return
	 */
	List<CmCustfamily> listCmCustfamilyAllByCust(Map<String, String> param);
	
	/**
	 * 根据主账号查询多有家庭账户成员的投顾个数
	 * @param param
	 * @return
	 */
	int getDistinctConscodeNumByMastercode(Map<String, String> param);
	
	
	/**
	 * 根据投顾code获取其下所有的家庭账户
	 * @param param
	 * @return
	 */
	List<Map<String,String>> listConsFamilyByConscodeMap(Map<String, String> param);

}
