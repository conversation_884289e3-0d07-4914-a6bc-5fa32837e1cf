package com.howbuy.crm.hb.enums;
/**
 * 创新产品   创新方案 枚举类
 * <AUTHOR>
 * 方案一1   方案二2
 */
public enum BxCommissionWayEnum {

	/**
	 * 方案一
	 */
	BXCOMMISSIONWAY_ONE("1", "方案一"),
	/**
	 * 方案二
	 */
	BXCOMMISSIONWAY_TWO("2", "方案二");
	/**
	 * 编码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String description;

	private BxCommissionWayEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得字段描述
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		BxCommissionWayEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}


	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return ChartTypeEnum
	 */
	public static BxCommissionWayEnum getEnum(String code) {
		for(BxCommissionWayEnum statusEnum : BxCommissionWayEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
