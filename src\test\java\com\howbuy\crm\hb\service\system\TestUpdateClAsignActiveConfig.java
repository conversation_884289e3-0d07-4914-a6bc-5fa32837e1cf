//package com.howbuy.crm.hb.service.system;
//
//import com.howbuy.crm.hb.domain.system.ClAsignActiveConfig;
//import com.howbuy.crm.hb.persistence.common.CommonMapper;
//import com.howbuy.crm.hb.persistence.system.ClAsignActiveConfigMapper;
//import com.howbuy.crm.hb.service.system.impl.ClAsignActiveConfigServiceImpl;
//
//import crm.howbuy.base.constants.StaticVar;
//
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.api.support.membermodification.MemberModifier;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.testng.PowerMockTestCase;
//import org.springframework.util.ReflectionUtils;
//import org.testng.Assert;
//import org.testng.annotations.Test;
//
///**
// * @description: 单元测试:更新存量激活配置的更新判断
// * @author: haibo.yu
// * @create: 2023/01/09 10:35
// * @since: JDK 1.8
// */
//@PowerMockIgnore("javax.management.*")
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({ClAsignActiveConfigServiceImpl.class})
//public class TestUpdateClAsignActiveConfig extends PowerMockTestCase {
//    @Mock
//    private ClAsignActiveConfigMapper mockMapper;
//
//    @Mock
//    private CommonMapper mockCommonMapper;
//
//    @InjectMocks
//    private ClAsignActiveConfigServiceImpl serviceMock;
//
//    /**
//     * 类型等于接管，存在同一管理层、同一投顾的数据
//     */
//    @Test
//    public void test01() throws Exception {
//    	ClAsignActiveConfigServiceImpl spy = PowerMockito.spy(serviceMock);
//        // mock存量激活配置对象
//    	ClAsignActiveConfig clAsignActiveConfig = new ClAsignActiveConfig();
//    	clAsignActiveConfig.setType(StaticVar.CLCONFIG_TYPE_JG);
//    	//mock查询接管的同一管理层、同一投顾存在数据
//        PowerMockito.when(mockMapper.selectOverJgCount(Mockito.any())).thenReturn(1);
//        // mock目标方法
//        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ClAsignActiveConfigServiceImpl.class, "updateClAsignActiveConfig")[0], spy,
//        		clAsignActiveConfig);
//        Assert.assertTrue(result.contains("存在重复数据，请在原数据上进行修改！"),"存在重复数据，请在原数据上进行修改！");
//    }
//    /**
//     * 类型等于接管，不存在同一管理层、同一投顾的数据
//     */
//    @Test
//    public void test02() throws Exception {
//    	ClAsignActiveConfigServiceImpl spy = PowerMockito.spy(serviceMock);
//        // mock存量激活配置对象
//    	ClAsignActiveConfig clAsignActiveConfig = new ClAsignActiveConfig();
//    	clAsignActiveConfig.setType(StaticVar.CLCONFIG_TYPE_JG);
//    	//mock查询接管的同一管理层、同一投顾存在数据
//        PowerMockito.when(mockMapper.selectOverJgCount(Mockito.any())).thenReturn(0);
//        //mock插入更新删除操作
//        PowerMockito.when(mockCommonMapper.getSeqValue(Mockito.any())).thenReturn("1");
//        PowerMockito.doNothing().when(mockMapper).updateClAsignActiveConfig(Mockito.any());
//        PowerMockito.doNothing().when(mockMapper).delClAsignActiveCustno(Mockito.any());
//        PowerMockito.doNothing().when(mockMapper).insertClAsignActiveCustno(Mockito.any());
//        // mock目标方法
//        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ClAsignActiveConfigServiceImpl.class, "updateClAsignActiveConfig")[0], spy,
//        		clAsignActiveConfig);
//        Assert.assertTrue(result.contains("success"),"success");
//    }
//
//    /**
//     * 类型等于非接管，存在重叠日期
//     */
//    @Test
//    public void test03() throws Exception {
//    	ClAsignActiveConfigServiceImpl spy = PowerMockito.spy(serviceMock);
//        // mock存量激活配置对象
//    	ClAsignActiveConfig clAsignActiveConfig = new ClAsignActiveConfig();
//    	clAsignActiveConfig.setType(StaticVar.CLCONFIG_TYPE_YC);
//    	//mock查询非接管，存在重叠日期
//        PowerMockito.when(mockMapper.selectOverOtherCount(Mockito.any())).thenReturn(1);
//        // mock目标方法
//        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ClAsignActiveConfigServiceImpl.class, "updateClAsignActiveConfig")[0], spy,
//        		clAsignActiveConfig);
//        Assert.assertTrue(result.contains("存在计算日期重叠的数据！"),"存在计算日期重叠的数据！");
//    }
//
//    /**
//     * 类型等于非接管，不存在重叠日期
//     */
//    @Test
//    public void test04() throws Exception {
//    	ClAsignActiveConfigServiceImpl spy = PowerMockito.spy(serviceMock);
//        // mock存量激活配置对象
//    	ClAsignActiveConfig clAsignActiveConfig = new ClAsignActiveConfig();
//    	clAsignActiveConfig.setType(StaticVar.CLCONFIG_TYPE_YC);
//    	//mock查询非接管，存在重叠日期
//        PowerMockito.when(mockMapper.selectOverOtherCount(Mockito.any())).thenReturn(0);
//        //mock插入更新删除操作
//        PowerMockito.when(mockCommonMapper.getSeqValue(Mockito.any())).thenReturn("1");
//        PowerMockito.doNothing().when(mockMapper).updateClAsignActiveConfig(Mockito.any());
//        PowerMockito.doNothing().when(mockMapper).delClAsignActiveCustno(Mockito.any());
//        PowerMockito.doNothing().when(mockMapper).insertClAsignActiveCustno(Mockito.any());
//        // mock目标方法
//        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(ClAsignActiveConfigServiceImpl.class, "updateClAsignActiveConfig")[0], spy,
//        		clAsignActiveConfig);
//        Assert.assertTrue(result.contains("success"),"success");
//    }
//
//}