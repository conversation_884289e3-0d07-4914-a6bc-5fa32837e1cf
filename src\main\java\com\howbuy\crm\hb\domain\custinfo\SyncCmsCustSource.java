package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> on 2021/11/2 14:43
 */
@Data
public class SyncCmsCustSource implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 一账通号 */
    private String hboneno;

    /** 分享人一账通号 */
    private String shareHboneno;

    /** 分享人是否投顾（0 否；1 是） share_tg没有值的话，默认为“是” */
    private String shareTg;
    private String shareTgName;

    /** 注册/登录时间 */
    private String regTime;

    /** 0=登录  1=注册 */
    private String regOrLogin;
    private String regOrLoginName;

    /** 同步时间 */
    private String syncDate;

    /** 来源分类 */
    private String sourceType;

    /** 来源页面 */
    private String sourceUrl;

    /** 来源标题 */
    private String sourceTitle;

    // ---------------------以下是关联出来的字段---------------------
    /** 投顾客户号 */
    private String conscustno;

    /** 客户姓名 */
    private String custname;

    /** 分享人投顾客户号 */
    private String shareConscustno;

    /** 分享人姓名 */
    private String shareCustname;

    /** 分享人相关投顾的投顾号 */
    private String shareRelateConscode;

    /** 分享人相关投顾的投顾名称 */
    private String shareRelateConsname;

    /** 分享人所属区域 */
    private String shareUporgname;

    /** 分享人所属部门 */
    private String shareOrgname;

    /**
     *分享人所属中心
     */
    private String shareCenterOrgCode;

    /**
     * 分享人所属中心名称
     */
    private String shareCenterOrgName;
}
