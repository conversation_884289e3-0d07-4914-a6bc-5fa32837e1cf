package com.howbuy.crm.hb.persistence.callout;


import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface TeleAuthorityMapper {

	/**
	 * 查询列表
	 * @param orgcode
	 * @return
	 */
    List<Map<String, String>> listHbOrganizationTree(String orgcode);

    /**
     * 小组查询
     * @param teamCode
     * @return
     */
    List<Map<String, String>> listConsultantTeleNoByTeamCode(String teamCode);

    /**
     * 机构查询
     * @param paramMap
     * @return
     */
    List<Map<String, String>> listConsultantTeleNoByOuletCode(Map<String, Object> paramMap);

    /**
     * 未分配组查询
     * @param ouleteCode
     * @return
     */
    List<Map<String, String>> listConsultantTeleNoByOtherTeam(String ouleteCode);


}
