## 测试的类
> com.howbuy.crm.hb.service.prosale.impl.CustprivatefundServiceImpl
## 测试的方法
> checktrade(UpLoadPrivateTrade vo)

## 分支伪代码
```java
if (没有持仓且交易类型是非强增情况) {
    返回，提示"客户不持有产品"
} 
if (有持仓且非强增且持仓份额小于等于1情况) {
   返回，提示"客户持仓份额小于1"
} 
if (有持仓且强制调减的情况且当前持仓份额不够调减) {
   返回，提示"持仓份额不够强制调减份额"
}
if (有持仓且强制赎回的情况且当前持仓份额不够赎回) {
   返回，提示"持仓份额不够强制赎回份额"
}
```

## 测试案例
##### 1、testQzNotfund：没有持仓且交易类型是强增情况
##### 2、testNotQzNotfund：没有持仓且交易类型是非强增情况
##### 3、testNotQzVolLtOne：有持仓且非强增且持仓份额小于等于1情况
##### 4、testDumpNotEnoughVol：有持仓且强制调减的情况且当前持仓份额不够调减
##### 5、testDumpEnoughVol：有持仓且强制调减的情况且当前持仓份额够调减
##### 6、testQsNotEnoughVol：有持仓且强制赎回的情况且当前持仓份额不够赎回
##### 7、testQsEnoughVol：有持仓且强制赎回的情况且当前持仓份额够赎回