<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.CmHmcBlacklistMapper">
	<insert id="insertCmHmcBlacklist" parameterType="com.howbuy.crm.hb.domain.system.CmHmcBlacklist">
        INSERT INTO CM_HMC_BLACKLIST (
       <trim suffix="" suffixOverrides=",">   
                <if test="id != null"> id, </if>
                <if test="conscode != null"> conscode, </if>
				<if test="creator != null"> creator, </if>
				<if test="credate != null"> credate, </if>
				<if test="modifier != null"> modifier, </if>
				<if test="moddate != null"> moddate, </if>
				<if test="stat !=null"> stat, </if>
        </trim>
           ) values (
        <trim suffix="" suffixOverrides=",">
                <if test="id != null"> #{id}, </if>
				<if test="conscode != null"> #{conscode}, </if>
				<if test="creator != null"> #{creator}, </if>
				<if test="credate != null"> #{credate}, </if>
				<if test="modifier != null"> #{modifier}, </if>
				<if test="moddate != null"> #{moddate}, </if>
				<if test="stat != null"> #{stat}, </if>
        </trim>  
         )
    </insert>
    
    <update id="updateCmHmcBlacklist" parameterType="com.howbuy.crm.hb.domain.system.CmHmcBlacklist">
        UPDATE CM_HMC_BLACKLIST	  
    	<set>
            <if test="id != null"> id = #{id}, </if>             
            <if test="conscode != null"> conscode = #{conscode}, </if>             
            <if test="creator != null"> creator = #{creator}, </if>             
            <if test="credate != null"> credate = #{credate}, </if>             
            <if test="modifier != null"> modifier = #{modifier}, </if>             
            <if test="moddate != null"> moddate = #{moddate}, </if> 
            <if test="stat != null">  stat = #{stat}, </if>             
         </set>
          where 
          <if test="id != null"> id = #{id} </if>    
          <if test="ids != null"> id in ${ids} </if> 
    </update>
    
    <select id="getCmHmcBlacklist" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmHmcBlacklist" useCache="false">
	  	SELECT id,conscode,creator,credate,modifier,moddate,stat
		  FROM CM_HMC_BLACKLIST
		  where 1=1 
		  <if test="id != null"> and id = #{id} </if> 
		  <if test="stat != null"> and stat = #{stat} </if>
	  </select>
	  
	  <select id="listCmHmcBlacklist" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmHmcBlacklist" useCache="false">
	  	SELECT id,conscode,creator,credate,modifier,moddate,stat
		  FROM CM_HMC_BLACKLIST
		  where 1=1 
		    <if test="id != null"> and id = #{id} </if> 
		    <if test="stat != null"> and stat = #{stat} </if>
		  order by credate desc,moddate desc
	  </select>
	  
	  <select id="listCmHmcBlacklistByPage" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmHmcBlacklist" useCache="false">
	  	SELECT t1.id,t1.conscode,t2.consname,t1.creator,t1.credate,t1.modifier,t1.moddate,t1.stat
		  FROM CM_HMC_BLACKLIST t1
		  left join CM_CONSULTANT t2
		  on t1.conscode = t2.conscode
		  where 1=1 
		    <if test="param.id != null"> and t1.id = #{param.id} </if> 
		    <if test="param.conscode != null"> and t1.conscode like '%' || #{param.conscode} || '%'</if> 
		    <if test="param.stat != null"> and t1.stat = #{param.stat} </if>
		    <if test="param.consname != null"> and t2.consname like '%' || #{param.consname} || '%' </if> 
		    <if test="param.orgcode != null"> 
		     	and ( T2.TEAMCODE = #{param.orgcode} OR
		     		T2.OUTLETCODE in (
		     			SELECT ORGCODE
						  FROM HB_ORGANIZATION HO
						 WHERE HO.STATUS = '0'
						 START WITH HO.ORGCODE = #{param.orgcode}
						CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		     		)
		     	)
		     </if>
		  order by t1.credate desc,t1.moddate desc 
	  </select>
	  
	  
	  
	  <insert id="insertBatchCmHmcBlacklist" parameterType="java.util.List">
        DECLARE
        seq_val NUMBER;
        counts  INTEGER;
        BEGIN
        <foreach collection="list" item="item" index="index" separator=";">
            select count(1) into counts from CM_HMC_BLACKLIST where conscode = #{item.conscode} and stat = '1';
            IF counts = 0 THEN
            execute immediate 'select SEQ_CM_HMC_BLACKLIST_ID.nextval from dual' into seq_val;

            insert into CM_HMC_BLACKLIST (ID, CONSCODE, creator, credate, stat)
            values (seq_val, #{item.conscode}, #{item.creator}, #{item.credate}, #{item.stat});
            END IF
        </foreach>
        ; END ;
    </insert>
</mapper>