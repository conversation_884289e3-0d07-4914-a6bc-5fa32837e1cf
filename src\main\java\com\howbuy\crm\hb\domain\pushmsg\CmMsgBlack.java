package com.howbuy.crm.hb.domain.pushmsg;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * CM_MSG_BLACK
 * <AUTHOR>
@Data
public class CmMsgBlack implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户编码
     */
    private String consName;

    /**
     * 消息类型
     */
    private String msgTypeId;

    /**
     * 消息类型
     */
    private String msgTypeName;

    /**
     * 消息类型
     */
    private String mainTypeName;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 添加时间
     */
    private String createTimeStr;

    /**
     * 添加人
     */
    private String creator;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}