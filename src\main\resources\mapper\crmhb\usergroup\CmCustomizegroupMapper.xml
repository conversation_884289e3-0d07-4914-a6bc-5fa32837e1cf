<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.usergroup.CmCustomizegroupMapper">   
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	
	 <select id="getCmCustomizegroup" parameterType="Map" resultType="CmCustomizegroup" useCache="false">
	    SELECT
	          *
	    FROM CM_CUSTOMIZEGROUP
	    where 1=1  
	         <if test="groupid != null"> AND groupid = #{groupid} </if>             
             <if test="groupname != null"> AND groupname = #{groupname} </if>             
             <if test="grouplevel != null"> AND grouplevel = #{grouplevel} </if>             
             <if test="memo != null"> AND memo = #{memo} </if>             
             <if test="orgcode != null"> AND orgcode = #{orgcode} </if>               
             <if test="delflag != null"> AND delflag = #{delflag} </if>             
             <if test="creator != null"> AND creator = #{creator} </if>             
             <if test="modifier != null"> AND modifier = #{modifier} </if>             
             <if test="credt != null"> AND credt = #{credt} </if>             
             <if test="moddt != null"> AND moddt = #{moddt} </if>             
      </select>
	  
	  
	  <insert id="insertCmCustomizegroup" parameterType="CmCustomizegroup" >
	    INSERT INTO CM_CUSTOMIZEGROUP (
	     <trim suffix="" suffixOverrides=",">	
	      	 <if test="groupid != null"> groupid, </if> 
	      	 <if test="groupname != null"> groupname, </if> 
	      	 <if test="grouplevel != null"> grouplevel, </if> 
	      	 <if test="memo != null"> memo, </if> 
	      	 <if test="orgcode != null"> orgcode, </if>    
	      	 <if test="delflag != null"> delflag, </if> 
	      	 <if test="creator != null"> creator, </if> 
	      	 <if test="modifier != null"> modifier, </if> 
	      	 <if test="credt != null"> credt, </if> 
	      	 <if test="moddt != null"> moddt, </if>
			 <if test="publictype != null"> publictype, </if>
		 </trim>
         ) values (
         <trim suffix="" suffixOverrides=",">
          	 <if test="groupid != null"> #{groupid}, </if> 
	      	 <if test="groupname != null"> #{groupname}, </if> 
	      	 <if test="grouplevel != null"> #{grouplevel}, </if> 
	      	 <if test="memo != null"> #{memo}, </if> 
	      	 <if test="orgcode != null"> #{orgcode}, </if>   
	      	 <if test="delflag != null"> #{delflag}, </if> 
	      	 <if test="creator != null"> #{creator}, </if> 
	      	 <if test="modifier != null"> #{modifier}, </if> 
	      	 <if test="credt != null"> #{credt}, </if> 
	      	 <if test="moddt != null"> #{moddt}, </if>
			 <if test="publictype!= null"> #{publictype}, </if>
	     </trim>	
         )      
	  </insert>
	  
	  
	  <update id="updateCmCustomizegroup" parameterType="CmCustomizegroup" >
	    UPDATE CM_CUSTOMIZEGROUP
	    <set>
	         <if test="groupid != null"> groupid = #{groupid}, </if>
             <if test="groupname != null"> groupname = #{groupname}, </if>
             <if test="grouplevel != null"> grouplevel = #{grouplevel}, </if>
			<if test="publictype != null"> publictype = #{publictype}, </if>
             <if test="memo != null"> memo = #{memo}, </if>
             <if test="orgcode != null"> orgcode = #{orgcode}, </if> 
             <if test="delflag != null"> delflag = #{delflag}, </if>
             <if test="creator != null"> creator = #{creator}, </if>
             <if test="modifier != null"> modifier = #{modifier}, </if>
             <if test="credt != null"> credt = #{credt}, </if>
             <if test="moddt != null"> moddt = #{moddt}, </if>
        </set>
        where groupid = #{groupid}
	  </update>
	  
	  <update id="updateByPrimaryKey" parameterType="CmCustomizegroup" >
	   update CM_CUSTOMIZEGROUP
	   set 
			 groupname = #{groupname,jdbcType=VARCHAR},
			 grouplevel = #{grouplevel,jdbcType=VARCHAR},
			 publictype = #{publictype,jdbcType=VARCHAR},
			 memo = #{memo,jdbcType=VARCHAR},
			 orgcode = #{orgcode,jdbcType=VARCHAR},
			 modifier = #{modifier,jdbcType=VARCHAR},
			 moddt = #{moddt,jdbcType=VARCHAR}
	   where groupid = #{groupid}
  </update>
	  
	  
	  <delete id="delCmCustomizegroup" parameterType="String">
	    UPDATE CM_CUSTOMIZEGROUP set delflag = '1' 
	    where groupid = #{groupid}
	  </delete>
	  
	  
	  <delete id="delListCmCustomizegroup" parameterType="String">
	    DELETE  from CM_CUSTOMIZEGROUP
	    where groupid in ($id$)
	  </delete>
	  
	  
	  <select id="listCmCustomizegroup" parameterType="Map" resultType="CmCustomizegroup" useCache="false">
	    SELECT
	          *
	    FROM CM_CUSTOMIZEGROUP
	    where 1=1  
	      <if test="groupid != null"> AND groupid = #{groupid} </if>             
          <if test="groupname != null"> AND groupname = #{groupname} </if>             
          <if test="grouplevel != null"> AND grouplevel = #{grouplevel} </if>             
          <if test="memo != null"> AND memo = #{memo} </if>             
          <if test="orgcode != null"> AND orgcode = #{orgcode} </if>               
          <if test="delflag != null"> AND delflag = #{delflag} </if>             
          <if test="creator != null"> AND creator = #{creator} </if>             
          <if test="modifier != null"> AND modifier = #{modifier} </if>             
          <if test="credt != null"> AND credt = #{credt} </if>             
          <if test="moddt != null"> AND moddt = #{moddt} </if>             
      </select>
	  
	  <select id="listCmCustomizegroupByPage" parameterType="Map" resultType="CmCustomizegroup" useCache="false">
	   select * from (
		    SELECT A.groupid,
	               A.groupname,
	               A.grouplevel,
	               A.orgcode,
	               A.creator,
	               A.credt,
	               '${param.loginOrgCode}' as loginOrgCode,
			  	   '${param.loginUser}' as loginUser,
			  	   '${param.orgCode}' as orgcode2,
			  	   '${param.userLevel}' as userLevel,
			  	   A.publictype,
	               A.memo,
	               NVL(B.custcount,0) custcount
			 FROM
				(SELECT t1.groupid,
			            t1.groupname,
			            t1.grouplevel,
			            t1.orgcode,
			            t1.creator,
			            t1.credt,
			  			t1.publictype,
			            t1.memo
			     FROM CM_CUSTOMIZEGROUP T1
			     	WHERE T1.DELFLAG = '0' 
			        <if test="param.groupname != null"> AND t1.groupname LIKE '%'||#{param.groupname}||'%' </if>
	         	    <if test="param.grouplevel != null"> AND t1.grouplevel = '${param.grouplevel}' </if>
	         	    AND (  T1.GROUPLEVEL in ('1','2') 
		         	    <if test="param.queryorgcode != null"> 
	         	                 and T1.ORGCODE IN
							            	(SELECT T.ORGCODE
							                 FROM HB_ORGANIZATION T
							                	WHERE T.STATUS = 0
							                	START WITH T.ORGCODE = '${param.queryorgcode}'
							               		CONNECT BY PRIOR  T.ORGCODE = T.PARENTORGCODE)
							    <if test="param.queryorgcode == '0'.toString()"> 
	         	                      or  (T1.CREATOR = '${param.loginUser}' and T1.GROUPLEVEL = '0')  
						        </if>
						</if>
					)	
					AND ( T1.CREATOR = '${param.loginUser}'
					       or (T1.GROUPLEVEL in ('1','2') AND T1.PUBLICTYPE='0'
					            AND  T1.ORGCODE IN
					            	(SELECT T.ORGCODE
					                 FROM HB_ORGANIZATION T
					                	WHERE T.STATUS = 0
					                	START WITH T.ORGCODE = '${param.loginOrgCode}'
					               		CONNECT BY PRIOR T.PARENTORGCODE = T.ORGCODE)) 
				        <if test='param.orgCode !=null  and  param.orgCode !=""'>
				           OR (   T1.ORGCODE IN
					            	(SELECT T.ORGCODE
					                 FROM HB_ORGANIZATION T
					                	WHERE T.STATUS = 0
					                	START WITH T.ORGCODE = '${param.orgCode}'
					               		CONNECT BY PRIOR T.ORGCODE =T.PARENTORGCODE))
				        </if> 
				       <!--  <if test='param.orgCode ==null and  creator  != null'>
				           OR  T1.CREATOR = '${param.creator}'
				        </if>  -->	
			         ) 
			     ) A
			LEFT JOIN 
				(SELECT t1.groupid,
			           count(t2.custno) as custcount
			      FROM CM_CUSTOMIZEGROUP T1 
			      LEFT JOIN (select t1.custno, t1.groupid, t2.conscode
			                   from CM_CUSTOMIZEGROUP_USER t1
			                   left join cm_custconstant t2
			                     on t1.custno = t2.custno) T2
			      ON t1.groupid = t2.groupid
			      WHERE T1.DELFLAG = '0'
				  <if test='param.orgCode!=null and param.orgCode!=""'>
					  and  EXISTS(select * from cm_consultant cc where  cc.conscode =T2.CONSCODE
								  <if test='param.userLevel == "0" or param.userLevel == "1"'>
									  AND   nvl(CC.TEAMCODE,CC.Outletcode) in (
									  SELECT T.ORGCODE
									  FROM HB_ORGANIZATION T
									  WHERE T.STATUS = 0
									  <if test='param.userLevel =="1"'>
									  START WITH T.ORGCODE =(SELECT  nvl(teamcode,outletcode) FROM CM_CONSULTANT WHERE CONSCODE= '${param.loginUser}')
									  </if>
									  <if test='param.userLevel =="0"'>
									   START WITH T.ORGCODE = '${param.orgCode}'
									  </if>
									  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE
									  )
								  </if>
					  )
			     </if>
			     <if test='param.orgCode==null or param.orgCode==""'>
					  and  T2.conscode= '${param.loginUser}'
			     </if>
				 <!-- <if test='param.creator != null'>
				      AND t2.conscode = '${param.creator}'
				 </if> -->
			      GROUP BY t1.groupid
		       ) B 
		     ON A.groupid = B.groupid
		 )
	     <if test="param.sort != null and param.order != null" > ORDER BY  ${param.sort} ${param.order} nulls last</if>
         <if test="param.sort == null or param.order == null" > ORDER BY  credt desc nulls last</if> 
      </select>
      
      <select id="listCmCustomizegroupByLoginuser" parameterType="Map" resultType="CmCustomizegroup" useCache="false">
      	 
	    SELECT *
		  FROM CM_CUSTOMIZEGROUP T1
		 WHERE T1.DELFLAG = '0'
		   AND ((T1.CREATOR = #{userid})

		  		<if test='allgroup == null and operation ==1 and isIC =="TRUE"'>
					OR (T1.GROUPLEVEL = '1'   AND T1.ORGCODE IN (SELECT T.ORGCODE
					FROM HB_ORGANIZATION T
					WHERE T.STATUS = 0
					START WITH T.ORGCODE = '1'
					CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE))
				</if>
				  <if test='allgroup == null and operation ==2 and isIC =="TRUE"  '>
					  OR (T1.GROUPLEVEL = '1'  AND T1.ORGCODE IN ( SELECT T.ORGCODE
					  FROM HB_ORGANIZATION T
					  WHERE T.STATUS = 0
					  START WITH T.ORGCODE = (SELECT ORGCODE
					  FROM (SELECT T.ORGCODE, T.PARENTORGCODE
					  FROM HB_ORGANIZATION T
					  WHERE T.STATUS = 0
					  START WITH T.ORGCODE = #{loginoutletcode}
					  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE)
					  WHERE PARENTORGCODE = '1')
					  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE))
				  </if>

		   		<if test='allgroup == null and isIC =="TRUE" and rolecode =="ROLE_IC_HEAD" and orgcode != null'>
		   		OR (T1.GROUPLEVEL = '1' AND T1.ORGCODE IN (SELECT T.ORGCODE
					  FROM HB_ORGANIZATION T
					 WHERE T.STATUS = 0
					 START WITH T.ORGCODE = '1'
					CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE))
		   		</if>
		   		<if test='allgroup == null and isIC =="TRUE" and rolecode =="ROLE_SIC_HEAD" and orgcode != null'>
		   		OR (T1.GROUPLEVEL = '1'  AND T1.ORGCODE IN ( SELECT T.ORGCODE
					   FROM HB_ORGANIZATION T
					  WHERE T.STATUS = 0
					  START WITH T.ORGCODE = (SELECT ORGCODE
						                            FROM (SELECT T.ORGCODE, T.PARENTORGCODE
						                                    FROM HB_ORGANIZATION T
						                                   WHERE T.STATUS = 0
						                                   START WITH T.ORGCODE = #{orgcode}
						                                  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE)
						                           WHERE PARENTORGCODE = '1')
						 CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE))
		   		</if>
		   		<if test='allgroup == null and isIC =="TRUE" and rolecode =="ROLE_SIC_TEAM_HEAD" and orgcode != null'>
		   		OR (T1.GROUPLEVEL = '1'  AND T1.PUBLICTYPE='1'  AND T1.ORGCODE IN
		        (SELECT TEAMCODE FROM CM_CONSULTANT WHERE CONSCODE='${userid}'))
		   		</if>
		  		<if test='allgroup == null and isIC=="TRUE" and rolecode !="ROLE_IC_OPDB" and rolecode!="ROLE_IC_OPDB_ASSISTANT"'>
					OR (T1.GROUPLEVEL = '1' AND T1.PUBLICTYPE='0' AND
					EXISTS(SELECT * FROM CM_CONSULTANT
					WHERE CONSCODE = '${userid}'
					AND  nvl(teamcode, outletcode) in
					(select t.orgcode
					from hb_organization t
					where t.status = 0
					start with t.orgcode = T1.ORGCODE
					connect by prior t.orgcode = t.parentorgcode)
					))
				</if>
		   		<if test='allgroup == null and isIC =="FALSE" and orgcode != null '>
		   		OR (T1.GROUPLEVEL = '1' AND
		       T1.ORGCODE IN
		       (SELECT T.ORGCODE
		            FROM HB_ORGANIZATION T
		           WHERE T.STATUS = 0
		           START WITH T.ORGCODE = #{orgcode}
		          CONNECT BY PRIOR T.PARENTORGCODE = T.ORGCODE))
		        </if >
		        <if test='allgroup != null '>
		        OR (T1.GROUPLEVEL = '1')
		        </if> 
		       
		          )
		        
		 ORDER BY T1.GROUPLEVEL ASC            
      </select>
     
   <select id="getIsBelongToIC" parameterType="string" resultType="string" useCache="false">
       select (case
         when (select count(1)
                 from (SELECT T.ORGCODE
                         FROM HB_ORGANIZATION T
                        WHERE T.STATUS = 0
                        START WITH T.ORGCODE = #{loginOrgCode}
                       CONNECT BY PRIOR T.PARENTORGCODE = T.ORGCODE)
                where orgcode = '1') > 0 then
          'TRUE'
         ELSE
          'FALSE'
       END)
  FROM DUAL
       </select> 
       
        <select id="getConclusion" parameterType="Map" resultType="string" useCache="false">
       	select (case
		         when (select count(1)
		                 from hb_userrole
		                where usercode = #{usercode}
		                  and rolecode = #{rolecode}) = 0 then
		          'FALSE'
		         ELSE
		          'TRUE'
		       END)
		  FROM DUAL 
       </select>
		  
	<select id="getOperangecode" parameterType="String" resultType="String" useCache="false">
		select min(ha.auth_para3)
		  from hb_userrole hu
		  join hb_auth ha
			on hu.rolecode = ha.user_role_id
		   and ha.auth_type = '3'
		   and ha.menu_code = '020106'
		 where hu.usercode = #{userId}
	</select>
</mapper>



