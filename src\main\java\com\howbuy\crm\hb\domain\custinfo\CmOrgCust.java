package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/6/2 13:36
 */
@Data
public class CmOrgCust implements Serializable {

    private static final long serialVersionUID = 1L;
    /** 客户号 */
    private String conscustno;

    /** 客户类型 */
    private String investType;

    /** 客户姓名 */
    private String custName;

    /** 机构类型 */
    private String orgType;

    /** 证件号 */
    private String idNo;

    /** 联系人 */
    private String linkmanName;

    /** 地址 */
    private String address;

    /** 联系人手机 */
    private String linkmobile;

    /** 最近拜访日期 */
    private String lastVisitDt;

    /** 拜访内容 */
    private String commContent;

    /** 所属投顾 */
    private String conscode;

    /** 所属投顾 */
    private String consName;

    /** 所属部门 */
    private String orgName;

}
