package com.howbuy.crm.hb.domain.wage;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmWageConsGoal implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 投顾编号 */
	private String conscode;
	/** 等级 */
	private String nessLevel;
	/** 季度 */
	private String yearMonth;
	
	public Map<String,Object>  toMap(){
		Map<String,Object> map = new HashMap<>(3);
		if (this.conscode!=null) {
			map.put("conscode", this.conscode);
		}
		if (this.nessLevel!=null) {
			map.put("nessLevel", this.nessLevel);
		}
		if (this.yearMonth!=null) {
			map.put("yearMonth", this.yearMonth);
		}
		
		return map;
	}
}
