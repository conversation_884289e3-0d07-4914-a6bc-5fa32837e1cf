package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class Conscust implements Serializable {

private static final long serialVersionUID = 1L;

    /** 客户号 */
	private String conscustno;
	
	/** 客户姓名 */
	private String custname;
	
	/** 客户级别 */
	private String conscustlvl;
	
	/** 客户评分 */
	private Integer conscustgrade;
	
	/** 客户状态 */
	private String conscuststatus;
	
	/** 证件类型 */
	private String idtype;
	
	/** 证件号 */
	private String idno;
	
	/** 所在省份code */
	private String provcode;
	
	/** 所在省份 */
	private String provname;
	
	/** 所在城市code */
	private String citycode;
	
	/** 所在城市 */
	private String cityname;
	
	/** 手机号 */
	private String mobile;
	
	/** 来源编号 */
	private String source;
	
	/** 来源名称 */
	private String sourcename;
	
	/** 联系人 */
	private String linkman;

	/** 是否好买入会 */
	private String isjoinclub;

	/** 是否好臻入会 */
	private String ishzjoinclub;

	/** 地址 */
	private String addr;

	/** 联系人手机 */
	private String linkmobile;
	
	/** 分配日期 */
	private String assignconsdt;
	
	/** 所属投顾 */
	private String conscode;
	
	/** 所属投顾姓名 */
	private String consname;
	
	/** 最近拜访日期 */
	private String visittime;
	
	/** 拜访摘要 */
	private String visitsummary;
	
	/** 零售资产合计 */
	private BigDecimal retailamount;
	
	/** 高端资产合计 */
	private BigDecimal premiumamount;
	
	private BigDecimal totalamount;
	
	/** 最近购买高净值日期 */
	private String latesttradedt;
	
	/** 入会日期 */
	private String joinclubdt;
	
	/** 公募客户号 */
	private String pubcustno;
	
	/** 开户日期 */
	private String pubregdt;
	
	/** 投顾所在组织架构名称（部门） */
	private String orgname;

	/** 投顾所在组织架构名称（所属区域） */
	private String uporgname;

	/** 投顾所在组织架构名称（组） */
	private String teamname;
	
	/** 资金量标签 */
	private String zjllabel; 
	
	/** 客户意向标签 */
	private String khyxlabel;
	
	/** 联系情况标签 */
	private String lxqklabel;
	
	/** 是否可以看到打标情况 */
	private String canseelabel;
	
	/** 打标日期 */
	private String labeldt;
	
	/** 客户标签值 */
	private String custlabelval;
	
	/** 是否在待划转表中 */
	private String istrans;

	/** 客户类型 */
	private String invsttype;

	/** 邮箱 */
	private String email;

	/** 3.5.3 来源重构 */
	private String newsourceno;

	/**  3.5.3 来源重构 */
	private String newsubsourceno;

	/**  3.5.3 来源重构 */
	private String newsubsourcetypeno;

	/** 3.5.3 来源重构 */
	private String newsourcename;

	/** 3.5.3 来源重构 */
	private String newsubsourcename;

	/** 3.5.3 来源重构 */
	private String newsubsourcetypename;

	/** 四级来源add by haibo.yu 20200603 crm8.4 */
	private String foursourcename;

	/** 3.5.3 来源重构 */
	private String newsource;

	/** 3.5.8一账通账号 add by yu.zhang */
	private String hboneno;

	/** 3.6.2 客户状态 */
	private String custstatus;

	/** 修改时间 */
	private String uddt;
	
	/** 修改人 */
	private String modifier;
	
	/** 修改时间 */
	private String modifydt;

	/** GPS资产 */
	private String gpsinvestlevel;

	/** GPS风险 */
	private String gpsrisklevel;

	/** 是否实际维护投顾 */
	private String isrealcustcode;

	/** 电话2 */
	private String mobile2;
	
	private String custSourceRemark;
	
	private String regdt;
    
	private String ispremium;
	/** 资源类型0：公司资源；1：投顾资源 */
    private String restype;
    
    private String telno;
    
    private String linktel;
    
    private String email2;
    
    private String linkemail;
    
    private String addr2;
    
    private String linkaddr;
    
    private String fundcode;
    
    private List<String> conscustnoList;
    
    private String conscustStr;
    
    /** 是否高端成交 */
    private String gdcjlabel;
    
    private String fax;
    
    private String wechatcode;
    
    private String postcode;

    /** 证件有限期 */
    private String validity;

    /** 证件有限期日期 */
    private String validitydt;
    
    private String gender;
    
    private String birthday;
    
    private String vocation;
    
    private String remark;
    
    private String linkpostcode;
    
    private String postcode2;
    
    private String company;
    
    private String decisionflag;
    
    private String officetelno;
    
    private String interests;
    
	private String married;
    
    private String contacttime;
    
    private String edulevel;
    
    private String beforeinvest;
    
    private String pincome;
    
    private String salon;
    
    private String fincome;
    
    private String specialflag;
    
    private String subknow;
    
    private String subknowtype;
   
    private String visitfqcy;
    
    private String newsourceno2;
    
    private String restypename;
    
    private String knowchan;
    
    private String knowhowbuy;
    
    /**
	 * added by wu.long at 20190912
	 * 汇总标签
	 */
    private String totallabel;



	private String subsource;

	private String subsourcetype;
	
	/**
	 * 机构类型 add by haibo.yu 20200603
	 */
	private String orgtype;
	
	private String seniormgrcode;
	
	private String saledirection;

	/**
	 * 以Digest结尾的是脱敏信息的摘要，以Mask结尾的是掩码，以Cipher结尾的是密文
	 */
	private String idnoDigest;
	private String idnoMask;
	private String idnoCipher;

	private String addrDigest;
	private String addrMask;
	private String addrCipher;

	private String mobileDigest;
	private String mobileMask;
	private String mobileCipher;

	private String telnoDigest;
	private String telnoMask;
	private String telnoCipher;

	private String emailDigest;
	private String emailMask;
	private String emailCipher;

	private String addr2Digest;
	private String addr2Mask;
	private String addr2Cipher;

	private String mobile2Digest;
	private String mobile2Mask;
	private String mobile2Cipher;

	private String email2Digest;
	private String email2Mask;
	private String email2Cipher;

	private String linkaddrDigest;
	private String linkaddrMask;
	private String linkaddrCipher;

	private String linkmobileDigest;
	private String linkmobileMask;
	private String linkmobileCipher;

	private String linktelDigest;
	private String linktelMask;
	private String linktelCipher;

	private String linkemailDigest;
	private String linkemailMask;
	private String linkemailCipher;

	/**
	 * 手机 地区码
	 */
	private String mobileAreaCode;

	/**
	 * 手机2 地区码
	 */
	private String mobile2AreaCode;

	/**
	 * 联系人手机 地区码
	 */
	private String linkmobileAreaCode;

	/**
	 * 证件地区码
	 */
	private String idSignAreaCode;

	/**
	 * 国籍
	 */
	private String nationCode;

	/**
	 * 一账通号关联时间
	 */
	private Date hboneTimestamp;

	/**
	 * 客户创建时间戳
	 */
	private Date createTimestamp;

	/**
	 * 县代码
	 */
	private String countyCode;

}
