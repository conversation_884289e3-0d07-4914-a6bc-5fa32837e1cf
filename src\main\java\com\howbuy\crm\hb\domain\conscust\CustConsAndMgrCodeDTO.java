/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/25 17:57
 * @since JDK 1.8
 */
public class CustConsAndMgrCodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户号
     */
    private String custNo;

    /**
     * 投顾号
     */
    private String consCode;

    /**
     * 高管号
     */
    private String seniorMgrCode;


    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getSeniorMgrCode() {
        return seniorMgrCode;
    }

    public void setSeniorMgrCode(String seniorMgrCode) {
        this.seniorMgrCode = seniorMgrCode;
    }
}