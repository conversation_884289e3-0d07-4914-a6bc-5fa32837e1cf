/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.hbone;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: 一账通客户信息属性
 * @date 2023/12/21 19:27
 * @since JDK 1.8
 */
@Data
public class HboneAcctCustDetailInfoDTO {

    /**
     * 证件地区码-身份证签发地区编码
     */
    private String idSignAreaCode;

    /**
     * 手机号码区号
     */
    private String mobileAreaCode;


    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码摘要
     */
    private String idNoDigest;
    /**
     * 证件号码掩码
     */
    private String idNoMask;

    /**
     * 客户号
     */
    private String custNo;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 邮编
     */
    private String postCode;
    /**
     * 住址摘要
     */
    private String addrDigest;
    /**
     * 住址掩码
     */
    private String addrMask;

    /**
     * 联系电话摘要
     */
    private String telNoDigest;
    /**
     * 联系电话掩码
     */
    private String telNoMask;
    /**
     * 邮箱摘要
     */
    private String emailDigest;
    /**
     * 邮箱掩码
     */
    private String emailMask;

    private String fax;
    /**
     * 传真授权交易标志
     */
    private String faxFlag;

    /**
     * 住址电话摘要
     */
    private String homeTelNoDigest;
    /**
     * 住址电话掩码
     */
    private String homeTelNoMask;

    /**
     * 办公室电话
     */
    private String officeTelNo;

    /**
     * 手机摘要
     */
    private String mobileDigest;
    /**
     * 手机掩码
     */
    private String mobileMask;

    /**
     * 投资者学历01-初中及以下；02-高中/中专；03-大学/本科；04-硕士及以上
     */
    private String eduLevel;
    /**
     * 投资者职业代码
     * 01-政府部门；02-教科文；03-金融；04-商贸；05-房地产；06-制造业；07-自由职业；08-其他
     */
    private String vocation;
    /**
     * 投资人国籍
     */
    private String nationality;
    /**
     * 投资者年收入
     */
    private String incLevel;
    /**
     * 投资者生日
     */
    private String birthday;
    /**
     * 投资者性别 0-女；1-男；
     */
    private String gender;
    /**
     * 未成年人标志0-否；1-是
     */
    private String minorFlag;
    /**
     * 未成年人ID号
     */
    private String minorId;
    /**
     * 省份代码
     */
    private String provCode;
    /**
     * 城市代码
     */
    private String cityCode;
    /**
     * 省份名称
     */
    private String provName;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 所在单位
     */
    private String company;
    /**
     * 证件有效截至日
     */
    private String idValidityEnd;
    /**
     * 是否长期有效0-否,1-是
     */
    private String idAlwaysValidFlag;
    /**
     * 证件有效性：0-无效；1-有效（证件长期有效||截止日期大于当前日期）
     */
    private String idValidityFlag;

    /**
     * 基金公司对帐单选择
     */
    private String fundManDlvyType;
    /**
     * 销售公司对帐单选择
     */
    private String agentDlvyType;
    /**
     * 销售公司对帐单寄送方式
     */
    private String agentDlvyMode;
    /**
     * 电话交易
     */
    private String ctxStat;
    /**
     * 网上交易
     */
    private String etxStatus;
    /**
     * 证件有效起始日
     */
    private String idValidityStart;
    /**
     * 证券投资经历
     */
    private String secuInvestExp;
    /**
     * 婚姻状况
     */
    private String marriageStat;
    /**
     * 是否VIP 1-是, 0-否
     */
    private String vipFlag;
    /**
     * 柜台激活状态0-未激活；1-已激活
     */
    private String activeStat;

    /**
     * 开户日期
     */
    private String regDt;
    /**
     * 开户网点号
     */
    private String regOutletCode;
    /**
     * 开户渠道
     */
    private String regTradeChan;
    /**
     * 开户机构
     */
    private String regDisCode;

    /**
     * 已标记过期字段   crm 不再使用
     */
//    private String disFundTxAcctNo;// 分销交易账户
    /**
     * 客户分销交易账户状态 0-有效
     */
    private String fundtxState;
    /**
     * 交易密码是否正确 0-正确，1-错误
     */
    private String txPwdMatchs;

    /**
     * 手机是否验证:1-未验证，0-验证成功
     */
    private String mobileVrfyStat;

    /**
     * 机构客户性质
     * 0-国企 1-民营 2-合资 3-其它
     */
    private String property;
    /**
     * 机构客户资质
     * 1.金融机构
     * 2.金融机构产品
     * 3.社会保障基金
     * 4.企业年金等养老基金
     * 5.慈善基金等社会公益基金
     * 6.合格境外机构投资者（QFII）
     * 7.人民币合格境外机构投资者（RQFII）
     * 0其他
     */
    private String qualification;
    /**
     * 经营范围
     */
    private String businessScope;
    /**
     * 办公地址
     */
    private String officeAddress;

    /**
     * 投资者资质类别:0普通、1专业
     */
    private String qualificationType;

    /**
     * 投资者类型：0-机构；1-个人
     */
    private String invstType;

    /**
     * 回款协议方式
     * 1-回款至银行卡（系统默认）
     * 2-回款至银行卡（用户选择）
     * 3-回款至储蓄罐（系统默认）
     * 4-回款至储蓄罐（用户选择）
     */
    private String collectProtocolMethod;

    /**
     * 是否签署资管合格投资者承诺书：0-未签署；1-已签署
     */
    private String fundFlag;

    /**
     * 资管合格投资者满足条件：
     * 1-家庭金融净资产不低于300万元
     * 2-家庭金融资产不低于500万元
     * 3-近3年本人年均收入不低于40万元
     * 4-最近1年末净资产不低于1000万元的法人单位（只针对机构）
     */
    private String qualifyFlag;

    /**
     * 资管合格投资者确认日期：yyyyMMdd，可为空， 为空则默认取系统日期，
     */
    private String fundConfirmedDate;

    /**
     * 实际控制人
     */
    private String actualController;

    /**
     * 实际控制人或控股股东证件号-digest
     */
    private String controllerIdNoDigest;
    /**
     * 实际控制人或控股股东证件号 -掩码
     */
    private String controllerIdNoMask;

    /**
     * 实际控制人或控股股东证件类型
     */
    private String controllerIdType;

    /**
     * 证件有效期限yyyyMMdd
     */
    private String controllerIdValidityEnd;

    /**
     * 证件是否长期有效:1-是；0-否
     */
    private String controllerIdAlwaysValidFlag;

    /**
     * 证件上传状态：0-未上传;1-柜台上传；2-crm上传
     *  @Deprecated 不再使用
     */
//    private String idImageUploadStatus;

    /**
     * 证件上传日期yyyyMMdd
     * @Deprecated 不再使用
     */
//    private String idImageUploadDate;

    /**
     * 控股股东
     */
    private String stockHolder;

    /**
     * 控股股东证件号-digest
     */
    private String stockerIdNoDigest;
    /**
     * 控股股东证件号- 掩码
     */
    private String stockerIdNoMask;

    /**
     * 证件类型
     */
    private String stockerIdType;

    /**
     * 证件有效期yyyyMMdd
     */
    private String stockerIdValidityEnd;

    /**
     * 证件是否长期有效：1-是；0-否
     */
    private String stockerIdAlwaysValidFlag;

    /**
     * 县代码
     */
    private String countyCode;

    /**
     * 现居国家或地区
     */
    private String residenceCountry;

    /**
     * 法人代表证件类型
     */
    private String corpIdType;

    /**
     * 法人代表姓名
     */
    private String corporation;

    /**
     * 摘要-法人代表证件号码
     */

    private String corpIdNoDigest;

    /**
     * 掩码-法人代表证件号码
     */
    private String corpIdNoMask;
}