package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * @Description: 实体类CmSubsRedeemCalendar.java
 * <AUTHOR>
 */
@Data
public class CmSubsRedeemCalendar implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	/** 日历类型：0-申购日历；1-赎回日历；2-外部产品赎回日历 */
	private String calendartype;                          
	/** 开放结束日 */
	private String openenddt;                             
	/** 基金代码 */
	private String fundcode;                              
	/** 基金名称 */
	private String fundname;                              
	/** 基金成立日期 */
	private String funddt;                                
	/** 开放日描述 */
	private String opendtdesc;                            
	/** 截止日描述 */
	private String enddtdesc;                             
	/** 备注 */
	private String remark;                                
	/** 预计开始日期 */
	private String prestartdt;                            
	/** 预计结束日期 */
	private String preenddt;                              
	/** 预约结束位移量 */
	private BigDecimal preenddtdisplace;                               
	private String moddate;                              
	private String credate;                              
	private String updater;                              
	private String creater;                              
	private String stat;     
	private String bBeginDate;
	private String bEndDate;
	private String eBeginDate;
	private String eEndDate;
	private String preenddtdisplaceStr;

}
