package com.howbuy.crm.configuration;

import com.github.pagehelper.PageInterceptor;
import com.howbuy.crm.page.framework.core.AbstractPageConfigration;
import com.howbuy.crm.page.framework.core.PackagesSqlSessionFactoryBean;
import com.howbuy.crm.page.framework.dbutil.plugins.PaginationInterceptorPlugin;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.IOException;
import java.util.Properties;

/**
 * 
 * <AUTHOR>
 *
 */
@Configuration
@ComponentScan(basePackages = {"com.howbuy.crm.hb.service", "com.howbuy.crm.hb.outersevice"})
@MapperScan(basePackages = "com.howbuy.crm.hb.persistence", sqlSessionFactoryRef = "CrmSell-SqlSessionFactoryBean")
@ImportResource(value = "classpath:config/spring-crmhb-manager.xml")
public class CrmHbServiceConfig extends AbstractPageConfigration {

    @Override
    @Bean(name = "CrmSell-SqlSessionFactoryBean")
    protected PackagesSqlSessionFactoryBean createSqlSessionFactoryBean() {
        PackagesSqlSessionFactoryBean sqlSessionFactoryBean = super.createSqlSessionFactoryBean();
        try {
            sqlSessionFactoryBean.setTypeAliasesPackage("com.howbuy.crm.hb.domain.**");
            sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/**/*Mapper.xml"));
            PageInterceptor pageInterceptor = new PageInterceptor();
            // pagecommon 定义好的分页
            PaginationInterceptorPlugin paginationInterceptorPlugin = new PaginationInterceptorPlugin();
            Properties properties = new Properties();
            properties.setProperty("helperDialect", "oracle");
            properties.setProperty("rowBoundsWithCount", "true");
            pageInterceptor.setProperties(properties);
            sqlSessionFactoryBean.setPlugins(new Interceptor[]{pageInterceptor,paginationInterceptorPlugin});
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return sqlSessionFactoryBean;
    }
}