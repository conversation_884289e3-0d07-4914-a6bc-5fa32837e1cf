package com.howbuy.crm.hb.domain.system;

import java.math.BigDecimal;
import java.util.Date;

public class CmConsultantExpSyncFlag {
    private BigDecimal id;

    private String userid;

    /**
    * 投顾姓名-北森可以同步标识_1是0否2已同步
    */
    private String consnameSyncFlag;

    /**
    * 省市区-北森可以同步标识_1是0否2已同步
    */
    private String citycodeSyncFlag;

    /**
    * 业务中心-北森可以同步标识_1是0否2已同步
    */
    private String centerOrgSyncFlag;

    /**
    * 员工状态-北森可以同步标识_1是0否2已同步
    */
    private String worktypeSyncFlag;

    /**
    * 在职状态-北森可以同步标识_1是0否2已同步
    */
    private String workstateSyncFlag;

    /**
    * 当月职级-北森可以同步标识_1是0否2已同步
    */
    private String curmonthlevelSyncFlag;

    /**
    * 层级-北森可以同步标识_1是0否
    */
    private String userlevelSyncFlag;

    /**
    * 当月薪资-北森可以同步标识_1是0否2已同步
    */
    private String curmonthsalarySyncFlag;

    /**
    * 副职-北森可以同步标识_1是0否2已同步
    */
    private String subpositionsSyncFlag;

    /**
    * 入职日期-北森可以同步标识_1是0否2已同步
    */
    private String startdtSyncFlag;

    /**
    * 入职职级-北森可以同步标识_1是0否2已同步
    */
    private String startlevelSyncFlag;

    /**
    * 入职薪资-北森可以同步标识_1是0否2已同步
    */
    private String salarySyncFlag;

    /**
    * 转正日期-北森可以同步标识_1是0否2已同步
    */
    private String regulardtSyncFlag;

    /**
    * 转正职级-北森可以同步标识_1是0否2已同步
    */
    private String regularlevelSyncFlag;

    /**
    * 转正薪资-北森可以同步标识_1是0否2已同步
    */
    private String regularsalarySyncFlag;

    /**
    * 离职日期-北森可以同步标识_1是0否2已同步
    */
    private String quitdtSyncFlag;

    /**
    * 离职职级-北森可以同步标识_1是0否2已同步
    */
    private String quitlevelSyncFlag;

    /**
    * 离职薪资-北森可以同步标识_1是0否2已同步
    */
    private String quitsalarySyncFlag;

    /**
    * 上家公司-北森可以同步标识_1是0否2已同步
    */
    private String backgroundSyncFlag;

    /**
    * 背景来源-北森可以同步标识_1是0否2已同步
    */
    private String sourceSyncFlag;

    /**
    * 上家工作月份数-北森可以同步标识_1是0否2已同步
    */
    private String beforepositionageSyncFlag;

    /**
    * 招聘经理-北森可以同步标识_1是0否2已同步
    */
    private String recruitSyncFlag;

    /**
    * 推荐人-北森可以同步标识_1是0否2已同步
    */
    private String recommendSyncFlag;

    /**
    * 推荐人工号-北森可以同步标识_1是0否2已同步
    */
    private String recommendusernoSyncFlag;

    /**
    * 招聘渠道-北森可以同步标识_1是0否2已同步
    */
    private String recommendtypeSyncFlag;

    private String creator;

    private String modifier;

    private Date credt;

    private Date moddt;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getConsnameSyncFlag() {
        return consnameSyncFlag;
    }

    public void setConsnameSyncFlag(String consnameSyncFlag) {
        this.consnameSyncFlag = consnameSyncFlag;
    }

    public String getCitycodeSyncFlag() {
        return citycodeSyncFlag;
    }

    public void setCitycodeSyncFlag(String citycodeSyncFlag) {
        this.citycodeSyncFlag = citycodeSyncFlag;
    }

    public String getCenterOrgSyncFlag() {
        return centerOrgSyncFlag;
    }

    public void setCenterOrgSyncFlag(String centerOrgSyncFlag) {
        this.centerOrgSyncFlag = centerOrgSyncFlag;
    }

    public String getWorktypeSyncFlag() {
        return worktypeSyncFlag;
    }

    public void setWorktypeSyncFlag(String worktypeSyncFlag) {
        this.worktypeSyncFlag = worktypeSyncFlag;
    }

    public String getWorkstateSyncFlag() {
        return workstateSyncFlag;
    }

    public void setWorkstateSyncFlag(String workstateSyncFlag) {
        this.workstateSyncFlag = workstateSyncFlag;
    }

    public String getCurmonthlevelSyncFlag() {
        return curmonthlevelSyncFlag;
    }

    public void setCurmonthlevelSyncFlag(String curmonthlevelSyncFlag) {
        this.curmonthlevelSyncFlag = curmonthlevelSyncFlag;
    }

    public String getUserlevelSyncFlag() {
        return userlevelSyncFlag;
    }

    public void setUserlevelSyncFlag(String userlevelSyncFlag) {
        this.userlevelSyncFlag = userlevelSyncFlag;
    }

    public String getCurmonthsalarySyncFlag() {
        return curmonthsalarySyncFlag;
    }

    public void setCurmonthsalarySyncFlag(String curmonthsalarySyncFlag) {
        this.curmonthsalarySyncFlag = curmonthsalarySyncFlag;
    }

    public String getSubpositionsSyncFlag() {
        return subpositionsSyncFlag;
    }

    public void setSubpositionsSyncFlag(String subpositionsSyncFlag) {
        this.subpositionsSyncFlag = subpositionsSyncFlag;
    }

    public String getStartdtSyncFlag() {
        return startdtSyncFlag;
    }

    public void setStartdtSyncFlag(String startdtSyncFlag) {
        this.startdtSyncFlag = startdtSyncFlag;
    }

    public String getStartlevelSyncFlag() {
        return startlevelSyncFlag;
    }

    public void setStartlevelSyncFlag(String startlevelSyncFlag) {
        this.startlevelSyncFlag = startlevelSyncFlag;
    }

    public String getSalarySyncFlag() {
        return salarySyncFlag;
    }

    public void setSalarySyncFlag(String salarySyncFlag) {
        this.salarySyncFlag = salarySyncFlag;
    }

    public String getRegulardtSyncFlag() {
        return regulardtSyncFlag;
    }

    public void setRegulardtSyncFlag(String regulardtSyncFlag) {
        this.regulardtSyncFlag = regulardtSyncFlag;
    }

    public String getRegularlevelSyncFlag() {
        return regularlevelSyncFlag;
    }

    public void setRegularlevelSyncFlag(String regularlevelSyncFlag) {
        this.regularlevelSyncFlag = regularlevelSyncFlag;
    }

    public String getRegularsalarySyncFlag() {
        return regularsalarySyncFlag;
    }

    public void setRegularsalarySyncFlag(String regularsalarySyncFlag) {
        this.regularsalarySyncFlag = regularsalarySyncFlag;
    }

    public String getQuitdtSyncFlag() {
        return quitdtSyncFlag;
    }

    public void setQuitdtSyncFlag(String quitdtSyncFlag) {
        this.quitdtSyncFlag = quitdtSyncFlag;
    }

    public String getQuitlevelSyncFlag() {
        return quitlevelSyncFlag;
    }

    public void setQuitlevelSyncFlag(String quitlevelSyncFlag) {
        this.quitlevelSyncFlag = quitlevelSyncFlag;
    }

    public String getQuitsalarySyncFlag() {
        return quitsalarySyncFlag;
    }

    public void setQuitsalarySyncFlag(String quitsalarySyncFlag) {
        this.quitsalarySyncFlag = quitsalarySyncFlag;
    }

    public String getBackgroundSyncFlag() {
        return backgroundSyncFlag;
    }

    public void setBackgroundSyncFlag(String backgroundSyncFlag) {
        this.backgroundSyncFlag = backgroundSyncFlag;
    }

    public String getSourceSyncFlag() {
        return sourceSyncFlag;
    }

    public void setSourceSyncFlag(String sourceSyncFlag) {
        this.sourceSyncFlag = sourceSyncFlag;
    }

    public String getBeforepositionageSyncFlag() {
        return beforepositionageSyncFlag;
    }

    public void setBeforepositionageSyncFlag(String beforepositionageSyncFlag) {
        this.beforepositionageSyncFlag = beforepositionageSyncFlag;
    }

    public String getRecruitSyncFlag() {
        return recruitSyncFlag;
    }

    public void setRecruitSyncFlag(String recruitSyncFlag) {
        this.recruitSyncFlag = recruitSyncFlag;
    }

    public String getRecommendSyncFlag() {
        return recommendSyncFlag;
    }

    public void setRecommendSyncFlag(String recommendSyncFlag) {
        this.recommendSyncFlag = recommendSyncFlag;
    }

    public String getRecommendusernoSyncFlag() {
        return recommendusernoSyncFlag;
    }

    public void setRecommendusernoSyncFlag(String recommendusernoSyncFlag) {
        this.recommendusernoSyncFlag = recommendusernoSyncFlag;
    }

    public String getRecommendtypeSyncFlag() {
        return recommendtypeSyncFlag;
    }

    public void setRecommendtypeSyncFlag(String recommendtypeSyncFlag) {
        this.recommendtypeSyncFlag = recommendtypeSyncFlag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCredt() {
        return credt;
    }

    public void setCredt(Date credt) {
        this.credt = credt;
    }

    public Date getModdt() {
        return moddt;
    }

    public void setModdt(Date moddt) {
        this.moddt = moddt;
    }
}