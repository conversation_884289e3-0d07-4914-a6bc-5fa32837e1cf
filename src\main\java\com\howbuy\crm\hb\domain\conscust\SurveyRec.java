package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类SurveyRec.java
 * <AUTHOR>
 * @version 1.0
 */
public class SurveyRec implements Serializable {

	private static final long serialVersionUID = 1L;

	private String appserialno;

	private String tradedt;

	private String appcode;

	private String txcode;

	private String txappflag;

	private String txchkflag;

	private String tradechan;

	private String regioncode;

	private String outletcode;

	private String appdt;

	private String apptm;

	private String custno;

	private String pcustid;

	private String surveyid;

	private String setid;

	private String risklevel;

	private String suggestionid;

	private String portfolioid;

	private Double totalwp;

	private String memo;

	private String retcode;

	private String retmsg;

	private String creator;

	private String checker;

	private String stimestamp;

	private String conscustno;

	public String getAppserialno() {
		return this.appserialno;
	}

	public void setAppserialno(String appserialno) {
		this.appserialno = appserialno;
	}

	public String getTradedt() {
		return this.tradedt;
	}

	public void setTradedt(String tradedt) {
		this.tradedt = tradedt;
	}

	public String getAppcode() {
		return this.appcode;
	}

	public void setAppcode(String appcode) {
		this.appcode = appcode;
	}

	public String getTxcode() {
		return this.txcode;
	}

	public void setTxcode(String txcode) {
		this.txcode = txcode;
	}

	public String getTxappflag() {
		return this.txappflag;
	}

	public void setTxappflag(String txappflag) {
		this.txappflag = txappflag;
	}

	public String getTxchkflag() {
		return this.txchkflag;
	}

	public void setTxchkflag(String txchkflag) {
		this.txchkflag = txchkflag;
	}

	public String getTradechan() {
		return this.tradechan;
	}

	public void setTradechan(String tradechan) {
		this.tradechan = tradechan;
	}

	public String getRegioncode() {
		return this.regioncode;
	}

	public void setRegioncode(String regioncode) {
		this.regioncode = regioncode;
	}

	public String getOutletcode() {
		return this.outletcode;
	}

	public void setOutletcode(String outletcode) {
		this.outletcode = outletcode;
	}

	public String getAppdt() {
		return this.appdt;
	}

	public void setAppdt(String appdt) {
		this.appdt = appdt;
	}

	public String getApptm() {
		return this.apptm;
	}

	public void setApptm(String apptm) {
		this.apptm = apptm;
	}

	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}

	public String getPcustid() {
		return this.pcustid;
	}

	public void setPcustid(String pcustid) {
		this.pcustid = pcustid;
	}

	public String getSurveyid() {
		return this.surveyid;
	}

	public void setSurveyid(String surveyid) {
		this.surveyid = surveyid;
	}

	public String getSetid() {
		return this.setid;
	}

	public void setSetid(String setid) {
		this.setid = setid;
	}

	public String getRisklevel() {
		return this.risklevel;
	}

	public void setRisklevel(String risklevel) {
		this.risklevel = risklevel;
	}

	public String getSuggestionid() {
		return this.suggestionid;
	}

	public void setSuggestionid(String suggestionid) {
		this.suggestionid = suggestionid;
	}

	public String getPortfolioid() {
		return this.portfolioid;
	}

	public void setPortfolioid(String portfolioid) {
		this.portfolioid = portfolioid;
	}

	public Double getTotalwp() {
		return this.totalwp;
	}

	public void setTotalwp(Double totalwp) {
		this.totalwp = totalwp;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRetcode() {
		return this.retcode;
	}

	public void setRetcode(String retcode) {
		this.retcode = retcode;
	}

	public String getRetmsg() {
		return this.retmsg;
	}

	public void setRetmsg(String retmsg) {
		this.retmsg = retmsg;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public String getStimestamp() {
		return this.stimestamp;
	}

	public void setStimestamp(String stimestamp) {
		this.stimestamp = stimestamp;
	}

	public String getConscustno() {
		return this.conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
