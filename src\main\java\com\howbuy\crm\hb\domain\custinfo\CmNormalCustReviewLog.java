package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @Description: 实体类CmNormalCustReview.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmNormalCustReviewLog implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;
	private BigDecimal reviewid;
	private String conscustno;
	private String iswrong;
	private String wrongdes;
	private String isconnect;
	private String notifymessagedt;
	private String communicate;
	private String optor;
	private Date optd;
	private String creator;
	private Date creatdt;
	public BigDecimal getId() {
		return id;
	}
	public void setId(BigDecimal id) {
		this.id = id;
	}
	public BigDecimal getReviewid() {
		return reviewid;
	}
	public void setReviewid(BigDecimal reviewid) {
		this.reviewid = reviewid;
	}
	public String getConscustno() {
		return conscustno;
	}
	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	public String getIswrong() {
		return iswrong;
	}
	public void setIswrong(String iswrong) {
		this.iswrong = iswrong;
	}
	public String getWrongdes() {
		return wrongdes;
	}
	public void setWrongdes(String wrongdes) {
		this.wrongdes = wrongdes;
	}
	public String getIsconnect() {
		return isconnect;
	}
	public void setIsconnect(String isconnect) {
		this.isconnect = isconnect;
	}
	public String getNotifymessagedt() {
		return notifymessagedt;
	}
	public void setNotifymessagedt(String notifymessagedt) {
		this.notifymessagedt = notifymessagedt;
	}
	public String getCommunicate() {
		return communicate;
	}
	public void setCommunicate(String communicate) {
		this.communicate = communicate;
	}
	public String getOptor() {
		return optor;
	}
	public void setOptor(String optor) {
		this.optor = optor;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public Date getCreatdt() {
		return creatdt;
	}
	public void setCreatdt(Date creatdt) {
		this.creatdt = creatdt;
	}
	public Date getOptd() {
		return optd;
	}
	public void setOptd(Date optd) {
		this.optd = optd;
	}
	
}
