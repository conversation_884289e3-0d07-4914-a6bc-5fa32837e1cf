package com.howbuy.crm.hb.domain.doubletrade;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 实体类CmDoubleTradeChecklog.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmDoubleTradeChecklog implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String tradeid;

	private String auditmind;

	private String handleflag;

	private String checker;

	private Date checkdt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTradeid() {
		return tradeid;
	}

	public void setTradeid(String tradeid) {
		this.tradeid = tradeid;
	}

	public String getAuditmind() {
		return auditmind;
	}

	public void setAuditmind(String auditmind) {
		this.auditmind = auditmind;
	}

	public String getHandleflag() {
		return handleflag;
	}

	public void setHandleflag(String handleflag) {
		this.handleflag = handleflag;
	}

	public String getChecker() {
		return checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public Date getCheckdt() {
		return checkdt;
	}

	public void setCheckdt(Date checkdt) {
		this.checkdt = checkdt;
	}

	
}
