package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class AddPCustByAdviserInfo implements Serializable{
	private static final long serialVersionUID = 1L;

	private String newSourceNo;
	private String custName;
	private String addr;
	private String postCode;
	private String mobileAreaCode;
	private String mobile;
	private String telNo;
	private String email;
	private String consCode;
	private Integer PGrade;
	private String source;
	private String subSource;
	private String memo;

	public String getNewSourceNo() {
		return newSourceNo;
	}
	public void setNewSourceNo(String newSourceNo) {
		this.newSourceNo = newSourceNo;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getAddr() {
		return addr;
	}
	public void setAddr(String addr) {
		this.addr = addr;
	}
	public String getPostCode() {
		return postCode;
	}
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getTelNo() {
		return telNo;
	}
	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getConsCode() {
		return consCode;
	}
	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}
	public Integer getPGrade() {
		return PGrade;
	}
	public void setPGrade(Integer pGrade) {
		PGrade = pGrade;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getSubSource() {
		return subSource;
	}
	public void setSubSource(String subSource) {
		this.subSource = subSource;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public String getMobileAreaCode() {
		return mobileAreaCode;
	}

	public void setMobileAreaCode(String mobileAreaCode) {
		this.mobileAreaCode = mobileAreaCode;
	}
	
}
