package com.howbuy.crm.hb.domain.hkconscust;

import lombok.Data;

import java.io.Serializable;

/**
 * 香港客户维护表实体
 * <AUTHOR>
 * @date 2022/4/20 9:02
 */
@Data
public class HkConscust implements Serializable {

    private static final long serialVersionUID = 2629820574803943982L;

    /** 主键 */
    private String id;

    /** 投顾客户号 */
    private String conscustno;

    /** 客户香港id */
    private String hkcustid;


    /**
     * 香港交易账号
     */
    private String hkTxAcctNo;

    /** 创建人 */
    private String creator;

    /** 记录创建日期 */
    private String credt;

    /** 修改人 */
    private String modifier;

    /** 修改日期 */
    private String moddt;


    /** 所属投顾 */
    private String conscode;
    private String consname;
    /** 客户姓名 */
    private String custname;
    /** 所属区域 */
    private String regionName;
    /** 所属部门 */
    private String deptName;
}
