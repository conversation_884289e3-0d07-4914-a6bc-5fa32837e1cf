package com.howbuy.crm.hb.persistence.conscust;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.conscust.SurveyRec;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface SurveyRecMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    SurveyRec getSurveyRec(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param surveyRec
      */
	void insertSurveyRec(SurveyRec surveyRec);
	
	/**
	 * 单条修改数据对象
	 * @param surveyRec
	 */
	void updateSurveyRec(SurveyRec surveyRec);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delSurveyRec(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListSurveyRec(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<SurveyRec> listSurveyRec(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getSurveyRecCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<SurveyRec> listSurveyRecByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
