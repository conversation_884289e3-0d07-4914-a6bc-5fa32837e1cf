package com.howbuy.crm.hb.domain.prosale.balancefactor;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/25 16:10
 */
@Getter
@Setter
@EqualsAndHashCode
public class BalanceFactorImportEntity {

    /**
     * 香港客户id
     */
    @ExcelProperty(value = "香港客户id", index = 0)
    private String xgId;

    /**
     * 产品代码
     */
    @ExcelProperty(value = "产品代码", index = 1)
    private String fundCode;

    /**
     * 净值日期
     */
    @ExcelProperty(value = "净值日期", index = 2)
    private String navDt;

    /**
     * 平衡因子
     */
    @ExcelProperty(value = "平衡因子", index = 3)
    private BigDecimal balanceFactor;

    /**
     * 是否参与计算
     */
    @ExcelProperty(value = "是否参与计算", index = 4)
    private String isCal;

    public String getXgId() {
        return xgId;
    }

    public void setXgId(String xgId) {
        this.xgId = xgId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public BigDecimal getBalanceFactor() {
        return balanceFactor;
    }

    public void setBalanceFactor(BigDecimal balanceFactor) {
        this.balanceFactor = balanceFactor;
    }

    public String getIsCal() {
        return isCal;
    }

    public void setIsCal(String isCal) {
        this.isCal = isCal;
    }
}
