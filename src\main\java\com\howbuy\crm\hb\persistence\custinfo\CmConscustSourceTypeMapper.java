package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.CmConscustSourceType;

/**
* <AUTHOR>
* @description 针对表【CM_CONSCUST_SOURCE_TYPE(客户来源类型表)】的数据库操作Mapper
* @createDate 2024-09-25 17:08:24
* @Entity com.howbuy.crm.hb.domain.custinfo.CmConscustSourceType
*/
public interface CmConscustSourceTypeMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CmConscustSourceType record);

    int insertSelective(CmConscustSourceType record);

    CmConscustSourceType selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CmConscustSourceType record);

    int updateByPrimaryKey(CmConscustSourceType record);

}
