package com.howbuy.crm.hb.domain.doubletrade;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmDoubleBlack implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String fundCode;

	private String fundName;

	private String needDouble;

	private String needVisit;

	private String recState;

	private String creator;

	private String creDt;

	private String modifier;

	private String modDt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getNeedDouble() {
		return needDouble;
	}

	public void setNeedDouble(String needDouble) {
		this.needDouble = needDouble;
	}

	public String getNeedVisit() {
		return needVisit;
	}

	public void setNeedVisit(String needVisit) {
		this.needVisit = needVisit;
	}

	public String getRecState() {
		return recState;
	}

	public void setRecState(String recState) {
		this.recState = recState;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreDt() {
		return creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModDt() {
		return modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
