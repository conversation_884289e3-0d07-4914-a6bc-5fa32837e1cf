package com.howbuy.crm.hb.domain.doubletrade;

import com.howbuy.common.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 实体类CmDoubleTrade.java
 *                   删除属性：[riskLevel]
 * @2023-04-17 11:13 删除属性：[custrisklevel、fundrisklevel、investortype、ismatchflag] ；
 *                   修改为： [custRiskLevel、fundRiskLevel、kycInvestType、custMatchFlag]
 *                   新增属性：[prodType]
 * @2024年9月14日 16:26:35  新增属性：
 * @2024年6月21日 10:35:02 新增属性  visitForceFlag
 * <AUTHOR>
 * @version 1.0
 */
public class CmDoubleTrade implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String contractNo;

	private String hboneNo;

	private String pubcustNo;

	private String fundCode;

	/**
	 * 分销产品渠道代码
	 */
	private String disChannelCode;

	private String isrealconscode;


	private String taTradeDt;

	private String tradeType;

	/**
	 * 预约类型（1：纸质成单；2：电子成单；）
	 */
	private String preType;

	private String txCode;

	private String calmendDt;

	private String appDt;

	private String appTm;

	private BigDecimal appAmt;

	private String pmtDt;

	private String fundOpenDate;

	private String fundCloseDate;

	private String manHandleStatus;

	private String priority;

	private String visitRequire;

	private String taskType;

	private String needFlag;

	/**
	 * 处理标识：0-无需处理；1-待处理；2-审核通过；3-已处理待审核;4-审核不通过;5-待投顾处理;6-失效
	 */
	private String handleFlag;

	private Date handleDt;

	private String creDt;

	private String systemFlag;

	private String pushFlag;

	private String pushDesc;

	private String modifier;

	private Date modDt;

	private String recState;

	private String sameNo;

	private String remark;

	private String uploader;
	
	private String uploadType;

	private String uploadDt;

	private String icRemark;

	private String conscustNo;

	private String custName;

	private String mobile;

	private String linkMobile;

	private String mobile2;

	private String linkMobile2;

	private String consCode;

	private String consName;

	private String seniorMgrCode;

	private String upOrgName;

	private String orgCode;

	private String orgName;

	private String sourceName;

	private String invstType;


	private String fundName;

	private String sfmsjg;

	private String jjjc;


	private String checker;

	private Date checkdt;

	private String urge;

	/** 催办原因 */
	private String urgeReason;

	private String idNo;
	/**
	 * 身份证掩码
	 */
	private String idNoMask;
	/**
	 * 身份证密文
	 */
	private String idNoCipher;

	private String auditmind;

	private String isopcode;

	private BigDecimal fee;

	private BigDecimal premiumAmount;

	/**
	 * 反馈回访开始时间
	 */
	private String visitStartDate;

	/**
	 * 反馈回访结束时间
	 */
	private String visitEndDate;

	/**
	 * 投顾备注
	 */
	private String investRemark;

	/**
	 * 反馈人
	 */
	private String feedback;

	/**
	 * 反馈时间
	 */
	private String feedbackTime;
	
	/**
	 * 实际打款金额
	 */
	private BigDecimal realpayamt;

	/**
	 * 处理人
	 */
	private String conductor;

	/**
	 * 未处理的今天已分配任务个数
	 */
	private int countWclCurrday;

	/**
	 * 未处理的所有已分配任务个数
	 */
	private int countWclAllday;

	/**
	 * 已处理的今天已分配任务个数
	 */
	private int countYclCurrday;

	/**
	 * 已处理的所有已分配任务个数
	 */
	private int countYclAllday;

	private String conductorName;
	/**
	 * 分配时间
	 */
	private String assignTime;
	/**
	 * 存在问题code
	 */
	private String visitProblem;
	/**
	 * 存在问题
	 */
	private String visitProblemVal;
	/**
	 * 已访状态
	 */
	private String hasVisitStatus;
	private String hasVisitStatusVal;

	/** 回访时间 */
	private Date visitDt;

	/**
	 * 掩码
	 */
	private String mobileMask;

	/**
	 * 密文
	 */
	private String mobileCipher;

	/**
	 * 联系人掩码
	 */
	private String linkMobileMask;

	/**
	 * 联系人密文
	 */
	private String linkMobileCipher;
	
	/**
	 * 审核流水意见
	 */
	private String logauditmind;
	
	/**
	 * 审核流水审核状态
	 */
	private String loghandleflag;
	
	/**
	 * 审核流水审核人
	 */
	private String logchecker;
	
	/**
	 * 审核流水审核时间
	 */
	private Date logcheckdt;

	/**
	 * 是否意向单的双录 1-是
	 */
	private String isFixed;

	/**
	 * 文件名
	 */
	private String fileName;
	/**
	 * 是否规范 1-规范 0-不规范
	 */
	private String isLegal;

	/**
	 * 不合规信息
	 */
	private List<VisitQualityResult.LegalResult> legalResultList;
	
	/**
	 * 双录不合规信息
	 */
	private List<DtQualityResult.LegalResult> slLegalResultList;
	
	/**
	 * 原始文本
	 */
	private String originAsrRaw;


	/**
	 * 是否添加企微且绑定  1-是 0-否
	 */
	private String addWechatAccount;


	/**
	 * 产品风险等级
	 */
	private String fundRiskLevel;


	/********************************added by haoran.zhang  交易相关 kyc信息  2023-04-11 20:34**********************************************************************/

	/**
	 * 产品类型：1:其他；2：私募；3：资管
	 */
	private String prodType;

	/**
	 * kyc投资者类型：PRO-专业 NORMAL-普通
	 */
	private String kycInvestType;

	/**
	 * kyc客户风险等级
	 */
	private String custRiskLevel;

	/**
	 * 客户风险等级是否匹配 1-匹配 0-不匹配
	 */
	private String custMatchFlag;


/********************************added by haoran.zhang  双路改造  2024年6月21日 **********************************************************************/
	/**
	 * 双录方式：1-人工双录 2-人机双录
	 */
	private String doubleType;

	/**
	 * 双录原因
	 */
	private String doubleReason;

	/**
	 * 双录模版ID
	 */
	private String doubleTempId;


	/**
	 * 双录模板名称
	 */
	private String doubleTempName;

	/**
	 * 客户风险等级测评时间
	 */
	private String custKycTime;

	/**
	 * 客户证件类型
	 */
	private String idType;

	/**
	 * 回访使用：上报是否需要强制回访  0-否 1-是
	 */
	private String visitForceFlag;

	/**
	 * 是否临近上报日期
	 * 逻辑：0 ≤(【上报截止日期】-【当前时间】）≤1天
	 */
	private boolean closeTaTradeDt=false;




	public String getIsLegal() {
		return isLegal;
	}

	public void setIsLegal(String isLegal) {
		this.isLegal = isLegal;
	}

	public List<VisitQualityResult.LegalResult> getLegalResultList() {
		return legalResultList;
	}

	public void setLegalResultList(List<VisitQualityResult.LegalResult> legalResultList) {
		this.legalResultList = legalResultList;
	}

	public String getOriginAsrRaw() {
		return originAsrRaw;
	}

	public void setOriginAsrRaw(String originAsrRaw) {
		this.originAsrRaw = originAsrRaw;
	}

	public String getFeedbackTime() {
		return feedbackTime;
	}

	public void setFeedbackTime(String feedbackTime) {
		this.feedbackTime = feedbackTime;
	}

	public String getVisitStartDate() {
		return visitStartDate;
	}

	public void setVisitStartDate(String visitStartDate) {
		this.visitStartDate = visitStartDate;
	}

	public String getVisitEndDate() {
		return visitEndDate;
	}

	public void setVisitEndDate(String visitEndDate) {
		this.visitEndDate = visitEndDate;
	}

	public String getInvestRemark() {
		return investRemark;
	}

	public void setInvestRemark(String investRemark) {
		this.investRemark = investRemark;
	}

	public String getFeedback() {
		return feedback;
	}

	public void setFeedback(String feedback) {
		this.feedback = feedback;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getContractNo() {
		return contractNo;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	public String getHboneNo() {
		return hboneNo;
	}

	public void setHboneNo(String hboneNo) {
		this.hboneNo = hboneNo;
	}

	public String getPubcustNo() {
		return pubcustNo;
	}

	public void setPubcustNo(String pubcustNo) {
		this.pubcustNo = pubcustNo;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundRiskLevel() {
		return fundRiskLevel;
	}

	public void setFundRiskLevel(String fundRiskLevel) {
		this.fundRiskLevel = fundRiskLevel;
	}

	public String getTaTradeDt() {
		return taTradeDt;
	}

	public void setTaTradeDt(String taTradeDt) {
		this.taTradeDt = taTradeDt;
	}

	public String getTradeType() {
		return tradeType;
	}

	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}

	public String getTxCode() {
		return txCode;
	}

	public void setTxCode(String txCode) {
		this.txCode = txCode;
	}

	public String getCalmendDt() {
		return calmendDt;
	}

	public void setCalmendDt(String calmendDt) {
		this.calmendDt = calmendDt;
	}

	public String getAppDt() {
		return appDt;
	}

	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}

	public String getAppTm() {
		return appTm;
	}

	public void setAppTm(String appTm) {
		this.appTm = appTm;
	}

	public BigDecimal getAppAmt() {
		return appAmt;
	}

	public void setAppAmt(BigDecimal appAmt) {
		this.appAmt = appAmt;
	}

	public String getPmtDt() {
		return pmtDt;
	}

	public void setPmtDt(String pmtDt) {
		this.pmtDt = pmtDt;
	}

	public String getFundOpenDate() {
		return fundOpenDate;
	}

	public void setFundOpenDate(String fundOpenDate) {
		this.fundOpenDate = fundOpenDate;
	}

	public String getFundCloseDate() {
		return fundCloseDate;
	}

	public void setFundCloseDate(String fundCloseDate) {
		this.fundCloseDate = fundCloseDate;
	}

	public String getManHandleStatus() {
		return manHandleStatus;
	}

	public void setManHandleStatus(String manHandleStatus) {
		this.manHandleStatus = manHandleStatus;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getVisitRequire() {
		return visitRequire;
	}

	public void setVisitRequire(String visitRequire) {
		this.visitRequire = visitRequire;
	}

	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	public String getNeedFlag() {
		return needFlag;
	}

	public void setNeedFlag(String needFlag) {
		this.needFlag = needFlag;
	}

	public String getHandleFlag() {
		return handleFlag;
	}

	public void setHandleFlag(String handleFlag) {
		this.handleFlag = handleFlag;
	}

	public Date getHandleDt() {
		return handleDt;
	}

	public void setHandleDt(Date handleDt) {
		this.handleDt = handleDt;
	}

	public String getCreDt() {
		return creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public String getSystemFlag() {
		return systemFlag;
	}

	public void setSystemFlag(String systemFlag) {
		this.systemFlag = systemFlag;
	}

	public String getPushFlag() {
		return pushFlag;
	}

	public void setPushFlag(String pushFlag) {
		this.pushFlag = pushFlag;
	}

	public String getPushDesc() {
		return pushDesc;
	}

	public void setPushDesc(String pushDesc) {
		this.pushDesc = pushDesc;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public Date getModDt() {
		return modDt;
	}

	public void setModDt(Date modDt) {
		this.modDt = modDt;
	}

	public String getRecState() {
		return recState;
	}

	public void setRecState(String recState) {
		this.recState = recState;
	}

	public String getSameNo() {
		return sameNo;
	}

	public void setSameNo(String sameNo) {
		this.sameNo = sameNo;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getUploader() {
		return uploader;
	}

	public void setUploader(String uploader) {
		this.uploader = uploader;
	}

	public String getUploadType() {
		return uploadType;
	}

	public void setUploadType(String uploadType) {
		this.uploadType = uploadType;
	}

	public String getUploadDt() {
		return uploadDt;
	}

	public void setUploadDt(String uploadDt) {
		this.uploadDt = uploadDt;
	}

	public String getIcRemark() {
		return icRemark;
	}

	public void setIcRemark(String icRemark) {
		this.icRemark = icRemark;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getUrgeReason() {
		return urgeReason;
	}

	public void setUrgeReason(String urgeReason) {
		this.urgeReason = urgeReason;
	}

	public String getLinkMobile() {
		return linkMobile;
	}

	public void setLinkMobile(String linkMobile) {
		this.linkMobile = linkMobile;
	}

	public String getMobile2() {
		return mobile2;
	}

	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}

	public String getLinkMobile2() {
		return linkMobile2;
	}

	public void setLinkMobile2(String linkMobile2) {
		this.linkMobile2 = linkMobile2;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getSeniorMgrCode() {
		return seniorMgrCode;
	}

	public void setSeniorMgrCode(String seniorMgrCode) {
		this.seniorMgrCode = seniorMgrCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getUpOrgName() {
		return upOrgName;
	}

	public void setUpOrgName(String upOrgName) {
		this.upOrgName = upOrgName;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getSourceName() {
		return sourceName;
	}

	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}

	public String getInvstType() {
		return invstType;
	}

	public void setInvstType(String invstType) {
		this.invstType = invstType;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getSfmsjg() {
		return sfmsjg;
	}

	public void setSfmsjg(String sfmsjg) {
		this.sfmsjg = sfmsjg;
	}

	public String getJjjc() {
		return jjjc;
	}

	public void setJjjc(String jjjc) {
		this.jjjc = jjjc;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getChecker() {
		return checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public Date getCheckdt() {
		return checkdt;
	}

	public void setCheckdt(Date checkdt) {
		this.checkdt = checkdt;
	}

	public String getUrge() {
		return urge;
	}

	public void setUrge(String urge) {
		this.urge = urge;
	}

	public String getAuditmind() {
		return auditmind;
	}

	public void setAuditmind(String auditmind) {
		this.auditmind = auditmind;
	}

	public String getIsopcode() {
		return isopcode;
	}

	public void setIsopcode(String isopcode) {
		this.isopcode = isopcode;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	public String getIdNoCipher() {
		return idNoCipher;
	}

	public void setIdNoCipher(String idNoCipher) {
		this.idNoCipher = idNoCipher;
	}

	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

	public BigDecimal getPremiumAmount() {
		return premiumAmount;
	}

	public void setPremiumAmount(BigDecimal premiumAmount) {
		this.premiumAmount = premiumAmount;
	}

	public BigDecimal getRealpayamt() {
		return realpayamt;
	}

	public void setRealpayamt(BigDecimal realpayamt) {
		this.realpayamt = realpayamt;
	}

	public String getConductor() {
		return conductor;
	}

	public void setConductor(String conductor) {
		this.conductor = conductor;
	}

	public int getCountWclCurrday() {
		return countWclCurrday;
	}

	public void setCountWclCurrday(int countWclCurrday) {
		this.countWclCurrday = countWclCurrday;
	}

	public int getCountWclAllday() {
		return countWclAllday;
	}

	public void setCountWclAllday(int countWclAllday) {
		this.countWclAllday = countWclAllday;
	}

	public int getCountYclCurrday() {
		return countYclCurrday;
	}

	public void setCountYclCurrday(int countYclCurrday) {
		this.countYclCurrday = countYclCurrday;
	}

	public int getCountYclAllday() {
		return countYclAllday;
	}

	public void setCountYclAllday(int countYclAllday) {
		this.countYclAllday = countYclAllday;
	}

	public String getConductorName() {
		return conductorName;
	}

	public void setConductorName(String conductorName) {
		this.conductorName = conductorName;
	}

	public String getAssignTime() {
		return assignTime;
	}

	public void setAssignTime(String assignTime) {
		this.assignTime = assignTime;
	}

	public String getVisitProblem() {
		return visitProblem;
	}

	public void setVisitProblem(String visitProblem) {
		this.visitProblem = visitProblem;
	}

	public String getVisitProblemVal() {
		return visitProblemVal;
	}

	public void setVisitProblemVal(String visitProblemVal) {
		this.visitProblemVal = visitProblemVal;
	}

	public String getHasVisitStatus() {
		return hasVisitStatus;
	}

	public void setHasVisitStatus(String hasVisitStatus) {
		this.hasVisitStatus = hasVisitStatus;
	}

	public String getHasVisitStatusVal() {
		return hasVisitStatusVal;
	}

	public void setHasVisitStatusVal(String hasVisitStatusVal) {
		this.hasVisitStatusVal = hasVisitStatusVal;
	}

	public Date getVisitDt() {
		return visitDt;
	}

	public void setVisitDt(Date visitDt) {
		this.visitDt = visitDt;
	}

	public String getMobileMask() {
		return mobileMask;
	}

	public void setMobileMask(String mobileMask) {
		this.mobileMask = mobileMask;
	}

	public String getMobileCipher() {
		return mobileCipher;
	}

	public void setMobileCipher(String mobileCipher) {
		this.mobileCipher = mobileCipher;
	}

	public String getLinkMobileMask() {
		return linkMobileMask;
	}

	public void setLinkMobileMask(String linkMobileMask) {
		this.linkMobileMask = linkMobileMask;
	}

	public String getLinkMobileCipher() {
		return linkMobileCipher;
	}

	public void setLinkMobileCipher(String linkMobileCipher) {
		this.linkMobileCipher = linkMobileCipher;
	}

	public String getIdNoMask() {
		return idNoMask;
	}

	public void setIdNoMask(String idNoMask) {
		this.idNoMask = idNoMask;
	}

	public String getLogauditmind() {
		return logauditmind;
	}

	public void setLogauditmind(String logauditmind) {
		this.logauditmind = logauditmind;
	}

	public String getLoghandleflag() {
		return loghandleflag;
	}

	public void setLoghandleflag(String loghandleflag) {
		this.loghandleflag = loghandleflag;
	}

	public String getLogchecker() {
		return logchecker;
	}

	public void setLogchecker(String logchecker) {
		this.logchecker = logchecker;
	}

	public Date getLogcheckdt() {
		return logcheckdt;
	}

	public void setLogcheckdt(Date logcheckdt) {
		this.logcheckdt = logcheckdt;
	}

	public String getIsFixed() {
		return isFixed;
	}

	public void setIsFixed(String isFixed) {
		this.isFixed = isFixed;
	}

	public String getDisChannelCode() {
		return disChannelCode;
	}

	public void setDisChannelCode(String disChannelCode) {
		this.disChannelCode = disChannelCode;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public List<DtQualityResult.LegalResult> getSlLegalResultList() {
		return slLegalResultList;
	}

	public void setSlLegalResultList(
			List<DtQualityResult.LegalResult> slLegalResultList) {
		this.slLegalResultList = slLegalResultList;
	}

	public String getAddWechatAccount() {
		return addWechatAccount;
	}

	public void setAddWechatAccount(String addWechatAccount) {
		this.addWechatAccount = addWechatAccount;
	}

	public String getProdType() {
		return prodType;
	}

	public void setProdType(String prodType) {
		this.prodType = prodType;
	}

	public String getKycInvestType() {
		return kycInvestType;
	}

	public void setKycInvestType(String kycInvestType) {
		this.kycInvestType = kycInvestType;
	}

	public String getCustRiskLevel() {
		return custRiskLevel;
	}

	public void setCustRiskLevel(String custRiskLevel) {
		this.custRiskLevel = custRiskLevel;
	}

	public String getCustMatchFlag() {
		return custMatchFlag;
	}

	public void setCustMatchFlag(String custMatchFlag) {
		this.custMatchFlag = custMatchFlag;
	}

	public String getIsrealconscode() {
		return isrealconscode;
	}

	public void setIsrealconscode(String isrealconscode) {
		this.isrealconscode = isrealconscode;
	}

	public String getDoubleType() {
		return doubleType;
	}

	public void setDoubleType(String doubleType) {
		this.doubleType = doubleType;
	}

	public String getDoubleReason() {
		return doubleReason;
	}

	public void setDoubleReason(String doubleReason) {
		this.doubleReason = doubleReason;
	}

	public String getDoubleTempId() {
		return doubleTempId;
	}

	public void setDoubleTempId(String doubleTempId) {
		this.doubleTempId = doubleTempId;
	}

	public String getCustKycTime() {
		return custKycTime;
	}

	public void setCustKycTime(String custKycTime) {
		this.custKycTime = custKycTime;
	}

	public String getDoubleTempName() {
		return doubleTempName;
	}

	public void setDoubleTempName(String doubleTempName) {
		this.doubleTempName = doubleTempName;
	}

	public String getPreType() {
		return preType;
	}

	public void setPreType(String preType) {
		this.preType = preType;
	}

	public String getIdType() {
		return idType;
	}

	public void setIdType(String idType) {
		this.idType = idType;
	}

	public String getVisitForceFlag() {
		return visitForceFlag;
	}

	public void setVisitForceFlag(String visitForceFlag) {
		this.visitForceFlag = visitForceFlag;
	}

	/**
	 * @description:(判断：上报日期是否在当前日期1天以内)
	 * @param
	 * @return boolean
	 * @author: haoran.zhang
	 * @date: 2024/9/14 16:40
	 * @since JDK 1.8
	 */
	public boolean isCloseTaTradeDt() {
//		逻辑：0 ≤(【上报截止日期yyyyMMdd】-【当前时间】）≤1天
		if(StringUtil.isNotNullStr(taTradeDt)){
			//上报截止日期 将yyyyMMdd 转成 Date类型
			Date  taTradeDate=DateUtil.string2Date(taTradeDt,DateUtil.SHORT_DATEPATTERN);
			//当前时间戳， 转换为 yyyyMMdd .  再转成 Date类型
			Date  curDate=DateUtil.string2Date(DateUtil.formatNowDate(DateUtil.SHORT_DATEPATTERN),DateUtil.SHORT_DATEPATTERN);
			int elapseDate=DateUtil.elapsedDay(curDate,taTradeDate);
			return  elapseDate>=0 && elapseDate<=1;
		}
		return closeTaTradeDt;
	}

}