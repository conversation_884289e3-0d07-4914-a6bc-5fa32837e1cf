/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

import java.util.Objects;

/**
 * @description: ()
 * <AUTHOR>
 * @date 2023/11/14 17:29
 * @since JDK 1.8
 */
public enum ConferAuditStatusEnum {

    /**
     * 待审核
     */
    WAIT_AUDIT("0", "待审核"),
    /**
     * 审核驳回
     */
    AUDIT_REJECT("2", "审核驳回"),
    /**
     * 待执行
     */
    AUDIT_PASS("1", "待执行"),
    /**
     * 归档
     */
    ARCHIVE("3", "归档"),
    /**
     * 作废
     */
    INVALID("4", "作废");

    /**
     * 字典值
     */
    private String code;
    /**
     * 翻译
     */
    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    ConferAuditStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取描述
     * @param code
     * @return
     */
    public static String getDescByCode(String code){
        for(ConferAuditStatusEnum statusEnum : ConferAuditStatusEnum.values()){
            if(Objects.equals(statusEnum.getCode(), code)){
                return statusEnum.getDesc();
            }
        }
        return null;
    }
}