package com.howbuy.crm.hb.domain.manage;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Description: 实体类UpLoadPrivateTrade.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class UpLoadPrivateTrade implements Serializable {

private static final long serialVersionUID = 1L;

	/**
	 * 1代表私募股权回款；2代表分红；3代表强制调减；4代表到期赎回；5代表强制调增 7 非交易过户转出 8非交易过户转入 9 基金清算
	 */
	private String tradetype;
	
	private String custname;
	
	private String conscustno;
	
	private String fundcode;
	
	private String fundname;
	
	private String tradedt;
	
	private String discsummary;
	
	private BigDecimal nav;

	private BigDecimal ackamt;

	private BigDecimal ackvol;

	/**
	 * 是否发送短信；交易类别 = 私募股权回款/分红时，必填；0代表不发送短信；1代表发送短信；
	 */
	private String ismessage;
	
	/**
	 * 好买产品线
	 */
	private String hmcpx;
	
	/**
	 * 是否海外处理1:是；0：否
	 */
	private String ishaiwai;

	/**
	 * 转让价格
	 */
	private BigDecimal transferPrice;

	/**
	 * 分红方式
	 */
	private String dividendMode;

}
