/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.reward;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/9 3:01 PM
 * @since JDK 1.8
 */
@Data
public class ResultInfoForTradeNumPO implements Serializable {
    private Long preid;
    private String tradeDt;
    private String familyCode;
    private String manycallFlag;
    private String conscustno;
    private String creator;
    private String pcode;
    private String paycheckdt;
    private String consStartDt;
    private String consEndDt;
    private BigDecimal amt;
    private String lybzcustno;
    private String accountProductType;
    private Long tradeid;
}