<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.usergroup.CmCustomizegroupUserHisMapper">   
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	
	 <select id="getCustomizegroupUserHis" parameterType="Map" resultType="CmCustomizegroupUserHis" useCache="false">
	    SELECT
	          *
	    FROM CM_CUSTOMIZEGROUP_USER_HIS
	    where 1=1  
	              <if test="id != null"> AND id = #{id} </if>             
                  <if test="groupid != null"> AND groupid = #{groupid} </if>             
                  <if test="custno != null"> AND custno = #{custno} </if>             
                  <if test="startdt != null"> AND startdt = #{startdt} </if>             
                  <if test="enddt != null"> AND enddt = #{enddt} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="credt != null"> AND credt = #{credt} </if>             
                  <if test="moddt != null"> AND moddt = #{moddt} </if>             
        	  </select>
	  
	  
	  <insert id="insertCustomizegroupUserHis" parameterType="CmCustomizegroupUserHis" >
	    INSERT INTO CM_CUSTOMIZEGROUP_USER_HIS (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="id != null"> id, </if> 
	      	      <if test="groupid != null"> groupid, </if> 
	      	      <if test="custno != null"> custno, </if> 
	      	      <if test="startdt != null"> startdt, </if> 
	      	      <if test="enddt != null"> enddt, </if> 
	      	      <if test="creator != null"> creator, </if> 
	      	      <if test="modifier != null"> modifier, </if> 
	      	      <if test="credt != null"> credt, </if> 
	      	      <if test="moddt != null"> moddt, </if> 
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="id != null"> #{id}, </if> 
	      	      <if test="groupid != null"> #{groupid}, </if> 
	      	      <if test="custno != null"> #{custno}, </if> 
	      	      <if test="startdt != null"> #{startdt}, </if> 
	      	      <if test="enddt != null"> #{enddt}, </if> 
	      	      <if test="creator != null"> #{creator}, </if> 
	      	      <if test="modifier != null"> #{modifier}, </if> 
	      	      <if test="credt != null"> #{credt}, </if> 
	      	      <if test="moddt != null"> #{moddt}, </if> 
	               </trim>	
         )      
	  </insert>
	  
	  <insert id="insertCustomizegroupUserHisBatch" parameterType="java.util.List">
	    INSERT INTO CM_CUSTOMIZEGROUP_USER_HIS 
		(id, groupid, custno, startdt, enddt, creator, modifier, credt,moddt)
		  <foreach collection="list" item="item" index="index" separator="UNION ALL" open="select to_char(SEQ_CUSTREC.NEXTVAL), a.* from (" close=") a">
	       SELECT #{item.groupid},#{item.custno},#{item.startdt},#{item.enddt},#{item.creator},#{item.modifier},#{item.credt},#{item.moddt} FROM DUAL
	      </foreach>
	   </insert>
	  
	  <update id="updateCustomizegroupUserHis" parameterType="CmCustomizegroupUserHis" >
	    UPDATE CM_CUSTOMIZEGROUP_USER_HIS	    
	    <set>
	                <if test="id != null"> id = #{id}, </if>             
                    <if test="groupid != null"> groupid = #{groupid}, </if>             
                    <if test="custno != null"> custno = #{custno}, </if>             
                    <if test="startdt != null"> startdt = #{startdt}, </if>             
                    <if test="enddt != null"> enddt = #{enddt}, </if>             
                    <if test="creator != null"> creator = #{creator}, </if>             
                    <if test="modifier != null"> modifier = #{modifier}, </if>             
                    <if test="credt != null"> credt = #{credt}, </if>             
                    <if test="moddt != null"> moddt = #{moddt}, </if>             
                 </set>
          where id = #id
	  </update>
	  
	  <update id="updateCustomizegroupUserHisByIds" parameterType="Map" >
	    UPDATE CM_CUSTOMIZEGROUP_USER_HIS t	    
	    <set>
           <if test="enddt != null"> enddt = #{enddt}, </if>             
           <if test="modifier != null"> modifier = #{modifier}, </if>             
           <if test="enddt != null"> moddt = #{enddt}, </if>             
        </set>
        where t.groupid = '${groupid}' and t.custno in (${ids})
	  </update>
	  
	  <update id="updateCustomizegroupUserHisByAll" parameterType="Map" >
	    UPDATE CM_CUSTOMIZEGROUP_USER_HIS t	    
	    <set>
           <if test="enddt != null"> enddt = #{enddt}, </if>             
           <if test="modifier != null"> modifier = #{modifier}, </if>             
           <if test="enddt != null"> moddt = #{enddt}, </if>             
        </set>
        where t.groupid = #{groupid}  
        <if test="ids != null and ids.size()>0 ">
		      and t.custno in
		  	  <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">  
		           #{item}
		      </foreach> 
		    </if>
	  </update>
	  
	  
	  <delete id="delCustomizegroupUserHis" parameterType="String">
	    DELETE  from CM_CUSTOMIZEGROUP_USER_HIS
	    where id = #id
	  </delete>
	  
	  
	  <delete id="delListCustomizegroupUserHis" parameterType="String">
	    DELETE  from CM_CUSTOMIZEGROUP_USER_HIS
	    where id in ($id$)
	        
	  </delete>
	  
	  
	  <select id="listCustomizegroupUserHis" parameterType="Map" resultType="CmCustomizegroupUserHis" useCache="false">
	    SELECT
	          *
	    FROM CM_CUSTOMIZEGROUP_USER_HIS
	    where 1=1  
	              <if test="id != null"> AND id = #{id} </if>             
                  <if test="groupid != null"> AND groupid = #{groupid} </if>             
                  <if test="custno != null"> AND custno = #{custno} </if>             
                  <if test="startdt != null"> AND startdt = #{startdt} </if>             
                  <if test="enddt != null"> AND enddt = #{enddt} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="credt != null"> AND credt = #{credt} </if>             
                  <if test="moddt != null"> AND moddt = #{moddt} </if>             
        	  </select>
	  
	  <select id="listCustomizegroupUserHisByPage" parameterType="Map" resultType="CmCustomizegroupUserHis" useCache="false">
	    SELECT
	          *
	    FROM CM_CUSTOMIZEGROUP_USER_HIS
	    where 1=1   
	              <if test="param.id != null"> AND id = #{param.id} </if>             
                  <if test="param.groupid != null"> AND groupid = #{param.groupid} </if>             
                  <if test="param.custno != null"> AND custno = #{param.custno} </if>             
                  <if test="param.startdt != null"> AND startdt = #{param.startdt} </if>             
                  <if test="param.enddt != null"> AND enddt = #{param.enddt} </if>             
                  <if test="param.creator != null"> AND creator = #{param.creator} </if>             
                  <if test="param.modifier != null"> AND modifier = #{param.modifier} </if>             
                  <if test="param.credt != null"> AND credt = #{param.credt} </if>             
                  <if test="param.moddt != null"> AND moddt = #{param.moddt} </if>             
        	  </select>
	  
	  <select id="getCustomizegroupUserHisCount" parameterType="Map" resultType="int" useCache="false">
	    SELECT
	          COUNT(*)
	    FROM CM_CUSTOMIZEGROUP_USER_HIS
	    where 1=1  
	              <if test="id != null"> AND id = #{id} </if>             
                  <if test="groupid != null"> AND groupid = #{groupid} </if>             
                  <if test="custno != null"> AND custno = #{custno} </if>             
                  <if test="startdt != null"> AND startdt = #{startdt} </if>             
                  <if test="enddt != null"> AND enddt = #{enddt} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="credt != null"> AND credt = #{credt} </if>             
                  <if test="moddt != null"> AND moddt = #{moddt} </if>             
        	  </select>
	  
	  
</mapper>



