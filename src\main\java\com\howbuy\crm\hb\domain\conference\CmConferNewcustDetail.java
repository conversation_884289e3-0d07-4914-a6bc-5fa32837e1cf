package com.howbuy.crm.hb.domain.conference;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(扫码参会新客分配任务明细表)
 * @return
 * @author: xufan<PERSON>o
 * @date: 2023/11/7 19:56
 * @since JDK 1.8
 */
public class CmConferNewcustDetail {
    /**
    * 主键
    */
    private String id;

    /**
    * 新客分配任务ID CM_CONFER_NEWCUST_TASK.ID
    */
    private String taskId;

    /**
    * 会议ID CM_CONFERENCE.ID
    */
    private String conferenceId;

    /**
    * 扫码参会ID CM_CONFERENCE_SCAN.ID
    */
    private BigDecimal scanId;

    /**
    * 手机号摘要
    */
    private String mobileDigest;

    /**
    * 客户姓名 -允许OP修改
    */
    private String custName;

    /**
    * 投顾姓名 -允许OP修改
    */
    private String consName;

    /**
    * 新客处理，待提交的表单信息，JSON字符串格式保存
    */
    private String dealJsonInfo;

    /**
    * 处理状态 0-待处理、2-处理失败、1-处理成功 3-无需处理
    */
    private String dealStatus;

    /**
    * 处理意见
    */
    private String dealRemark;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTimestamp;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getConferenceId() {
        return conferenceId;
    }

    public void setConferenceId(String conferenceId) {
        this.conferenceId = conferenceId;
    }

    public BigDecimal getScanId() {
        return scanId;
    }

    public void setScanId(BigDecimal scanId) {
        this.scanId = scanId;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getDealJsonInfo() {
        return dealJsonInfo;
    }

    public void setDealJsonInfo(String dealJsonInfo) {
        this.dealJsonInfo = dealJsonInfo;
    }

    public String getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus;
    }

    public String getDealRemark() {
        return dealRemark;
    }

    public void setDealRemark(String dealRemark) {
        this.dealRemark = dealRemark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTimestamp() {
        return modifyTimestamp;
    }

    public void setModifyTimestamp(Date modifyTimestamp) {
        this.modifyTimestamp = modifyTimestamp;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }
}