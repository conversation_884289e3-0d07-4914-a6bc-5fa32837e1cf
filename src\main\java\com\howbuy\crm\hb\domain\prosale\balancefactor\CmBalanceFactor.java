package com.howbuy.crm.hb.domain.prosale.balancefactor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/19 15:44
 */
public class CmBalanceFactor implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 客户号
     */
    private String conscustno;
    /**
     * 香港客户id
     */
    private String HKCustId;

    /**
     * 净值日期
     */
    private String navDt;

    /**
     * 是否参与计算
     */
    private String isCal;

    /**
     * 平衡因子
     */
    private BigDecimal balanceFactor;

    /**
     * 审核状态
     */
    private String auditStatus;
    
    /**
     * 是否转换完成1是0否
     */
    private String convertFinish;

    private String creator;

    private String updateMan;

    private Date createTime;

    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public String getIsCal() {
        return isCal;
    }

    public void setIsCal(String isCal) {
        this.isCal = isCal;
    }

    public BigDecimal getBalanceFactor() {
        return balanceFactor;
    }

    public void setBalanceFactor(BigDecimal balanceFactor) {
        this.balanceFactor = balanceFactor;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdateMan() {
        return updateMan;
    }

    public void setUpdateMan(String updateMan) {
        this.updateMan = updateMan;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getHKCustId() {
        return HKCustId;
    }

    public void setHKCustId(String HKCustId) {
        this.HKCustId = HKCustId;
    }

	public String getConvertFinish() {
		return convertFinish;
	}

	public void setConvertFinish(String convertFinish) {
		this.convertFinish = convertFinish;
	}
}
