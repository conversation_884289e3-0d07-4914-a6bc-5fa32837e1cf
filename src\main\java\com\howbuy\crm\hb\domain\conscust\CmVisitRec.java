package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

/**
 * @Description: 实体类CmVisitRec.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmVisitRec implements Serializable {

	private static final long serialVersionUID = 1L;

	private String appSerialNo;

	private String tradeDt;

	private String appCode;

	private String txCode;

	private String txAppFlag;

	private String txChkFlag;

	private String tradeChan;

	private String regionCode;

	private String outletCode;

	private String appDt;

	private String appTm;

	private String custNo;

	private String pcustId;

	private String contactStaff;

	private String howLong;

	private String visitRs;

	private String visitCust;

	private String visitTime;

	private String visitType;

	private String visitSummary;

	private Double visitFee;

	private String feePurpose;

	private String servStaff;

	private String memo;

	private String retCode;

	private String retMsg;

	private String creator;

	private String checker;

	private String stimeStamp;

	private String consCustNo;

	private String nextDt;

	private String consBookingId;

	private String nextSummary;
	
	private String callinTime;
	
	private String visitClassify;
	
	private String nextStartTime;
	
	private String nextEndTime;
	
	private String nextVisitType;
	
	private String bookingStatus;

	public String getAppSerialNo() {
		return this.appSerialNo;
	}

	public void setAppSerialNo(String appSerialNo) {
		this.appSerialNo = appSerialNo;
	}

	public String getTradeDt() {
		return this.tradeDt;
	}

	public void setTradeDt(String tradeDt) {
		this.tradeDt = tradeDt;
	}

	public String getAppCode() {
		return this.appCode;
	}

	public void setAppCode(String appCode) {
		this.appCode = appCode;
	}

	public String getTxCode() {
		return this.txCode;
	}

	public void setTxCode(String txCode) {
		this.txCode = txCode;
	}

	public String getTxAppFlag() {
		return this.txAppFlag;
	}

	public void setTxAppFlag(String txAppFlag) {
		this.txAppFlag = txAppFlag;
	}

	public String getTxChkFlag() {
		return this.txChkFlag;
	}

	public void setTxChkFlag(String txChkFlag) {
		this.txChkFlag = txChkFlag;
	}

	public String getTradeChan() {
		return this.tradeChan;
	}

	public void setTradeChan(String tradeChan) {
		this.tradeChan = tradeChan;
	}

	public String getRegionCode() {
		return this.regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getOutletCode() {
		return this.outletCode;
	}

	public void setOutletCode(String outletCode) {
		this.outletCode = outletCode;
	}

	public String getAppDt() {
		return this.appDt;
	}

	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}

	public String getAppTm() {
		return this.appTm;
	}

	public void setAppTm(String appTm) {
		this.appTm = appTm;
	}

	public String getCustNo() {
		return this.custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getPcustId() {
		return this.pcustId;
	}

	public void setPcustId(String pcustId) {
		this.pcustId = pcustId;
	}

	public String getContactStaff() {
		return this.contactStaff;
	}

	public void setContactStaff(String contactStaff) {
		this.contactStaff = contactStaff;
	}

	public String getHowLong() {
		return this.howLong;
	}

	public void setHowLong(String howLong) {
		this.howLong = howLong;
	}

	public String getVisitRs() {
		return this.visitRs;
	}

	public void setVisitRs(String visitRs) {
		this.visitRs = visitRs;
	}

	public String getVisitCust() {
		return this.visitCust;
	}

	public void setVisitCust(String visitCust) {
		this.visitCust = visitCust;
	}

	public String getVisitTime() {
		return this.visitTime;
	}

	public void setVisitTime(String visitTime) {
		this.visitTime = visitTime;
	}

	public String getVisitType() {
		return this.visitType;
	}

	public void setVisitType(String visitType) {
		this.visitType = visitType;
	}

	public String getVisitSummary() {
		return this.visitSummary;
	}

	public void setVisitSummary(String visitSummary) {
		this.visitSummary = visitSummary;
	}

	public Double getVisitFee() {
		return this.visitFee;
	}

	public void setVisitFee(Double visitFee) {
		this.visitFee = visitFee;
	}

	public String getFeePurpose() {
		return this.feePurpose;
	}

	public void setFeePurpose(String feePurpose) {
		this.feePurpose = feePurpose;
	}

	public String getServStaff() {
		return this.servStaff;
	}

	public void setServStaff(String servStaff) {
		this.servStaff = servStaff;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRetCode() {
		return this.retCode;
	}

	public void setRetCode(String retCode) {
		this.retCode = retCode;
	}

	public String getRetMsg() {
		return this.retMsg;
	}

	public void setRetMsg(String retMsg) {
		this.retMsg = retMsg;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public String getStimeStamp() {
		return this.stimeStamp;
	}

	public void setStimeStamp(String stimeStamp) {
		this.stimeStamp = stimeStamp;
	}

	public String getConsCustNo() {
		return this.consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getNextDt() {
		return this.nextDt;
	}

	public void setNextDt(String nextDt) {
		this.nextDt = nextDt;
	}

	public String getConsBookingId() {
		return this.consBookingId;
	}

	public void setConsBookingId(String consBookingId) {
		this.consBookingId = consBookingId;
	}

	public String getNextSummary() {
		return this.nextSummary;
	}

	public void setNextSummary(String nextSummary) {
		this.nextSummary = nextSummary;
	}
	
	public String getCallinTime() {
		return callinTime;
	}

	public void setCallinTime(String callinTime) {
		this.callinTime = callinTime;
	}

	public String getVisitClassify() {
		return visitClassify;
	}

	public void setVisitClassify(String visitClassify) {
		this.visitClassify = visitClassify;
	}

	public String getNextStartTime() {
		return nextStartTime;
	}

	public void setNextStartTime(String nextStartTime) {
		this.nextStartTime = nextStartTime;
	}

	public String getNextEndTime() {
		return nextEndTime;
	}

	public void setNextEndTime(String nextEndTime) {
		this.nextEndTime = nextEndTime;
	}

	public String getNextVisitType() {
		return nextVisitType;
	}

	public void setNextVisitType(String nextVisitType) {
		this.nextVisitType = nextVisitType;
	}
	
	public String getBookingStatus() {
		return bookingStatus;
	}

	public void setBookingStatus(String bookingStatus) {
		this.bookingStatus = bookingStatus;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
