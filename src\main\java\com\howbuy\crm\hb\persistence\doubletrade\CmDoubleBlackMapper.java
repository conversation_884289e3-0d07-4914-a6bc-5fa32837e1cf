package com.howbuy.crm.hb.persistence.doubletrade;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleBlack;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmDoubleBlackMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmDoubleBlack getCmDoubleBlack(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmDoubleBlack
      */
	void insertCmDoubleBlack(CmDoubleBlack cmDoubleBlack);
	
	/**
	 * 单条修改数据对象
	 * @param cmDoubleBlack
	 */
	void updateCmDoubleBlack(CmDoubleBlack cmDoubleBlack);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmDoubleBlack(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmDoubleBlack(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleBlack> listCmDoubleBlack(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmDoubleBlackCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmDoubleBlack> listCmDoubleBlackByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
