package com.howbuy.crm.hb.domain.joinclub;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * @Description: 实体类CmConscustsurveyrec.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class CmConscustsurveyrec implements Serializable {

private static final long serialVersionUID = 1L;

	private String appserialno;
	
	private String tradedt;
	
	private String appcode;
	
	private String txcode;
	
	private String txappflag;
	
	private String txchkflag;
	
	private String txchkflagval;
	
	private String tradechan;
	
	private String regioncode;
	
	private String outletcode;
	
	private String appdt;
	
	private String apptm;
	
	private String conscustno;
	
	private String conscustname;
	
	private String surveyid;
	
	private String setid;
	
	private Double totalwp;
	
	private String pririsklevel;
	
	private String suggestionid;
	
	private String memo;
	
	private String retcode;
	
	private String retmsg;
	
	private String creator;
	
	private String checker;
	
	private Date stimestamp;
	
	private String gpsinvestlevel;
	
	private String checkadvice;
	
	private String upcheckadvice;
	
	private String gpsrisklevel;
	
	private String checkdt;

	private String maxcheckdt;
	
	private String checktm;
	
	private String custname;
	
	private String mobile;
	
	private String idno;
	
	private String inverstamt;
	
	private String inverstterm;
	
	private String inverstaim;

	private String consname;

	private String uporgname;

	private String orgcode;

	private String orgname;

	private String dataflag;

	private String singdate;

	private String idnoDigest;
	private String idnoMask;
	private String idnoCipher;

	private String mobileDigest;
	private String mobileMask;
	private String mobileCipher;
	
	private List<String> conscustnoList;

}
