package com.howbuy.crm.hb.domain.conscust;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@ColumnWidth(25)
public class CmTransfLogEntity {
    /**
     * 申请日期
     */
    @ExcelProperty(value = "申请日期", index = 0)
    private String appdt;

    /**
     * 申请中心
     */
    @ExcelProperty(value = "申请中心", index = 1)
    private String appupcentername;

    /**
     * 申请区域
     */
    @ExcelProperty(value = "申请区域", index = 2)
    private String appuporgname;

    /**
     * 申请部门
     */
    @ExcelProperty(value = "申请部门", index = 3)
    private String apporgname;

    /**
     * 申请投顾
     */
    @ExcelProperty(value = "申请投顾", index = 4)
    private String appconsname;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名", index = 5)
    private String custname;

    /**
     * 投顾客户号
     */
    @ExcelProperty(value = "投顾客户号", index = 6)
    private String conscustno;

    /**
     * 原投顾
     */
    @ExcelProperty(value = "原投顾", index = 7)
    private String oldconsname;

    /**
     * 原投顾是否分总
     */
    @ExcelProperty(value = "原投顾是否分总", index = 8)
    private String oldconsCodeIsFz;
    
    
    /**
     * 原中心
     */
    @ExcelProperty(value = "原中心", index = 9)
    private String oldupcentername;

    /**
     * 原区域
     */
    @ExcelProperty(value = "原区域", index = 10)
    private String olduporgname;

    /**
     * 原部门
     */
    @ExcelProperty(value = "原部门", index = 11)
    private String oldorgname;

    /**
     * 潜在/成交客户
     */
    @ExcelProperty(value = "潜在/成交客户", index = 12)
    private String custstatus;

    /**
     * 申请原因
     */
    @ExcelProperty(value = "申请原因", index = 13)
    private String transfcause;

    /**
     * 不符合条件
     */
    @ExcelProperty(value = "不符合条件", index = 14)
    private String miscondition;

    /**
     * 处理状态
     */
    @ExcelProperty(value = "处理状态", index = 15)
    private String opstatus;

    /**
     * 处理方式
     */
    @ExcelProperty(value = "处理方式", index = 16)
    private String optype;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 17)
    private String memo;
    
    /**
     * 处理后客户成交状态
     */
    @ExcelProperty(value = "处理后客户成交状态", index = 18)
    private String transstatus;

}
