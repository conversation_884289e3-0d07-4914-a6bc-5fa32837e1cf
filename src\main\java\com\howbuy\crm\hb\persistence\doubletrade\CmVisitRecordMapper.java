package com.howbuy.crm.hb.persistence.doubletrade;

import com.howbuy.crm.hb.domain.doubletrade.CmVisitRecord;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/4/28 13:42
 */
public interface CmVisitRecordMapper {

	/**
	 * 获取单个
	 * @param id
	 * @return
	 */
    CmVisitRecord getRecordByTradeId(String id);

    /**
     * 插入
     * @param cmVisitRecord
     */
    void insert(CmVisitRecord cmVisitRecord);

    /**
     * 更新
     * @param cmVisitRecord
     */
    void update(CmVisitRecord cmVisitRecord);

    /**
     * 分页查询
     * @param param
     * @param pageBean
     * @return
     */
    List<CmVisitRecord> listVisitRecordByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 查询列表
     * @param param
     * @return
     */
    List<CmVisitRecord> listVisitRecord(Map<String, String> param);
    
    /**
     * 查询单个
     * @param list
     * @return
     */
    List<String> selectHisCommRecord(List<String> list);

    /**
     * 查询用户权限
     * @param param
     * @return
     */
    List<Map<String, String>> listUserByAuth(Map<String, String> param);
}
