package com.howbuy.crm.hb.domain.system;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: dubbo调用日志实体类对象
 * @author: jianyi.tao
 * @create: 2022/07/20 16:25
 * @since: JDK 1.8
 */
@Data
public class CmDubboRequestLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID字段
     */
    private Long id;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 调用详细信息
     */
    private String invokerDetail;

    /**
     * 耗时
     */
    private Long usedTime;

    /**
     * 系统类型：1-crmcore；2-crmtd；3-crmnt；4-crmhb
     */
    private String sysType;
}
