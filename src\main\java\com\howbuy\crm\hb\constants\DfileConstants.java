/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.constants;

/**
 * @description: (文件写入读取 ， 配置常量)
 * <AUTHOR>
 * @date 2025/2/13 17:53
 * @since JDK 1.8
 */
public class DfileConstants {


    /**
     * 路演会议 文件 上传DFile 配置
     */
    public static final String  CONFERENCE_STORE_CONFIG = "conference_store_config";


    /**
     * 双录回访 文件 上传Dfile 配置
     */
    public  static  final String    DOUBLE_TRADE_STORE_CONFIG ="doubleTrade_storeConfig";


    /**
     * 资金到账证明 文件  Dfile 配置
     */
    public static final String  SIMU_MIDDLE_ARRIVAL_PROOF = "arrivalProof_storeConfig";

    /**
     * http://webdav-nfs01.inner.ehowbuy.com/simu/middle/highfundarrivalproof/YNJF/PE0328/伍先帆_淄博景良创业投资合伙企业（有限合伙）_20250207_202501240000010718.pdf
     * 解析
     * [http://webdav-nfs01.inner.ehowbuy.com][/simu/middle/highfundarrivalproof/][/YNJF/PE0328/][伍先帆_淄博景良创业投资合伙企业（有限合伙）_20250207_202501240000010718.pdf]
     */
    public static final String  SIMU_MIDDLE_ARRIVAL_PROOF_RELATIVE_PATH = "/simu/middle/highfundarrivalproof/";



    /**
     * 下载份额确认书
     */
    public static final String  VOL_CONFIRM_CONFIG = "vol_confirm_config";


    /**
     * 机构客户沟通记录
     * 历史功能，从未使用
     */
    public static final String ORG_COMMUNICATE_STORE_CONFIG = "orgCommunicate_store_config";


    /**
     * 私募客户调查问卷流水文件
     * 历史功能，私募客户调查问卷流水文件 日期停留在2022-10-14 16:22:01
     * 数据表：CM_CONSCUSTSURVEYREC_FILE 。
     * 数据库存储 ： /data/files/conscustsurveyrec/doc/20200716  。
     *
     * NOTICE : 映射关系   [http://webdav-nfs03.inner.ehowbuy.com/conscustsurveyrec/]   vs   [/data/files/conscustsurveyrec/]
     */
    public static final String CUST_SURVEYREC_STORE_CONFIG = "custSurveyRec_store_config";

    /**
     * 数据库存储 ： /data/files/conscustsurveyrec/doc/20200716  。
     * 该 常量定义 前端 ： /data/files/conscustsurveyrec
     * 方便 webdav处理时，获取相对路径：   /doc/20200716
     */
    public static final String CUST_SURVEYREC_PREFIX_PATH = "/data/files/conscustsurveyrec";




    /**
     * 家庭账户上传文件表
     * 历史功能，家庭账户上传文件表 CM_CUSTFAMILY_FILE 日期停留在  2022-03-21 18:16:27.151000
     * 数据表：CM_CUSTFAMILY_FILE 。
     * 数据库存储 ： /data/files/familyfile/doc/20210303  。
     *
     * NOTICE : 映射关系   [http://webdav-nfs03.inner.ehowbuy.com/familyfile/]   vs   [/data/files/familyfile/]
     */
    public static final String CUST_FAMILY_STORE_CONFIG = "custFamily_store_config";

    /**
     * 数据库存储 ： /data/files/familyfile/doc/20210303  。
     * 该 常量定义 前端 ： /data/files/familyfile
     * 方便 webdav处理时，获取相对路径：   /doc/20210303
     */
    public static final String CUST_FAMILY_PREFIX_PATH = "/data/files/familyfile";




}