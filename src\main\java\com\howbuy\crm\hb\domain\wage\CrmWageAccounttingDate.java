package com.howbuy.crm.hb.domain.wage;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 工资核算日期实体类
 * <AUTHOR>
 *
 */
@Data
public class CrmWageAccounttingDate implements Serializable {

    private static final long serialVersionUID = 3284708248442335979L;

    /** 预约ID */
    private long id;
    /** 工资核算日期 */
    private String accountingDate;
    /** 创建人 */
    private String creator;
    /** 创建时间 */
    private Timestamp createTime;
    /** 修改人 */
    private String updator;
    /** 修改时间 */
    private Timestamp updateTime;


}
