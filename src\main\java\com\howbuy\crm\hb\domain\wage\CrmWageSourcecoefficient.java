package com.howbuy.crm.hb.domain.wage;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CrmWageSourcecoefficient implements Serializable {

    private static final long serialVersionUID = 4532543889578184926L;

    private long id;

    private String fundType;

    private String fundTypename;

    private String sourceType;

    private String sourceTypename;

    private String orgcode;

    private String orgname;

    private String buyCnt;

    private String buyCntname;
    /** 来源系数（公司资源还是投顾资源） */
    private BigDecimal sourcecoefficient;

    private String startdt;

    private String enddt;

    private String creator;

    private String modifier;

    private String credt;

    private String moddt;
}
