package com.howbuy.crm.hb.persistence.conference;

import com.howbuy.crm.hb.domain.conference.CmConferenceInfomation;

import java.util.List;
import java.util.Map;


/**
 * 路演参会部门
 * <AUTHOR>
 *
 */
public interface CmConferenceInfomationMapper {

	/**
	 * 插入
	 * @param cmConferenceInfomation
	 */
	void insertCmConferenceInfomation(CmConferenceInfomation cmConferenceInfomation);
	
	/**
	 * 更新
	 * @param cmConferenceInfomation
	 */
	void updateCmConferenceInfomation(CmConferenceInfomation cmConferenceInfomation);
	
	/**
	 * 查询列表
	 * @param conferenceid
	 * @return
	 */
	List<CmConferenceInfomation> listCmConferenceInfomation(String conferenceid);

	/**
	 * 删除
	 * @param param
	 */
	void deleteInfomation(Map<String, String> param);

	/**
	 * 获取单个
	 * @param param
	 * @return
	 */
	CmConferenceInfomation getInfomationinfo(Map<String, String> param);
}
