package com.howbuy.crm.hb.domain.conscust;

import lombok.Data;

/**
 * @description:(客户 关键账户信息 )
 * @param
 * @return 
 * @author: haoran.zhang
 * @date: 2024/1/22 14:36
 * @since JDK 1.8
 */
@Data
public class CustAcctAttrVo  {


	/**
	 * 客户号
	 */
	private String custNo;



	/**
	 * 客户名称
	 */
	private String custName;

	/**
	 * 香港客户号
	 */
	private String hkTxAcctNo;


	/**
	 * 一账通号
	 */
	private String hboneNo;


	/**
	 * 投顾code
	 */
	private String consCode;

	/**
	 * 投顾名称
	 */
	private String consName;




}
