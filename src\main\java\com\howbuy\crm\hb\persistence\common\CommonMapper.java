package com.howbuy.crm.hb.persistence.common;

import java.util.Map;
import org.mybatis.spring.annotation.MapperScan;

/**
 * 
 * <AUTHOR>
 *
 */
@MapperScan
public interface CommonMapper{
	/**
	 * 获取序列
	 * @param seq
	 * @return
	 */
	public String getSeqValue(final String seq) ;
	/**
	 * 获取序列
	 * @param seq
	 * @return
	 */
	public long getLongSeqValue(final String seq) ;
	/**
	 * 获取函数值
	 * @param seq
	 * @return
	 */
	public Double getFuncValue(final String seq);
	/**
	 * 插入sql语句
	 * @param param
	 */
	public void insertBySql(final Map<String,Object> param);
}
