/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums.prebook;

/**
 * <AUTHOR>
 * @description: 折扣申请中 - [折扣方式]枚举
 * @date 2025/4/11 10:03
 * @since JDK 1.8
 */
public enum DiscountWaysEnum {


    /**
     * 直接少汇
     */
    DIRECTLY_REDUCE("1", "直接少汇"),

    /**
     * 好买返回
     */
    HOWBUY_RETURN("2", "好买返回");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    private DiscountWaysEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code 系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        DiscountWaysEnum statusEnum = getEnum(code);
        return statusEnum == null ? null : statusEnum.getDescription();
    }

    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code 系统返回参数编码
     * @return PreOccupyTypeEnum
     */
    public static DiscountWaysEnum getEnum(String code) {
        for (DiscountWaysEnum statusEnum : DiscountWaysEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}