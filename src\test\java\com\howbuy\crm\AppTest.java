package com.howbuy.crm;

import cn.hutool.core.date.DateUtil;
import org.junit.Test;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static org.junit.Assert.assertTrue;

/**
 * Unit test for simple App.
 */
public class AppTest 
{
    /**
     * Rigorous Test :-)
     */
    @Test
    public void shouldAnswerWithTrue()
    {
        assertTrue( true );
    }

    @Test
    public void test(){
//        Date date = new Date();
//        System.out.println(date);
//        System.out.println(DateUtil.date());
//        System.out.println(DateUtil.date(System.currentTimeMillis()));
//        BigDecimal bd = new BigDecimal("你是谁");
//        System.out.println(bd);
    }

    @Test
    public void stringToDate() throws ParseException {
        String date = "20201020142837";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyymmdd hh:mm:ss");
        String format = String.format(date, dateFormat);
        System.out.println(format);
        SimpleDateFormat input = new SimpleDateFormat("yyyyMMddhhmmss");//输入格式
        SimpleDateFormat target = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //转化成为
        String dateStr = target.format(input.parse(date));
        System.out.println(dateStr);
    }
    public static void main(String[] args) {
        String date = "20201020142837";
        SimpleDateFormat input = new SimpleDateFormat("yyyyMMddhhmmss");//输入格式
        SimpleDateFormat target = new SimpleDateFormat("yyyyMMdd HH:mm:ss"); //转化成为
        String dateStr = null;
        try {
            dateStr = target.format(input.parse(date));
//            String format = String.format("%1$tY%1$tm%1$td %1$tT", input.parse(date));
//            System.out.println(format);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        System.out.println(dateStr);
    }

}
