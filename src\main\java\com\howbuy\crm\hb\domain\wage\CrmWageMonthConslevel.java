package com.howbuy.crm.hb.domain.wage;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CrmWageMonthConslevel implements Serializable {

    private static final long serialVersionUID = -7468987208051042938L;

    private long id;

    private String orgCode;

    private BigDecimal monthBackstepMin;

    private BigDecimal monthBackstepMax;

    private String conslevel;

    private String startdt;

    private String enddt;

    private String creator;

    private String  modifier;

    private Timestamp credt;

    private Timestamp moddt;
}
