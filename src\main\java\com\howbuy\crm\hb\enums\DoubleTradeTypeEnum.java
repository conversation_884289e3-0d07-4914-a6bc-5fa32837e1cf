package com.howbuy.crm.hb.enums;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/11/17 15:05
 */
public enum DoubleTradeTypeEnum {
    /**
     * 持仓份额
     */
    DOUBLE_TREADE("1", "双录"),
    /**
     * 当前收益
     */
    VISIT_CALL("2", "回访");
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    DoubleTradeTypeEnum(String code, String description){
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
