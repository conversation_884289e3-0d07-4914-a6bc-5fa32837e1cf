package com.howbuy.crm.hb.persistence.associationmail;

import com.howbuy.crm.hb.domain.associationmail.BdpMatchLog;
import com.howbuy.crm.hb.domain.associationmail.ResolveMailLog;
import com.howbuy.crm.hb.domain.associationmail.SendMessageLog;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 协会邮件-系统日志
 * <AUTHOR> on 2021/6/15 13:31
 */
public interface AssociationMailLogMapper {

    /**
     * 查询邮件解析日志
     * @param param 查询参数
     * @return
     */
    ResolveMailLog findResolveMailLog(@Param("param") Map<String, String> param);

    /**
     * 查询BDP匹配日志
     * @param param 查询参数
     * @return
     */
    BdpMatchLog findBdpMatchLog(@Param("param") Map<String, String> param);

    /**
     * 查询短信发送日志
     * @param param 查询参数
     * @return
     */
    SendMessageLog findSendMessageLog(@Param("param") Map<String, String> param);
}
