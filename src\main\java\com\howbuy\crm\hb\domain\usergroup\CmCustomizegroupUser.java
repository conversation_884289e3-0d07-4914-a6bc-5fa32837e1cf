package com.howbuy.crm.hb.domain.usergroup;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmCustomizegroupUser implements Serializable {
	private static final long serialVersionUID = 1L;
	private String id;
	private String groupid;
	private String custno;
	private String creator;
	private String modifier;
	private String credt;
	private String moddt;
	
	/** 下面为后来追加信息 */
	private String groupname;
	private String custname;
	private String provcode;
	private String citycode;
	private String mobile;
	private String telno;
	private String email;
	private String idno;
	private String source;
	private String sourcename;
	private String conscode;
	private String consname;
	private String seniormgrcode;
	private String startdt;
	private String orgcode;
	private String latesttradedt;
	private String idnoDigest;
	private String idnoMask;
	private String idnoCipher;

	private String mobileDigest;
	private String mobileMask;
	private String mobileCipher;

	private String telnoDigest;
	private String telnoMask;
	private String telnoCipher;

	private String emailDigest;
	private String emailMask;
	private String emailCipher;
	

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getGroupid() {
		return this.groupid;
	}

	public void setGroupid(String groupid) {
		this.groupid = groupid;
	}

	public String getCustno() {
		return this.custno;
	}

	public void setCustno(String custno) {
		this.custno = custno;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public String getGroupname() {
		return groupname;
	}

	public void setGroupname(String groupname) {
		this.groupname = groupname;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getProvcode() {
		return provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTelno() {
		return telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getIdno() {
		return idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSourcename() {
		return sourcename;
	}

	public void setSourcename(String sourcename) {
		this.sourcename = sourcename;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

	public String getSeniormgrcode() {
		return seniormgrcode;
	}

	public void setSeniormgrcode(String seniormgrcode) {
		this.seniormgrcode = seniormgrcode;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getStartdt() {
		return startdt;
	}

	public void setStartdt(String startdt) {
		this.startdt = startdt;
	}

	public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	public String getLatesttradedt() {
		return latesttradedt;
	}

	public void setLatesttradedt(String latesttradedt) {
		this.latesttradedt = latesttradedt;
	}

	public String getIdnoDigest() {
		return idnoDigest;
	}

	public void setIdnoDigest(String idnoDigest) {
		this.idnoDigest = idnoDigest;
	}

	public String getIdnoMask() {
		return idnoMask;
	}

	public void setIdnoMask(String idnoMask) {
		this.idnoMask = idnoMask;
	}

	public String getIdnoCipher() {
		return idnoCipher;
	}

	public void setIdnoCipher(String idnoCipher) {
		this.idnoCipher = idnoCipher;
	}

	public String getMobileDigest() {
		return mobileDigest;
	}

	public void setMobileDigest(String mobileDigest) {
		this.mobileDigest = mobileDigest;
	}

	public String getMobileMask() {
		return mobileMask;
	}

	public void setMobileMask(String mobileMask) {
		this.mobileMask = mobileMask;
	}

	public String getMobileCipher() {
		return mobileCipher;
	}

	public void setMobileCipher(String mobileCipher) {
		this.mobileCipher = mobileCipher;
	}

	public String getTelnoDigest() {
		return telnoDigest;
	}

	public void setTelnoDigest(String telnoDigest) {
		this.telnoDigest = telnoDigest;
	}

	public String getTelnoMask() {
		return telnoMask;
	}

	public void setTelnoMask(String telnoMask) {
		this.telnoMask = telnoMask;
	}

	public String getTelnoCipher() {
		return telnoCipher;
	}

	public void setTelnoCipher(String telnoCipher) {
		this.telnoCipher = telnoCipher;
	}

	public String getEmailDigest() {
		return emailDigest;
	}

	public void setEmailDigest(String emailDigest) {
		this.emailDigest = emailDigest;
	}

	public String getEmailMask() {
		return emailMask;
	}

	public void setEmailMask(String emailMask) {
		this.emailMask = emailMask;
	}

	public String getEmailCipher() {
		return emailCipher;
	}

	public void setEmailCipher(String emailCipher) {
		this.emailCipher = emailCipher;
	}
	
}
