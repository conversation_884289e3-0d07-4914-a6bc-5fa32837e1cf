package com.howbuy.crm.hb.domain.associationmail;

import lombok.Data;

import java.io.Serializable;

/**
 * 短信发送日志
 * <AUTHOR> on 2021/6/15 11:19
 */
@Data
public class SendMessageLog implements Serializable {

    /** 主键 */
    private String id;

    /** 创建人 */
    private String creator;

    /** 记录创建日期 */
    private String credt;

    /** 待发送总数 */
    private Integer needSendTotalNum;

    /** 无一账通号数量 */
    private Integer notHaveHboneNum;

    /** 待发送数量（开通通知数） */
    private Integer needSendNum;

    /** 待重发数量（密码重置通知数） */
    private Integer needResendNum;

    /** 成功推送数 */
    private Integer pushSuccessNum;

    /** 推送失败数 */
    private Integer pushFailNum;

    /** 耗时 */
    private Long elapsedTime;
}
