package com.howbuy.crm.hb.domain.conference;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/7/17 13:20
 */
public class DepartmentForLimitVo {

    private String conferenceId;
    private String orgCode;
    private String orgName;
    private Integer orgNum;
    private Integer joinNum;
    private List<DepartmentForLimitVo> subDepartments = new ArrayList<>();
    private Integer unallocatedJoinNum;
    private Integer unallocatedNum;
    private String unallocatedName;
    private boolean isEndNode;

    public String getConferenceId() {
        return conferenceId;
    }

    public void setConferenceId(String conferenceId) {
        this.conferenceId = conferenceId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOrgNum() {
        return orgNum;
    }

    public void setOrgNum(Integer orgNum) {
        this.orgNum = orgNum;
    }

    public Integer getJoinNum() {
        return joinNum;
    }

    public void setJoinNum(Integer joinNum) {
        this.joinNum = joinNum;
    }

    public List<DepartmentForLimitVo> getSubDepartments() {
        return subDepartments;
    }

    public void setSubDepartments(List<DepartmentForLimitVo> subDepartments) {
        this.subDepartments = subDepartments;
    }

    public Integer getUnallocatedJoinNum() {
        return unallocatedJoinNum;
    }

    public void setUnallocatedJoinNum(Integer unallocatedJoinNum) {
        this.unallocatedJoinNum = unallocatedJoinNum;
    }

    public boolean getIsEndNode() {
        return isEndNode;
    }

    public void setIsEndNode(boolean isEndNode) {
        this.isEndNode = isEndNode;
    }

    public Integer getUnallocatedNum() {
        return unallocatedNum;
    }

    public void setUnallocatedNum(Integer unallocatedNum) {
        this.unallocatedNum = unallocatedNum;
    }

    public String getUnallocatedName() {
        return unallocatedName;
    }

    public void setUnallocatedName(String unallocatedName) {
        this.unallocatedName = unallocatedName;
    }

}
