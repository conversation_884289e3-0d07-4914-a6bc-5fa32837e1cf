/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.hb.enums;
/**
 * @description:(佣金结算操作：1-开票、2-结算、3-取消结算)
 * @author: haoran.zhang
 * @notice :
 * 1-开票 前置状态：待结算  开票完成  后置状态：已开票
 * 2-结算 前置状态：已开票  结算完成  后置状态：已结算
 * 3-取消结算 前置状态：已结算  取消结算完成  后置状态：待结算
 * @param null
 * @return
 * @author: haoran.zhang
 * @date: 2024/10/14 17:49
 * @since JDK 1.8
 */
public enum CommissionOptEnum {

	/**
	 * 待结算
	 */
	KP("1", "开票",CommissionStateEnum.WAIT_SETTLE,CommissionStateEnum.ALREADY_INVOICE),
    /**
     * 已开票
     */
    SETTLE("2", "结算",CommissionStateEnum.ALREADY_INVOICE,CommissionStateEnum.ALREADY_SETTLE),
    /**
     * 已结算
     */
    CANCEL_SETTLE("3", "取消结算",CommissionStateEnum.ALREADY_SETTLE,CommissionStateEnum.WAIT_SETTLE);

	/**
	 * 编码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String description;

	/**
	 * 前置要求数据 状态
	 */
	private CommissionStateEnum preRequireStateEnum;


	/**
	 * 处理后的数据 状态
	 */
	private CommissionStateEnum afterDealStateEnum
	;

	private CommissionOptEnum(String code,
							  String description,
							  CommissionStateEnum preRequireStateEnum,
							  CommissionStateEnum afterDealStateEnum) {
		this.code = code;
		this.description = description;
		this.preRequireStateEnum = preRequireStateEnum;
		this.afterDealStateEnum = afterDealStateEnum;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CommissionOptEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static CommissionOptEnum getEnum(String code) {
		for(CommissionOptEnum statusEnum : CommissionOptEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public CommissionStateEnum getPreRequireStateEnum() {
		return preRequireStateEnum;
	}

	public void setPreRequireStateEnum(CommissionStateEnum preRequireStateEnum) {
		this.preRequireStateEnum = preRequireStateEnum;
	}

	public CommissionStateEnum getAfterDealStateEnum() {
		return afterDealStateEnum;
	}

	public void setAfterDealStateEnum(CommissionStateEnum afterDealStateEnum) {
		this.afterDealStateEnum = afterDealStateEnum;
	}
}
