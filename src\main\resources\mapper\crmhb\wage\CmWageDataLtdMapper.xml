<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CmWageBaseDataLtdMapper">
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	<!--  季度绩效提成-->
	<select id="excuteProWageQuaperLtd" statementType="CALLABLE"   useCache="false" parameterType="Map" resultType="Map">
		<![CDATA[
		{call PRO_WAGE_QUAPER_LTD(
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} )}
		]]>
	</select>
	<!--产品系数-->
	<select id="excuteProWageProcoeLtd" statementType="CALLABLE"  useCache="false" parameterType="Map" resultType="Map">
		<![CDATA[
		{call PRO_WAGE_PROCOE_LTD(
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} )}
		]]>
	</select>
	<!--投顾等级系数表-->
	<select id="excuteProWageConslevelLtd" statementType="CALLABLE"   useCache="false" parameterType="Map" resultType="Map">
		<![CDATA[
		{call PRO_WAGE_CONSLEVEL_LTD(
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} )}
		]]>
	</select>
	<!--客户来源系数表-->
	<select id="excuteProWageSoucoeLtd" statementType="CALLABLE"   useCache="false" parameterType="Map" resultType="Map">
		<![CDATA[
		{call PRO_WAGE_SOUCOE_LTD(
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} )}
		]]>
	</select>


</mapper>



