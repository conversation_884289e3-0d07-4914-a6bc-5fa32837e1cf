package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.conscust.SurveyAnswerPtsRec;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface SurveyAnswerPtsRecMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    SurveyAnswerPtsRec getSurveyAnswerPtsRec(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param surveyAnswerPtsRec
      */
	void insertSurveyAnswerPtsRec(SurveyAnswerPtsRec surveyAnswerPtsRec);
	
	/**
	 * 单条修改数据对象
	 * @param surveyAnswerPtsRec
	 */
	void updateSurveyAnswerPtsRec(SurveyAnswerPtsRec surveyAnswerPtsRec);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delSurveyAnswerPtsRec(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListSurveyAnswerPtsRec(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<SurveyAnswerPtsRec> listSurveyAnswerPtsRec(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getSurveyAnswerPtsRecCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<SurveyAnswerPtsRec> listSurveyAnswerPtsRecByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
