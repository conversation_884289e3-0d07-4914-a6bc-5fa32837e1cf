package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/6/11 17:48
 */
@Data
public class OrgCommunicateAttend implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 沟通记录主键
     */
    private Long communicateId;

    /**
     * 参会人
     */
    private String attendMan;

    /**
     * 参会人类型 1 客户 2 我司
     */
    private String attendManType;

    /**
     * 其他参会人
     */
    private String otherAttendMan;

    /**
     * 职位
     */
    private String positionName;
}
