package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品预约日历操作日志
 * <AUTHOR>
 * @time 2021-11-08 15:45:22
 */
public class CmPreCalendarLog  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;
    
    /**
     * 类型模块：1-日历修改；2-参数修改
     */
    private String opType;
    
    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
      */
    private Date createStimestamp;
    
    /**
     * 审核状态:1-待审核、2-审核通过、3-审核不通过
     */
    private String checkStatus;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getOpType() {
		return opType;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateStimestamp() {
		return createStimestamp;
	}

	public void setCreateStimestamp(Date createStimestamp) {
		this.createStimestamp = createStimestamp;
	}

	public String getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(String checkStatus) {
		this.checkStatus = checkStatus;
	}
}
