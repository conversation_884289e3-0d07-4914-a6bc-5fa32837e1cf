package com.howbuy.crm.hb.domain.conference;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * (路演参会人员)
 * <AUTHOR>
 * @ClassName: CmConferenceConscust
 * 											  
 * 创建日期    		                            修改人员   	        版本	 	     修改内容  
 * -------------------------------------------------  
 * 2017年8月14日 下午5:09:21   yu.zhang     1.0    	初始化创建
 *
 * 修改记录:
 * @since		JDK1.6
 */
public class CmConferenceConscust implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 参与会议(外键) */
	private String conferenceid;
	
	/** 参与会议(外键) */
	private String conferencename;
	
	/** 投顾客户号 */
	private String conscustno;
	
	/** 所属投顾 */
	private String conscode;

	/** 所属投顾 */
	private String uporgname;
	
	/** 所属部门 */
	private String orgcode;
	
	/** 预约人数 */
	private int appointmentsnub;
	
	/** 参会预约码 */
	private String appointmentscode;
	
	/** 实际到场人数 */
	private int actualnub;

	/** 签到时间 */
	private Date actualnubdt;

	/** 签到时间 */
	private String actualnubdtstr;

	/** 创建日期 */
	private String creatdt;
	
	/** 创建人 */
	private String creater;

	/** 创建人姓名 */
	private String createrName;
	
	/** 修改日期 */
	private String modifydt;
	
	/** 修改人 */
	private String modifier;
	
	/** 修改人 */
	private String custname;
	
	/** 投顾姓名 */
	private String consname;
	
	/** 参会截止操作时间 */
	private String cutoffdt;

	private String appointmentstype;

	private String appointmentstypename;

	/** 报名时客户状态（1：成交；0：潜客） */
	private String gdcjlabel;
	private String gdcjlabelname;

	private String numb;

	private String infomationnumb;
	
	private List<String> conscustnoList;
	
	/** 会议时间 */
	private String conferencedt;
	
	/** 会议省份 */
	private String provcode;

	/** 会议市区 */
	private String citycode;
	
	/** 会议类型 */
	private String conferencetype;

	/** 会议id */
	private String courseid;

	/**
	 * 高端首次交易日期
	 */
	private String firtrdt;

	/**
	 * 会议总数
	 */
	private String count;

	/**
	 * 数据来源
	 */
	private String source;

	public String getCourseid() {
		return courseid;
	}

	public void setCourseid(String courseid) {
		this.courseid = courseid;
	}

	public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

	public String getConferencename() {
		return conferencename;
	}

	public void setConferencename(String conferencename) {
		this.conferencename = conferencename;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getConferenceid() {
		return conferenceid;
	}

	public void setConferenceid(String conferenceid) {
		this.conferenceid = conferenceid;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	public int getAppointmentsnub() {
		return appointmentsnub;
	}

	public void setAppointmentsnub(int appointmentsnub) {
		this.appointmentsnub = appointmentsnub;
	}

	public String getAppointmentscode() {
		return appointmentscode;
	}

	public void setAppointmentscode(String appointmentscode) {
		this.appointmentscode = appointmentscode;
	}

	public int getActualnub() {
		return actualnub;
	}

	public void setActualnub(int actualnub) {
		this.actualnub = actualnub;
	}

	public String getCreatdt() {
		return creatdt;
	}

	public void setCreatdt(String creatdt) {
		this.creatdt = creatdt;
	}

	public String getCreater() {
		return creater;
	}

	public void setCreater(String creater) {
		this.creater = creater;
	}

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public String getModifydt() {
		return modifydt;
	}

	public void setModifydt(String modifydt) {
		this.modifydt = modifydt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCutoffdt() {
		return cutoffdt;
	}

	public void setCutoffdt(String cutoffdt) {
		this.cutoffdt = cutoffdt;
	}

	public String getAppointmentstype() {
		return appointmentstype;
	}

	public void setAppointmentstype(String appointmentstype) {
		this.appointmentstype = appointmentstype;
	}

	public String getAppointmentstypename() {
		return appointmentstypename;
	}

	public void setAppointmentstypename(String appointmentstypename) {
		this.appointmentstypename = appointmentstypename;
	}

	public String getGdcjlabel() {
		return gdcjlabel;
	}

	public void setGdcjlabel(String gdcjlabel) {
		this.gdcjlabel = gdcjlabel;
	}

	public String getGdcjlabelname() {
		return gdcjlabelname;
	}

	public void setGdcjlabelname(String gdcjlabelname) {
		this.gdcjlabelname = gdcjlabelname;
	}

	public String getNumb() {
		return numb;
	}

	public void setNumb(String numb) {
		this.numb = numb;
	}

	public String getInfomationnumb() {
		return infomationnumb;
	}

	public void setInfomationnumb(String infomationnumb) {
		this.infomationnumb = infomationnumb;
	}

	public List<String> getConscustnoList() {
		return conscustnoList;
	}

	public void setConscustnoList(List<String> conscustnoList) {
		this.conscustnoList = conscustnoList;
	}

	public Date getActualnubdt() {
		return actualnubdt;
	}

	public void setActualnubdt(Date actualnubdt) {
		this.actualnubdt = actualnubdt;
	}

	public String getActualnubdtstr() {
		return actualnubdtstr;
	}

	public void setActualnubdtstr(String actualnubdtstr) {
		this.actualnubdtstr = actualnubdtstr;
	}

	public String getUporgname() {
		return uporgname;
	}

	public void setUporgname(String uporgname) {
		this.uporgname = uporgname;
	}

	public String getConferencedt() {
		return conferencedt;
	}

	public void setConferencedt(String conferencedt) {
		this.conferencedt = conferencedt;
	}

	public String getProvcode() {
		return provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public String getConferencetype() {
		return conferencetype;
	}

	public void setConferencetype(String conferencetype) {
		this.conferencetype = conferencetype;
	}

	public String getFirtrdt() {
		return firtrdt;
	}

	public void setFirtrdt(String firtrdt) {
		this.firtrdt = firtrdt;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
}
