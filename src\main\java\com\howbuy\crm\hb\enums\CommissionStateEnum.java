/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.hb.enums;
/**
 * @description:(佣金结算状态：1-待结算、2-已开票、3-已结算)
 * @param null
 * @return
 * @author: haoran.zhang
 * @date: 2024/10/14 17:49
 * @since JDK 1.8
 */
public enum CommissionStateEnum {

	/**
	 * 待结算
	 */
	WAIT_SETTLE("1", "待结算"),
    /**
     * 已开票
     */
    ALREADY_INVOICE("2", "已开票"),
    /**
     * 已结算
     */
    ALREADY_SETTLE("3", "已结算");

	/**
	 * 编码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String description;

	private CommissionStateEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CommissionStateEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static CommissionStateEnum getEnum(String code) {
		for(CommissionStateEnum statusEnum : CommissionStateEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
