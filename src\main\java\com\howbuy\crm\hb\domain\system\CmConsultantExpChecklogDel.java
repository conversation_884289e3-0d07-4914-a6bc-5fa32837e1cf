package com.howbuy.crm.hb.domain.system;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**  
    * @description: TODO
    * <AUTHOR>
    * @date 2024/8/7 11:29
    */
/**
 * 投顾HR扩展审核记录删除表
 */
@Getter
@Setter
public class CmConsultantExpChecklogDel {
    /**
    * 投顾编号
    */
    private String userid;

    /**
    * 工号
    */
    private String userno;

    /**
    * 省份code
    */
    private String provcode;

    /**
    * 城市code
    */
    private String citycode;

    /**
    * 性别1：男；2：女
    */
    private String gender;

    /**
    * 出生日期YYYYMMDD
    */
    private String birthday;

    /**
    * 学历：1高中及以下、2专科、3本科、4硕士、5MBA、6博士
    */
    private String edulevel;

    /**
    * 是否在职：1在职、2离职、3在途新人
    */
    private String worktype;

    /**
    * 在职状态：1正式、2试用期
    */
    private String workstate;

    /**
    * 层级：1理财师、2团队总监、3分总、4区域执行副总、5区域总、6销售总监、7中后台
    */
    private String userlevel;

    /**
    * 当月职级：1观察期、2理财师1级、3理财师2级、4理财师3级、5资深理财师1级、6资深理财师2级、7资深理财师3级、8私人银行家1级、9私人银行家2级、10私人银行家3级、11私人银行家4级、12一级财富管理部总监、13二级财富管理部总监、14三级财富管理部总监、15四级财富管理部总监（区域副总）、16区域执行副总、17一级区域总经理、18二级区域总经理、19三级区域总经理、20销售总监
    */
    private String curmonthlevel;

    /**
    * 入职日期YYYYMMDD
    */
    private String startdt;

    /**
    * 入职职级：枚举同当月职级
    */
    private String startlevel;

    /**
    * 入职薪资
    */
    private BigDecimal salary;

    /**
    * 试用期截止日期YYYYMMDD
    */
    private String probationenddt;

    /**
    * 转正日期YYYYMMDD
    */
    private String regulardt;

    /**
    * 转正职级：枚举同当月职级
    */
    private String regularlevel;

    /**
    * 转正薪资
    */
    private BigDecimal regularsalary;

    /**
    * 离职日期YYYYMMDD
    */
    private String quitdt;

    /**
    * 离职职级：枚举同当月职级
    */
    private String quitlevel;

    /**
    * 离职薪资
    */
    private BigDecimal quitsalary;

    /**
    * 离职原因:1主动离职、2淘汰、3转岗
    */
    private String quitreason;

    /**
    * 好买司龄
    */
    private BigDecimal servingage;

    /**
    * 审核状态：1待审核；2审核通过；3审核不通过
    */
    private String checkflag;

    /**
    * 审核人
    */
    private String checkor;

    /**
    * 基金从业资格编码
    */
    private String jjcardno;

    /**
    * 是否挂靠:1是；2否
    */
    private String attachtype;

    /**
    * 上家公司
    */
    private String background;

    /**
    * 背景来源
    */
    private String source;

    /**
    * 入职好买前职位类型
    */
    private String beforepositiontype;

    /**
    * 入职好买前工作年限
    */
    private String beforepositionage;

    /**
    * 招聘经理
    */
    private String recruit;

    /**
    * 推荐人
    */
    private String recommend;

    /**
    * 招聘渠道：1MGM、2管理层自带、3管理层自带-层级穿透、4其他
    */
    private String recommendtype;

    /**
    * 备注
    */
    private String remark;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date creatdt;

    /**
    * 修改人
    */
    private String modor;

    /**
    * 修改时间
    */
    private Date moddt;

    /**
    * 邮箱
    */
    private String email;

    /**
    * 所属小组
    */
    private String teamcode;

    /**
    * 所属部门
    */
    private String outletcode;

    /**
    * 投顾名称
    */
    private String consname;

    /**
    * 推荐人工号
    */
    private String recommenduserno;

    /**
    * 副职 字典subpositionslevel
    */
    private String subpositions;

    /**
    * 下次考核截止日期 YYYYMMDD
    */
    private String nexttestdate;

    /**
    * 试用3M考核结果
    */
    private String probationresult3m;

    /**
    * 试用6M考核结果
    */
    private String probationresult6m;

    /**
    * 当月薪资
    */
    private BigDecimal curmonthsalary;

    /**
    * 离职去向
    */
    private String quitinfo;

    /**
    * 管理日期
    */
    private String promotedate;

    /**
    * 投顾创新方案
    */
    private String bxcommissionway;

    /**
    * 北森ID
    */
    private String beisenid;

    /**
    * 12m考核结果
    */
    private String probationResult12m;

    /**
    * 业务中心
    */
    private String centerOrg;

    /**
    * 调整司龄(月)
    */
    private BigDecimal adjustServingMonth;

    /**
    * 调整管理司龄(月)
    */
    private BigDecimal adjustManageServingMonth;

    /**
    * 3M日期
    */
    private String dt3m;

    /**
    * 3M日期人工修改标识1是0否
    */
    private String dt3mFlag;

    /**
    * 12M日期
    */
    private String dt12m;

    /**
    * 12M日期人工修改标识1是0否
    */
    private String dt12mFlag;

    /**
    * 转正日期人工修改标识1是0否
    */
    private String regulardtFlag;

    /**
    * 下次考核截止日期人工修改标识1是0否
    */
    private String nexttestdateFlag;

    /**
    * 下次考核周期
    */
    private String nextTestPeriod;

    /**
    * 试用3M薪资
    */
    private BigDecimal probationSalary3m;

    /**
    * 12M薪资
    */
    private BigDecimal salary12m;

    /**
    * 入职档级
    */
    private String joinRank;

    /**
    * 试用3M档级
    */
    private String probationRank3m;

    /**
    * 转正档级
    */
    private String regularRank;

    /**
    * 12M档级
    */
    private String rank12m;

    /**
    * 入职社保基数
    */
    private String joinSsb;

    /**
    * 试用3M社保基数
    */
    private String probationSsb3m;

    /**
    * 转正社保基数
    */
    private String regularSsb;

    /**
    * 12M社保基数
    */
    private String ssb12m;

    /**
    * 试用3M考核职级
    */
    private String probationlevel3m;

    /**
    * 12M考核职级
    */
    private String testlevel12m;
}