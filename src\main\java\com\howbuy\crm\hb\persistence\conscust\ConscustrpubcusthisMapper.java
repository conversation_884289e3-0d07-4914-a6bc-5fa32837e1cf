package com.howbuy.crm.hb.persistence.conscust;

import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ConscustrpubcusthisMapper {

	/**
	 * 查询公募客户关联历史记录
	 * @param param
	 * @param pageBean
	 * @return
	 */
	public List<Map<String,Object>> listPubCustRelationHisByPage(@Param("p") Map<String, String> param, @Param("page") CommPageBean pageBean);
}
