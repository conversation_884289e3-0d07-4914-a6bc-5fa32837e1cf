package com.howbuy.crm.hb.persistence.insur;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.insur.CmBxPrebookMatch;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxPrebookMatchMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxPrebookMatch getCmBxPrebookMatch(CmBxPrebookMatch CmBxPrebookMatch);
    
     /**
      * 新增数据对象
      * @param CmBxPrebookMatch
      */
	void insertCmBxPrebookMatch(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 单条修改数据对象
	 * @param CmBxPrebookMatch
	 */
	void updateCmBxPrebookMatch(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 单条删除数据对象
	 * @param CmBxPrebookMatch
	 */
	void deleteCmBxPrebookMatch(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 批量删除数据对象
	 * @param CmBxPrebookMatch
	 */
	void deleteCmBxPrebookMatchOpt(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 合并归档数据
	 * @param CmBxPrebookMatch
	 */
	void mergeCmBxPrebookMatch(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxPrebookMatch> listCmBxPrebookMatch(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxPrebookMatch> listCmBxPrebookMatchOpt(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 查询导入的新数据与原归档的数据有相同的数据
	 * @param param
	 * @return
	 */
	List<CmBxPrebookMatch> listSameMatchOpt(CmBxPrebookMatch CmBxPrebookMatch);
	
	/**
	 * 分页查询
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmBxPrebookMatch> listCmBxPrebookMatchByPage(@Param("param") CmBxPrebookMatch param, @Param("page") CommPageBean pageBean);
	
	/**
     * 查询导入的数据和核保管理的匹配结果列表
     *
     * @param cmBxPrebookMatch
     * @return
     */
    List<CmBxPrebookMatch> listMatchBxPrebookOpt(CmBxPrebookMatch cmBxPrebookMatch) ;
}
