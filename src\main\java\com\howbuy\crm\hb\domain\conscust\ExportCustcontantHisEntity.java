package com.howbuy.crm.hb.domain.conscust;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@ColumnWidth(25)
public class ExportCustcontantHisEntity {
    /**
     * 投顾客户号
     */
    @ExcelProperty(value = "投顾客户号", index = 0)
    private String custNo;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 1)
    private String custName;

    /**
     * 原投顾
     */
    @ExcelProperty(value = "原投顾", index = 2)
    private String fromConsName;

    /**
     * 原区域
     */
    @ExcelProperty(value = "原区域", index = 3)
    private String fromUporgName;

    /**
     * 原部门
     */
    @ExcelProperty(value = "原部门", index = 4)
    private String fromOrgName;

    /**
     * 分配投顾
     */
    @ExcelProperty(value = "分配投顾", index = 5)
    private String toConsName;

    /**
     * 分配区域
     */
    @ExcelProperty(value = "分配区域", index = 6)
    private String toUporgName;

    /**
     * 分配部门
     */
    @ExcelProperty(value = "分配部门", index = 7)
    private String toOrgName;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人", index = 8)
    private String creator;
    
    
    /**
     * 操作日期
     */
    @ExcelProperty(value = "操作日期", index = 9)
    private String credt;

    /**
     * 分配原因
     */
    @ExcelProperty(value = "分配原因", index = 10)
    private String reason;

    /**
     * 客户状态
     */
    @ExcelProperty(value = "客户状态", index = 11)
    private String custTradeType;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 12)
    private String remark;

    /**
     * 地区
     */
    @ExcelProperty(value = "地区", index = 13)
    private String area;

    /**
     * 无线渠道
     */
    @ExcelProperty(value = "无线渠道", index = 14)
    private String wifiChannel;

    /**
     * 客户来源
     */
    @ExcelProperty(value = "客户来源", index = 15)
    private String custSource;

    /**
     * 处理类型
     */
    @ExcelProperty(value = "处理类型", index = 16)
    private String dealType;

    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型", index = 17)
    private String custType;
    
    /**
     * 省
     */
    @ExcelProperty(value = "省", index = 18)
    private String provCode;

    /**
     * 市
     */
    @ExcelProperty(value = "市", index = 19)
    private String cityCode;
}
