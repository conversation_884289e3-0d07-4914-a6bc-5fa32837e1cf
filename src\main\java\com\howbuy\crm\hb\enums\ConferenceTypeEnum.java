/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

import crm.howbuy.base.utils.StringUtil;

/**
 * @description: (路演会议类型  key--- 对应crm  value--- 对应cms)
 * <AUTHOR>
 * @date 2023/7/31 19:55
 * @since JDK 1.8
 */
public enum ConferenceTypeEnum {

    /**
     * --理财九章-长宽高
     */
    LCJZ_CKG("9a", "1"),
    /**
     * --理财九章-财富健康度
     */
    LCJZ_CFJKD("9b", "2"),
    /**
     * ---理财九章-大类-股票
     */
    LCJZ_BIG_GP("9c", "3"),
    /**
     * --理财九章-大类-固收
     */
    LCJZ_BIG_GS("9d", "4"),
    /**
     * --理财九章-大类-股权
     */
    LCJZ_BIG_GQ("9e", "5"),
    /**
     * --理财九章-大类-FOF
     */
    LCJZ_BIG_FOF("9f", "6"),
    /**
     * --理财九章-大类-海外
     */
    LCJZ_BIG_HW("9g", "7"),
    /**
     * --理财九章-大类-保险
     */
    LCJZ_BIG_BX("9h", "8"),
    /**
     * --理财九章-大类-家办
     */
    LCJZ_BIG_JB("9i", "9");

    private String key;
    private String value;


    public String getValue() {
        return value;
    }
    ConferenceTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public static String getValueByKey(String key) {
        if (StringUtil.isNullStr(key)) {
            return "";
        }
        for (ConferenceTypeEnum item : ConferenceTypeEnum.values()) {
            if (item.key.equals(key)) {
                return item.value;
            }
        }
        return null;
    }

}