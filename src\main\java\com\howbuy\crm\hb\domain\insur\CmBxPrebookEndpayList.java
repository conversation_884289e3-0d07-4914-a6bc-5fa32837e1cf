package com.howbuy.crm.hb.domain.insur;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 实体类CmBxChannel.java
 * <AUTHOR>
 */
@Data
public class CmBxPrebookEndpayList implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;

	private BigDecimal buyid;

	private String isdel;
	
	private String paydt;
	
	private String paystate;
	/**
	 * 缴款金额
	 */
	private BigDecimal payamt;
	
	private String paystatedetail;
	
	private String paystateval;
	
	private String version;

	private String creator;

	private Date creatdt;

	private String modifier;

	private Date modifydt;
	
	private String orgname;

	private String orgcode;

	private String uporgname;
	/**
	 * 所属中心
	 */
	private String centerOrgName;
	
	private String consname;
	
	private String custname;
	
	private String fundname;
	
	private String signdt;
	
	private String payyears;
	
	private BigDecimal yearamk;
	
	private String conscustno;
	
	private String insurname;
	
	private String insurid;
	
	private String realpaydt;
	
	private String checkstate;
	
	private String checkstateval;
	
	private Date checkdt;
	
	private String checkdtStr;
	
	private Date dealdt;
	
	private String dealdtStr;
	
	private String checkor;

	private String expcommissiondt;

	private BigDecimal expcommissionratio;

	private BigDecimal yearno;

	/**
	 * 新增的投顾（算绩效）
	 */
	private String calconscode;

	/**
	 * 管理系数
	 */
	private String managePoint;
	/**
	 * 基金代码
	 */
	private String fundCode;
	/**
	 * 缴费币种
	 */
	private String currency;
}
