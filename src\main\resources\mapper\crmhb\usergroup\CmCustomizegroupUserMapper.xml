<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.usergroup.CmCustomizegroupUserMapper">   
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	
	 <select id="getCustgroupUser" parameterType="Map" resultType="CmCustomizegroupUser" useCache="false">
	    SELECT
	          *
	    FROM CM_CUSTOMIZEGROUP_USER
	    where 1=1  
	              <if test="id != null"> AND id = #{id} </if>             
                  <if test="groupid != null"> AND groupid = #{groupid} </if>             
                  <if test="custno != null"> AND custno = #{custno} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="credt != null"> AND credt = #{credt} </if>             
                  <if test="moddt != null"> AND moddt = #{moddt} </if>             
        	  </select>
	  
	  
	  <insert id="insertCustgroupUser" parameterType="CmCustomizegroupUser" >
	    INSERT INTO CM_CUSTOMIZEGROUP_USER (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="id != null"> id, </if> 
	      	      <if test="groupid != null"> groupid, </if> 
	      	      <if test="custno != null"> custno, </if> 
	      	      <if test="creator != null"> creator, </if> 
	      	      <if test="modifier != null"> modifier, </if> 
	      	      <if test="credt != null"> credt, </if> 
	      	      <if test="moddt != null"> moddt, </if> 
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="id != null"> #{id}, </if> 
	      	      <if test="groupid != null"> #{groupid}, </if> 
	      	      <if test="custno != null"> #{custno}, </if> 
	      	      <if test="creator != null"> #{creator}, </if> 
	      	      <if test="modifier != null"> #{modifier}, </if> 
	      	      <if test="credt != null"> #{credt}, </if> 
	      	      <if test="moddt != null"> #{moddt}, </if> 
	               </trim>	
         )      
	  </insert>
	  
	  <insert id="insertCustgroupUserBatch" parameterType="java.util.List">
	    INSERT INTO CM_CUSTOMIZEGROUP_USER 
		(id, groupid, custno, creator, modifier, credt,moddt)
		  <foreach collection="list" item="item" index="index" separator="UNION ALL" open="select to_char(SEQ_CUSTREC.NEXTVAL), a.* from (" close=") a">
	       SELECT #{item.groupid},#{item.custno},#{item.creator},#{item.modifier},#{item.credt},#{item.moddt} FROM DUAL
	      </foreach>
	   </insert>
	   
	  <update id="updateCustgroupUser" parameterType="CmCustomizegroupUser" >
	    UPDATE CM_CUSTOMIZEGROUP_USER	    
	    <set>
	                <if test="id != null"> id = #{id}, </if>             
                    <if test="groupid != null"> groupid = #{groupid}, </if>             
                    <if test="custno != null"> custno = #{custno}, </if>             
                    <if test="creator != null"> creator = #{creator}, </if>             
                    <if test="modifier != null"> modifier = #{modifier}, </if>             
                    <if test="credt != null"> credt = #{credt}, </if>             
                    <if test="moddt != null"> moddt = #{moddt}, </if>             
                 </set>
          where id = #id
	  </update>
	  
	  
	  <delete id="delCustgroupUser" parameterType="String">
	    DELETE  from CM_CUSTOMIZEGROUP_USER
	    where id = #id
	  </delete>
	  
	  
	  <delete id="delListCustgroupUser" parameterType="Map">
	    DELETE  from CM_CUSTOMIZEGROUP_USER t 
	    where t.groupid = '${groupid}' and t.custno in (${ids})
	  </delete>
	  
	  
	  <select id="listCustgroupUser" parameterType="Map" resultType="CmCustomizegroupUser" useCache="false">
	    SELECT
	          *
	    FROM CM_CUSTOMIZEGROUP_USER
	    where 1=1  
	              <if test="id != null"> AND id = #{id} </if>             
                  <if test="groupid != null"> AND groupid = #{groupid} </if>             
                  <if test="custno != null"> AND custno = #{custno} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="credt != null"> AND credt = #{credt} </if>             
                  <if test="moddt != null"> AND moddt = #{moddt} </if>             
        	  </select>
	  
	  <delete id="delListCustgroupUserAll" parameterType="Map">
		    DELETE  from CM_CUSTOMIZEGROUP_USER t 
		    where t.groupid = '${param.groupid}' 
		    <if test="param.ids != null and param.ids.size()>0 ">
		      and t.custno in
		  	  <foreach collection="param.ids" index="index" item="item" open="(" separator="," close=")">  
		           #{item}
		      </foreach> 
		    </if>     
	  </delete>
	  
	  <select id="listCustgroupUserByPage" parameterType="Map" resultType="CmCustomizegroupUser" useCache="false">
	     select * from (
		    SELECT t1.groupid,
		    	   t1.custno,
		    	   t2.groupname,
			       t3.custname,
			       t3.provcode,
			       t3.citycode,
			       t3.mobile_mask mobileMask,
			       t3.telno_mask telnoMask,
			       t3.email_mask emailMask,
			       t3.idno_mask idnoMask,
			       t5.first_level_code as source,
				   t4.conscode,
				   t4.startdt,
			       ( select dict_name
				   from cm_sourceinfo_dict m
				  where dict_level = 1
				    and m.dict_no = t5.first_level_code) as sourcename,
			       t21.conscode as seniormgrcode ,
			       t1.credt,
			       CHC.latesttradedt
			  FROM CM_CUSTOMIZEGROUP_USER t1
			  LEFT JOIN CM_CUSTOMIZEGROUP t2
			    ON t1.groupid = t2.groupid
			  LEFT JOIN CM_CONSCUST t3
			    ON t1.custno = t3.conscustno
			  LEFT JOIN CM_CUSTCONSTANT t4
			    ON t3.conscustno = t4.custno
			  LEFT JOIN CM_SOURCEINFO_new t5
			    ON t3.newsourceno=t5.sourceno and t5.is_merge=1 and t5.REC_STAT=1
			  LEFT JOIN CM_CUSTMGR T21
          		ON t3.conscustno = t21.custno
          	  LEFT JOIN CM_HIGH_CUSTINFO CHC
			    ON T1.custno = CHC.CONSCUSTNO	
			  where t2.delflag = '0'
			  <if test="param.groupid != null"> AND t2.groupid = '${param.groupid}' </if>
			  <if test='param.orgCode!=null and param.orgCode!=""'>
					 and  EXISTS(select * from cm_consultant cc where  cc.conscode =t4.CONSCODE
								  <if test='param.userLevel == "0" or param.userLevel == "1"'>
									  AND   nvl(CC.TEAMCODE,CC.Outletcode) in (
									  SELECT T.ORGCODE
									  FROM HB_ORGANIZATION T
									  WHERE T.STATUS = 0
									  <if test='param.userLevel =="1"'>
									  START WITH T.ORGCODE =(SELECT  nvl(teamcode,outletcode) FROM CM_CONSULTANT WHERE CONSCODE= '${param.loginUser}')
									  </if>
									  <if test='param.userLevel =="0"'>
									   START WITH T.ORGCODE = '${param.orgCode}'
									  </if>
									  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE
									  )
								  </if>
					  )
			  </if>
			  <if test='param.orgCode==null or param.orgCode==""'>
					 and  T4.conscode= '${param.loginUser}'
			  </if>
			  
              <if test="param.custname != null"> AND t3.custname LIKE '%'||#{param.custname}||'%' </if>
              <if test="param.conscustno != null"> AND t1.custno = '${param.conscustno}' </if>
              <if test="param.mobile != null"> AND t3.mobile_digest = '${param.mobile}' </if>
              <if test="param.conscode != null">
			    AND t4.conscode = '${param.conscode}'
			  </if>
			  <!-- <if test="param.qryOrgCode != null">
			    AND (t2.GROUPLEVEL = '0' or  (t2.GROUPLEVEL in('1','2') and t2.orgcode IN  (SELECT T.ORGCODE
								               FROM HB_ORGANIZATION T
								               WHERE T.STATUS = 0
								               START WITH T.ORGCODE = '${param.qryOrgCode}'
								               CONNECT BY PRIOR T.ORGCODE = T.PARENTORGCODE ))  
					)
			  </if>   -->
			  <if test="param.qryOrgCode != null and param.conscode == null">
			    and  EXISTS(select * from cm_consultant cc where  cc.conscode =t4.CONSCODE
								  <if test='param.userLevel == "0" or param.userLevel == "1"'>
									  AND   nvl(CC.TEAMCODE,CC.Outletcode) in (
									  SELECT T.ORGCODE
									  FROM HB_ORGANIZATION T
									  WHERE T.STATUS = 0
									  START WITH T.ORGCODE = '${param.qryOrgCode}'
									  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE
									  )
								  </if>
					  )
			  </if>  
			)  
		    <if test="param.sort != null and param.order != null" > ORDER BY  ${param.sort} ${param.order} nulls last</if>
            <if test="param.sort == null or param.order == null" >  ORDER BY  latesttradedt  desc  nulls last</if> 
       </select>
       
       
       <select id="listCustgroupUserByCondition" parameterType="Map" resultType="CmCustomizegroupUser" useCache="false">
		    SELECT t1.groupid,
		    	   t1.custno,
		    	   t2.groupname,
			       t3.custname,
			       t3.provcode,
			       t3.citycode,
			       t5.first_level_code as source,
				   t4.conscode,
				   t4.startdt,
			       ( select dict_name
				   from cm_sourceinfo_dict m
				  where dict_level = 1
				    and m.dict_no = t5.first_level_code) as sourcename,
			       t21.conscode as seniormgrcode ,
			       t1.credt,
			       CHC.latesttradedt
			  FROM CM_CUSTOMIZEGROUP_USER t1
			  LEFT JOIN CM_CUSTOMIZEGROUP t2
			    ON t1.groupid = t2.groupid
			  LEFT JOIN CM_CONSCUST t3
			    ON t1.custno = t3.conscustno
			  LEFT JOIN CM_CUSTCONSTANT t4
			    ON t3.conscustno = t4.custno
			  LEFT JOIN CM_SOURCEINFO_new t5
			    ON t3.newsourceno=t5.sourceno and t5.is_merge=1 and t5.REC_STAT=1
			  LEFT JOIN CM_CUSTMGR T21
          		ON t3.conscustno = t21.custno
          	  LEFT JOIN CM_HIGH_CUSTINFO CHC
			    ON T1.custno = CHC.CONSCUSTNO	
			  where t2.delflag = '0'
			  <if test="param.groupid != null"> AND t2.groupid = '${param.groupid}' </if>
			  <if test='param.orgCode!=null '>
					 and  EXISTS(select * from cm_consultant cc where  cc.conscode =t4.CONSCODE
								  <if test='param.userLevel == "0" or param.userLevel == "1"'>
									  AND   nvl(CC.TEAMCODE,CC.Outletcode) in (
									  SELECT T.ORGCODE
									  FROM HB_ORGANIZATION T
									  WHERE T.STATUS = 0
									  <if test='param.userLevel =="1"'>
									  START WITH T.ORGCODE =(SELECT  nvl(teamcode,outletcode) FROM CM_CONSULTANT WHERE CONSCODE= '${param.loginUser}')
									  </if>
									  <if test='param.userLevel =="0"'>
									   START WITH T.ORGCODE ='${param.orgCode}'
									  </if>
									  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE
									  )
								  </if>
					  )
			  </if>
			  <if test='param.orgCode==null '>
					 and  T4.conscode= '${param.loginUser}'
			  </if>
			  
              <if test="param.custname != null"> AND t3.custname LIKE '%'||#{param.custname}||'%' </if>
              <if test="param.conscustno != null"> AND t1.custno = '${param.conscustno}' </if>
              <if test="param.mobile != null"> AND t3.mobile_digest = '${param.mobile}' </if>
              <if test="param.conscode != null">
			    AND t4.conscode = '${param.conscode}'
			  </if>
			  <if test="param.qryOrgCode != null">
			    AND (t2.GROUPLEVEL = '0' or  (t2.GROUPLEVEL in('1','2') and t2.orgcode IN  (SELECT T.ORGCODE
								               FROM HB_ORGANIZATION T
								               WHERE T.STATUS = 0
								               START WITH T.ORGCODE = '${param.qryOrgCode}'
								               CONNECT BY PRIOR T.ORGCODE = T.PARENTORGCODE ))  
					)
			  </if>  
			  order by CHC.latesttradedt desc nulls last
       </select>
       
	   <select id="exportCustgroupUser" parameterType="Map" resultType="CmCustomizegroupUser" useCache="false">
	     SELECT t1.groupid,
		    	   t1.custno,
		    	   t2.groupname,
			       t3.custname,
			       t3.provcode,
			       t3.citycode,
			       t3.mobile_mask mobilemask,
			       t3.telno_mask telnomask,
			       t3.email_mask emailmask,
			       t3.idno_mask idnomask,
			       t5.first_level_code as source,
				   t4.conscode,
				   t4.startdt,
			       ( select dict_name
				   from cm_sourceinfo_dict m
				  where dict_level = 1
				    and m.dict_no = t5.first_level_code) as sourcename,
			       t21.conscode as seniormgrcode ,
			       t1.credt,
			       CHC.latesttradedt
			  FROM CM_CUSTOMIZEGROUP_USER t1
			  LEFT JOIN CM_CUSTOMIZEGROUP t2
			    ON t1.groupid = t2.groupid
			  LEFT JOIN CM_CONSCUST t3
			    ON t1.custno = t3.conscustno
			  LEFT JOIN CM_CUSTCONSTANT t4
			    ON t3.conscustno = t4.custno
			  LEFT JOIN CM_SOURCEINFO_new t5
			    ON t3.newsourceno=t5.sourceno and t5.is_merge=1 and t5.REC_STAT=1
			  LEFT JOIN CM_CUSTMGR T21
          		ON t3.conscustno = t21.custno
          	  LEFT JOIN CM_HIGH_CUSTINFO CHC
			    ON T1.custno = CHC.CONSCUSTNO	
			  where t2.delflag = '0'
			  <if test="param.groupid != null"> AND t2.groupid = '${param.groupid}' </if>
			  <if test='param.orgCode!=null and param.orgCode!=""'>
					 and  EXISTS(select * from cm_consultant cc where  cc.conscode =t4.CONSCODE
								  <if test='param.userLevel == "0" or param.userLevel == "1"'>
									  AND   nvl(CC.TEAMCODE,CC.Outletcode) in (
									  SELECT T.ORGCODE
									  FROM HB_ORGANIZATION T
									  WHERE T.STATUS = 0
									  <if test='param.userLevel =="1"'>
									  START WITH T.ORGCODE =(SELECT  nvl(teamcode,outletcode) FROM CM_CONSULTANT WHERE CONSCODE= '${param.loginUser}')
									  </if>
									  <if test='param.userLevel =="0"'>
									   START WITH T.ORGCODE = '${param.orgCode}'
									  </if>
									  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE
									  )
								  </if>
					  )
			  </if>
			  <if test='param.orgCode==null or param.orgCode==""'>
					 and  T4.conscode= '${param.loginUser}'
			  </if>
			  
              <if test="param.custname != null"> AND t3.custname LIKE '%'||#{param.custname}||'%' </if>
              <if test="param.conscustno != null"> AND t1.custno = '${param.conscustno}' </if>
              <if test="param.mobile != null"> AND t3.mobile_digest = '${param.mobile}' </if>
              <if test="param.conscode != null">
			    AND t4.conscode = '${param.conscode}'
			  </if>
			  <if test="param.qryOrgCode != null and param.conscode == null">
			    and  EXISTS(select * from cm_consultant cc where  cc.conscode =t4.CONSCODE
								  <if test='param.userLevel == "0" or param.userLevel == "1"'>
									  AND   nvl(CC.TEAMCODE,CC.Outletcode) in (
									  SELECT T.ORGCODE
									  FROM HB_ORGANIZATION T
									  WHERE T.STATUS = 0
									  START WITH T.ORGCODE = '${param.qryOrgCode}'
									  CONNECT BY PRIOR T.orgcode = T.PARENTORGCODE
									  )
								  </if>
					  )
			  </if>   
			  order by CHC.latesttradedt desc nulls last  
       </select>
</mapper>



