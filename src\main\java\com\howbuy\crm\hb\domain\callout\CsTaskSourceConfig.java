package com.howbuy.crm.hb.domain.callout;

import lombok.Data;
import java.io.Serializable;

/**
 * @Description: 任务来源（类型）实体类
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class CsTaskSourceConfig implements Serializable {

	private static final long serialVersionUID = 8492650386779452212L;

	/** 任务来源编号 */
	private String sourceId;

	/** 任务来源名称 */
	private String sourceName; 

	/** 任务父来源编号 */
	private String parentId; 

	/** 任务来源层级 */
	private String sourceLevel; 

	/** 任务来源备注 */
	private String remark; 

	/** 任务来源状态 */
	private String sourceState; 
}
