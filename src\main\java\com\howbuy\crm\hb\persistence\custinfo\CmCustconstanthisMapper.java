package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.custinfo.CmCustconstanthis;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmCustconstanthisMapper {
    /**
     * 插入客户投顾历史信息
     * @param cmCustconstanthis
     */
	void insertCmCustconstanthis(CmCustconstanthis cmCustconstanthis);
    
	/**
	 * 获取需要撤销的检查信息
	 * @param param
	 * @return
	 */
	Map<String,Object> getCancelCheckInfo(Map<String, String> param);
	
	/**
	 * 撤掉分配的历史记录插入撤销表
	 * @param param
	 */
	void insertCmCustconstanthisCancel(Map<String, String> param);
	
	/**
	 * 恢复撤销的分配记录
	 * @param param
	 */
	void updateRecoverCmCustconstant(Map<String, String> param);
	
	/**
	 * 删除撤销的分配历史
	 * @param param
	 */
	void delCmCustconstantHis(Map<String, String> param);

	/**
	 * 根据客户号查询所有历史分配投顾数据
	 * @param custno
	 * @return
	 */
	List<CmCustconstanthis> getcustconstanthisinfo(String custno);
}
