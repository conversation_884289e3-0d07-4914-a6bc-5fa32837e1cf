package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class OrgCommunicateVisit implements Serializable {

    private static final long serialVersionUID = -8090330991942412012L;

    private Long id;
    private String  visitdt;
    private String  visitaddr;
    private String  starttime;
    private String  endtime;
    private String  visittype;
    private String  orgtype;
    private String  commcontent;
    private String  commintent;
    private String  support;
    private String  remark;
    private String  ourattend;
    private String  othersideattend;
    private String  ourattendposition;
    private String  othersideattendposition;
    private String  creator;
    private Date  credt;
    private Date  moddt;
    private String  modifier;
    private String  custno;
    private String cratedt;
}
