package com.howbuy.crm.hb.persistence.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationMail;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 协会邮件-正常解析
 * <AUTHOR> on 2021/5/26 16:14
 */
public interface AssociationMailMapper {

    /**
     * 分页查询邮件列表
     * @param param 查询参数
     * @param pageBean 分页参数
     * @return
     */
    List<AssociationMail> listAssociationMailByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 查询邮件列表
     * @param param 查询参数
     * @return
     */
    List<AssociationMail> listAssociationMail(@Param("param") Map<String, String> param);

    /**
     * 根据主键获取正常解析邮件
     * @param id
     * @return
     */
    AssociationMail findAssociationMailById(String id);

    /**
     * 修改正常解析邮件的信息
     * @param associationMail
     * @return
     */
    void updateAssociationMail(AssociationMail associationMail);

    /**
     * 批量修改正常解析邮件信息
     * @param list 待修改记录id的集合
     * @param infoStatus 信息状态
     * @param modifier 修改人
     * @param remark 备注
     */
    void batchUpdateStatusRemark(@Param("list") List<String> list, @Param("infoStatus") String infoStatus,
                                 @Param("modifier") String modifier, @Param("remark") String remark);

    /**
     * 获取指定投顾所在部门所处的层级
     * @param consCode 投顾号
     * @return
     */
    int queryTierCount(String consCode);

    /**
     * 获取指定投顾，指定层级的所属部门名称
     * @param params
     * @return
     */
    String queryDeptNameWithGivenTier(Map<String, Object> params);

    /**
     * 将excel中获取的数据批量导入至数据库
     * @param associationMailList
     */
    void batchInsertAssociationMailFromExcel(List<AssociationMail> associationMailList);

    /**
     * 判断数据库中是否存在excel中的记录
     * @param associationMail
     * @return
     */
    boolean checkExcelDataExist(AssociationMail associationMail);

    /**
     * 该邮件是否已经解析过
     * @param param
     * @return
     */
    boolean existAssociationMail(Map<String, String> param);

    /**
     * 提前获取正常表上传成功的ID 以便后续标蓝
     * @return
     */
    String getSeqIdForAssociationMail();
}
