/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.birthdaygift;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 生日礼品修改记录VO
 * <AUTHOR>
 * @date 2024/9/24 15:06
 * @since JDK 1.8
 */

@Getter
@Setter
public class SmGiftModifyRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 用户生日礼物表主键
     */
    private Long infoId;

    /**
     * 更新前的礼物代码
     */
    private String beforeGiftCode;

    /**
     * 更新后的礼物代码
     */
    private String afterGiftCode;

    /**
     * 更新前的礼物名称
     */
    private String afterGiftName;

    /**
     * 操作人的ID
     */
    private String operationId;

    /**
     * 操作人的姓名
     */
    private String operationName;

    /**
     * 操作来源(1:用户创建、2:用户修改、3:投顾修改)
     */
    private String operationSource;

    /**
     * 操作来源 描述
     */
    private String operationSourceDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间描述
     */
    private String createTimeDesc;

    /**
     * 更新时间
     */
    private Date updateTime;

}