/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.system;

import com.howbuy.crm.hb.service.system.CmConsultantExpService;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @description: (花名册获取管理层等级类)
 * <AUTHOR>
 * @date 2025/5/9 15:40
 * @since JDK 1.8
 */
@Getter
@Setter
public class CmConsultantExpLeaderLevelBO {
    /**
     * 获取所有职级对应层级数据
     */
    private List<HbConstantExpBO> positionsLevelAndUserLevelList;
    /**
     * 层级代码对应的职级代码map，key:userLevelCode，value:层级下属所有职级代码
     */
    private Map<String, List<HbConstantExpBO>> positionsLevelByUserLevelMap;
    /**
     * 获得团队总监和分总区总下属理财师
     */
    private List<CmConsultantExp> consCodeInTeamCodeOrOutletCodeList;
    /**
     * 获得团队总监下属理财师, key:teamCode, value:（key:curmonthlevel, value:consname列表
     */
    private Map<String, Map<String, List<String>>> consNameByTeamCodeMap;
    /**
     * 获得分总区总下属理财师, key:teamCode, value:（key:curmonthlevel, value:consname列表
     */
    private Map<String, Map<String, List<String>>> consNameByOutletCodeMap;
    /**
     * 获得机构对应所有投顾名称，key：orgcode, value: consname列表
     */
    private Map<String, String> consNameInOrgCodeMap;

    /**
     * @description:(初始化管理层等级数据)
     * @param cmConsultantExpService
     * @return void
     * @author: shijie.wang
     * @date: 2025/5/9 16:36
     * @since JDK 1.8
     */
    public void initData(CmConsultantExpService cmConsultantExpService) {
        //获取所有职级对应层级数据
        this.setPositionsLevelAndUserLevelList(cmConsultantExpService.positionsLevelAndUserLevel());
        //层级代码对应的职级代码map，key:userLevelCode，value:层级下属所有职级代码
        this.setPositionsLevelByUserLevelMap(cmConsultantExpService.getPositionsLevelByUserLevelMap(this.getPositionsLevelAndUserLevelList()));
        //获得团队总监和分总区总下属理财师
        this.setConsCodeInTeamCodeOrOutletCodeList(cmConsultantExpService.getConsCodeInTeamCodeOrOutletCode());
        //获得团队总监下属理财师, key:teamCode, value:（key:curmonthlevel, value:consname列表
        this.setConsNameByTeamCodeMap(cmConsultantExpService.getConsNameByTeamCodeOrOutletCode(this.getConsCodeInTeamCodeOrOutletCodeList(),
                CmConsultantExp::getTeamcode));
        //获得分总区总下属理财师, key:teamCode, value:（key:curmonthlevel, value:consname列表
        this.setConsNameByOutletCodeMap(cmConsultantExpService.getConsNameByTeamCodeOrOutletCode(this.getConsCodeInTeamCodeOrOutletCodeList(),
                CmConsultantExp::getOutletcode));
        //获得机构对应所有投顾名称，key：orgcode, value: consname列表
        this.setConsNameInOrgCodeMap(cmConsultantExpService.getConsNameInOrgCode());
    }

}