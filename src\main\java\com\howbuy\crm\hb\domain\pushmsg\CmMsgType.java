package com.howbuy.crm.hb.domain.pushmsg;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 消息管理-消息类型
 * @reason:
 * @Date: 2021/1/12 18:10
 */
@Data
public class CmMsgType implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 消息类型代码
     */
    private String msgTypeCode;

    /**
     * 消息类型名称
     */
    private String msgTypeName;

    /**
     * 类型  0-主类 1-子类
     */
    private String dataType;

    /**
     * 类型为子类时的主类id
     */
    private String masterId;

    /**
     * 类型为子类时的主类名称
     */
    private String masterName;

    /**
     * 是否启用  0-停用 1-启用
     */
    private String isEnable;

    private String creator;

    private String updateMan;

    private Date createTime;

    private Date updateTime;

    private Integer level;
    
    /**
     * 是否弹窗  0-不 1-是
     */
    private String isPopup;

}
