package com.howbuy.crm.hb.persistence.callout;

import com.howbuy.crm.hb.domain.callout.CsCommunicateVisit;
import com.howbuy.crm.hb.domain.callout.CsVisitNewest;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CsCommunicateVisitMapper {

	/**
	 * 得到单个数据对象
	 * @param param
	 * @return
	 */
	CsCommunicateVisit getCsCommunicateVisit(Map<String, String> param);

	/**
	 * 新增数据对象
	 * @param csCommunicateVisit
	 */
	void insertCsCommunicateVisit(CsCommunicateVisit csCommunicateVisit);

	/**
	 * 新增数据对象
	 * @param csVisitNewest
	 */
	void insertCsVisitNewest(CsVisitNewest csVisitNewest);

	/**
	 * 单条修改数据对象
	 * @param csCommunicateVisit
	 */
	void updateCsCommunicateVisit(CsCommunicateVisit csCommunicateVisit);

	/**
	 * 单条修改数据对象
	 * @param csVisitNewest
	 */
	void updateCsVisitNewest(CsVisitNewest csVisitNewest);

	/**
	 * 单条删除数据对象
	 * @param conscustNo
	 */
	void delCsVisitNewest(String conscustNo);

	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CsCommunicateVisit> listCsCommunicateVisit(Map<String, String> param);

	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CsCommunicateVisit> listCsCommunicateVisitByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 查看用户拥有包含角色及权限
	 * @param userCode
	 * @return
	 */
	Integer isViewRole(@Param("userCode") String userCode);

	/**
	 * 查询客服拜访记录
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Map<String, Object>> queryCustVisitListByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
