## 测试的类
> com.howbuy.crm.hb.service.product.impl.ProductInfoCheckServiceImpl
## 测试的方法 
> checkProductInfoList(Map<String, Object> paramMap)

## 分支伪代码
``` java

根据checkId查询该条审核记录
if(审核人不等于自己  或  有审核权限){
          更新审核记录
    if(处理审核状态为审核通过){
                    根据待审核的基金代码查询基金信息
        if(审核记录的操作类型等于新增   && 查询到的基金信息为空){
                             插入基金信息
        }
        if(审核记录的操作类型等于修改 ){
           if(查询到的基金信息不为空){
                                      更新基金信息
           }else{
                                     插入基金信息
           }                 
        }
    }
}else{
    return "editorErr"
}
return "editorSucc"


## 测试案例
### 1、testCheckRefuse : 审核不通过
### 2、testCheckPassAndInsertNull : 审核通过、审核记录的操作类型等于新增   && 查询到的基金信息为空
### 3、testCheckPassAndQryNotNull: 审核通过、审核记录的操作类型等于新增   && 查询到的基金信息不为空
### 4、testCheckPassAndUpNotNull :  审核通过、审核记录的操作类型等于修改   && 查询到的基金信息不为空
### 5、testCheckPassAndUpNull : 审核通过、审核记录的操作类型等于修改   && 查询到的基金信息为空
### 6、testCreEqualsChe : 创建人等于审核人
### 7、testNotPermission : 没有审核权限

