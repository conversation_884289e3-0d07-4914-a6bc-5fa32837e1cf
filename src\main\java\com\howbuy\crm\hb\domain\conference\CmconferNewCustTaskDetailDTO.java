/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.conference;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: (分配单详细数据前端实体类)
 * @date 2023/11/14 09:07
 * @since JDK 1.8
 */
@Data
public class CmconferNewCustTaskDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 申请单ID
     */
    private String taskId;

    /**
     * 扫码表主键ID
     */
    private String scanId;

    /**
     * 会议ID CM_CONFERENCE.ID
     */
    private String conferenceId;

    /**
     * 会议名称
     */
    private String conferenceName;

    /**
     * 参会手机号
     */
    private String mobile;

    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 签到时间
     */
    private String signDt;

    /**
     * 客户姓名(输入)
     */
    private String custNamesr;

    /**
     * 投顾姓名(输入)
     */
    private String consNamesr;

    /**
     * 处理状态
     */
    private String dealStatus;

    /**
     * 处理意见
     */
    private String dealRemark;

    /**
     * 手机号是否匹配上投顾客户号
     */
    private String isMatch;
}