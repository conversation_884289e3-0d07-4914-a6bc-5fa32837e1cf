<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantZjSalArchMapper">
	<insert id="insertCmConsultantExpZjSal" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantZjSalArch">
        INSERT INTO CM_CONSULTANT_ZJ_SAL_ARCH (
       <trim suffix="" suffixOverrides=",">   
                <if test="conscode != null"> conscode, </if>
                <if test="years != null"> years, </if>
				<if test="ranklevel1m != null"> ranklevel1m, </if>
				<if test="salary1m != null"> salary1m, </if>
				<if test="ranklevel2m != null"> ranklevel2m, </if>
				<if test="salary2m != null"> salary2m, </if>
				<if test="ranklevel3m != null"> ranklevel3m, </if>
				<if test="salary3m != null"> salary3m, </if>
				<if test="ranklevel4m != null"> ranklevel4m, </if>
				<if test="salary4m != null"> salary4m, </if>
				<if test="ranklevel5m != null"> ranklevel5m, </if>
				<if test="salary5m != null"> salary5m, </if>
				<if test="ranklevel6m != null"> ranklevel6m, </if>
				<if test="salary6m != null"> salary6m, </if>
				<if test="ranklevel7m != null"> ranklevel7m, </if>
				<if test="salary7m != null"> salary7m, </if>
				<if test="ranklevel8m != null"> ranklevel8m, </if>
				<if test="salary8m != null"> salary8m, </if>
				<if test="ranklevel9m != null"> ranklevel9m, </if>
				<if test="salary9m != null"> salary9m, </if>
				<if test="ranklevel10m != null"> ranklevel10m, </if>
				<if test="salary10m != null"> salary10m, </if>
				<if test="ranklevel11m != null"> ranklevel11m, </if>
				<if test="salary11m != null"> salary11m, </if>
				<if test="ranklevel12m != null"> ranklevel12m, </if>
				<if test="salary12m != null"> salary12m, </if>
				<if test="creatdt !=null"> creatdt, </if>
				<if test="modor !=null"> modor, </if>
				<if test="moddt !=null"> moddt, </if>
        </trim>
           ) values (
        <trim suffix="" suffixOverrides=",">
                <if test="conscode != null"> #{conscode}, </if>
				<if test="years != null"> #{years}, </if>
				<if test="ranklevel1m != null"> #{ranklevel1m}, </if>
				<if test="salary1m != null"> #{salary1m}, </if>
				<if test="ranklevel2m != null"> #{ranklevel2m}, </if>
				<if test="salary2m != null"> #{salary2m}, </if>
				<if test="ranklevel3m != null"> #{ranklevel3m}, </if>
				<if test="salary3m != null"> #{salary3m}, </if>
				<if test="ranklevel4m != null"> #{ranklevel4m}, </if>
				<if test="salary4m != null"> #{salary4m}, </if>
				<if test="ranklevel5m != null"> #{ranklevel5m}, </if>
				<if test="salary5m != null"> #{salary5m}, </if>
				<if test="ranklevel6m != null"> #{ranklevel6m}, </if>
				<if test="salary6m != null"> #{salary6m}, </if>
				<if test="ranklevel7m != null"> #{ranklevel7m}, </if>
				<if test="salary7m != null"> #{salary7m}, </if>
				<if test="ranklevel8m != null"> #{ranklevel8m}, </if>
				<if test="salary8m != null"> #{salary8m}, </if>
				<if test="ranklevel9m != null"> #{ranklevel9m}, </if>
				<if test="salary9m != null"> #{salary9m}, </if>
				<if test="ranklevel10m != null"> #{ranklevel10m}, </if>
				<if test="salary10m != null"> #{salary10m}, </if>
				<if test="ranklevel11m != null"> #{ranklevel11m}, </if>
				<if test="salary11m != null"> #{salary11m}, </if>
				<if test="ranklevel12m != null"> #{ranklevel12m}, </if>
				<if test="salary12m != null"> #{salary12m}, </if>
				<if test="creatdt !=null"> #{creatdt}, </if>
				<if test="modor !=null"> #{modor}, </if>
				<if test="moddt !=null"> #{moddt}, </if>
        </trim>  
         )
    </insert>
    
    <update id="updateCmConsultantZjSalArch" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantZjSalArch">
        UPDATE CM_CONSULTANT_ZJ_SAL_ARCH	  
    	set
            ranklevel1m = #{ranklevel1m,jdbcType=VARCHAR},
            salary1m = #{salary1m,jdbcType=NUMERIC},
            ranklevel2m = #{ranklevel2m,jdbcType=VARCHAR},
            salary2m = #{salary2m,jdbcType=NUMERIC},
            ranklevel3m = #{ranklevel3m,jdbcType=VARCHAR},
            salary3m = #{salary3m,jdbcType=NUMERIC},
            ranklevel4m = #{ranklevel4m,jdbcType=VARCHAR},
            salary4m = #{salary4m,jdbcType=NUMERIC},
            ranklevel5m = #{ranklevel5m,jdbcType=VARCHAR},
            salary5m = #{salary5m,jdbcType=NUMERIC},
            ranklevel6m = #{ranklevel6m,jdbcType=VARCHAR},
            salary6m = #{salary6m,jdbcType=NUMERIC},
            ranklevel7m = #{ranklevel7m,jdbcType=VARCHAR},
            salary7m = #{salary7m,jdbcType=NUMERIC},
            ranklevel8m = #{ranklevel8m,jdbcType=VARCHAR},
            salary8m = #{salary8m,jdbcType=NUMERIC},
            ranklevel9m = #{ranklevel9m,jdbcType=VARCHAR},
            salary9m = #{salary9m,jdbcType=NUMERIC},
            ranklevel10m = #{ranklevel10m,jdbcType=VARCHAR},
            salary10m = #{salary10m,jdbcType=NUMERIC},
            ranklevel11m = #{ranklevel11m,jdbcType=VARCHAR},
            salary11m = #{salary11m,jdbcType=NUMERIC},
            ranklevel12m = #{ranklevel12m,jdbcType=VARCHAR},
            salary12m = #{salary12m,jdbcType=NUMERIC},
			modor = #{modor,jdbcType=VARCHAR},
			checkflag = #{checkflag,jdbcType=VARCHAR},
			moddt = sysdate
          where conscode = #{conscode}   
          and years = #{years}
    </update>
	<update id="batchUpdateCmConsultantZjSalArch"  parameterType="com.howbuy.crm.hb.domain.system.CmConsultantZjSalArch">
 		UPDATE CM_CONSULTANT_ZJ_SAL_ARCH
		<set>
			<if test="ranklevel1m != null">ranklevel1m = #{ranklevel1m,jdbcType=VARCHAR}, </if>
			<if test="salary1m != null">salary1m = #{salary1m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel2m != null">ranklevel2m = #{ranklevel2m,jdbcType=VARCHAR}, </if>
			<if test="salary2m != null">salary2m = #{salary2m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel3m != null">ranklevel3m = #{ranklevel3m,jdbcType=VARCHAR}, </if>
			<if test="salary3m != null">salary3m = #{salary3m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel4m != null">ranklevel4m = #{ranklevel4m,jdbcType=VARCHAR}, </if>
			<if test="salary4m != null">salary4m = #{salary4m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel5m != null">ranklevel5m = #{ranklevel5m,jdbcType=VARCHAR}, </if>
			<if test="salary5m != null">salary5m = #{salary5m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel6m != null">ranklevel6m = #{ranklevel6m,jdbcType=VARCHAR}, </if>
			<if test="salary6m != null">salary6m = #{salary6m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel7m != null">ranklevel7m = #{ranklevel7m,jdbcType=VARCHAR}, </if>
			<if test="salary7m != null">salary7m = #{salary7m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel8m != null">ranklevel8m = #{ranklevel8m,jdbcType=VARCHAR}, </if>
			<if test="salary8m != null">salary8m = #{salary8m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel9m != null">ranklevel9m = #{ranklevel9m,jdbcType=VARCHAR}, </if>
			<if test="salary9m != null">salary9m = #{salary9m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel10m != null">ranklevel10m = #{ranklevel10m,jdbcType=VARCHAR}, </if>
			<if test="salary10m != null">salary10m = #{salary10m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel11m != null">ranklevel11m = #{ranklevel11m,jdbcType=VARCHAR}, </if>
			<if test="salary11m != null">salary11m = #{salary11m,jdbcType=NUMERIC}, </if>
			<if test="ranklevel12m != null">ranklevel12m = #{ranklevel12m,jdbcType=VARCHAR}, </if>
			<if test="salary12m != null">salary12m = #{salary12m,jdbcType=NUMERIC}, </if>
			modor = #{modor,jdbcType=VARCHAR},
			checkflag = #{checkflag,jdbcType=VARCHAR},
			moddt = sysdate
		</set>
          where years = #{years}
			<if test="conscodes !=null">
				AND conscode IN
				<foreach collection="conscodes" item="conscode" index="index" open="(" close=")" separator=",">
					#{conscode}
				</foreach>
			</if>
	</update>

	<update id="saveCheckCmConsultantZjSalArch"  parameterType="com.howbuy.crm.hb.domain.system.CmConsultantZjSalArch">
		UPDATE CM_CONSULTANT_ZJ_SAL_ARCH
    	set
			modor = #{modor,jdbcType=VARCHAR},
			checkflag = #{checkflag,jdbcType=VARCHAR},
			moddt = sysdate
          where conscode = #{conscode}
          and years = #{years}
	</update>

	<select id="getCmConsultantZjSalArch" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantZjSalArch" useCache="false">
	  	SELECT T2.conscode,
			  	T1.CONSNAME,
			  	T1.OUTLETCODE,
			  	T1.TEAMCODE,
			  	T1.USERID,
			  	T1.USERNO,
			  	T1.PROVCODE,
			  	T1.CITYCODE,
		       	T1.EDULEVEL,
		       	T1.WORKTYPE,
		       	T1.WORKSTATE,
		       	T1.CURMONTHLEVEL,
		       	T1.STARTDT,
		       	T1.REGULARDT,
		       	T1.QUITDT,
		       	T2.YEARS,
		       	T2.RANKLEVEL1M,
		       	T2.SALARY1M,
		       	T2.RANKLEVEL2M,
		       	T2.SALARY2M,
		       	T2.RANKLEVEL3M,
		       	T2.SALARY3M,
		       	T2.RANKLEVEL4M,
		       	T2.SALARY4M,
		       	T2.RANKLEVEL5M,
		       	T2.SALARY5M,
		       	T2.RANKLEVEL6M,
		       	T2.SALARY6M,
		       	T2.RANKLEVEL7M,
		       	T2.SALARY7M,
		       	T2.RANKLEVEL8M,
		       	T2.SALARY8M,
		       	T2.RANKLEVEL9M,
		       	T2.SALARY9M,
		       	T2.RANKLEVEL10M,
		       	T2.SALARY10M,
		       	T2.RANKLEVEL11M,
		       	T2.SALARY11M,
		       	T2.RANKLEVEL12M,
		       	T2.SALARY12M
		  FROM CM_CONSULTANT_ZJ_SAL_ARCH T2
		  left join CM_CONSULTANT_EXP T1
		  on t1.USERID = T2.conscode
		  where  T2.conscode = #{conscode} 
		  	and T2.years = #{years}
	  </select>
	  
	  <select id="listCmConsultantZjSalArchByPage" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantZjSalArch" useCache="false">
	  	select * from
	  	(
	  	SELECT  nvl2(T3.id,T3.USERID, T1.USERID) as conscode,
			  nvl2(T3.id,T3.CONSNAME, T1.CONSNAME) as CONSNAME,
			  nvl2(T3.id,T3.OUTLETCODE, T1.OUTLETCODE) as OUTLETCODE,
			  nvl2(T3.id,T3.TEAMCODE, T1.TEAMCODE) as TEAMCODE,
			  nvl2(T3.id,T3.USERID, T1.USERID) as USERID,
			  nvl2(T3.id,T3.USERNO,  T1.USERNO) as USERNO,
			  nvl2(T3.id,T3.PROVCODE, T1.PROVCODE) as PROVCODE,
			  nvl2(T3.id,T3.CITYCODE, T1.CITYCODE) as CITYCODE,
			  nvl2(T3.id,T3.EDULEVEL, T1.EDULEVEL) as EDULEVEL,
			  nvl2(T3.id,T3.WORKTYPE, T1.WORKTYPE) as WORKTYPE,
			  nvl2(T3.id,T3.WORKSTATE, T1.WORKSTATE) as WORKSTATE,
			  nvl2(T3.id,T3.CURMONTHLEVEL, T1.CURMONTHLEVEL) as CURMONTHLEVEL,
			  nvl2(T3.id,T3.STARTDT, T1.STARTDT) as STARTDT,
			  nvl2(T3.id,T3.REGULARDT, T1.REGULARDT) as REGULARDT,
			  nvl2(T3.id,T3.QUITDT, T1.QUITDT) as QUITDT,
		       	T2.YEARS,
		       	T2.RANKLEVEL1M,
		       	T2.SALARY1M,
		       	T2.RANKLEVEL2M,
		       	T2.SALARY2M,
		       	T2.RANKLEVEL3M,
		       	T2.SALARY3M,
		       	T2.RANKLEVEL4M,
		       	T2.SALARY4M,
		       	T2.RANKLEVEL5M,
		       	T2.SALARY5M,
		       	T2.RANKLEVEL6M,
		       	T2.SALARY6M,
		       	T2.RANKLEVEL7M,
		       	T2.SALARY7M,
		       	T2.RANKLEVEL8M,
		       	T2.SALARY8M,
		       	T2.RANKLEVEL9M,
		       	T2.SALARY9M,
		       	T2.RANKLEVEL10M,
		       	T2.SALARY10M,
		       	T2.RANKLEVEL11M,
		       	T2.SALARY11M,
		       	T2.RANKLEVEL12M,
		       	T2.SALARY12M,
		       	T2.checkflag
		  FROM CM_CONSULTANT_EXP T1
		       left join CM_CONSULTANT_ZJ_SAL_ARCH T2
		       on t1.USERID = T2.conscode
			  left join  CM_CONSULTANT_EXP_ARCH T3
			  on T3.USERID = T2.conscode and T2.YEARS = T3.YEAR
		    <where>
		    <if test="param.conscode != null"> and T1.USERID = #{param.conscode,jdbcType=VARCHAR} </if>
		    <if test="param.consname != null"> and T1.CONSNAME like '%'||#{param.consname ,jdbcType=VARCHAR}||'%' </if>
		  <if test="param.beginStartDate != null"> and T1.STARTDT &gt;= #{param.beginStartDate,jdbcType=VARCHAR} </if>
		  <if test="param.endStartDate != null"> and T1.STARTDT &lt;= #{param.endStartDate,jdbcType=VARCHAR} </if>
		  <if test="param.beginQuitdt != null"> and T1.QUITDT &gt;= #{param.beginQuitdt,jdbcType=VARCHAR} </if>
		  <if test="param.endQuitdt != null"> and T1.QUITDT &lt;= #{param.endQuitdt,jdbcType=VARCHAR} </if>
		    <if test="param.orgcode != null"> 
		     	and ( T1.TEAMCODE = #{param.orgcode} OR
		     		T1.OUTLETCODE in (
		     			SELECT ORGCODE
						  FROM HB_ORGANIZATION HO
						 WHERE HO.STATUS = '0'
						 START WITH HO.ORGCODE = #{param.orgcode}
						CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		     		)
		     	)
		     </if>
		     <if test="param.userno != null"> and T1.USERNO = #{param.userno,jdbcType=VARCHAR} </if> 
		     <if test="param.curmonthlevel != null"> ${param.curmonthlevel} </if>
		     <if test="param.userlevel != null ">
		     and exists( (select regexp_substr( T1.CURMONTHLEVEL, '[^,]+', 1, level)
				 from dual
				 connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null )
				 intersect
				 (select regexp_substr( #{param.userlevel,jdbcType=VARCHAR}, '[^|]+', 1, level)
				 from dual
				 connect by regexp_substr(#{param.userlevel,jdbcType=VARCHAR}, '[^|]+', 1, level) is not null ) )
			  </if>
		     <if test="param.years != null"> and T2.YEARS = #{param.years,jdbcType=VARCHAR} </if>
		     <if test="param.checkflag != null"> and T2.checkflag = #{param.checkflag,jdbcType=VARCHAR} </if>
		  </where>
		     )
		  	ORDER BY  STARTDT desc nulls last
		     <if test="param.sort != null and param.order != null" >,${param.sort} ${param.order} nulls last</if>
	  </select>
	  
</mapper>