package com.howbuy.crm.hb.domain.fixed;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmFixedAnnex implements Serializable{
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private BigDecimal planid;
	private String filetype;
	private String filetypeval;
	private String filename;
	private String filepath;
	private String sufname;
	private BigDecimal filesize;
	private String isdel;
	private String creator;
	private Date creatdt;
	private String creatdtval;
	private String modifier;
	private Date modifydt;
	
}
