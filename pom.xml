<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.howbuy.crm</groupId>
	<artifactId>crm-hb-service</artifactId>
	<version>2.0.5.6-RELEASE</version>
	<name>crm-hb-service</name>
	<!-- FIXME change it to the project's website -->
	<url>http://www.example.com</url>
	<repositories>
		<repository>
			<id>nexus_crm</id>
			<name>nexus_crm</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
		</repository>
		<repository>
			<id>nexus_ec</id>
			<name>nexus_ec</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
		</repository>
		<repository>
			<id>releases</id>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
		</repository>
	</repositories>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<howbuy.version>1.0.0-release</howbuy.version>
		<com.howbuy.howbuy_dfile.version>1.18.1-RELEASE</com.howbuy.howbuy_dfile.version>
		<com.howbuy.page-commons.version>bugfix20241203-RELEASE</com.howbuy.page-commons.version>
        <com.howbuy.crm-core-client.version>bugfix-********-RELEASE</com.howbuy.crm-core-client.version>
        <com.howbuy.crm-nt-client.version>1.9.6.5-RELEASE</com.howbuy.crm-nt-client.version>
        <com.howbuy.crm-td-client.version>1.9.6.5-RELEASE</com.howbuy.crm-td-client.version>
        <com.howbuy.acc-center-facade.version>20250702-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.howbuy-fund-client.version>release-20250415-xcxeq-RELEASE</com.howbuy.howbuy-fund-client.version>
		<com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
		<com.howbuy.message-public-client.version>5.1.16-RELEASE</com.howbuy.message-public-client.version>
		<com.howbuy.center-client.version>6.4.10-RELEASE</com.howbuy.center-client.version>
		<com.howbuy.fin-online-facade.version>20250724-RELEASE</com.howbuy.fin-online-facade.version>
		<com.howbuy.high-order-center-client.version>4.8.88-RELEASE</com.howbuy.high-order-center-client.version>
		<com.howbuy.howbuy-simu-client.version>release-20250717-oldtable-offline-RELEASE</com.howbuy.howbuy-simu-client.version>
		<com.howbuy.howbuy-content-client.version>release-report-2.5-RELEASE</com.howbuy.howbuy-content-client.version>
		<com.howbuy.base-commons.version>1.8.2.2-RELEASE</com.howbuy.base-commons.version>
		<com.howbuy.high-batch-center-client.version>20250729-001-RELEASE</com.howbuy.high-batch-center-client.version>
		<com.howbuy.product-center-client.version>4.8.59-RELEASE</com.howbuy.product-center-client.version>
		<com.howbuy.howbuy-member-client.version>release-report-2.5-RELEASE</com.howbuy.howbuy-member-client.version>
		<com.howbuy.product-center-model.version>4.8.59-RELEASE</com.howbuy.product-center-model.version>
		<com.howbuy.param.version>3.2.2.2-RELEASE</com.howbuy.param.version>
		<powermock.version>2.0.7</powermock.version>
        <com.howbuy.howbuy-persistence.version>release-20250425-hw2.9-RELEASE</com.howbuy.howbuy-persistence.version>
        <com.howbuy.param-server-facade.version>3.41.0-RELEASE</com.howbuy.param-server-facade.version>
		<com.howbuy.howbuy-web-client.version>release-20250425-hw2.9-RELEASE</com.howbuy.howbuy-web-client.version>
		<com.howbuy.crm-trade.version>1.9.2.4-RELEASE</com.howbuy.crm-trade.version>
		<com.howbuy.cms.client>week-********-lcjzv3-crm-RELEASE</com.howbuy.cms.client>
		<com.howbuy.howbuy-cms-client.version>release-********-khhx-RELEASE</com.howbuy.howbuy-cms-client.version>
		<com.howbuy.howbuy-cachemanagement.version>4.9.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.crm-account.version>2.0.5.6-RELEASE</com.howbuy.crm-account.version>
        <com.howbuy.crm-wechat.version>bugfix-********-RELEASE</com.howbuy.crm-wechat.version>
        <com.howbuy.crm-account-client.version>1.9.6.5-RELEASE</com.howbuy.crm-account-client.version>
        <com.howbuy.crm-trade-api.version>1.9.2.4-RELEASE</com.howbuy.crm-trade-api.version>
<com.howbuy.crm-wechat-client.version>bugfix-********-RELEASE</com.howbuy.crm-wechat-client.version>
</properties>

	<dependencies>
		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-account-client</artifactId>
			<version>${com.howbuy.crm-account.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cms-client</artifactId>
			<version>${com.howbuy.howbuy-cms-client.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.15.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.15.0</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.25</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>2.15.0</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-trade-api</artifactId>
			<version>${com.howbuy.crm-trade-api.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>pagehelper-spring-boot-starter</artifactId>
					<groupId>com.github.pagehelper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-boot-starter</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
				<exclusion>
					<artifactId>pagehelper-spring-boot-autoconfigure</artifactId>
					<groupId>com.github.pagehelper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>mybatis-spring-boot-starter</artifactId>
					<groupId>org.mybatis.spring.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>page-commons</artifactId>
			<version>${com.howbuy.page-commons.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
				<exclusion>
					<artifactId>base-commons</artifactId>
					<groupId>com.howbuy.crm</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>base-commons</artifactId>
			<version>${com.howbuy.base-commons.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-wechat-client</artifactId>
			<version>${com.howbuy.crm-wechat-client.version}</version>
		</dependency>
        <dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cachemanagement</artifactId>
			<version>${com.howbuy.howbuy-cachemanagement.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>spring-context-support</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-context</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-core</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>activemq-all</artifactId>
					<groupId>org.apache.activemq</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
			</exclusions>
        </dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-ccms-watcher</artifactId>
			<version>${com.howbuy.howbuy-ccms-watcher.version}</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.5</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>4.3.5.RELEASE</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-oscache</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc14</artifactId>
			<version>10.2.0.4.0</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>

		<!--单元测试 start-->
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-testng</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4-rule-agent</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<!--单元测试 end-->

		<!--单元测试 start-->
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-testng</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4-rule-agent</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<!--单元测试 end-->

		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-core-client</artifactId>
			<version>${com.howbuy.crm-core-client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-nt-client</artifactId>
			<version>${com.howbuy.crm-nt-client.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>base-commons</artifactId>
					<groupId>com.howbuy.crm</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-td-client</artifactId>
			<version>${com.howbuy.crm-td-client.version}</version>
		</dependency>

		<!-- 中台查询接口 -->
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-order-center-client</artifactId>
			<version>${com.howbuy.high-order-center-client.version}</version>
		</dependency>

		<!-- pdf生成依赖包 -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.2.0</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.52</version>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>jbig2-imageio</artifactId>
			<version>3.0.2</version>
		</dependency>

		<!-- pdf生成中文依赖包 -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itext-asian</artifactId>
			<version>5.2.0</version>
		</dependency>

		<!-- svg 生成png格式图片 -->
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-svggen</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-awt-util</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-bridge</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-css</artifactId>
			<version>1.6</version>
		</dependency>

		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-dom</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-gvt</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-parser</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-script</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-svg-dom</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-transcoder</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-util</artifactId>
			<version>1.6</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-xml</artifactId>
			<version>1.6</version>
		</dependency>
		<!--
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.10-beta2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.10-beta2</version>
		</dependency>-->

		<!-- 获取基金报告 -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-content-client</artifactId>
			<version>${com.howbuy.howbuy-content-client.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>mybatis-plus-annotation</artifactId>
					<groupId>com.baomidou</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--账户中心获取问卷-->
		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-facade</artifactId>
			<version>${com.howbuy.acc-center-facade.version}</version>
		</dependency>

		<!--发送短信-->
		<dependency>
			<groupId>com.howbuy.cc.message</groupId>
			<artifactId>message-public-client</artifactId>
			<version>${com.howbuy.message-public-client.version}</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.5</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-simu-client</artifactId>
			<version>${com.howbuy.howbuy-simu-client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.caucho</groupId>
			<artifactId>hessian</artifactId>
			<version>4.0.7-fixed</version>
		</dependency>
		<dependency>
			<groupId>org.jboss.netty</groupId>
			<artifactId>netty</artifactId>
			<version>3.2.5.Final</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.10.1</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy.cc</groupId>
			<artifactId>center-client</artifactId>
			<version>${com.howbuy.center-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-batch-center-client</artifactId>
			<version>${com.howbuy.high-batch-center-client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
			<artifactId>product-center-client</artifactId>
			<version>${com.howbuy.product-center-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
			<artifactId>product-center-model</artifactId>
			<version>${com.howbuy.product-center-model.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-fund-client</artifactId>
			<version>${com.howbuy.howbuy-fund-client.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.4.1</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>5.4.1.Final</version>
		</dependency>

		<!-- 加解密接口 -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-auth-facade</artifactId>
			<version>${com.howbuy.howbuy-auth-facade.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy.finonline</groupId>
			<artifactId>fin-online-facade</artifactId>
			<version>${com.howbuy.fin-online-facade.version}</version>
		</dependency>

		<!-- 参数系统 -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>param-server-facade</artifactId>
			<version>${com.howbuy.param-server-facade.version}</version>
		</dependency>


		<!-- rar解压 -->
		<dependency>
			<groupId>com.github.junrar</groupId>
			<artifactId>junrar</artifactId>
			<version>3.0.0</version>
		</dependency>


		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-persistence</artifactId>
			<version>${com.howbuy.howbuy-persistence.version}</version>
		</dependency>

		<!--查询高端用户生日礼物信息 start-->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-web-client</artifactId>
			<version>${com.howbuy.howbuy-web-client.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>activemq-all</artifactId>
					<groupId>org.apache.activemq</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
				<exclusion>
					<artifactId>howbuy-cachemanagement</artifactId>
					<groupId>com.howbuy</groupId>
                </exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-member-client</artifactId>
			<version>${com.howbuy.howbuy-member-client.version}</version>
		</dependency>

		<!--查询高端用户生日礼物信息 end-->

		<!-- 引入Excel处理工具包 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.0.5</version>
		</dependency>

		<!--webDav中文文件名称处理时，最低要求版本：4.5.10 -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.10</version>
		</dependency>

		<!--引入dfile的依赖 begin-->
		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-service</artifactId>
			<version>${com.howbuy.howbuy_dfile.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-impl-local</artifactId>
			<version>${com.howbuy.howbuy_dfile.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-impl-webdav</artifactId>
			<version>${com.howbuy.howbuy_dfile.version}</version>
		</dependency>
		<!--引入dfile的依赖 end-->
		<!--单元测试 start-->
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-testng</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4-rule-agent</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
		</dependency>
		<!--单元测试 end-->
	</dependencies>

	<build>
		<pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->
			<plugins>
				<plugin>
					<artifactId>maven-clean-plugin</artifactId>
					<version>3.0.0</version>
				</plugin>
				<!-- see http://maven.apache.org/ref/current/maven-core/default-bindings.html#Plugin_bindings_for_jar_packaging -->
				<plugin>
					<artifactId>maven-resources-plugin</artifactId>
					<version>3.0.2</version>
				</plugin>
				<plugin>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.7.0</version>
				</plugin>
				<plugin>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>2.20.1</version>
				</plugin>
				<plugin>
					<artifactId>maven-jar-plugin</artifactId>
					<version>3.0.2</version>
				</plugin>
				<plugin>
					<artifactId>maven-install-plugin</artifactId>
					<version>2.5.2</version>
				</plugin>
				<plugin>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.8.2</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<configuration>
						<archive>
							<manifestEntries>
								<Package-Stamp>${parelease}</Package-Stamp>
							</manifestEntries>
						</archive>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>