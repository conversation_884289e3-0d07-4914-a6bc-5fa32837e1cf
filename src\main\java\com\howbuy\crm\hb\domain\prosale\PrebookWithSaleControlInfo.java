package com.howbuy.crm.hb.domain.prosale;

import lombok.Data;

import java.io.Serializable;

/**
 * 预约单附带销控信息
 * <AUTHOR>
 */
@Data
public class PrebookWithSaleControlInfo  extends Prebookproductinfo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 预约单是否在期间内  0-否 1-是s
	 */
	private String intoControl;

	/**
	 * 预约单销控日历参数id
	 */
	private String  calenderId;


	/**
	 * 占位状态：1-未占位，2-已占位
	 */
	private String  occupyStatus;

	/**
	 * 占位类型：0-网银匹配占位，1-手工标记到账，2-人工预留
	 */
	private String  occupyType;

}
