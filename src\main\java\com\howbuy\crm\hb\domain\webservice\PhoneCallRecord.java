package com.howbuy.crm.hb.domain.webservice;

import java.util.Date;

/**
 * 通话记录类
 * <AUTHOR>
 *
 */
public class PhoneCallRecord {
	private static final long serialVersionUID = 1L;
	/**
	 * 开始日期
	 */
	private String startDt; 
	private Date startDate;
	private String departName;
	private String chnNo;
	private String talktimesStr;
	private String callee;
	/**
	 * 呼叫类型
	 */
	private String callType;
	private String caller;
	private String fileName;
	private String fileUrl;
	/**
	 * 通话时长
	 */
	private long talktimes;
	/**
	 * 呼出时长
	 */
	private long outtimes; 
	private String outtimesStr;
	/**
	 * 呼入时长
	 */
	private long intimes; 
	private String intimesStr;
	/**
	 * 通话次数
	 */
	private int talkcount;
	/**
	 * 呼入次数
	 */
	private int incount; 
	/**
	 * 呼出次数
	 */
	private int outcount; 
	private int misscount;
	/**
	 * 所在服务器
	 */
	private String location; 
	/**
	 * 分机号
	 */
	private String agentNo; 
	/**
	 * 结束日期
	 */
	private String endDt; 
	private Date endDate;
	/**
	 * 用户名
	 */
	private String userName; 

	public String getCallType() {
		return callType;
	}

	public void setCallType(String callType) {
		this.callType = callType;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getStartDt() {
		return startDt;
	}

	public void setStartDt(String startDt) {
		this.startDt = startDt;
	}

	public String getDepartName() {
		return departName;
	}

	public void setDepartName(String departName) {
		this.departName = departName;
	}

	public String getChnNo() {
		return chnNo;
	}

	public void setChnNo(String chnNo) {
		this.chnNo = chnNo;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public PhoneCallRecord() {

	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public String getEndDt() {
		return endDt;
	}

	public void setEndDt(String endDt) {
		this.endDt = endDt;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public long getTalktimes() {
		return talktimes;
	}

	public void setTalktimes(long talktimes) {
		this.talktimes = talktimes;
	}

	public long getOuttimes() {
		return outtimes;
	}

	public void setOuttimes(long outtimes) {
		this.outtimes = outtimes;
	}

	public long getIntimes() {
		return intimes;
	}

	public void setIntimes(long intimes) {
		this.intimes = intimes;
	}

	public int getTalkcount() {
		return talkcount;
	}

	public void setTalkcount(int talkcount) {
		this.talkcount = talkcount;
	}

	public int getIncount() {
		return incount;
	}

	public void setIncount(int incount) {
		this.incount = incount;
	}

	public int getOutcount() {
		return outcount;
	}

	public void setOutcount(int outcount) {
		this.outcount = outcount;
	}

	public int getMisscount() {
		return misscount;
	}

	public void setMisscount(int misscount) {
		this.misscount = misscount;
	}

	public String getOuttimesStr() {
		return outtimesStr;
	}

	public void setOuttimesStr(String outtimesStr) {
		this.outtimesStr = outtimesStr;
	}

	public String getCaller() {
		return caller;
	}

	public void setCaller(String caller) {
		this.caller = caller;
	}

	public String getIntimesStr() {
		return intimesStr;
	}

	public String getTalktimesStr() {
		return talktimesStr;
	}

	public void setTalktimesStr(String talktimesStr) {
		this.talktimesStr = talktimesStr;
	}

	public String getCallee() {
		return callee;
	}

	public void setCallee(String callee) {
		this.callee = callee;
	}

	public void setIntimesStr(String intimesStr) {
		this.intimesStr = intimesStr;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public String toString() {
		return "PhoneCallRecord [startDt=" + startDt + ", startDate=" + startDate + ", departName=" + departName
				+ ", chnNo=" + chnNo + ", talktimesStr=" + talktimesStr + ", callee=" + callee + ", caller=" + caller
				+ ", fileName=" + fileName + ", fileUrl=" + fileUrl + ", talktimes=" + talktimes + ", outtimes="
				+ outtimes + ", outtimesStr=" + outtimesStr + ", intimes=" + intimes + ", intimesStr=" + intimesStr
				+ ", talkcount=" + talkcount + ", incount=" + incount + ", outcount=" + outcount + ", misscount="
				+ misscount + ", location=" + location + ", agentNo=" + agentNo + ", endDt=" + endDt + ", endDate="
				+ endDate + ", userName=" + userName + "]";
	}

}
