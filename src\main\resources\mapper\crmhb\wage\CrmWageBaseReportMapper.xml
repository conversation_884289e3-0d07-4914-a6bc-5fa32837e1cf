<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CmWageBaseReportMapper">
	<cache type="org.mybatis.caches.oscache.OSCache" />
	<select id="queryWageBaseCollect" statementType="CALLABLE"  parameterType="Map" resultType="Map" useCache="false">
		<![CDATA[
		{call pro_wage_base_collect(
			#{P_START_DATE,mode=IN,jdbcType=VARCHAR},
			#{P_END_DATE,mode=IN,jdbcType=VARCHAR},
			#{P_ORG_CODE,mode=IN,jdbcType=VARCHAR},
			#{P_CUSTNAME,mode=IN,jdbcType=VARCHAR},
			#{P_YEAR,mode=IN,jdbcType=VARCHAR},
			#{P_MONTH,mode=IN,jdbcType=VARCHAR},
			#{P_TYPE,mode=IN,jdbcType=VARCHAR},
			#{P_PAGE_SIZE,mode=IN,jdbcType=INTEGER},
			#{P_PAGE,mode=IN,jdbcType=INTEGER},
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} ,
			#{V_DATA_CURSOR ,mode=OUT, jdbcType=CURSOR,javaType=ResultSet,resultMap=basecollectCursorMap},
			#{P_TOTAL,mode=OUT,jdbcType=INTEGER})}
		]]>
	</select>

	<!--配置返回游标中别名对应的resultMap -->
	<resultMap type ="java.util.HashMap" id= "basecollectCursorMap">
		<result column ="ID" property="ID" /><!-- 预约ID -->
		<result column ="EXPECTTRADEDT" property="EXPECTTRADEDT" /><!-- 交易日期 -->
		<result column ="ACCOUNTING_DATE" property="ACCOUNTING_DATE" /><!-- 核算日期 -->
		<result column ="CONSCUSTNAME" property="CONSCUSTNAME" /><!-- 客户姓名 -->
		<result column ="CONSNAME" property="CONSNAME" /><!--  投顾姓名-->
		<result column ="ORGCODE" property="ORGCODE" /><!-- 机构编码 -->
		<result column ="PNAME" property="PNAME" /><!--  -->
		<result column ="PCODE" property="PCODE" /><!--  -->
		<result column ="BUYAMT_RMB" property="BUYAMT_RMB" /><!--  -->
		<result column ="BACKSTEPRATIO" property="BACKSTEPRATIO" /><!--  -->
		<result column ="COE_BUYAMT_RMB" property="COE_BUYAMT_RMB" /><!--  -->
		<result column ="CONSCUSTNO" property="CONSCUSTNO" /><!--  -->
		<result column ="CONSCODE" property="CONSCODE" /><!--  -->
		<result column ="TEAMCODE" property="TEAMCODE" /><!--  -->
		<result column ="Q_START_DATE" property="Q_START_DATE" /><!--  -->
		<result column ="Q_END_DATE" property="Q_END_DATE" /><!--  -->
		<result column ="RECORD_STATUS" property="RECORD_STATUS" /><!--  -->
		<result column ="EDIT_COFF" property="EDIT_COFF" /><!--  -->
		<result column ="EDIT_COFF_RMB" property="EDIT_COFF_RMB" /><!--  -->
	</resultMap >

	<resultMap type ="java.util.HashMap" id= "basecountCursorMap">
		<result column ="ORGCODE" property="ORGCODE" />	<!--机构编号  -->
		<result column ="TEAMCODE" property="TEAMCODE" />	<!--团队编号 -->
		<result column ="CONSCODE" property="CONSCODE" />	<!--投顾编号 -->
		<result column ="SAVE_YEAR" property="SAVE_YEAR" /><!--年份 -->
		<result column ="WORKSTARTDT" property="WORKSTARTDT" /><!--入职日期 -->
		<result column ="CONSNAME" property="CONSNAME" />	<!--客户姓名 -->
        <!--月份-->
        <result column ="MON_1" property="MON_1" />	<!--月份 -->
        <result column ="MON_2" property="MON_2" />	<!--月份 -->
        <result column ="MON_3" property="MON_3" />	<!--月份 -->
        <result column ="MON_4" property="MON_4" />	<!--月份 -->
        <result column ="MON_5" property="MON_5" />	<!--月份 -->
        <result column ="MON_6" property="MON_6" />	<!--月份 -->
        <result column ="MON_7" property="MON_7" />	<!--月份 -->
        <result column ="MON_8" property="MON_8" />	<!--月份 -->
        <result column ="MON_9" property="MON_9" />	<!--月份 -->
        <result column ="MON_10" property="MON_10" />	<!--月份 -->
        <result column ="MON_11" property="MON_11" />	<!--月份 -->
        <result column ="MON_12" property="MON_12" />	<!--月份 -->
        <!--职级-->
        <result column ="LEVEL_1" property="LEVEL_1" />	<!--职级 -->
        <result column ="LEVEL_2" property="LEVEL_2" />	<!--职级 -->
        <result column ="LEVEL_3" property="LEVEL_3" />	<!--职级 -->
        <result column ="LEVEL_4" property="LEVEL_4" />	<!--职级 -->
        <result column ="LEVEL_5" property="LEVEL_5" />	<!--职级 -->
        <result column ="LEVEL_6" property="LEVEL_6" />	<!--职级 -->
        <result column ="LEVEL_7" property="LEVEL_7" />	<!--职级 -->
        <result column ="LEVEL_8" property="LEVEL_8" />	<!--职级 -->
        <result column ="LEVEL_9" property="LEVEL_9" />	<!--职级 -->
        <result column ="LEVEL_10" property="LEVEL_10" />	<!--职级 -->
        <result column ="LEVEL_11" property="LEVEL_11" />	<!--职级 -->
        <result column ="LEVEL_12" property="LEVEL_12" />	<!--职级 -->
    </resultMap >


	<select id="queryWageBaseCount" statementType="CALLABLE"  parameterType="Map" useCache="false">
		<![CDATA[
		{call PRO_WAGE_BASE_COUNT(
			#{P_YEAR ,mode=IN ,jdbcType=VARCHAR},
			#{P_ORGCODE ,mode=IN, jdbcType=VARCHAR},
			#{P_CONSCODE ,mode=IN ,jdbcType=VARCHAR},
			#{P_TYPE ,mode=IN,jdbcType=VARCHAR},
			#{P_PAGE_SIZE ,mode=IN, jdbcType=INTEGER},
			#{P_PAGE ,mode=IN ,jdbcType=INTEGER},
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} ,
			#{P_TOTAL,mode=OUT ,jdbcType=INTEGER},
			#{V_DATA_CURSOR ,mode=OUT, jdbcType=CURSOR,javaType=ResultSet,resultMap=basecountCursorMap})}
		]]>
	</select>
	
	<select id="publishWageBaseCount" statementType="CALLABLE"  parameterType="Map" useCache="false">
		<![CDATA[
		{call PRO_WAGE_BASE_COUNT_PUBLISH(
			#{P_YEAR ,mode=IN ,jdbcType=VARCHAR},
			#{P_ORGCODE ,mode=IN, jdbcType=VARCHAR},
			#{P_CONSCODE ,mode=IN ,jdbcType=VARCHAR},
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR})}
		]]>
	</select>
	
	<select id="listCmWageBaseCountResultMap" parameterType="Map" resultType="Map" useCache="false">
	    SELECT
	          t.*
	    FROM CM_WAGE_BASE_COUNT_RESULT t
	    LEFT JOIN CM_CONSULTANT CONS
    		ON T.CONSCODE = CONS.CONSCODE
	    where t.save_year = #{year}
	      <if test="teamcode != null" >
		   	AND CONS.TEAMCODE = #{teamcode}
		   </if>
		   <if test="othertearm != null ">
		   	AND CONS.OUTLETCODE = #{othertearm} and CONS.TEAMCODE is null
		   </if>
		   <if test="conscode != null ">
		   	AND T.CONSCODE = #{conscode}
		   </if>
		   <if test="outletcodes != null ">
		   	AND CONS.OUTLETCODE IN (${outletcodes})
		   </if>      
		   order by t.ORGCODE,t.CONSNAME       
       </select>

</mapper>



