package com.howbuy.crm.hb.persistence.insur;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookSigninfo;
import org.apache.ibatis.annotations.Param;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxPrebookSigninfoMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxPrebookSigninfo getCmBxPrebookSigninfo(Map<String, Object> param);
    
     /**
      * 新增数据对象
      * @param cmBxPrebookSigninfo
      */
	void insertCmBxPrebookSigninfo(CmBxPrebookSigninfo cmBxPrebookSigninfo);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxPrebookSigninfo
	 */
	void updateCmBxPrebookSigninfo(CmBxPrebookSigninfo cmBxPrebookSigninfo);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxPrebookSigninfo> listCmBxPrebookSigninfo(Map<String, Object> param);


	/**
	 * @description:(请在此添加描述)
	 * @param preId
	 * @return com.howbuy.crm.hb.domain.insur.CmBxPrebookSigninfo
	 * @author: haoran.zhang
	 * @date: 2024/9/30 15:44
	 * @since JDK 1.8
	 */
	CmBxPrebookSigninfo getSigninfoByPreId(@Param("preId") BigDecimal preId);
}
