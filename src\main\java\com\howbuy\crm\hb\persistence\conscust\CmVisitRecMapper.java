package com.howbuy.crm.hb.persistence.conscust;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.conscust.CmVisitRec;
import com.howbuy.crm.hb.domain.conscust.VisitRec;
import com.howbuy.crm.hb.domain.conscust.VisitRecNewest;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmVisitRecMapper {
	/**
	 * 查询客服拜访记录
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<Map<String,Object>>  qryCustVisitListByPage(@Param("p")Map<String, String> param, @Param("page") CommPageBean pageBean);
	/**
	 * 保存拜访记录
	 * @param v
	 */
	void saveCustVisit(VisitRec v);

	/**
	 * 保存
	 * @param v
	 */
	void saveCustVisitNew(VisitRecNewest v);
	
	/**
	 * 删除
	 * @param consCustNo
	 */
	void deleteCustVisitNew(String consCustNo);
	
	/**
	 * 判断
	 * @param userCode
	 * @return
	 */
	int isViewRole(@Param("userCode")String userCode);
	
	/**
	 * 得到单个数据对象
	 * @param param
	 * @return
	 */
    CmVisitRec getCmVisitRec(Map<String, String> param);
    
    /**
     * 得到单个数据对象
     * @param param
     * @return
     */
    VisitRecNewest getCmVisitRecNewest(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmVisitRec
      */
	void insertCmVisitRec(CmVisitRec cmVisitRec);
	
	/**
	 * 单条修改数据对象
	 * @param cmVisitRec
	 */
	void updateCmVisitRec(CmVisitRec cmVisitRec);
	
	/**
	 * 新增数据对象
	 * @param visitRecNewest
	 */
	void insertCmVisitRecNewest(VisitRecNewest visitRecNewest);
	
	/**
	 * 单条修改数据对象
	 * @param visitRecNewest
	 */
	void updateCmVisitRecNewest(VisitRecNewest visitRecNewest);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmVisitRec(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmVisitRec(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmVisitRec> listCmVisitRec(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmVisitRecCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmVisitRec> listCmVisitRecByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
}
