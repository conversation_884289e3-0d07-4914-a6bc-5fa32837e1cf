package com.howbuy.crm.hb.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Description 选礼物物流轨迹状态枚举
 * <AUTHOR>
 * @Date 2024/10/14 10:46
 * @Version 1.0.0
 */
@Getter
public enum UserBirthdayLogisticsStateEnum {

    /**
     * 京东接口返回枚举（https://open.jdl.com/#/open-business-document/access-guide/201/54418）
     */
    UNALLOCATED("1000", "未配载"),
    WAITING_FOR_BID("1500", "待抢单"),
    MODIFY_CARRIER_CANCELING("1700", "修改承运商-取消中"),
    /**
     * 2000枚举由"已接单"变更为"待取件"(产品需求)，同时描述调整为"订单已创建"
     */
    ORDER_ACCEPTED("2000", "待取件"),
    OUTBOUND_WEIGHING("2300", "出库称重"),
    BID_ACCEPTED("2500", "已抢单"),
    VEHICLE_DISPATCHED("3000", "已派车"),
    PICKUP_COMPLETED("4000", "已取件"),
    SCHEDULE_CANCELED("4200", "取消调度"),
    REVERSE_TRANSPORT_IN_PROGRESS("4400", "逆向运输中"),
    IN_TRANSIT("4500", "运输中"),
    DELIVERY_IN_PROGRESS("5000", "配送中"),
    PROBLEM_ITEM("5001", "问题件"),
    RETURN_REQUEST_PENDING("5100", "拒收待审核"),
    REVIEW_COMPLETED("5101", "审核完成"),
    PAYMENT_COMPLETED("5200", "付款完成"),
    SIGNED_OFF("6000", "已签收"),
    REVERSE_SIGNED_OFF("6100", "逆向已签收"),
    CANCEL_IN_PROGRESS("7000", "取消中"),
    CANCELED("8000", "已取消"),
    CANCEL_FAILED("9000", "取消失败"),
    FULL_REJECTION("10000", "全拒收"),
    PARTIAL_REJECTION("11000", "部分拒收"),
    COLLECTION_TERMINATED("12000", "揽件终止"),
    TIMEOUT_NOT_BID("8001", "超时未抢单");


    /**
     * 编号
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    public static Map<String, String> getUserBirthdayLogisticsStateEnumMap() {
        Map<String, String> reportMap = new LinkedHashMap<String, String>();
        UserBirthdayLogisticsStateEnum[] values = UserBirthdayLogisticsStateEnum.values();
        for (UserBirthdayLogisticsStateEnum type : values) {
            reportMap.put(type.getCode(), type.getName());
        }
        return reportMap;
    }

    public static UserBirthdayLogisticsStateEnum getUserBirthdayLogisticsStateEnum(String code) {
        for (UserBirthdayLogisticsStateEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    UserBirthdayLogisticsStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

}



