## 测试的类
> com.howbuy.crm.hb.service.prosale.impl.PrebookproductinfoServiceImpl
## 测试的方法
> getCurLastZjj(String currency,String enddt)

## 分支伪代码
```java
if (currency为空) {
    返回null
}
if (enddt为空) {
   返回null
}
if (currency和enddt不为空) {
    if(接口没有返回){
    	返回null
    }else{
    	返回距离传入日期最近日期的数据
    }
}

```

## 测试案例
##### 1、test1：currency为空
##### 2、test2：enddt为空
##### 3、test3：接口返回null
##### 4、test4：接口返回的列表第一条是最近的日期，返回第一条数据
##### 5、test5：接口返回的列表第一条不是最近的日期，返回日期最近的数据
