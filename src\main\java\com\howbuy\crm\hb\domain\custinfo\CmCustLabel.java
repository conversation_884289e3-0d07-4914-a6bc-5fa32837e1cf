package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @Description: 实体类CmCustfamily.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmCustLabel implements Serializable {

private static final long serialVersionUID = 1L;

	private BigDecimal id;
	
	private String conscode;
	
	private String conscustno;
	
	private String hboneno;
	
	private String zjllabel;
	
	private String khyxlabel;
	
	private String lxqklabel;
	
	private String labeldt;
	
	private Date credt;
	
	private Date upddt;

	public BigDecimal getId() {
		return id;
	}

	public void setId(BigDecimal id) {
		this.id = id;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getHboneno() {
		return hboneno;
	}

	public void setHboneno(String hboneno) {
		this.hboneno = hboneno;
	}

	public String getZjllabel() {
		return zjllabel;
	}

	public void setZjllabel(String zjllabel) {
		this.zjllabel = zjllabel;
	}

	public String getKhyxlabel() {
		return khyxlabel;
	}

	public void setKhyxlabel(String khyxlabel) {
		this.khyxlabel = khyxlabel;
	}

	public String getLxqklabel() {
		return lxqklabel;
	}

	public void setLxqklabel(String lxqklabel) {
		this.lxqklabel = lxqklabel;
	}

	public String getLabeldt() {
		return labeldt;
	}

	public void setLabeldt(String labeldt) {
		this.labeldt = labeldt;
	}

	public Date getCredt() {
		return credt;
	}

	public void setCredt(Date credt) {
		this.credt = credt;
	}

	public Date getUpddt() {
		return upddt;
	}

	public void setUpddt(Date upddt) {
		this.upddt = upddt;
	}
	
}
