package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.service.custinfo.impl.ImportConstantHisService;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (分配记录刷数据)
 * @date 2023/11/8 13:19
 * @since JDK 1.8
 */
public interface ImportConstantHisMapper {

    /**
     * @description 备份数据
     * @param
     * @return void
     * @author: jianjian.yang
     * @date: 2023/12/4 14:31
     * @since JDK 1.8
     */
    void bakData();
    /**
     * 查出客户信息
     * @param custNos
     * @return java.util.List<com.howbuy.crm.hb.service.custinfo.impl.ImportConstantHisService.CustInfo>
     * @author: jianjian.yang
     * @date: 2023/11/8 13:20
     * @since JDK 1.8
     */
    List<ImportConstantHisService.CustInfo> listCustInfo(@Param("custNos") List<String> custNos);

    /**
     * 批量插入数据
     * @param custInfos
     * @return int
     * @author: jianjian.yang
     * @date: 2023/11/8 18:19
     * @since JDK 1.8
     */
    int batchInsertCustHis(@Param("custInfos") List<ImportConstantHisService.CustInfo> custInfos);

    /**
     *  匹配分配记录
     * @param consMatchInfos
     * @return java.util.List
     * @author: jianjian.yang
     * @date: 2023/11/9 13:20
     * @since JDK 1.8
     */
    List<ImportConstantHisService.ConsMatchBO> matchAssignRecord(@Param("consMatchInfos") List<ImportConstantHisService.ConsMatchInfo> consMatchInfos);

    /**
     * 修改
     * @param consMatchList
     * @return int
     * @author: jianjian.yang
     * @date: 2023/11/9 15:26
     * @since JDK 1.8
     */
    int updateHisLeads(@Param("consMatchList") List<ImportConstantHisService.ConsMatchBO> consMatchList);

    /**
     * @description 查询
     * @param
     * @return java.util.List<com.howbuy.crm.hb.service.custinfo.impl.ImportConstantHisService.UpdateConsNull>
     * @author: jianjian.yang
     * @date: 2023/11/9 15:14
     * @since JDK 1.8
     */
    List<ImportConstantHisService.UpdateConsNull> listConsNullData();
    /**
     * 更新分配投顾为空的数据为下一条数据的原投顾
     * @param updateConsNulls
     * @return int
     * @author: jianjian.yang
     * @date: 2023/11/9 10:07
     * @since JDK 1.8
     */
    int updateConsNullData(@Param("updateConsNulls") List<ImportConstantHisService.UpdateConsNull> updateConsNulls);

    /**
     * 刷Rs库leads分配记录
     * @param custNos	
     * @return int
     * @author: jianjian.yang
     * @date: 2023/11/8 18:33
     * @since JDK 1.8
     */
    int updateRsLeads(@Param("custNos") List<String> custNos);

    /**
     *  匹配20w分配记录
     * @param consMatchInfos
     * @return java.util.List<com.howbuy.crm.hb.service.custinfo.impl.ImportConstantHisService.ConsMatchBO>
     * @author: jianjian.yang
     * @date: 2023/11/9 19:19
     * @since JDK 1.8
     */
    List<ImportConstantHisService.ConsMatchBO> matchAssign20wRecord(@Param("consMatchInfos") List<ImportConstantHisService.ConsMatchInfo> consMatchInfos);

    /**
     *  更新
     * @param consMatchList
     * @return int
     * @author: jianjian.yang
     * @date: 2023/11/9 19:19
     * @since JDK 1.8
     */
    int updateHis20w(@Param("consMatchList") List<ImportConstantHisService.ConsMatchBO> consMatchList);
}
