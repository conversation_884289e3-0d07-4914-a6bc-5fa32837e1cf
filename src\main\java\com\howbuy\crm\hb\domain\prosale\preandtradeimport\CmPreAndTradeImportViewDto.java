package com.howbuy.crm.hb.domain.prosale.preandtradeimport;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (交易记录导入 - - excel 映射)
 * @date 2023/3/2 13:20
 * @since JDK 1.8
 */
@Data
public class CmPreAndTradeImportViewDto {

    /**
     *预约Id
     */
    @ExcelProperty(value = "预约ID")
    private BigDecimal preId;

    /**
     *分次call 的  mainId [认缴ID].
     */
    @ExcelProperty(value = "认缴ID")
    private BigDecimal mainCallId;
    /**
     *投顾客户号
     */
    @ExcelProperty(value = "客户号")
    private String custNo;
    //
    @ExcelProperty(value = "客户姓名")
    private String custName;
    //
    @ExcelProperty(value = "产品代码")
    private String prodCode;
    //
    @ExcelProperty(value = "产品名称")
    private String prodName;
    /**
     * 预约类型（1：纸质成单；2：电子成单）
     */
    @ExcelProperty(value = "预约类型")
    private String preType;
    /**
     * 币种【"156":"人民币","840":"美元","344":"港元","954":"欧元","392":"日元","826":"英镑","250":"法郎","280":"马克" 】
     */
    @ExcelProperty(value = "币种")
    private String currency;
    //
    @ExcelProperty(value = "购买金额")
    private BigDecimal appAmt;
    /**
     * 支付方式 01-自划款；04-银行卡代扣；06-储蓄罐代扣
     */
    @ExcelProperty(value = "支付方式")
    private String paymentType;
    //
    @ExcelProperty(value = "赎回份额")
    private BigDecimal appVol;
    /**
     *  赎回去向0：银行卡；1：储蓄罐  2:留账好买香港账户
     */
    @ExcelProperty(value = "赎回至")
    private String redeemDirection;
    //
    @ExcelProperty(value = "预计打款日期")
    private String expectPayDt;
    //
    @ExcelProperty(value = "预计交易日期")
    private String expectTradeDt;
    //
    @ExcelProperty(value = "实际打款日期")
    private String realPayDt;

    //
    @ExcelProperty(value = "实际打款金额")
    private BigDecimal realPayAmt;

    //
    @ExcelProperty(value = "实际打款手续费")
    private BigDecimal realPayFee;

    //
    @ExcelProperty(value = "确认日期")
    private String ackDt;

    //
    @ExcelProperty(value = "确认净值")
    private BigDecimal nav;

    //
    @ExcelProperty(value = "确认份额")
    private BigDecimal ackVol;

    //
    @ExcelProperty(value = "确认金额")
    private BigDecimal ackAmt;

    //
    @ExcelProperty(value = "确认手续费")
    private BigDecimal ackFee;

    //
    @ExcelProperty(value = "汇率日期")
    private String rateDt;

    //
    @ExcelProperty(value = "平衡因子")
    private BigDecimal balanceFactor;

    /**
     * 是否海外特殊处理 1-是；0-否
     */
    @ExcelProperty(value = "是否海外特殊处理")
    private String haiwai;

    /**
     * 是否发送短信（1-是、0-否）
     */
    @ExcelProperty(value = "是否发送短信")
    private String message;

    //
    @ExcelProperty(value = "备注")
    private String impSummary;

    /**
     * 交易代码 120-认购 122-申购 124-赎回 142-强赎
     */
    private String busiCode;

    /**
     * 导入类型：1-常规 2-认缴 3-实缴
     */
    private String impType;


    /**
     * 创建人
     */
    private String creator;

}
