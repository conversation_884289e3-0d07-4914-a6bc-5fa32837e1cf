package com.howbuy.crm.hb.constants;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 绩效常量
 * @date 2024/4/23 17:14
 * @since JDK 1.8
 */
public class RewardConstants {


    /**
     * 分销机构枚举值 1-好买香港、2-好臻、3-好买(代销)、4-直销
     */
    public static final List<String> DIS_ORGAN_CODE_LIST = Lists.newArrayList("1","2","3","4");

    /**
     * 修改标志 1-是
     */
    public static final String MODIFY_FLAG_YES = "1";
    /**
     * 修改标志 0-否
     */
    public static final String MODIFY_FLAG_NO = "0";
    /**
     * 修改标志 2-已同步
     */
    public static final String MODIFY_FLAG_SYNC = "2";
}
