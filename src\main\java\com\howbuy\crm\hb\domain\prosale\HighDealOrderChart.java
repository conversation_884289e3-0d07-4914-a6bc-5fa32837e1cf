package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 高端持仓时序图实体类
* <AUTHOR>
* @date 2022/10/21
*/
public class HighDealOrderChart implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 图表日期
	 */
	private String chartDate;

	/**
	 * 图表值
	 */
	private BigDecimal chartValue;

	/**
	 * 图表值
	 */
	private String chartValueStr;

	/**
	 * 追加显示收益率值
	 */
	private String rateValueStr;

	public String getChartDate() {
		return chartDate;
	}

	public void setChartDate(String chartDate) {
		this.chartDate = chartDate;
	}

	public BigDecimal getChartValue() {
		return chartValue;
	}

	public void setChartValue(BigDecimal chartValue) {
		this.chartValue = chartValue;
	}

	public String getChartValueStr() {
		return chartValueStr;
	}

	public void setChartValueStr(String chartValueStr) {
		this.chartValueStr = chartValueStr;
	}

	public String getRateValueStr() {
		return rateValueStr;
	}

	public void setRateValueStr(String rateValueStr) {
		this.rateValueStr = rateValueStr;
	}
}
	