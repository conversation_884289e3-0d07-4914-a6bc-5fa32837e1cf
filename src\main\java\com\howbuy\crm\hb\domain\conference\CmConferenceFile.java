package com.howbuy.crm.hb.domain.conference;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/2/12 15:27
 * @since JDK 1.8
 */
@Data
public class CmConferenceFile implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    private String id;

    /**
    * 会议ID
    */
    private String conferenceId;

    /**
    * 文件名
    */
    private String fileName;

    /**
    * 是否删除 0-正常 1-删除
    */
    private String isDel;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 文件相对路径
    */
    private String relativeFilePath;

    /**
    * 更新修改时间
    */
    private Date modifyTime;

    /**
    * 更新修改人
    */
    private String modifier;
}