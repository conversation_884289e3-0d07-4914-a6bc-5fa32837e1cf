package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * @Description: 实体类CmBxChannel.java
 * <AUTHOR>
 */
@Data
public class CmBxChannel implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;

	private String channcode;

	private String channname;

	private String isdel;

	private String creator;

	private Date creatdt;

	private String modifier;

	private Date modifydt;
}
