package com.howbuy.crm.hb.domain.usergroup;

import java.io.Serializable;
import lombok.Data;


/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmCustomizegroup implements Serializable {

	private static final long serialVersionUID = 1L;

	private String groupid;

	private String groupname;

	private String grouplevel;
	
	private String grouplevelz;

	private String memo;

	private String orgcode;
	
	private String orgcodez;
	
	private String teamcode;
	
	private String teamcodez;

	private String delflag;

	private String creator;
	
	private String creatorName;
	
	private String loginUser;

	private String modifier;

	private String credt;

	private String moddt;

	private String custcount;
	
	private String isic;

	private String isauth;

	private String publictype;
	
	private String publictypez;
	
	private String isopdb;
	
	private String roleCode;
	
	private String loginOrgCode;
	
	private String userLevel;
	
	private String orgcode2;
}
