/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.birthdaygift;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 高端生日礼物-修改列表
 * <AUTHOR>
 * @date 2024/9/23 15:13
 * @since JDK 1.8
 */

@Getter
@Setter
public class SmGiftInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 礼物描述
     */
    private String desc;

    /**
     * 礼物图片地址
     */
    private String imgUrl;

    /**
     * 礼物种类id
     */
    private String giftCode;

    /**
     * 京东CLPS商品编号
     */
    private String itemId;

}