package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 投顾添加的客户-微信信息
 * @author: haoran.zhang
 * @create: 2021-08-20 14:36
 **/
@Data
public class WebChatUserDto implements Serializable {

    private static final long serialVersionUID = -8725424981804913937L  ;

    private String id;
    /**
     * 投顾
     */
    private String conscode;

    /**
     * 一账通账号
     */
    private String hboneNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 国内一账通号
     */
    private String gnHboneNo;
    /**
     * 香港一账通号
     */
    private String hkHboneNo;

    /**
     * 投顾客户号
     */
    private String custNo;

    /**
     * 客户姓名
     */
    private String custName;


    /**
     * 添加客户的微信昵称，若客户有更改，需显示最新昵称
     */
    private String webChatNickName;


    /**
     * 该客户微信添加企业微信用户总数
     */
    private int followCount;
    /**
     * 投顾所有高端客户数
     */
    private int custCount;


    /****************投顾信息begin**********************/

    /**
     * 企业微信添加投顾名称
     */
    private String  addConsName;
    /**
     * 企业微信添加投顾号
     */
    private String  addConsCode;
    /**
     * 企业微信添加投顾部门
     */
    private String  addConsOrgName;
    /**
     * 企业微信添加投顾区域
     */
    private String  addConsUpOrgName;
    /**
     * 企业微信添加投顾中心
     */
    private String  addConsCenterName;
    /**
     * 投顾名称
     */
    private String  consName;

    /**
     * 投顾部门名称
     */
    private String orgname;

    /**
     * 投顾区域
     */
    private String uporgname;
    /**
     * 投顾所属中心
     */
    private String centerName;

    /**
     * 当前状态
     */
    private String status;
    private String statusCode;
    /**
     * 添加时间
     */
    private String addTime;
    /**
     * 流失时间
     */
    private String delTime;
    /**
     * 微信ID
     */
    private String unionId;
    /**
     * 企业微信id
     */
    private String externalUserId;
    /**
     * 添加客户的来源
     */
    private String addWay;
    /**
     * 添加客户的渠道
     */
    private String state;
    /**
     * 客户状态
     */
    private String custLabelStat;
    /**
     * 客户来源(一级)
     */
    private String sourceName;
    /**
     * 来源类型
     */
    private String sourceType;
    /**
     * 是否成交
     */
    private String iscj;
    /**
     * 首次分配时间
     */
    private String firstAssignConsdt;
    /**
     * 首次成交时间
     */
    private String firstCj;
    /**
     * 最近分配时间
     */
    private String lastAssignConsdt;
    /**
     * 添加率
     */
    private String addScale;
    /**
     * 客户类型
     */
    private String invstType;
    /**
     * 最近购买高净值日期
     */
    private String latestTradeDt;

    /**
     * unionId 来源
     */
    private String unionIdSource;
}
