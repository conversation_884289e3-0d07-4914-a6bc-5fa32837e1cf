package com.howbuy.crm.hb.persistence.custinfo;


import com.howbuy.crm.hb.domain.custinfo.CmRealConsultant;
import com.howbuy.crm.hb.domain.custinfo.CmRealConsultantVo;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CmRealConsultantMapper {
    /**
     * 插入实际投顾数据
     * @param cmRealConsultant
     * @return
     */
    int insert(CmRealConsultant cmRealConsultant);

    int insertSelective(CmRealConsultant cmRealConsultant);

    /**
     * 分页查询数据
     * @param pageBean
     * @return
     */
    List<CmRealConsultant> listCmRealConsultantByPage(@Param("param") CmRealConsultantVo vo, @Param("page") CommPageBean pageBean);

    /**
     * 获取单条记录
     * @param param
     * @return
     */
    CmRealConsultant getCmRealConsultant(Map<String, Object> param);

    /**
     * 单条记录更新
     * @param vo
     */
    void updateCmRealConsultant(CmRealConsultantVo vo);

    /**
     * 删除单条数据
     * @param conscustno
     */
    void deleteCmRealConsultant(String conscustno);

    /**
     * @description: 根据客户编号列表查询实际投顾信息
     * @param custNoList 客户编号列表
     * @return java.util.List<com.howbuy.crm.hb.domain.custinfo.CmRealConsultant> 实际投顾信息列表
     * @since JDK 1.8
     */
    List<CmRealConsultant> listCmRealConsultantByCustNoList(@Param("custNoList") List<String> custNoList);
}