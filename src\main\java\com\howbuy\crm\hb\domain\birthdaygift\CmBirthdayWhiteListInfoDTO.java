/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.birthdaygift;

import java.util.Date;

/**
 * @description: 高端选礼物白名单信息表DTO
 * <AUTHOR>
 * @date 2025/2/21 15:44
 * @since JDK 1.8
 */
public class CmBirthdayWhiteListInfoDTO {


    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = -5982525284353192033L;

    /**
     * id
     */
    private Long id;

    /**
     * idStr
     */
    private String idStr;

    /**
     * 申请人类型(1:客户，2:投顾)
     */
    private String applicantType;

    /**
     * 申请人类型名称(1:客户，2:投顾)
     */
    private String applicantTypeName;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请来源
     */
    private String applicantSource;

    /**
     * 用户一账通
     */
    private String hboneNo;

    /**
     * 投顾客户号
     */
    private String consCustNo;

    /**
     * 所属投顾
     */
    private String consName;

    /**
     * 投顾号
     */
    private String consCode;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 状态 01:新增 02:更新 03:删除
     */
    private String mOptType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建时间(yyyy-MM-dd HH:mm:ss)
     */
    private String createTimeFMT;

    /**
     * 修改时间(yyyy-MM-dd HH:mm:ss)
     */
    private String updateTimeFMT;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApplicantType() {
        return applicantType;
    }

    public void setApplicantType(String applicantType) {
        this.applicantType = applicantType;
    }

    public String getApplicantTypeName() {
        return applicantTypeName;
    }

    public void setApplicantTypeName(String applicantTypeName) {
        this.applicantTypeName = applicantTypeName;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getApplicantSource() {
        return applicantSource;
    }

    public void setApplicantSource(String applicantSource) {
        this.applicantSource = applicantSource;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getmOptType() {
        return mOptType;
    }

    public void setmOptType(String mOptType) {
        this.mOptType = mOptType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateTimeFMT() {
        return createTimeFMT;
    }

    public void setCreateTimeFMT(String createTimeFMT) {
        this.createTimeFMT = createTimeFMT;
    }

    public String getUpdateTimeFMT() {
        return updateTimeFMT;
    }

    public void setUpdateTimeFMT(String updateTimeFMT) {
        this.updateTimeFMT = updateTimeFMT;
    }

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getIdStr() {
        return idStr;
    }

    public void setIdStr(String idStr) {
        this.idStr = idStr;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
}