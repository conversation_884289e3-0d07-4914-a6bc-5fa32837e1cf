package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmXkPreRankingDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private BigDecimal id;
	
	private String credt;
	
	private String cretime;
	
	private String expecttradedt;
	
	private String pcode;
	
	private String pname;
	
	private String custname;
	
	private String idnoMask;
	
	private String occupyType;
	
	private String occupyStatus;
	
	private String tradestate;
	
	private String prebookstate;
	
	private String paystate;
	
	private boolean matchedDeposit;
	
	private String summaryInfo;
	
	private BigDecimal occurBalance;
	
	/**
	 * 等位情况-金额
	 */
	private Integer dwBalance;
	
	/**
	 * 等位情况-人数
	 */
	private Integer dwCount;
	
	/**
	 * 预约排位
	 */
	private Integer yypw;
	
	private String sfmsjg;
	
	private String txpmtflag;

	/**
	 * 资金匹配
	 */
	private String finMatched;
	
	/**
	 * 交易类型( 1：购买，2：追加，3：赎回)
	 */
	private String tradeType;

	/**
	 * 预约金额
	 */
	private BigDecimal buyAmt;

	/**
	 * 一账通账号
	 */
	private String hboneNo;
	
	/**
	 * //所属投顾
	 */
	private String creator;
	
	private String consname;
	
	/**
	 * 所属区域
	 */
	private String uporgname;
	/**
	 * 所属部门
	 */
	private String outletName;
}
