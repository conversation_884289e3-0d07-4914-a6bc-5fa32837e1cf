package com.howbuy.crm.enums;

/**
 * @description: 调用类型枚举
 * <AUTHOR>
 * @date 2025-04-02 20:40:47
 * @since JDK 1.8
 */
public enum CallTypeEnum {
     
    /**
     * Dubbo服务调用
     */
    DUBBO("dubbo"),
     
    /**
     * 消息队列调用
     */
    MQ("mq"),
     
    /**
     * HTTP接口调用
     */
    HTTP("http");
     
    private final String type;
     
    CallTypeEnum(String type) {
        this.type = type;
    }
     
    public String getType() {
        return type;
    }
}
