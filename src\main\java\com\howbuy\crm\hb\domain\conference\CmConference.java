package com.howbuy.crm.hb.domain.conference;

import java.io.Serializable;

/**
 * (路演会议记录表)
 * <AUTHOR>
 * @ClassName: CmConference
 * 											  
 * 创建日期    		                            修改人员   	        版本	 	     修改内容  
 * -------------------------------------------------  
 * 2017年8月14日 下午4:40:10   yu.zhang     1.0    	初始化创建
 *
 * 修改记录:
 * @since		JDK1.6
 */
public class CmConference implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 会议主键 */
	private String conferenceid;
	
	/** 会议名称 */
	private String conferencename;
	
	/** 会议时间 */
	private String conferencedt;
	
	/** 会议类型 */
	private String conferencetype;
	
	/** 举办部门 */
	private String orgcode;
	
	/** 会议省份 */
	private String provcode;

	/** 会议市区 */
	private String citycode;
	
	/** 参会人数上限 */
	private int maxnumber;

	/** 创建日期 */
	private String creatdt;
	
	/** 创建人 */
	private String creater;
	
	/** 修改日期 */
	private String modifydt;
	
	/** 修改人 */
	private String modifier;
	
	/** 产品 */
	private String pcodes;
	
	/** 管理人 */
	private String companys;
	
	/** 产品线 */
	private String lines;
	
	/** 产品线名称 */
	private String linenames;
	
	/** 截止报名时间 */
	private String cutoffdt;

	/** 会议内容 */
	private String meetingcontents;

	/** 未确认参会时间 */
	private String uncommitteddt;

	/** 未录证件时间 */
	private String noiddt;

	/** 未提交行程时间 */
	private String noroutingdt;
	/**
	 * 上传文件的审核状态  0-待审核 1-初审通过 2-复审通过 3-驳回
	 */
	private String fileAuditStatus;
	/**
	 * 审核意见
	 */
	private String auditText;

	/**
	 * 会议详细地址
	 */
	private String address;
	/**
	 * 纬度
	 */
	private String lat;
	/**
	 * 经度
	 */
	private String lng;

	/**
	 * 课程id
	 *
	 * @return
	 */
	private String courseId;

	/**
	 * 是否是理财九章线下课
	 */
	private String islcjz;

	public String getIslcjz() {
		return islcjz;
	}

	public void setIslcjz(String islcjz) {
		this.islcjz = islcjz;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	public String getFileAuditStatus() {
		return fileAuditStatus;
	}

	public void setFileAuditStatus(String fileAuditStatus) {
		this.fileAuditStatus = fileAuditStatus;
	}

	public String getAuditText() {
		return auditText;
	}

	public void setAuditText(String auditText) {
		this.auditText = auditText;
	}

	public String getPcodes() {
		return pcodes;
	}

	public void setPcodes(String pcodes) {
		this.pcodes = pcodes;
	}

	public String getCompanys() {
		return companys;
	}

	public void setCompanys(String companys) {
		this.companys = companys;
	}

	public String getLines() {
		return lines;
	}

	public void setLines(String lines) {
		this.lines = lines;
	}

	public String getLinenames() {
		return linenames;
	}

	public void setLinenames(String linenames) {
		this.linenames = linenames;
	}

	public String getConferencetype() {
		return conferencetype;
	}

	public void setConferencetype(String conferencetype) {
		this.conferencetype = conferencetype;
	}

	public String getConferenceid() {
		return conferenceid;
	}

	public void setConferenceid(String conferenceid) {
		this.conferenceid = conferenceid;
	}

	public String getConferencename() {
		return conferencename;
	}

	public void setConferencename(String conferencename) {
		this.conferencename = conferencename;
	}

	public String getConferencedt() {
		return conferencedt;
	}

	public void setConferencedt(String conferencedt) {
		this.conferencedt = conferencedt;
	}

	public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	public String getProvcode() {
		return provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public int getMaxnumber() {
		return maxnumber;
	}

	public void setMaxnumber(int maxnumber) {
		this.maxnumber = maxnumber;
	}

	public String getCreatdt() {
		return creatdt;
	}

	public void setCreatdt(String creatdt) {
		this.creatdt = creatdt;
	}

	public String getCreater() {
		return creater;
	}

	public void setCreater(String creater) {
		this.creater = creater;
	}

	public String getModifydt() {
		return modifydt;
	}

	public void setModifydt(String modifydt) {
		this.modifydt = modifydt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCutoffdt() {
		return cutoffdt;
	}

	public void setCutoffdt(String cutoffdt) {
		this.cutoffdt = cutoffdt;
	}

	public String getMeetingcontents() {
		return meetingcontents;
	}

	public void setMeetingcontents(String meetingcontents) {
		this.meetingcontents = meetingcontents;
	}

	public String getUncommitteddt() {
		return uncommitteddt;
	}

	public void setUncommitteddt(String uncommitteddt) {
		this.uncommitteddt = uncommitteddt;
	}

	public String getNoiddt() {
		return noiddt;
	}

	public void setNoiddt(String noiddt) {
		this.noiddt = noiddt;
	}

	public String getNoroutingdt() {
		return noroutingdt;
	}

	public void setNoroutingdt(String noroutingdt) {
		this.noroutingdt = noroutingdt;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}
}
