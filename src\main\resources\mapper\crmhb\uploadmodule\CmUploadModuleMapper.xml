<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.uploadmodule.CmUploadModuleMapper">
	
    <select id="listCmUploadModuleByPage" parameterType="Map" resultType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModule" useCache="false">
	  	select t1.id,t1.maintype,t1.subtype,t1.des from cm_upload_module t1
		  where 1=1
		  <if test="param.maintype != null"> and t1.maintype = #{param.maintype} </if>
		  <if test="param.subtype != null"> and t1.subtype = #{param.subtype} </if> 
		  <if test="param.uptype != null"> 
		  	AND exists (select 1 from CM_UPLOAD_MODULE_TYPE a where a.moduleid=T1.id and a.uptype = #{param.uptype})
		  </if> 
		  <if test="param.suffix != null"> 
		  	AND exists (select 1 from CM_UPLOAD_MODULE_TYPE a left join CM_UPLOAD_MODULE_TYPE_SUFFIX b on a.id = b.typeid where a.moduleid=T1.id and b.suffix = #{param.suffix})
		  </if> 
		  <if test="param.sort != null and param.order != null" > ORDER BY  ${param.sort} ${param.order} nulls last</if>
	</select>
	
	<select id="listCmUploadModuleType" parameterType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModuleType" resultType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModuleType" useCache="false">
			select t.id, t.moduleid, t.uptype, t.maxsize from CM_UPLOAD_MODULE_TYPE t
			where 1=1
			<if test="moduleid != null">
			and moduleid = #{moduleid}
			</if>
	</select>
	  
	<select id="getCmUploadModule" parameterType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModule" resultType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModule" useCache="false">
		select t1.id,t1.maintype,t1.subtype,t1.des from cm_upload_module t1 where t1.id = #{id}
	</select>
	
	<select id="listCmUploadModuleTypeSuffix" parameterType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModuleTypeSuffix" resultType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModuleTypeSuffix" useCache="false">
			select t.id, t.typeid, t.suffix from CM_UPLOAD_MODULE_TYPE_SUFFIX t
			where 1=1
			<if test="typeid != null">
			and typeid = #{typeid}
			</if>
	</select>
	
	<update id="delCmUploadModuleTypeSuffix" parameterType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModule">
        delete from CM_UPLOAD_MODULE_TYPE_SUFFIX t
		 where t.typeid in
		       (select a.id from CM_UPLOAD_MODULE_TYPE a where a.moduleid = #{id})   
    </update>
    
    <update id="delCmUploadModuleType" parameterType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModule">
        delete from CM_UPLOAD_MODULE_TYPE t where t.moduleid = #{id}  
    </update>
    
    <insert id="insertCmUploadModuleType" parameterType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModuleType">
        INSERT INTO CM_UPLOAD_MODULE_TYPE (
       <trim suffix="" suffixOverrides=",">   
                <if test="id != null"> id, </if>
                <if test="moduleid != null"> moduleid, </if>
				<if test="uptype != null"> uptype, </if>
				<if test="maxsize != null"> maxsize, </if>
				<if test="creator != null"> creator, </if>
        </trim>
           ) values (
        <trim suffix="" suffixOverrides=",">
                <if test="id != null"> #{id}, </if>
				<if test="moduleid != null"> #{moduleid}, </if>
				<if test="uptype != null"> #{uptype}, </if>
				<if test="maxsize != null"> #{maxsize}, </if>
				<if test="creator != null"> #{creator}, </if>
        </trim>  
         )
    </insert>
    
    <insert id="insertCmUploadModuleTypeSuffix" parameterType="com.howbuy.crm.hb.domain.uploadmodule.CmUploadModuleTypeSuffix">
        INSERT INTO CM_UPLOAD_MODULE_TYPE_SUFFIX (
       <trim suffix="" suffixOverrides=",">   
                <if test="id != null"> id, </if>
                <if test="typeid != null"> typeid, </if>
				<if test="suffix != null"> suffix, </if>
				<if test="creator != null"> creator, </if>
        </trim>
           ) values (
        <trim suffix="" suffixOverrides=",">
                <if test="id != null"> #{id}, </if>
				<if test="typeid != null"> #{typeid}, </if>
				<if test="suffix != null"> #{suffix}, </if>
				<if test="creator != null"> #{creator}, </if>
        </trim>  
         )
    </insert>
		
</mapper>