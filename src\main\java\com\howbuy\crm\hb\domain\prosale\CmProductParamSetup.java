package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品参数设置实体类
 * 
 * @author: wu.long
 * date: 2019年7月17日 下午3:40:02
 * version: V1.0
 * since: jdk 1.8,tomcat 8.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmProductParamSetup implements Serializable{
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	/**
	 * 产品代码
	 */
	private String pcode;
	/**
	 * 是否支持复购：0-否；1-是
	 */
	private BigDecimal supportRepurchase;
	/**
	 * 到期自动赎回：0-否；1-是
	 */
	private BigDecimal expireAutoRedeem;
	/**
	 * 是否内扣：0-否；1-是
	 */
	private BigDecimal internalDeduction;
	/**
	 * 创建
	 */
	private String creater;
	private String updater;
	/**
	 * 创建日期 
	 */
	private String createdate;
	/**
	 * 修改日期 
	 */
	private String updatedate;
	/**
	 * 产品名称
	 */
	private String pname;
	/**
	 * 状态：1-删除，0-正常
	 */
	private BigDecimal status;
	/**
	 * 产品类型：1为代销，2为直销，3为直转代
	 */
	private String sfmsjg; 
	private String jjdm;
	/**
	 * 个人是否可预约：0-否；1-是
	 */
	private String personalPreStatus;
	/**
	 * 产品是否可预约：0-否；1-是 
	 */
	private String productPreStatus;
	/**
	 * 机构是否可预约：0-否；1-是
	 */
	private String institutionPreStatus;
}
