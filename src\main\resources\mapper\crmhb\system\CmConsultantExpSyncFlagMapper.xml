<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpSyncFlagMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.hb.domain.system.CmConsultantExpSyncFlag">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT_EXP_SYNC_FLAG-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="CONSNAME_SYNC_FLAG" jdbcType="VARCHAR" property="consnameSyncFlag" />
    <result column="CITYCODE_SYNC_FLAG" jdbcType="VARCHAR" property="citycodeSyncFlag" />
    <result column="CENTER_ORG_SYNC_FLAG" jdbcType="VARCHAR" property="centerOrgSyncFlag" />
    <result column="WORKTYPE_SYNC_FLAG" jdbcType="VARCHAR" property="worktypeSyncFlag" />
    <result column="WORKSTATE_SYNC_FLAG" jdbcType="VARCHAR" property="workstateSyncFlag" />
    <result column="CURMONTHLEVEL_SYNC_FLAG" jdbcType="VARCHAR" property="curmonthlevelSyncFlag" />
    <result column="USERLEVEL_SYNC_FLAG" jdbcType="VARCHAR" property="userlevelSyncFlag" />
    <result column="CURMONTHSALARY_SYNC_FLAG" jdbcType="VARCHAR" property="curmonthsalarySyncFlag" />
    <result column="SUBPOSITIONS_SYNC_FLAG" jdbcType="VARCHAR" property="subpositionsSyncFlag" />
    <result column="STARTDT_SYNC_FLAG" jdbcType="VARCHAR" property="startdtSyncFlag" />
    <result column="STARTLEVEL_SYNC_FLAG" jdbcType="VARCHAR" property="startlevelSyncFlag" />
    <result column="SALARY_SYNC_FLAG" jdbcType="VARCHAR" property="salarySyncFlag" />
    <result column="REGULARDT_SYNC_FLAG" jdbcType="VARCHAR" property="regulardtSyncFlag" />
    <result column="REGULARLEVEL_SYNC_FLAG" jdbcType="VARCHAR" property="regularlevelSyncFlag" />
    <result column="REGULARSALARY_SYNC_FLAG" jdbcType="VARCHAR" property="regularsalarySyncFlag" />
    <result column="QUITDT_SYNC_FLAG" jdbcType="VARCHAR" property="quitdtSyncFlag" />
    <result column="QUITLEVEL_SYNC_FLAG" jdbcType="VARCHAR" property="quitlevelSyncFlag" />
    <result column="QUITSALARY_SYNC_FLAG" jdbcType="VARCHAR" property="quitsalarySyncFlag" />
    <result column="BACKGROUND_SYNC_FLAG" jdbcType="VARCHAR" property="backgroundSyncFlag" />
    <result column="SOURCE_SYNC_FLAG" jdbcType="VARCHAR" property="sourceSyncFlag" />
    <result column="BEFOREPOSITIONAGE_SYNC_FLAG" jdbcType="VARCHAR" property="beforepositionageSyncFlag" />
    <result column="RECRUIT_SYNC_FLAG" jdbcType="VARCHAR" property="recruitSyncFlag" />
    <result column="RECOMMEND_SYNC_FLAG" jdbcType="VARCHAR" property="recommendSyncFlag" />
    <result column="RECOMMENDUSERNO_SYNC_FLAG" jdbcType="VARCHAR" property="recommendusernoSyncFlag" />
    <result column="RECOMMENDTYPE_SYNC_FLAG" jdbcType="VARCHAR" property="recommendtypeSyncFlag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, USERID, CONSNAME_SYNC_FLAG, CITYCODE_SYNC_FLAG, CENTER_ORG_SYNC_FLAG, WORKTYPE_SYNC_FLAG, 
    WORKSTATE_SYNC_FLAG, CURMONTHLEVEL_SYNC_FLAG, USERLEVEL_SYNC_FLAG, CURMONTHSALARY_SYNC_FLAG, 
    SUBPOSITIONS_SYNC_FLAG, STARTDT_SYNC_FLAG, STARTLEVEL_SYNC_FLAG, SALARY_SYNC_FLAG, 
    REGULARDT_SYNC_FLAG, REGULARLEVEL_SYNC_FLAG, REGULARSALARY_SYNC_FLAG, QUITDT_SYNC_FLAG, 
    QUITLEVEL_SYNC_FLAG, QUITSALARY_SYNC_FLAG, BACKGROUND_SYNC_FLAG, SOURCE_SYNC_FLAG, 
    BEFOREPOSITIONAGE_SYNC_FLAG, RECRUIT_SYNC_FLAG, RECOMMEND_SYNC_FLAG, RECOMMENDUSERNO_SYNC_FLAG, 
    RECOMMENDTYPE_SYNC_FLAG, CREATOR, MODIFIER, CREDT, MODDT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_SYNC_FLAG
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from CM_CONSULTANT_EXP_SYNC_FLAG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpSyncFlag">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_EXP_SYNC_FLAG (ID, USERID, CONSNAME_SYNC_FLAG, 
      CITYCODE_SYNC_FLAG, CENTER_ORG_SYNC_FLAG, WORKTYPE_SYNC_FLAG, 
      WORKSTATE_SYNC_FLAG, CURMONTHLEVEL_SYNC_FLAG, 
      USERLEVEL_SYNC_FLAG, CURMONTHSALARY_SYNC_FLAG, 
      SUBPOSITIONS_SYNC_FLAG, STARTDT_SYNC_FLAG, 
      STARTLEVEL_SYNC_FLAG, SALARY_SYNC_FLAG, REGULARDT_SYNC_FLAG, 
      REGULARLEVEL_SYNC_FLAG, REGULARSALARY_SYNC_FLAG, 
      QUITDT_SYNC_FLAG, QUITLEVEL_SYNC_FLAG, QUITSALARY_SYNC_FLAG, 
      BACKGROUND_SYNC_FLAG, SOURCE_SYNC_FLAG, BEFOREPOSITIONAGE_SYNC_FLAG, 
      RECRUIT_SYNC_FLAG, RECOMMEND_SYNC_FLAG, RECOMMENDUSERNO_SYNC_FLAG, 
      RECOMMENDTYPE_SYNC_FLAG, CREATOR, MODIFIER, 
      CREDT, MODDT)
    values (#{id,jdbcType=DECIMAL}, #{userid,jdbcType=VARCHAR}, #{consnameSyncFlag,jdbcType=VARCHAR}, 
      #{citycodeSyncFlag,jdbcType=VARCHAR}, #{centerOrgSyncFlag,jdbcType=VARCHAR}, #{worktypeSyncFlag,jdbcType=VARCHAR}, 
      #{workstateSyncFlag,jdbcType=VARCHAR}, #{curmonthlevelSyncFlag,jdbcType=VARCHAR}, 
      #{userlevelSyncFlag,jdbcType=VARCHAR}, #{curmonthsalarySyncFlag,jdbcType=VARCHAR}, 
      #{subpositionsSyncFlag,jdbcType=VARCHAR}, #{startdtSyncFlag,jdbcType=VARCHAR}, 
      #{startlevelSyncFlag,jdbcType=VARCHAR}, #{salarySyncFlag,jdbcType=VARCHAR}, #{regulardtSyncFlag,jdbcType=VARCHAR}, 
      #{regularlevelSyncFlag,jdbcType=VARCHAR}, #{regularsalarySyncFlag,jdbcType=VARCHAR}, 
      #{quitdtSyncFlag,jdbcType=VARCHAR}, #{quitlevelSyncFlag,jdbcType=VARCHAR}, #{quitsalarySyncFlag,jdbcType=VARCHAR}, 
      #{backgroundSyncFlag,jdbcType=VARCHAR}, #{sourceSyncFlag,jdbcType=VARCHAR}, #{beforepositionageSyncFlag,jdbcType=VARCHAR}, 
      #{recruitSyncFlag,jdbcType=VARCHAR}, #{recommendSyncFlag,jdbcType=VARCHAR}, #{recommendusernoSyncFlag,jdbcType=VARCHAR}, 
      #{recommendtypeSyncFlag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{credt,jdbcType=TIMESTAMP}, #{moddt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpSyncFlag">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_EXP_SYNC_FLAG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="userid != null">
        USERID,
      </if>
      <if test="consnameSyncFlag != null">
        CONSNAME_SYNC_FLAG,
      </if>
      <if test="citycodeSyncFlag != null">
        CITYCODE_SYNC_FLAG,
      </if>
      <if test="centerOrgSyncFlag != null">
        CENTER_ORG_SYNC_FLAG,
      </if>
      <if test="worktypeSyncFlag != null">
        WORKTYPE_SYNC_FLAG,
      </if>
      <if test="workstateSyncFlag != null">
        WORKSTATE_SYNC_FLAG,
      </if>
      <if test="curmonthlevelSyncFlag != null">
        CURMONTHLEVEL_SYNC_FLAG,
      </if>
      <if test="userlevelSyncFlag != null">
        USERLEVEL_SYNC_FLAG,
      </if>
      <if test="curmonthsalarySyncFlag != null">
        CURMONTHSALARY_SYNC_FLAG,
      </if>
      <if test="subpositionsSyncFlag != null">
        SUBPOSITIONS_SYNC_FLAG,
      </if>
      <if test="startdtSyncFlag != null">
        STARTDT_SYNC_FLAG,
      </if>
      <if test="startlevelSyncFlag != null">
        STARTLEVEL_SYNC_FLAG,
      </if>
      <if test="salarySyncFlag != null">
        SALARY_SYNC_FLAG,
      </if>
      <if test="regulardtSyncFlag != null">
        REGULARDT_SYNC_FLAG,
      </if>
      <if test="regularlevelSyncFlag != null">
        REGULARLEVEL_SYNC_FLAG,
      </if>
      <if test="regularsalarySyncFlag != null">
        REGULARSALARY_SYNC_FLAG,
      </if>
      <if test="quitdtSyncFlag != null">
        QUITDT_SYNC_FLAG,
      </if>
      <if test="quitlevelSyncFlag != null">
        QUITLEVEL_SYNC_FLAG,
      </if>
      <if test="quitsalarySyncFlag != null">
        QUITSALARY_SYNC_FLAG,
      </if>
      <if test="backgroundSyncFlag != null">
        BACKGROUND_SYNC_FLAG,
      </if>
      <if test="sourceSyncFlag != null">
        SOURCE_SYNC_FLAG,
      </if>
      <if test="beforepositionageSyncFlag != null">
        BEFOREPOSITIONAGE_SYNC_FLAG,
      </if>
      <if test="recruitSyncFlag != null">
        RECRUIT_SYNC_FLAG,
      </if>
      <if test="recommendSyncFlag != null">
        RECOMMEND_SYNC_FLAG,
      </if>
      <if test="recommendusernoSyncFlag != null">
        RECOMMENDUSERNO_SYNC_FLAG,
      </if>
      <if test="recommendtypeSyncFlag != null">
        RECOMMENDTYPE_SYNC_FLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="consnameSyncFlag != null">
        #{consnameSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="citycodeSyncFlag != null">
        #{citycodeSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="centerOrgSyncFlag != null">
        #{centerOrgSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="worktypeSyncFlag != null">
        #{worktypeSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="workstateSyncFlag != null">
        #{workstateSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthlevelSyncFlag != null">
        #{curmonthlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="userlevelSyncFlag != null">
        #{userlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthsalarySyncFlag != null">
        #{curmonthsalarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="subpositionsSyncFlag != null">
        #{subpositionsSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="startdtSyncFlag != null">
        #{startdtSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="startlevelSyncFlag != null">
        #{startlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="salarySyncFlag != null">
        #{salarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="regulardtSyncFlag != null">
        #{regulardtSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularlevelSyncFlag != null">
        #{regularlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularsalarySyncFlag != null">
        #{regularsalarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitdtSyncFlag != null">
        #{quitdtSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitlevelSyncFlag != null">
        #{quitlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitsalarySyncFlag != null">
        #{quitsalarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="backgroundSyncFlag != null">
        #{backgroundSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="sourceSyncFlag != null">
        #{sourceSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="beforepositionageSyncFlag != null">
        #{beforepositionageSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recruitSyncFlag != null">
        #{recruitSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendSyncFlag != null">
        #{recommendSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendusernoSyncFlag != null">
        #{recommendusernoSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendtypeSyncFlag != null">
        #{recommendtypeSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpSyncFlag">
    <!--@mbg.generated-->
    update CM_CONSULTANT_EXP_SYNC_FLAG
    <set>
      <if test="userid != null">
        USERID = #{userid,jdbcType=VARCHAR},
      </if>
      <if test="consnameSyncFlag != null">
        CONSNAME_SYNC_FLAG = #{consnameSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="citycodeSyncFlag != null">
        CITYCODE_SYNC_FLAG = #{citycodeSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="centerOrgSyncFlag != null">
        CENTER_ORG_SYNC_FLAG = #{centerOrgSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="worktypeSyncFlag != null">
        WORKTYPE_SYNC_FLAG = #{worktypeSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="workstateSyncFlag != null">
        WORKSTATE_SYNC_FLAG = #{workstateSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthlevelSyncFlag != null">
        CURMONTHLEVEL_SYNC_FLAG = #{curmonthlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="userlevelSyncFlag != null">
        USERLEVEL_SYNC_FLAG = #{userlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthsalarySyncFlag != null">
        CURMONTHSALARY_SYNC_FLAG = #{curmonthsalarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="subpositionsSyncFlag != null">
        SUBPOSITIONS_SYNC_FLAG = #{subpositionsSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="startdtSyncFlag != null">
        STARTDT_SYNC_FLAG = #{startdtSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="startlevelSyncFlag != null">
        STARTLEVEL_SYNC_FLAG = #{startlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="salarySyncFlag != null">
        SALARY_SYNC_FLAG = #{salarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="regulardtSyncFlag != null">
        REGULARDT_SYNC_FLAG = #{regulardtSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularlevelSyncFlag != null">
        REGULARLEVEL_SYNC_FLAG = #{regularlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularsalarySyncFlag != null">
        REGULARSALARY_SYNC_FLAG = #{regularsalarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitdtSyncFlag != null">
        QUITDT_SYNC_FLAG = #{quitdtSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitlevelSyncFlag != null">
        QUITLEVEL_SYNC_FLAG = #{quitlevelSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitsalarySyncFlag != null">
        QUITSALARY_SYNC_FLAG = #{quitsalarySyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="backgroundSyncFlag != null">
        BACKGROUND_SYNC_FLAG = #{backgroundSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="sourceSyncFlag != null">
        SOURCE_SYNC_FLAG = #{sourceSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="beforepositionageSyncFlag != null">
        BEFOREPOSITIONAGE_SYNC_FLAG = #{beforepositionageSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recruitSyncFlag != null">
        RECRUIT_SYNC_FLAG = #{recruitSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendSyncFlag != null">
        RECOMMEND_SYNC_FLAG = #{recommendSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendusernoSyncFlag != null">
        RECOMMENDUSERNO_SYNC_FLAG = #{recommendusernoSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendtypeSyncFlag != null">
        RECOMMENDTYPE_SYNC_FLAG = #{recommendtypeSyncFlag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpSyncFlag">
    <!--@mbg.generated-->
    update CM_CONSULTANT_EXP_SYNC_FLAG
    set USERID = #{userid,jdbcType=VARCHAR},
      CONSNAME_SYNC_FLAG = #{consnameSyncFlag,jdbcType=VARCHAR},
      CITYCODE_SYNC_FLAG = #{citycodeSyncFlag,jdbcType=VARCHAR},
      CENTER_ORG_SYNC_FLAG = #{centerOrgSyncFlag,jdbcType=VARCHAR},
      WORKTYPE_SYNC_FLAG = #{worktypeSyncFlag,jdbcType=VARCHAR},
      WORKSTATE_SYNC_FLAG = #{workstateSyncFlag,jdbcType=VARCHAR},
      CURMONTHLEVEL_SYNC_FLAG = #{curmonthlevelSyncFlag,jdbcType=VARCHAR},
      USERLEVEL_SYNC_FLAG = #{userlevelSyncFlag,jdbcType=VARCHAR},
      CURMONTHSALARY_SYNC_FLAG = #{curmonthsalarySyncFlag,jdbcType=VARCHAR},
      SUBPOSITIONS_SYNC_FLAG = #{subpositionsSyncFlag,jdbcType=VARCHAR},
      STARTDT_SYNC_FLAG = #{startdtSyncFlag,jdbcType=VARCHAR},
      STARTLEVEL_SYNC_FLAG = #{startlevelSyncFlag,jdbcType=VARCHAR},
      SALARY_SYNC_FLAG = #{salarySyncFlag,jdbcType=VARCHAR},
      REGULARDT_SYNC_FLAG = #{regulardtSyncFlag,jdbcType=VARCHAR},
      REGULARLEVEL_SYNC_FLAG = #{regularlevelSyncFlag,jdbcType=VARCHAR},
      REGULARSALARY_SYNC_FLAG = #{regularsalarySyncFlag,jdbcType=VARCHAR},
      QUITDT_SYNC_FLAG = #{quitdtSyncFlag,jdbcType=VARCHAR},
      QUITLEVEL_SYNC_FLAG = #{quitlevelSyncFlag,jdbcType=VARCHAR},
      QUITSALARY_SYNC_FLAG = #{quitsalarySyncFlag,jdbcType=VARCHAR},
      BACKGROUND_SYNC_FLAG = #{backgroundSyncFlag,jdbcType=VARCHAR},
      SOURCE_SYNC_FLAG = #{sourceSyncFlag,jdbcType=VARCHAR},
      BEFOREPOSITIONAGE_SYNC_FLAG = #{beforepositionageSyncFlag,jdbcType=VARCHAR},
      RECRUIT_SYNC_FLAG = #{recruitSyncFlag,jdbcType=VARCHAR},
      RECOMMEND_SYNC_FLAG = #{recommendSyncFlag,jdbcType=VARCHAR},
      RECOMMENDUSERNO_SYNC_FLAG = #{recommendusernoSyncFlag,jdbcType=VARCHAR},
      RECOMMENDTYPE_SYNC_FLAG = #{recommendtypeSyncFlag,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=TIMESTAMP},
      MODDT = #{moddt,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectByUserid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_SYNC_FLAG
    where userid = #{userid,jdbcType=VARCHAR}
  </select>
</mapper>