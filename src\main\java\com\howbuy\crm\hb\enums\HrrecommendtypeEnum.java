/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

/**
 * @description: (推荐类型枚举)
 * <AUTHOR>
 * @date 2024/11/13 11:03
 * @since JDK 1.8
 */
public enum HrrecommendtypeEnum {
    COMMEND_TYPE_1("1", "内推"),
    COMMEND_TYPE_2("2", "管理层自带"),
    COMMEND_TYPE_3("3", "HR自主招聘"),
    COMMEND_TYPE_4("4", "猎头"),
    COMMEND_TYPE_9("9", "其他");

    /**
     * 推荐key
     */
    private String code;
    /**
     * 推荐描述
     */
    private String desc;
    HrrecommendtypeEnum(String code, String desc){
        this.code=code;
        this.desc=desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static HrrecommendtypeEnum getEnum(String desc) {
        for(HrrecommendtypeEnum typeEnum : HrrecommendtypeEnum.values()){
            if(typeEnum.getDesc().equals(desc)){
                return typeEnum;
            }
        }
        return null;
    }
}