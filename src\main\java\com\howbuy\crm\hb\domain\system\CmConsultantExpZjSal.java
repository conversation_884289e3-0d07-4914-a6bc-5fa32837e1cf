package com.howbuy.crm.hb.domain.system;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

import com.howbuy.crm.hb.domain.insur.PageVo;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmConsultantExpZjSal extends PageVo implements Serializable  {
	private static final long serialVersionUID = 1L;
	private String id;
	/**
	 * 职级
	 */
	private String ranklevel;
	/**
	 * 职级名称
	 */
	private String ranklevelval;
	/**
	 * 正式期-底薪（税前/月，元）
	 */
	private BigDecimal regularsalary  ;
	  
	/**
	 * 试用期-底薪（税前/月，元）
	 */
	private BigDecimal probationsalary     ;
	/**
	 * 正式期-净新增折标销量（月，万）
	 */
	private BigDecimal regularsale  ;
	  
	/**
	 * 试用期-净新增折标销量（月，万）
	 */
	private BigDecimal probationsale     ;
	/**
	 * 正式期-存量
	 */
	private BigDecimal regularstock  ;
	  
	/**
	 * 试用期-存量
	 */
	private BigDecimal probationstock     ;
	
	/**
	 * 起始日期
	 */
	private String startdt;
	
	/**
	 * 结束日期
	 */
	private String enddt;
	
	/**
	 * 是否有效1：有效；0：删除 
	 */
	private String isdel;
	
	private String creator;
    private Date creatdt;
    private String modor;
    private Date moddt;
    
    private String  sort;

	private String  order;
	/**
	 * 层级
	 */
	private List<String> userlevel;

	/**
	 *档级
	 */
	private String  rank;
	/**
	 *社保基数
	 */
	private String  socialInsuranceBase;

}
