<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpZjSalMapper">
	<insert id="insertCmConsultantExpZjSal" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal">
        INSERT INTO CM_CONSULTANT_EXP_ZJ_SAL (
       <trim suffix="" suffixOverrides=",">   
                <if test="id != null"> id, </if>
                <if test="ranklevel != null"> ranklevel, </if>
				<if test="regularsalary != null"> regularsalary, </if>
				<if test="probationsalary != null"> probationsalary, </if>
				<if test="regularsale != null"> regularsale, </if>
				<if test="probationsale != null"> probationsale, </if>
				<if test="regularstock !=null"> regularstock, </if>
				<if test="probationstock !=null"> probationstock, </if>
				<if test="startdt !=null"> startdt, </if>
				<if test="enddt !=null"> enddt, </if>
				<if test="isdel !=null"> isdel, </if>
				<if test="creator !=null"> creator, </if>
				<if test="creatdt !=null"> creatdt, </if>
				<if test="modor !=null"> modor, </if>
				<if test="moddt !=null"> moddt, </if>
				<if test="rank !=null"> rank, </if>
				<if test="socialInsuranceBase !=null"> socialinsurancebase, </if>
        </trim>
           ) values (
        <trim suffix="" suffixOverrides=",">
                <if test="id != null"> #{id}, </if>
				<if test="ranklevel != null"> #{ranklevel}, </if>
				<if test="regularsalary != null"> #{regularsalary}, </if>
				<if test="probationsalary != null"> #{probationsalary}, </if>
				<if test="regularsale != null"> #{regularsale}, </if>
				<if test="probationsale != null"> #{probationsale}, </if>
				<if test="regularstock != null"> #{regularstock}, </if>
				<if test="probationstock !=null"> #{probationstock}, </if>
				<if test="startdt !=null"> #{startdt}, </if>
				<if test="enddt !=null"> #{enddt}, </if>
				<if test="isdel !=null"> #{isdel}, </if>
				<if test="creator !=null"> #{creator}, </if>
				<if test="creatdt !=null"> #{creatdt}, </if>
				<if test="modor !=null"> #{modor}, </if>
				<if test="moddt !=null"> #{moddt}, </if>
				<if test="rank !=null"> #{rank}, </if>
				<if test="socialInsuranceBase !=null"> #{socialInsuranceBase}, </if>
        </trim>
         )
    </insert>
    
    <update id="updateCmConsultantExpZjSal" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal">
        UPDATE CM_CONSULTANT_EXP_ZJ_SAL	  
    	set
            regularsalary = #{regularsalary,jdbcType=NUMERIC}, 
			probationsalary = #{probationsalary,jdbcType=NUMERIC},
			regularsale = #{regularsale,jdbcType=NUMERIC},
			probationsale = #{probationsale,jdbcType=NUMERIC},
			regularstock = #{regularstock,jdbcType=NUMERIC},
			probationstock = #{probationstock ,jdbcType=NUMERIC},
			startdt = #{startdt,jdbcType=VARCHAR},
			enddt = #{enddt,jdbcType=VARCHAR}, 
			modor = #{modor,jdbcType=VARCHAR},
			rank = #{rank,jdbcType=VARCHAR},
			socialinsurancebase = #{socialInsuranceBase,jdbcType=VARCHAR},
			moddt = sysdate
          where id = #{id}   
    </update>
    
    <update id="delCmConsultantExpZjSal" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal">
        UPDATE CM_CONSULTANT_EXP_ZJ_SAL	  
    	set isdel = '0',
			modor = #{modor,jdbcType=VARCHAR},
			moddt = sysdate  
          where id = #{id}   
    </update>
    
    <select id="getCmConsultantExpZjSal" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal" useCache="false">
	  	SELECT *
		  FROM CM_CONSULTANT_EXP_ZJ_SAL
		  where isdel = '1' 
		  <if test="id != null"> and id = #{id} </if> 
	  </select>
	  
	  <select id="listCmConsultantExpZjSalByPage" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal" useCache="false">
	  	SELECT t1.*,t2.constdesc as ranklevelval
		  FROM CM_CONSULTANT_EXP_ZJ_SAL t1
		  left join hb_constant t2
		  on t1.ranklevel = t2.constcode
		  and t2.typecode = 'hrpositionslevel'
		  and t2.isvalid = '0'
		  where isdel = '1'
		  <if test="param.userlevel !=null">
			  AND t1.RANKLEVEL IN
			  <foreach collection="param.userlevel" item="level" index="index" open="(" close=")" separator=",">
				  #{level}
			  </foreach>
		  </if>

		  <if test="param.startdt != null"> and t1.STARTDT &gt;= #{param.startdt,jdbcType=VARCHAR} </if>
		  <if test="param.enddt != null"> and t1.ENDDT &lt;= #{param.enddt,jdbcType=VARCHAR} </if>

		  <if test="param.id != null"> and t1.id = #{param.id} </if>
		    <if test="param.ranklevel != null"> and t1.ranklevel = #{param.ranklevel} </if> 
		    <if test="param.sort != null and param.order != null" >
				<choose>
					<when test="param.sort == 'ranklevel'.toString()">
						ORDER BY t2.constlevel ${param.order} nulls last
					</when>
					<otherwise>
						ORDER BY ${param.sort} ${param.order} nulls last
					</otherwise>
				</choose>
			</if>
	  </select>
	  
	  <select id="selectOverlapCount" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal" resultType="int" useCache="false">
			select count(*)
			from CM_CONSULTANT_EXP_ZJ_SAL
			where isdel = '1'
			<if test="enddt == null or enddt == ''">
				and nvl(enddt,'20991231') >= #{startdt}
			</if>
			<if test="enddt != null and enddt != ''">
				and startdt &lt;= #{enddt}
				and nvl(enddt,'20991231') >= #{startdt}
			</if>
			and ranklevel = #{ranklevel}
			<if test="id != null">
				and id != #{id}
			</if>
		</select>
	<select id="getZjSalByNowDateAndRanklevel"
			resultType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal">
		SELECT *
		FROM CM_CONSULTANT_EXP_ZJ_SAL
		where isdel = '1'
		and ranklevel = #{rankLevel}
		and startdt &lt;= #{nowDate}
		and nvl(enddt,'20991231') >= #{nowDate}
	</select>
	
	<select id="getMaxUserLevelByZj" resultType="String">
        SELECT A.CONSTCODE
		  FROM HB_CONSTANT A
		 WHERE A.TYPECODE = 'hrpositionslevel'
		   AND A.CONSTEXT1 IN (SELECT MAX(TO_NUMBER(T.CONSTEXT1))
		                         FROM HB_CONSTANT T
		                        WHERE T.TYPECODE = 'hrpositionslevel'
		                          AND T.CONSTCODE IN 
		                        <foreach collection="ranklevellist" item="ranklevel"
								    index="index" open="(" close=")" separator=",">
								    #{ranklevel}
								</foreach>  
		                        )
    </select>

	<select id="selectAllData" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExpZjSal">
		SELECT * FROM CM_CONSULTANT_EXP_ZJ_SAL where isdel = '1'
	</select>
	
</mapper>