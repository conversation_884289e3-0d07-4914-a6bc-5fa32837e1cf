package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户来源类型表
 * @TableName CM_CONSCUST_SOURCE_TYPE
 */
public class CmConscustSourceType implements Serializable {
    /**
     * ID
     */
    private Long ID;

    /**
     * 投顾客户编号
     */
    private String CONS_CUST_NO;

    /**
     * 客户来源类型 1: 公司资源, 2: 投顾资源, 3: 公司-leads, 4: 公司-20w, 5: 自购, 6: 公司资源(划转), 7: 公司资源(重复), 8: 投顾资源(划转-潜在), 9: 投顾资源(划转-成交), 10: 投顾资源(潜在-重复), 11: 投顾资源(成交-重复), 12: 大V
     */
    private String SOURCE_TYPE;

    /**
     * 记录有效状态（1-正常  0-删除）
     */
    private String REC_STAT;

    /**
     * 创建人
     */
    private String CREATOR;

    /**
     * 创建时间
     */
    private Date CREATE_TIMESTAMP;

    /**
     * 修改人
     */
    private String MODIFIER;

    /**
     * 修改时间
     */
    private Date MODIFY_TIMESTAMP;


    public Long getID() {
        return ID;
    }

    public void setID(Long ID) {
        this.ID = ID;
    }

    public String getCONS_CUST_NO() {
        return CONS_CUST_NO;
    }

    public void setCONS_CUST_NO(String CONS_CUST_NO) {
        this.CONS_CUST_NO = CONS_CUST_NO;
    }

    public String getSOURCE_TYPE() {
        return SOURCE_TYPE;
    }

    public void setSOURCE_TYPE(String SOURCE_TYPE) {
        this.SOURCE_TYPE = SOURCE_TYPE;
    }

    public String getREC_STAT() {
        return REC_STAT;
    }

    public void setREC_STAT(String REC_STAT) {
        this.REC_STAT = REC_STAT;
    }

    public String getCREATOR() {
        return CREATOR;
    }

    public void setCREATOR(String CREATOR) {
        this.CREATOR = CREATOR;
    }

    public Date getCREATE_TIMESTAMP() {
        return CREATE_TIMESTAMP;
    }

    public void setCREATE_TIMESTAMP(Date CREATE_TIMESTAMP) {
        this.CREATE_TIMESTAMP = CREATE_TIMESTAMP;
    }

    public String getMODIFIER() {
        return MODIFIER;
    }

    public void setMODIFIER(String MODIFIER) {
        this.MODIFIER = MODIFIER;
    }

    public Date getMODIFY_TIMESTAMP() {
        return MODIFY_TIMESTAMP;
    }

    public void setMODIFY_TIMESTAMP(Date MODIFY_TIMESTAMP) {
        this.MODIFY_TIMESTAMP = MODIFY_TIMESTAMP;
    }
}