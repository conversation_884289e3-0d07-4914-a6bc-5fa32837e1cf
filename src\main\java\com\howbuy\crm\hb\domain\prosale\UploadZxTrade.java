package com.howbuy.crm.hb.domain.prosale;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (导入直销交易记录实体类)
 * @date 2023/3/2 13:20
 * @since JDK 1.8
 */
@Data
public class UploadZxTrade {

    @ExcelProperty(value = "交易类别",index = 0)
    private String tradeType;

    @ExcelProperty(value = "投顾客户号",index = 1)
    private String conscustNo;

    @ExcelProperty(value = "产品代码",index = 2)
    private String fundCode;

    @ExcelProperty(value = "交易确认日期", index = 3)
    private String tradeDt;

    @ExcelProperty(value = "申请金额",index = 4)
    private BigDecimal appamt;

    @ExcelProperty(value = "确认份额",index = 5)
    private BigDecimal ackvol;

    @ExcelProperty(value = "确认金额",index = 6)
    private BigDecimal ackamt;

    @ExcelProperty(value = "交易日净值",index = 7)
    private BigDecimal nav;

    @ExcelProperty(value = "确认手续费",index = 8)
    private BigDecimal fee;

    @ExcelProperty(value = "销售类型(直销/直转代)", index = 9)
    private String saleType;

    @ExcelProperty(value = "是否影响持仓(是/否)", index = 10)
    private String isInfluence;

}
