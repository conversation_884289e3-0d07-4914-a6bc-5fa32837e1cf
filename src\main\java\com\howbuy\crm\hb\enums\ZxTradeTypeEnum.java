/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

/**
 * <AUTHOR>
 * @description: (直销交易记录的交易类型枚举值)
 * @date 2023/3/9 14:25
 * @since JDK 1.8
 */
public enum ZxTradeTypeEnum {
    /**
     * 申购确认
     */
    ACK_CON("申购确认", "122"),

    /**
     * 赎回确认
     */
    REDEEM_CON("赎回确认","124"),
    /**
     * 认购确认
     */
    BUY_CON("认购确认","120"),
    /**
     * 基金终止
     */
    FUND_STOP("基金终止", "151"),
    /**
     * 到期收益
     */
    MATURE_DUE("到期收益", "186"),
    /**
     * 到期赎回
     */
    MATURE_REDEEM("到期赎回", "187"),
    /**
     * 红利发放
     */
    BONUS_PROVIDE("红利发放", "143"),
    /**
     * 强减
     */
    FORCE_MINUS("强减", "145"),
    /**
     * 非交易过户转入
     */
    NO_TRADE_IN("非交易过户转入", "134"),
    /**
     * 私募股权回款
     */
    PRIVATE_CLAWBACK("私募股权回款", "999"),
    /**
     * 强增
     */
    FORCE_INC("强增", "144"),
    /**
     * 强赎
     */
    FORCE_CALL("强赎", "142"),
    /**
     * 非交易过户转出
     */
    NO_TRADE_OUT("非交易过户转出", "135"),

    /**
     * 基金清算
     */
    FUND_CALCULATE("基金清算", "150"),


    /**
     * 是否影响持仓
     */
    INFLUENCE("是", "1"),

    NOINFLUENCE("否", "0");

    private String tradeName;
    private String tradeTypeCode;

    ZxTradeTypeEnum(String tradeName, String tradeTypeCode) {
        this.tradeName = tradeName;
        this.tradeTypeCode = tradeTypeCode;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getTradeTypecode() {
        return tradeTypeCode;
    }

    public void setTradeTypecode(String tradeTypeCode) {
        this.tradeTypeCode = tradeTypeCode;
    }


    /**
     * @description:(根据枚举类型返回code值)
     * @param tradeName 类型名称
     * @return java.lang.String
     * @author: your name
     * @date: 2023/3/9 14:54
     * @since JDK 1.8
     */
    public static String getEnum(String tradeName) {
        for(ZxTradeTypeEnum statusEnum : ZxTradeTypeEnum.values()){
            if(statusEnum.getTradeName().equals(tradeName)){
                return statusEnum.getTradeTypecode();
            }
        }
        return null;
    }
}