package com.howbuy.crm.hb.domain.joinclub;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * @Description: 实体类CmConscustsurveyanswer.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class CmConscustsurveyanswer implements Serializable {

private static final long serialVersionUID = 1L;

	private String answerid;
	
	private String tradedt;
	
	private String surveyserialno;
	
	private String conscustno;
	
	private String surveyid;
	
	private String setid;
	
	private String questionid;
	
	private Double qweight;
	
	private String acode;
	
	private String acontent;
	
	private Double apoint;
	
	private Double wapoint;
	
	private String memo;
	
	private Date stimestamp;
	
	private String errorFlag;
	
	private List<String> conscustnoList;

}
