package com.howbuy.crm.hb.domain.callout;

/**
 * 
 * <AUTHOR>
 *
 */
public class HbOneCustAbnormal {
	private String hboneNo;
	private String conscustNo;
	private String custName;
	private String dealStatus;
	private String dealStatusName;
	private String userType;
	private String idType;
	private String idNo;
	private String idNo2;
	private String mobile;
	private String mobile2;
	private String credt;
	private String idNoMask;
	private String idNoCipher;
	private String mobileMask;
	private String mobileCipher;
	
	public String getHboneNo() {
		return hboneNo;
	}
	public void setHboneNo(String hboneNo) {
		this.hboneNo = hboneNo;
	}
	public String getConscustNo() {
		return conscustNo;
	}
	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getDealStatus() {
		return dealStatus;
	}
	public void setDealStatus(String dealStatus) {
		this.dealStatus = dealStatus;
	}
	public String getDealStatusName() {
		return dealStatusName;
	}
	public void setDealStatusName(String dealStatusName) {
		this.dealStatusName = dealStatusName;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getIdType() {
		return idType;
	}
	public void setIdType(String idType) {
		this.idType = idType;
	}
	public String getIdNo() {
		return idNo;
	}
	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	
	public String getIdNo2() {
		return idNo2;
	}
	public void setIdNo2(String idNo2) {
		this.idNo2 = idNo2;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}	
	public String getMobile2() {
		return mobile2;
	}
	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}
	public String getCredt() {
		return credt;
	}
	public void setCredt(String credt) {
		this.credt = credt;
	}
	public String getIdNoMask() {
		return idNoMask;
	}
	public void setIdNoMask(String idNoMask) {
		this.idNoMask = idNoMask;
	}
	public String getMobileMask() {
		return mobileMask;
	}
	public void setMobileMask(String mobileMask) {
		this.mobileMask = mobileMask;
	}
	public String getIdNoCipher() {
		return idNoCipher;
	}
	public void setIdNoCipher(String idNoCipher) {
		this.idNoCipher = idNoCipher;
	}
	public String getMobileCipher() {
		return mobileCipher;
	}
	public void setMobileCipher(String mobileCipher) {
		this.mobileCipher = mobileCipher;
	}
	
}
