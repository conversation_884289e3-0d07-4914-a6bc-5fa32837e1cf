/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.joinclub;

import lombok.Data;

import java.util.Date;

/**
 * @description: (风险评测历史记录)
 * com.howbuy.acccenter.facade.query.kyc.QueryAnswerHistoryListFacade.execute 返回数据转换
 * 
 * <AUTHOR>
 * @date 2023/5/12 13:49
 * @since JDK 1.8
 */
@Data
public class CustAnswerHisBeanLocal {

    /**
     * 历史问卷id
     */
    private String answerHisId;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 问卷id
     */
    private String examId;

    /**
     * 分值
     */
    private Double score;

    /**
     * 过期日期（yyyyMMdd）
     */
    private String expireDate;

    /**
     * 风险等级
     */
    private String levelValue;
    private String levelName;

    /**
     * 网点号
     */
    private String outletCode;
    private String outletCodeName;

    private String ip;

    private Date createDate;
    /**
     * 创建时间（接口是Date格式，这里转换为yyyyMMdd HH:mm:ss格式展示）
     */
    private String createDateStr;
}
