package com.howbuy.crm.hb.persistence.insur;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.insur.CmBxProductCoeff;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxProductCoeffMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxProductCoeff getCmBxProductCoeff(Map<String, String> param);
    
    /**
     * 得到单个数据对象:支持空值
     * @param param
     * @return
     */
    CmBxProductCoeff getCmBxProductCoeffSupNull(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmBxProductCoeff
      */
	void insertCmBxProductCoeff(CmBxProductCoeff cmBxProductCoeff);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxProductCoeff
	 */
	void updateCmBxProductCoeff(CmBxProductCoeff cmBxProductCoeff);
	
	/**
	 * 单条修改数据对象：包含修改为空
	 * @param cmBxProductCoeff
	 */
	void updateCmBxProductCoeffNull(CmBxProductCoeff cmBxProductCoeff);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmBxProductCoeff(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmBxProductCoeff(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxProductCoeff> listCmBxProductCoeff(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmBxProductCoeffCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmBxProductCoeff> listCmBxProductCoeffByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 根据条件查询符合复制条件的所有付款年限
	 * @param param
	 * @return
	 */
	List<String> listNotPayyearsProductCoeff(Map<String, String> param);
	
	/**
	 * 导出
	 * @param param
	 * @return
	 */
	List<CmBxProductCoeff> exportCmBxProductCoeff(Map<String, String> param);
}
