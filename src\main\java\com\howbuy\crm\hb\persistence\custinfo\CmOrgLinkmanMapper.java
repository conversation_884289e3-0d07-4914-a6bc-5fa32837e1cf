package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.CmOrgLinkman;
import com.howbuy.crm.hb.domain.custinfo.CmOrgLinkmanLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/5/28 18:10
 */
public interface CmOrgLinkmanMapper {

    void batchInsert(@Param("list") List<CmOrgLinkman> list);

    List<CmOrgLinkman> listOrgLinkmanByCustNo(Map<String, String> params);

    void insert(CmOrgLinkman cmOrgLinkman);

    CmOrgLinkman getCmOrgLinkman(String id);

    void update(CmOrgLinkman cmOrgLinkman);

    void insertCmOrgLinkmanLog(CmOrgLinkmanLog cmOrgLinkmanLog);

    void delete(long id);

    List<Map<String, String>> ajaxlinkmanlog(Map<String, String> param);
}
