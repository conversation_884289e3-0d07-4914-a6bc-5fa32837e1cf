/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.conference;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (分配单详细数据前端请求对象实体类)
 * @date 2023/11/14 09:07
 * @since JDK 1.8
 */
@Data
public class CmconferNewCustTaskDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String id;

    /**
     * 申请单ID
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String taskId;

    /**
     * 扫码表主键ID
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String scanId;


    /**
     * 会议ID CM_CONFERENCE.ID
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String conferenceId;

    /**
     * 会议名称
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String conferenceName;

    /**
     * 参会手机号
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String mobile;

    /**
     * 手机号摘要
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String mobileDigest;

    /**
     * 签到时间
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String signDt;

    /**
     * 客户姓名(输入)
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String custNamesr;

    /**
     * 投顾姓名(输入)
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String consNamesr;

    /**
     * 处理状态
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String dealStatus;

    /**
     * 处理意见
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String dealRemark;

    @JsonIgnoreProperties(ignoreUnknown = true)
    private String isMatch;
}