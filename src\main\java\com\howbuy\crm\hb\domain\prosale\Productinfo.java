package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 产品信息:
* <AUTHOR>
* @date 2020/4/9
*/
public class Productinfo implements Serializable {

	private static final long serialVersionUID = 1L;

	private String pcode;

	private String pname;

	private String hbtype;

	private String publishMan;

	private String managerMan;

	private String orgWay;

	private String saleWay;

	private String capitalDemand;

	private Double salelimitation;

	private String manLimit;

	private String checkingaddition;

	private Integer deadline;

	private String expectAnnualIncome;

	private String buyFee;

	private String payEndDt;

	private String expectFundDt;

	private String appType;

	private String investmentScope;

	private String publishScope;

	private String rateAllocate;

	private String fundmanager;

	private String opendate;

	private String foundDt;

	private String dueDt;

	private String firstCapital;

	private String mainpartner;

	private String isupside;

	private String isimportant;

	private String productState;

	private String saleState;

	private String blockDate;

	private String introduce;

	private String isend;

	private String currency;

	private String investmentArea;

	private String acctType;

	private String publishWay;

	private String investmentType;

	private String plotType;

	private String zuhe;

	private String jgh;

	private String recstat;

	private String creator;

	private String modifier;

	private String credt;

	private String moddt;

	private String prebookstate;

	private String isCalBal;

	private String cpjgh;

	private String payStartDt;

	private String jjpy;

	private String pname2;

	private String pnameabbr;

	private String foundDt2;

	private String openDt2;

	private String latestBackdateCom;

	private String latestBackdateSa;

	private String element;
	
	private String hmfx;
	/**
	 * 中间价
	 */
	private String zjj;
	
	/**
	 * 汇率日期
	 */
	private String jzrq;
	
	/**
	 * 评级
	 */
	private String pj;
	
	/**
	 * 评级变动
	 */
	private String pjbd;

	/**
	 * 是否海外
	 */
	private String sfhwjj;
	
	/**
	 * 产品类型：1为代销，2为直销，3为直转代
	 */
	private String sfmsjg;
		
	/**
	 * 支持储蓄罐支付
	 */
	private String isSupportPiggyPay;
	
	
	
	
	
	
	/**
	 * 起购金额
	 */
	private String scrg;
	
	/**
	 * 追加最低金额
	 */
	private String zjrg;
	
	/**
	 * 认购费率（%）
	 */
	private String rgfl;
	
	/**
	 * 管理费率（%）
	 */
	private String glfl;
	
	/**
	 * 托管费率（%）
	 */
	private String tgflStr;

	/**
	 * 赎回费率（%）
	 */
	private String shfl;
	
	/**
	 * 赎回限制
	 */
	private String shxz;
	
	/**
	 * 赎回申请日说明
	 */
	private String shrqsm;
	
	/**
	 * 赎回资金到账说明
	 */
	private String shzjdzr;
	
	/**
	 * 业绩报酬计提率（%）
	 */
	private String fdfl;

	/**
	 * 业绩报酬计提日
	 */
	private String yjjtr;
	
	/**
	 * 业绩报酬计提形式
	 */
	private String jtfs;
	
	/**
	 * 业绩报酬计提条件
	 */
	private String yjjttj;
	
	/**
	 * 业绩报酬计提频率
	 */
	private String jtpl;
	
	/**
	 * 运营外包费率（%）
	 */
	private String yywbfwflStr;
	
	/**
	 * 认购费最低折扣率
	 */
	private BigDecimal feeDiscount;
	
	/**
	 * 支持下单方式
	 */
	private String supportPayType;
	
	/**
	 * 基金状态
	 */
	private String jjzt;
	
	/**
	 * 申购预约开始-结束日
	 */
	private String subsPreDt;
	
	/**
	 * 申购开放日
	 */
	private String subsOpenDt;
	
	/**
	 * 赎回预约开始-结束日
	 */
	private String redeemPreDt;
	
	/**
	 * 赎回开放日
	 */
	private String redeemOpenDt;

	/**
	 * 准封闭期
	 */
	private String zfbq;

	/**
	 * 赎回费率说明
	 */
	private String shflsm;
	
	/**
	 * 管理人码
	 */
	private String glrm;
	
	public String getSfhwjj() {
		return sfhwjj;
	}

	public void setSfhwjj(String sfhwjj) {
		this.sfhwjj = sfhwjj;
	}

	public String getPj() {
		return pj;
	}

	public void setPj(String pj) {
		this.pj = pj;
	}

	public String getPjbd() {
		return pjbd;
	}

	public void setPjbd(String pjbd) {
		this.pjbd = pjbd;
	}

	public String getZjj() {
		return zjj;
	}

	public void setZjj(String zjj) {
		this.zjj = zjj;
	}

	public String getJzrq() {
		return jzrq;
	}

	public void setJzrq(String jzrq) {
		this.jzrq = jzrq;
	}

	public String getHmfx() {
		return hmfx;
	}

	public void setHmfx(String hmfx) {
		this.hmfx = hmfx;
	}

	public String getPcode() {
		return this.pcode;
	}

	public void setPcode(String pcode) {
		this.pcode = pcode;
	}

	public String getPname() {
		return this.pname;
	}

	public void setPname(String pname) {
		this.pname = pname;
	}

	public String getHbtype() {
		return this.hbtype;
	}

	public void setHbtype(String hbtype) {
		this.hbtype = hbtype;
	}

	public String getPublishMan() {
		return this.publishMan;
	}

	public void setPublishMan(String publishMan) {
		this.publishMan = publishMan;
	}

	public String getManagerMan() {
		return this.managerMan;
	}

	public void setManagerMan(String managerMan) {
		this.managerMan = managerMan;
	}

	public String getOrgWay() {
		return this.orgWay;
	}

	public void setOrgWay(String orgWay) {
		this.orgWay = orgWay;
	}

	public String getSaleWay() {
		return this.saleWay;
	}

	public void setSaleWay(String saleWay) {
		this.saleWay = saleWay;
	}

	public String getCapitalDemand() {
		return this.capitalDemand;
	}

	public void setCapitalDemand(String capitalDemand) {
		this.capitalDemand = capitalDemand;
	}

	public Double getSalelimitation() {
		return this.salelimitation;
	}

	public void setSalelimitation(Double salelimitation) {
		this.salelimitation = salelimitation;
	}

	public String getManLimit() {
		return this.manLimit;
	}

	public void setManLimit(String manLimit) {
		this.manLimit = manLimit;
	}

	public String getCheckingaddition() {
		return this.checkingaddition;
	}

	public void setCheckingaddition(String checkingaddition) {
		this.checkingaddition = checkingaddition;
	}

	public Integer getDeadline() {
		return this.deadline;
	}

	public void setDeadline(Integer deadline) {
		this.deadline = deadline;
	}

	public String getExpectAnnualIncome() {
		return this.expectAnnualIncome;
	}

	public void setExpectAnnualIncome(String expectAnnualIncome) {
		this.expectAnnualIncome = expectAnnualIncome;
	}

	public String getBuyFee() {
		return this.buyFee;
	}

	public void setBuyFee(String buyFee) {
		this.buyFee = buyFee;
	}

	public String getPayEndDt() {
		return this.payEndDt;
	}

	public void setPayEndDt(String payEndDt) {
		this.payEndDt = payEndDt;
	}

	public String getExpectFundDt() {
		return this.expectFundDt;
	}

	public void setExpectFundDt(String expectFundDt) {
		this.expectFundDt = expectFundDt;
	}

	public String getAppType() {
		return this.appType;
	}

	public void setAppType(String appType) {
		this.appType = appType;
	}

	public String getInvestmentScope() {
		return this.investmentScope;
	}

	public void setInvestmentScope(String investmentScope) {
		this.investmentScope = investmentScope;
	}

	public String getPublishScope() {
		return this.publishScope;
	}

	public void setPublishScope(String publishScope) {
		this.publishScope = publishScope;
	}

	public String getRateAllocate() {
		return this.rateAllocate;
	}

	public void setRateAllocate(String rateAllocate) {
		this.rateAllocate = rateAllocate;
	}

	public String getFundmanager() {
		return this.fundmanager;
	}

	public void setFundmanager(String fundmanager) {
		this.fundmanager = fundmanager;
	}

	public String getOpendate() {
		return this.opendate;
	}

	public void setOpendate(String opendate) {
		this.opendate = opendate;
	}

	public String getFoundDt() {
		return this.foundDt;
	}

	public void setFoundDt(String foundDt) {
		this.foundDt = foundDt;
	}

	public String getDueDt() {
		return this.dueDt;
	}

	public void setDueDt(String dueDt) {
		this.dueDt = dueDt;
	}

	public String getFirstCapital() {
		return this.firstCapital;
	}

	public void setFirstCapital(String firstCapital) {
		this.firstCapital = firstCapital;
	}

	public String getMainpartner() {
		return this.mainpartner;
	}

	public void setMainpartner(String mainpartner) {
		this.mainpartner = mainpartner;
	}

	public String getIsupside() {
		return this.isupside;
	}

	public void setIsupside(String isupside) {
		this.isupside = isupside;
	}

	public String getIsimportant() {
		return this.isimportant;
	}

	public void setIsimportant(String isimportant) {
		this.isimportant = isimportant;
	}

	public String getProductState() {
		return this.productState;
	}

	public void setProductState(String productState) {
		this.productState = productState;
	}

	public String getSaleState() {
		return this.saleState;
	}

	public void setSaleState(String saleState) {
		this.saleState = saleState;
	}

	public String getBlockDate() {
		return this.blockDate;
	}

	public void setBlockDate(String blockDate) {
		this.blockDate = blockDate;
	}

	public String getIntroduce() {
		return this.introduce;
	}

	public void setIntroduce(String introduce) {
		this.introduce = introduce;
	}

	public String getIsend() {
		return this.isend;
	}

	public void setIsend(String isend) {
		this.isend = isend;
	}

	public String getCurrency() {
		return this.currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getInvestmentArea() {
		return this.investmentArea;
	}

	public void setInvestmentArea(String investmentArea) {
		this.investmentArea = investmentArea;
	}

	public String getAcctType() {
		return this.acctType;
	}

	public void setAcctType(String acctType) {
		this.acctType = acctType;
	}

	public String getPublishWay() {
		return this.publishWay;
	}

	public void setPublishWay(String publishWay) {
		this.publishWay = publishWay;
	}

	public String getInvestmentType() {
		return this.investmentType;
	}

	public void setInvestmentType(String investmentType) {
		this.investmentType = investmentType;
	}

	public String getPlotType() {
		return this.plotType;
	}

	public void setPlotType(String plotType) {
		this.plotType = plotType;
	}

	public String getZuhe() {
		return this.zuhe;
	}

	public void setZuhe(String zuhe) {
		this.zuhe = zuhe;
	}

	public String getJgh() {
		return this.jgh;
	}

	public void setJgh(String jgh) {
		this.jgh = jgh;
	}

	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public String getPrebookstate() {
		return this.prebookstate;
	}

	public void setPrebookstate(String prebookstate) {
		this.prebookstate = prebookstate;
	}

	public String getIsCalBal() {
		return this.isCalBal;
	}

	public void setIsCalBal(String isCalBal) {
		this.isCalBal = isCalBal;
	}

	public String getCpjgh() {
		return this.cpjgh;
	}

	public void setCpjgh(String cpjgh) {
		this.cpjgh = cpjgh;
	}

	public String getPayStartDt() {
		return this.payStartDt;
	}

	public void setPayStartDt(String payStartDt) {
		this.payStartDt = payStartDt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getJjpy() {
		return jjpy;
	}

	public void setJjpy(String jjpy) {
		this.jjpy = jjpy;
	}

	public String getPname2() {
		return pname2;
	}

	public void setPname2(String pname2) {
		this.pname2 = pname2;
	}

	public String getPnameabbr() {
		return pnameabbr;
	}

	public void setPnameabbr(String pnameabbr) {
		this.pnameabbr = pnameabbr;
	}

	public String getFoundDt2() {
		return foundDt2;
	}

	public void setFoundDt2(String foundDt2) {
		this.foundDt2 = foundDt2;
	}

	public String getOpenDt2() {
		return openDt2;
	}

	public void setOpenDt2(String openDt2) {
		this.openDt2 = openDt2;
	}

	public String getLatestBackdateCom() {
		return latestBackdateCom;
	}

	public void setLatestBackdateCom(String latestBackdateCom) {
		this.latestBackdateCom = latestBackdateCom;
	}

	public String getLatestBackdateSa() {
		return latestBackdateSa;
	}

	public void setLatestBackdateSa(String latestBackdateSa) {
		this.latestBackdateSa = latestBackdateSa;
	}

	public String getElement() {
		return element;
	}

	public void setElement(String element) {
		this.element = element;
	}

	public String getSfmsjg() {
		return sfmsjg;
	}

	public void setSfmsjg(String sfmsjg) {
		this.sfmsjg = sfmsjg;
	}

	public String getIsSupportPiggyPay() {
		return isSupportPiggyPay;
	}

	public void setIsSupportPiggyPay(String isSupportPiggyPay) {
		this.isSupportPiggyPay = isSupportPiggyPay;
	}

	public String getScrg() {
		return scrg;
	}

	public void setScrg(String scrg) {
		this.scrg = scrg;
	}

	public String getZjrg() {
		return zjrg;
	}

	public void setZjrg(String zjrg) {
		this.zjrg = zjrg;
	}

	public String getRgfl() {
		return rgfl;
	}

	public void setRgfl(String rgfl) {
		this.rgfl = rgfl;
	}

	public String getGlfl() {
		return glfl;
	}

	public void setGlfl(String glfl) {
		this.glfl = glfl;
	}

	public String getTgflStr() {
		return tgflStr;
	}

	public void setTgflStr(String tgflStr) {
		this.tgflStr = tgflStr;
	}

	public String getShfl() {
		return shfl;
	}

	public void setShfl(String shfl) {
		this.shfl = shfl;
	}

	public String getShxz() {
		return shxz;
	}

	public void setShxz(String shxz) {
		this.shxz = shxz;
	}

	public String getShrqsm() {
		return shrqsm;
	}

	public void setShrqsm(String shrqsm) {
		this.shrqsm = shrqsm;
	}

	public String getShzjdzr() {
		return shzjdzr;
	}

	public void setShzjdzr(String shzjdzr) {
		this.shzjdzr = shzjdzr;
	}

	public String getFdfl() {
		return fdfl;
	}

	public void setFdfl(String fdfl) {
		this.fdfl = fdfl;
	}

	public String getYjjtr() {
		return yjjtr;
	}

	public void setYjjtr(String yjjtr) {
		this.yjjtr = yjjtr;
	}

	public String getJtfs() {
		return jtfs;
	}

	public void setJtfs(String jtfs) {
		this.jtfs = jtfs;
	}

	public String getYjjttj() {
		return yjjttj;
	}

	public void setYjjttj(String yjjttj) {
		this.yjjttj = yjjttj;
	}

	public String getJtpl() {
		return jtpl;
	}

	public void setJtpl(String jtpl) {
		this.jtpl = jtpl;
	}

	public String getYywbfwflStr() {
		return yywbfwflStr;
	}

	public void setYywbfwflStr(String yywbfwflStr) {
		this.yywbfwflStr = yywbfwflStr;
	}

	public BigDecimal getFeeDiscount() {
		return feeDiscount;
	}

	public void setFeeDiscount(BigDecimal feeDiscount) {
		this.feeDiscount = feeDiscount;
	}

	public String getSupportPayType() {
		return supportPayType;
	}

	public void setSupportPayType(String supportPayType) {
		this.supportPayType = supportPayType;
	}

	public String getJjzt() {
		return jjzt;
	}

	public void setJjzt(String jjzt) {
		this.jjzt = jjzt;
	}

	public String getSubsPreDt() {
		return subsPreDt;
	}

	public void setSubsPreDt(String subsPreDt) {
		this.subsPreDt = subsPreDt;
	}

	public String getSubsOpenDt() {
		return subsOpenDt;
	}

	public void setSubsOpenDt(String subsOpenDt) {
		this.subsOpenDt = subsOpenDt;
	}

	public String getRedeemPreDt() {
		return redeemPreDt;
	}

	public void setRedeemPreDt(String redeemPreDt) {
		this.redeemPreDt = redeemPreDt;
	}

	public String getRedeemOpenDt() {
		return redeemOpenDt;
	}

	public void setRedeemOpenDt(String redeemOpenDt) {
		this.redeemOpenDt = redeemOpenDt;
	}

	public String getZfbq() {
		return zfbq;
	}

	public void setZfbq(String zfbq) {
		this.zfbq = zfbq;
	}

	public String getShflsm() {
		return shflsm;
	}

	public void setShflsm(String shflsm) {
		this.shflsm = shflsm;
	}

	public String getGlrm() {
		return glrm;
	}

	public void setGlrm(String glrm) {
		this.glrm = glrm;
	}
}
