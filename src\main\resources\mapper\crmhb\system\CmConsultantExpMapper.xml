<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpMapper">
	<insert id="insertCmConsultantExp" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
        INSERT INTO CM_CONSULTANT_EXP (
       <trim suffix="" suffixOverrides=",">   
                <if test="userid != null"> userid, </if>
				<if test="userno != null"> userno, </if>
				<if test="provcode != null"> provcode, </if>
				<if test="citycode != null"> citycode, </if>
				<if test="gender != null"> gender, </if>
				<if test="birthday != null"> birthday, </if>
				<if test="edulevel != null"> edulevel, </if>
				<if test="worktype != null"> worktype, </if>
				<if test="workstate != null"> workstate, </if>
				<if test="curmonthlevel != null"> curmonthlevel, </if>
				<if test="startdt != null"> startdt, </if>
				<if test="startlevel != null"> startlevel, </if>
				<if test="salary != null"> salary, </if>
				<if test="probationenddt != null"> probationenddt, </if>
				<if test="regulardt != null"> regulardt, </if>
				<if test="regularlevel != null"> regularlevel, </if>
				<if test="regularsalary != null"> regularsalary, </if>
				<if test="quitdt != null"> quitdt, </if>
				<if test="quitlevel != null"> quitlevel, </if>
				<if test="quitsalary != null"> quitsalary, </if>
				<if test="quitreason != null"> quitreason, </if>
				<if test="servingage != null"> servingage, </if>
				<if test="checkflag != null"> checkflag, </if>
				<if test="checkor != null"> checkor, </if>
				<if test="creator != null"> creator, </if>
				<if test="creatdt != null"> creatdt, </if>
				<if test="modor != null"> modor, </if>
				<if test="moddt != null"> moddt, </if>
				<if test="jjcardno !=null"> jjcardno, </if>
				<if test="attachtype !=null"> attachtype, </if>
				<if test="background !=null"> background, </if>
				<if test="source !=null"> source, </if>
				<if test="beforepositiontype !=null"> beforepositiontype, </if>
				<if test="beforepositionage !=null"> beforepositionage, </if>
				<if test="recruit !=null"> recruit, </if>
				<if test="recommend !=null"> recommend, </if>
				<if test="recommenduserno !=null"> recommenduserno, </if>
				<if test="recommendtype !=null"> recommendtype, </if>
				<if test="remark !=null"> remark, </if>
				<if test="teamcode !=null"> teamcode, </if>
				<if test="outletcode !=null"> outletcode, </if>
				<if test="email !=null"> email, </if>
				<if test="consname !=null"> consname, </if>

				<if test="curmonthsalary !=null"> curmonthsalary, </if>
				<if test="probationresult3m !=null"> probationresult3m, </if>
				<if test="probationresult6m !=null"> probationresult6m, </if>
				<if test="nexttestdate !=null"> nexttestdate, </if>
				<if test="subpositions !=null"> subpositions, </if>
				<if test="quitInfo !=null"> quitinfo, </if>
		        <if test="promotedate !=null"> promotedate, </if>
		        <if test="bxcommissionway !=null"> bxcommissionway, </if>

		        <if test="beisenid !=null"> beisenid, </if>
			   <if test="probationResult12m !=null"> probation_result_12m, </if>
			   <if test="centerOrg !=null"> center_org, </if>
			   <if test="adjustServingMonth !=null"> adjust_serving_month, </if>
			   <if test="adjustManageServingMonth !=null"> adjust_manage_serving_month, </if>

			   <if test="dt3m !=null"> DT3M, </if>
			   <if test="dt12m !=null"> DT12M, </if>
			   <if test="nextTestPeriod !=null"> NEXT_TEST_PERIOD, </if>
			   <if test="probationSalary3m !=null"> PROBATION_SALARY_3M, </if>
			   <if test="salary12m !=null"> SALARY_12M, </if>
			   <if test="joinRank !=null"> JOIN_RANK, </if>
			   <if test="probationRank3m !=null"> PROBATION_RANK_3M, </if>
			   <if test="regularRank !=null"> REGULAR_RANK, </if>
			   <if test="rank12m !=null"> RANK_12M, </if>
			   <if test="joinSsb !=null"> JOIN_SSB, </if>
			   <if test="probationSsb3m !=null"> PROBATION_SSB_3M, </if>
			   <if test="regularSsb !=null"> REGULAR_SSB, </if>
			   <if test="ssb12m !=null"> SSB_12M, </if>
			   <if test="probationLevel3m !=null"> PROBATIONLEVEL_3M, </if>
			   <if test="testLevel12m !=null"> TESTLEVEL_12M, </if>

		   <if test="dt3mFlag !=null"> DT3M_FLAG, </if>
		   <if test="dt12mFlag !=null"> DT12M_FLAG, </if>
		   <if test="regulardtFlag !=null"> REGULARDT_FLAG, </if>
		   <if test="nexttestdateFlag !=null"> NEXTTESTDATE_FLAG, </if>
		   <if test="countyCode !=null"> COUNTY_CODE, </if>
		   <if test="inTransitRemark !=null"> IN_TRANSIT_REMARK, </if>
		 </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
                <if test="userid != null"> #{userid}, </if>
				<if test="userno != null"> #{userno}, </if>
				<if test="provcode != null"> #{provcode}, </if>
				<if test="citycode != null"> #{citycode}, </if>
				<if test="gender != null"> #{gender}, </if>
				<if test="birthday != null"> #{birthday}, </if>
				<if test="edulevel != null"> #{edulevel}, </if>
				<if test="worktype != null"> #{worktype}, </if>
				<if test="workstate != null"> #{workstate}, </if>
				<if test="curmonthlevel != null"> #{curmonthlevel}, </if>
				<if test="startdt != null"> #{startdt}, </if>
				<if test="startlevel != null"> #{startlevel}, </if>
				<if test="salary != null"> #{salary}, </if>
				<if test="probationenddt != null"> #{probationenddt}, </if>
				<if test="regulardt != null"> #{regulardt}, </if>
				<if test="regularlevel != null"> #{regularlevel}, </if>
				<if test="regularsalary != null"> #{regularsalary}, </if>
				<if test="quitdt != null"> #{quitdt}, </if>
				<if test="quitlevel != null"> #{quitlevel}, </if>
				<if test="quitsalary != null"> #{quitsalary}, </if>
				<if test="quitreason != null"> #{quitreason}, </if>
				<if test="servingage != null"> #{servingage}, </if>
				<if test="checkflag != null"> #{checkflag}, </if>
				<if test="checkor != null"> #{checkor}, </if>
				<if test="creator != null"> #{creator}, </if>
				<if test="creatdt != null"> #{creatdt}, </if>
				<if test="modor != null"> #{modor}, </if>
				<if test="moddt != null"> #{moddt}, </if>
				<if test="jjcardno !=null"> #{jjcardno}, </if>
				<if test="attachtype !=null"> #{attachtype}, </if>
				<if test="background !=null"> #{background}, </if>
				<if test="source !=null"> #{source}, </if>
				<if test="beforepositiontype !=null"> #{beforepositiontype}, </if>
				<if test="beforepositionage !=null"> #{beforepositionage}, </if>
				<if test="recruit !=null"> #{recruit}, </if>
				<if test="recommend !=null"> #{recommend}, </if>
				<if test="recommenduserno !=null"> #{recommenduserno}, </if>
				<if test="recommendtype !=null"> #{recommendtype}, </if>
				<if test="remark !=null"> #{remark}, </if>
				<if test="teamcode !=null"> #{teamcode}, </if>
				<if test="outletcode !=null"> #{outletcode}, </if>
				<if test="email !=null"> #{email}, </if>
				<if test="consname !=null"> #{consname}, </if>

			 <if test="curmonthsalary !=null"> #{curmonthsalary}, </if>
			 <if test="probationresult3m !=null"> #{probationresult3m}, </if>
			 <if test="probationresult6m !=null"> #{probationresult6m}, </if>
			 <if test="nexttestdate !=null"> #{nexttestdate}, </if>
			 <if test="subpositions !=null"> #{subpositions}, </if>
			 <if test="quitInfo !=null"> #{quitInfo}, </if>
			 <if test="promotedate !=null"> #{promotedate}, </if>
			 <if test="bxcommissionway !=null"> #{bxcommissionway}, </if>

			 <if test="beisenid !=null"> #{beisenid}, </if>
			 <if test="probationResult12m !=null"> #{probationResult12m}, </if>
			 <if test="centerOrg !=null"> #{centerOrg}, </if>
			 <if test="adjustServingMonth !=null"> #{adjustServingMonth}, </if>
			 <if test="adjustManageServingMonth !=null"> #{adjustManageServingMonth}, </if>


			 <if test="dt3m !=null"> #{dt3m}, </if>
			 <if test="dt12m !=null"> #{dt12m}, </if>
			 <if test="nextTestPeriod !=null"> #{nextTestPeriod}, </if>
			 <if test="probationSalary3m !=null"> #{probationSalary3m}, </if>
			 <if test="salary12m !=null"> #{salary12m}, </if>
			 <if test="joinRank !=null"> #{joinRank}, </if>
			 <if test="probationRank3m !=null"> #{probationRank3m}, </if>
			 <if test="regularRank !=null"> #{regularRank}, </if>
			 <if test="rank12m !=null"> #{rank12m}, </if>
			 <if test="joinSsb !=null"> #{joinSsb}, </if>
			 <if test="probationSsb3m !=null"> #{probationSsb3m}, </if>
			 <if test="regularSsb !=null"> #{regularSsb}, </if>
			 <if test="ssb12m !=null"> #{ssb12m}, </if>
			 <if test="probationLevel3m !=null"> #{probationLevel3m}, </if>
			 <if test="testLevel12m !=null"> #{testLevel12m}, </if>

			 <if test="dt3mFlag !=null"> #{dt3mFlag}, </if>
			 <if test="dt12mFlag !=null"> #{dt12mFlag}, </if>
			 <if test="regulardtFlag !=null"> #{regulardtFlag}, </if>
			 <if test="nexttestdateFlag !=null"> #{nexttestdateFlag}, </if>
			 <if test="countyCode !=null"> #{countyCode}, </if>
			 <if test="inTransitRemark !=null"> #{inTransitRemark}, </if>
           </trim>
         )
    </insert>
    
    <insert id="insertCmConsultantExpCheckLog" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
        INSERT INTO CM_CONSULTANT_EXP_CHECKLOG (
       <trim suffix="" suffixOverrides=",">   
                <if test="userid != null"> userid, </if>
				<if test="userno != null"> userno, </if>
				<if test="provcode != null"> provcode, </if>
				<if test="citycode != null"> citycode, </if>
				<if test="gender != null"> gender, </if>
				<if test="birthday != null"> birthday, </if>
				<if test="edulevel != null"> edulevel, </if>
				<if test="worktype != null"> worktype, </if>
				<if test="workstate != null"> workstate, </if>
				<if test="curmonthlevel != null"> curmonthlevel, </if>
				<if test="startdt != null"> startdt, </if>
				<if test="startlevel != null"> startlevel, </if>
				<if test="salary != null"> salary, </if>
				<if test="probationenddt != null"> probationenddt, </if>
				<if test="regulardt != null"> regulardt, </if>
				<if test="regularlevel != null"> regularlevel, </if>
				<if test="regularsalary != null"> regularsalary, </if>
				<if test="quitdt != null"> quitdt, </if>
				<if test="quitlevel != null"> quitlevel, </if>
				<if test="quitsalary != null"> quitsalary, </if>
				<if test="quitreason != null"> quitreason, </if>
				<if test="servingage != null"> servingage, </if>
				<if test="checkflag != null"> checkflag, </if>
				<if test="checkor != null"> checkor, </if>
				<if test="creator != null"> creator, </if>
				<if test="creatdt != null"> creatdt, </if>
				<if test="modor != null"> modor, </if>
				<if test="moddt != null"> moddt, </if>
				<if test="jjcardno !=null"> jjcardno, </if>
				<if test="attachtype !=null"> attachtype, </if>
				<if test="background !=null"> background, </if>
				<if test="source !=null"> source, </if>
				<if test="beforepositiontype !=null"> beforepositiontype, </if>
				<if test="beforepositionage !=null"> beforepositionage, </if>
				<if test="recruit !=null"> recruit, </if>
				<if test="recommend !=null"> recommend, </if>
				<if test="recommenduserno !=null"> recommenduserno, </if>
				<if test="recommendtype !=null"> recommendtype, </if>
				<if test="remark !=null"> remark, </if>
			   <if test="teamcode !=null"> teamcode, </if>
			   <if test="outletcode !=null"> outletcode, </if>
			   <if test="email !=null"> email, </if>
			   <if test="consname !=null"> consname, </if>

		   <if test="curmonthsalary !=null"> curmonthsalary, </if>
		   <if test="probationresult3m !=null"> probationresult3m, </if>
		   <if test="probationresult6m !=null"> probationresult6m, </if>
		   <if test="nexttestdate !=null"> nexttestdate, </if>
		   <if test="subpositions !=null"> subpositions, </if>
		   <if test="quitInfo !=null"> quitinfo, </if>
		   <if test="promotedate !=null"> promotedate, </if>
		   <if test="bxcommissionway !=null"> bxcommissionway, </if>

		   <if test="beisenid !=null"> beisenid, </if>
		   <if test="probationResult12m !=null"> probation_result_12m, </if>
		   <if test="centerOrg !=null"> center_org, </if>
		   <if test="adjustServingMonth !=null"> adjust_serving_month, </if>
		   <if test="adjustManageServingMonth !=null"> adjust_manage_serving_month, </if>

		   <if test="dt3m !=null"> DT3M, </if>
		   <if test="dt12m !=null"> DT12M, </if>
		   <if test="nextTestPeriod !=null"> NEXT_TEST_PERIOD, </if>
		   <if test="probationSalary3m !=null"> PROBATION_SALARY_3M, </if>
		   <if test="salary12m !=null"> SALARY_12M, </if>
		   <if test="joinRank !=null"> JOIN_RANK, </if>
		   <if test="probationRank3m !=null"> PROBATION_RANK_3M, </if>
		   <if test="regularRank !=null"> REGULAR_RANK, </if>
		   <if test="rank12m !=null"> RANK_12M, </if>
		   <if test="joinSsb !=null"> JOIN_SSB, </if>
		   <if test="probationSsb3m !=null"> PROBATION_SSB_3M, </if>
		   <if test="regularSsb !=null"> REGULAR_SSB, </if>
		   <if test="ssb12m !=null"> SSB_12M, </if>
		   <if test="probationLevel3m !=null"> PROBATIONLEVEL_3M, </if>
		   <if test="testLevel12m !=null"> TESTLEVEL_12M, </if>

		   <if test="dt3mFlag !=null"> DT3M_FLAG, </if>
		   <if test="dt12mFlag !=null"> DT12M_FLAG, </if>
		   <if test="regulardtFlag !=null"> REGULARDT_FLAG, </if>
		   <if test="nexttestdateFlag !=null"> NEXTTESTDATE_FLAG, </if>
		   <if test="countyCode !=null"> COUNTY_CODE, </if>
		   <if test="inTransitRemark !=null"> IN_TRANSIT_REMARK, </if>
     </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
                <if test="userid != null"> #{userid}, </if>
				<if test="userno != null"> #{userno}, </if>
				<if test="provcode != null"> #{provcode}, </if>
				<if test="citycode != null"> #{citycode}, </if>
				<if test="gender != null"> #{gender}, </if>
				<if test="birthday != null"> #{birthday}, </if>
				<if test="edulevel != null"> #{edulevel}, </if>
				<if test="worktype != null"> #{worktype}, </if>
				<if test="workstate != null"> #{workstate}, </if>
				<if test="curmonthlevel != null"> #{curmonthlevel}, </if>
				<if test="startdt != null"> #{startdt}, </if>
				<if test="startlevel != null"> #{startlevel}, </if>
				<if test="salary != null"> #{salary}, </if>
				<if test="probationenddt != null"> #{probationenddt}, </if>
				<if test="regulardt != null"> #{regulardt}, </if>
				<if test="regularlevel != null"> #{regularlevel}, </if>
				<if test="regularsalary != null"> #{regularsalary}, </if>
				<if test="quitdt != null"> #{quitdt}, </if>
				<if test="quitlevel != null"> #{quitlevel}, </if>
				<if test="quitsalary != null"> #{quitsalary}, </if>
				<if test="quitreason != null"> #{quitreason}, </if>
				<if test="servingage != null"> #{servingage}, </if>
				<if test="checkflag != null"> #{checkflag}, </if>
				<if test="checkor != null"> #{checkor}, </if>
				<if test="creator != null"> #{creator}, </if>
				<if test="creatdt != null"> #{creatdt}, </if>
				<if test="modor != null"> #{modor}, </if>
				<if test="moddt != null"> #{moddt}, </if>
				<if test="jjcardno !=null"> #{jjcardno}, </if>
				<if test="attachtype !=null"> #{attachtype}, </if>
				<if test="background !=null"> #{background}, </if>
				<if test="source !=null"> #{source}, </if>
				<if test="beforepositiontype !=null"> #{beforepositiontype}, </if>
				<if test="beforepositionage !=null"> #{beforepositionage}, </if>
				<if test="recruit !=null"> #{recruit}, </if>
				<if test="recommend !=null"> #{recommend}, </if>
				<if test="recommenduserno !=null"> #{recommenduserno}, </if>
				<if test="recommendtype !=null"> #{recommendtype}, </if>
				<if test="remark !=null"> #{remark}, </if>
				 <if test="teamcode !=null"> #{teamcode}, </if>
				 <if test="outletcode !=null"> #{outletcode}, </if>
				 <if test="email !=null"> #{email}, </if>
				 <if test="consname !=null"> #{consname}, </if>

			 <if test="curmonthsalary !=null"> #{curmonthsalary}, </if>
			 <if test="probationresult3m !=null"> #{probationresult3m}, </if>
			 <if test="probationresult6m !=null"> #{probationresult6m}, </if>
			 <if test="nexttestdate !=null"> #{nexttestdate}, </if>
			 <if test="subpositions !=null"> #{subpositions}, </if>
			 <if test="quitInfo !=null"> #{quitInfo}, </if>
			 <if test="promotedate !=null"> #{promotedate}, </if>
			 <if test="bxcommissionway !=null"> #{bxcommissionway}, </if>
			 <if test="beisenid !=null"> #{beisenid}, </if>
			 <if test="probationResult12m !=null"> #{probationResult12m}, </if>
			 <if test="centerOrg !=null"> #{centerOrg}, </if>
			 <if test="adjustServingMonth !=null"> #{adjustServingMonth}, </if>
			 <if test="adjustManageServingMonth !=null"> #{adjustManageServingMonth}, </if>

			 <if test="dt3m !=null"> #{dt3m}, </if>
			 <if test="dt12m !=null"> #{dt12m}, </if>
			 <if test="nextTestPeriod !=null"> #{nextTestPeriod}, </if>
			 <if test="probationSalary3m !=null"> #{probationSalary3m}, </if>
			 <if test="salary12m !=null"> #{salary12m}, </if>
			 <if test="joinRank !=null"> #{joinRank}, </if>
			 <if test="probationRank3m !=null"> #{probationRank3m}, </if>
			 <if test="regularRank !=null"> #{regularRank}, </if>
			 <if test="rank12m !=null"> #{rank12m}, </if>
			 <if test="joinSsb !=null"> #{joinSsb}, </if>
			 <if test="probationSsb3m !=null"> #{probationSsb3m}, </if>
			 <if test="regularSsb !=null"> #{regularSsb}, </if>
			 <if test="ssb12m !=null"> #{ssb12m}, </if>
			 <if test="probationLevel3m !=null"> #{probationLevel3m}, </if>
			 <if test="testLevel12m !=null"> #{testLevel12m}, </if>

			 <if test="dt3mFlag !=null"> #{dt3mFlag}, </if>
			 <if test="dt12mFlag !=null"> #{dt12mFlag}, </if>
			 <if test="regulardtFlag !=null"> #{regulardtFlag}, </if>
			 <if test="nexttestdateFlag !=null"> #{nexttestdateFlag}, </if>
			 <if test="countyCode !=null"> #{countyCode}, </if>
			 <if test="inTransitRemark !=null"> #{inTransitRemark}, </if>
		 </trim>
		)
	</insert>

	<delete id="deleteExpCheckLog" parameterType="string">
		DELETE FROM CM_CONSULTANT_EXP_CHECKLOG
		WHERE USERID = #{userid}
	</delete>

    <insert id="insertCmConsultantExpDel" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
        INSERT INTO CM_CONSULTANT_EXP_DEL (
       <trim suffix="" suffixOverrides=",">   
                <if test="userid != null"> userid, </if>
				<if test="userno != null"> userno, </if>
				<if test="provcode != null"> provcode, </if>
				<if test="citycode != null"> citycode, </if>
				<if test="gender != null"> gender, </if>
				<if test="birthday != null"> birthday, </if>
				<if test="edulevel != null"> edulevel, </if>
				<if test="worktype != null"> worktype, </if>
				<if test="workstate != null"> workstate, </if>
				<if test="curmonthlevel != null"> curmonthlevel, </if>
				<if test="startdt != null"> startdt, </if>
				<if test="startlevel != null"> startlevel, </if>
				<if test="salary != null"> salary, </if>
				<if test="probationenddt != null"> probationenddt, </if>
				<if test="regulardt != null"> regulardt, </if>
				<if test="regularlevel != null"> regularlevel, </if>
				<if test="regularsalary != null"> regularsalary, </if>
				<if test="quitdt != null"> quitdt, </if>
				<if test="quitlevel != null"> quitlevel, </if>
				<if test="quitsalary != null"> quitsalary, </if>
				<if test="quitreason != null"> quitreason, </if>
				<if test="servingage != null"> servingage, </if>
				<if test="checkflag != null"> checkflag, </if>
				<if test="checkor != null"> checkor, </if>
				<if test="creator != null"> creator, </if>
				<if test="creatdt != null"> creatdt, </if>
				<if test="modor != null"> modor, </if>
				<if test="moddt != null"> moddt, </if>
				<if test="jjcardno !=null"> jjcardno, </if>
				<if test="attachtype !=null"> attachtype, </if>
				<if test="background !=null"> background, </if>
				<if test="source !=null"> source, </if>
				<if test="beforepositiontype !=null"> beforepositiontype, </if>
				<if test="beforepositionage !=null"> beforepositionage, </if>
				<if test="recruit !=null"> recruit, </if>
				<if test="recommend !=null"> recommend, </if>
				<if test="recommenduserno !=null"> recommenduserno, </if>
				<if test="recommendtype !=null"> recommendtype, </if>
				<if test="remark !=null"> remark, </if>
				<if test="teamcode !=null"> teamcode, </if>
				<if test="outletcode !=null"> outletcode, </if>
				<if test="email !=null"> email, </if>
				<if test="consname !=null"> consname, </if>
		   <if test="curmonthsalary !=null"> curmonthsalary, </if>
		   <if test="probationresult3m !=null"> probationresult3m, </if>
		   <if test="probationresult6m !=null"> probationresult6m, </if>
		   <if test="nexttestdate !=null"> nexttestdate, </if>
		   <if test="subpositions !=null"> subpositions, </if>
		   <if test="quitInfo !=null"> quitinfo, </if>
		   <if test="promotedate !=null"> promotedate, </if>
		   <if test="bxcommissionway !=null"> bxcommissionway, </if>

		   <if test="beisenid !=null"> beisenid, </if>
		   <if test="probationResult12m !=null"> probation_result_12m, </if>
		   <if test="centerOrg !=null"> center_org, </if>
		   <if test="adjustServingMonth !=null"> adjust_serving_month, </if>
		   <if test="adjustManageServingMonth !=null"> adjust_manage_serving_month, </if>

		   <if test="dt3m !=null"> DT3M, </if>
		   <if test="dt12m !=null"> DT12M, </if>
		   <if test="nextTestPeriod !=null"> NEXT_TEST_PERIOD, </if>
		   <if test="probationSalary3m !=null"> PROBATION_SALARY_3M, </if>
		   <if test="salary12m !=null"> SALARY_12M, </if>
		   <if test="joinRank !=null"> JOIN_RANK, </if>
		   <if test="probationRank3m !=null"> PROBATION_RANK_3M, </if>
		   <if test="regularRank !=null"> REGULAR_RANK, </if>
		   <if test="rank12m !=null"> RANK_12M, </if>
		   <if test="joinSsb !=null"> JOIN_SSB, </if>
		   <if test="probationSsb3m !=null"> PROBATION_SSB_3M, </if>
		   <if test="regularSsb !=null"> REGULAR_SSB, </if>
		   <if test="ssb12m !=null"> SSB_12M, </if>
		   <if test="probationLevel3m !=null"> PROBATIONLEVEL_3M, </if>
		   <if test="testLevel12m !=null"> TESTLEVEL_12M, </if>

		   <if test="dt3mFlag !=null"> DT3M_FLAG, </if>
		   <if test="dt12mFlag !=null"> DT12M_FLAG, </if>
		   <if test="regulardtFlag !=null"> REGULARDT_FLAG, </if>
		   <if test="nexttestdateFlag !=null"> NEXTTESTDATE_FLAG, </if>
		   <if test="countyCode !=null"> COUNTY_CODE, </if>
		   <if test="inTransitRemark !=null"> IN_TRANSIT_REMARK, </if>
     </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
                <if test="userid != null"> #{userid}, </if>
				<if test="userno != null"> #{userno}, </if>
				<if test="provcode != null"> #{provcode}, </if>
				<if test="citycode != null"> #{citycode}, </if>
				<if test="gender != null"> #{gender}, </if>
				<if test="birthday != null"> #{birthday}, </if>
				<if test="edulevel != null"> #{edulevel}, </if>
				<if test="worktype != null"> #{worktype}, </if>
				<if test="workstate != null"> #{workstate}, </if>
				<if test="curmonthlevel != null"> #{curmonthlevel}, </if>
				<if test="startdt != null"> #{startdt}, </if>
				<if test="startlevel != null"> #{startlevel}, </if>
				<if test="salary != null"> #{salary}, </if>
				<if test="probationenddt != null"> #{probationenddt}, </if>
				<if test="regulardt != null"> #{regulardt}, </if>
				<if test="regularlevel != null"> #{regularlevel}, </if>
				<if test="regularsalary != null"> #{regularsalary}, </if>
				<if test="quitdt != null"> #{quitdt}, </if>
				<if test="quitlevel != null"> #{quitlevel}, </if>
				<if test="quitsalary != null"> #{quitsalary}, </if>
				<if test="quitreason != null"> #{quitreason}, </if>
				<if test="servingage != null"> #{servingage}, </if>
				<if test="checkflag != null"> #{checkflag}, </if>
				<if test="checkor != null"> #{checkor}, </if>
				<if test="creator != null"> #{creator}, </if>
				<if test="creatdt != null"> #{creatdt}, </if>
				<if test="modor != null"> #{modor}, </if>
				<if test="moddt != null"> #{moddt}, </if>
				<if test="jjcardno !=null"> #{jjcardno}, </if>
				<if test="attachtype !=null"> #{attachtype}, </if>
				<if test="background !=null"> #{background}, </if>
				<if test="source !=null"> #{source}, </if>
				<if test="beforepositiontype !=null"> #{beforepositiontype}, </if>
				<if test="beforepositionage !=null"> #{beforepositionage}, </if>
				<if test="recruit !=null"> #{recruit}, </if>
				<if test="recommend !=null"> #{recommend}, </if>
				<if test="recommenduserno !=null"> #{recommenduserno}, </if>
				<if test="recommendtype !=null"> #{recommendtype}, </if>
				<if test="remark !=null"> #{remark}, </if>
				<if test="teamcode !=null"> #{teamcode}, </if>
				<if test="outletcode !=null"> #{outletcode}, </if>
				<if test="email !=null"> #{email}, </if>
				<if test="consname !=null"> #{consname}, </if>
			 <if test="curmonthsalary !=null"> #{curmonthsalary}, </if>
			 <if test="probationresult3m !=null"> #{probationresult3m}, </if>
			 <if test="probationresult6m !=null"> #{probationresult6m}, </if>
			 <if test="nexttestdate !=null"> #{nexttestdate}, </if>
			 <if test="subpositions !=null"> #{subpositions}, </if>
			 <if test="quitInfo !=null"> #{quitInfo}, </if>
			 <if test="promotedate !=null"> #{promotedate}, </if>
			 <if test="bxcommissionway !=null"> #{bxcommissionway}, </if>

			 <if test="beisenid !=null"> #{beisenid}, </if>
			 <if test="probationResult12m !=null"> #{probationResult12m}, </if>
			 <if test="centerOrg !=null"> #{centerOrg}, </if>
			 <if test="adjustServingMonth !=null"> #{adjustServingMonth}, </if>
			 <if test="adjustManageServingMonth !=null"> #{adjustManageServingMonth}, </if>


			 <if test="dt3m !=null"> #{dt3m}, </if>
			 <if test="dt12m !=null"> #{dt12m}, </if>
			 <if test="nextTestPeriod !=null"> #{nextTestPeriod}, </if>
			 <if test="probationSalary3m !=null"> #{probationSalary3m}, </if>
			 <if test="salary12m !=null"> #{salary12m}, </if>
			 <if test="joinRank !=null"> #{joinRank}, </if>
			 <if test="probationRank3m !=null"> #{probationRank3m}, </if>
			 <if test="regularRank !=null"> #{regularRank}, </if>
			 <if test="rank12m !=null"> #{rank12m}, </if>
			 <if test="joinSsb !=null"> #{joinSsb}, </if>
			 <if test="probationSsb3m !=null"> #{probationSsb3m}, </if>
			 <if test="regularSsb !=null"> #{regularSsb}, </if>
			 <if test="ssb12m !=null"> #{ssb12m}, </if>
			 <if test="probationLevel3m !=null"> #{probationLevel3m}, </if>
			 <if test="testLevel12m !=null"> #{testLevel12m}, </if>

			 <if test="dt3mFlag !=null"> #{dt3mFlag}, </if>
			 <if test="dt12mFlag !=null"> #{dt12mFlag}, </if>
			 <if test="regulardtFlag !=null"> #{regulardtFlag}, </if>
			 <if test="nexttestdateFlag !=null"> #{nexttestdateFlag}, </if>
			 <if test="countyCode !=null"> #{countyCode}, </if>
			 <if test="inTransitRemark !=null"> #{inTransitRemark}, </if>
                 </trim>
         )
    </insert>
	<insert id="insertCmConsultantExpOrg">

		INSERT INTO CM_CONSULTANT_EXP_ORG (
		<trim suffix="" suffixOverrides=",">
			id,
			<if test="userid != null"> userid, </if>
			<if test="userno != null"> userno, </if>
			<if test="orgcode != null"> orgcode, </if>
			<if test="creator != null"> creator, </if>
		</trim>
		) values (
		<trim suffix="" suffixOverrides=",">
			SEQ_CONSULTANT_EXP.Nextval,
			<if test="userid != null"> #{userid}, </if>
			<if test="userno != null"> #{userno}, </if>
			<if test="orgcode != null"> #{orgcode}, </if>
			<if test="creator != null"> #{creator}, </if>
		</trim>
		)
	</insert>


	<update id="updateCmConsultantExp" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
    	update CM_CONSULTANT_EXP
	    set 
	    userid=#{userid,jdbcType=VARCHAR},
		userno=#{userno,jdbcType=VARCHAR},
		provcode=#{provcode,jdbcType=VARCHAR},
		citycode=#{citycode,jdbcType=VARCHAR},
		gender=#{gender,jdbcType=VARCHAR},
		birthday=#{birthday,jdbcType=VARCHAR},
		edulevel=#{edulevel,jdbcType=VARCHAR},
		worktype=#{worktype,jdbcType=VARCHAR},
		workstate=#{workstate,jdbcType=VARCHAR},
		curmonthlevel=#{curmonthlevel,jdbcType=VARCHAR},
		startdt=#{startdt,jdbcType=VARCHAR},
		startlevel=#{startlevel,jdbcType=VARCHAR},
		salary=#{salary,jdbcType=NUMERIC},
		probationenddt=#{probationenddt,jdbcType=VARCHAR},
		regulardt=#{regulardt,jdbcType=VARCHAR},
		regularlevel=#{regularlevel,jdbcType=VARCHAR},
		regularsalary=#{regularsalary,jdbcType=NUMERIC},
		quitdt=#{quitdt,jdbcType=VARCHAR},
		quitlevel=#{quitlevel,jdbcType=VARCHAR},
		quitsalary=#{quitsalary,jdbcType=NUMERIC},
		quitreason=#{quitreason,jdbcType=VARCHAR},
		servingage=#{servingage,jdbcType=NUMERIC},
		checkflag=#{checkflag,jdbcType=VARCHAR},
		checkor=#{checkor,jdbcType=VARCHAR},
		jjcardno=#{jjcardno,jdbcType=VARCHAR},
		attachtype=#{attachtype,jdbcType=VARCHAR},
		background=#{background,jdbcType=VARCHAR},
		source=#{source,jdbcType=VARCHAR},
		beforepositiontype=#{beforepositiontype,jdbcType=VARCHAR},
		beforepositionage=#{beforepositionage,jdbcType=VARCHAR},
		recruit=#{recruit,jdbcType=VARCHAR},
		recommend=#{recommend,jdbcType=VARCHAR},
		recommenduserno=#{recommenduserno,jdbcType=VARCHAR},
		recommendtype=#{recommendtype,jdbcType=VARCHAR},
		remark=#{remark,jdbcType=VARCHAR},
		teamcode=#{teamcode,jdbcType=VARCHAR},
		outletcode=#{outletcode,jdbcType=VARCHAR},
		email=#{email,jdbcType=VARCHAR},
		consname=#{consname,jdbcType=VARCHAR},

		curmonthsalary=#{curmonthsalary,jdbcType=NUMERIC},
		probationresult3m=#{probationresult3m,jdbcType=VARCHAR},
		probationresult6m=#{probationresult6m,jdbcType=VARCHAR},
		nexttestdate=#{nexttestdate,jdbcType=VARCHAR},
		subpositions=#{subpositions,jdbcType=VARCHAR},
		quitinfo=#{quitInfo,jdbcType=VARCHAR},
		promotedate=#{promotedate,jdbcType=VARCHAR},
		bxcommissionway=#{bxcommissionway,jdbcType=VARCHAR},
		beisenid=#{beisenid,jdbcType=VARCHAR},
		creator=#{creator,jdbcType=VARCHAR},
		creatdt=#{creatdt,jdbcType=VARCHAR},
		modor=#{modor,jdbcType=VARCHAR},
		probation_result_12m=#{probationResult12m,jdbcType=VARCHAR},
		center_org=#{centerOrg,jdbcType=VARCHAR},
		adjust_serving_month=#{adjustServingMonth,jdbcType=NUMERIC},
		adjust_manage_serving_month=#{adjustManageServingMonth,jdbcType=NUMERIC},

		DT3M=#{dt3m,jdbcType=VARCHAR},
		DT12M=#{dt12m,jdbcType=VARCHAR},
		NEXT_TEST_PERIOD=#{nextTestPeriod,jdbcType=VARCHAR},
		PROBATION_SALARY_3M=#{probationSalary3m,jdbcType=NUMERIC},
		SALARY_12M=#{salary12m,jdbcType=NUMERIC},
		JOIN_RANK=#{joinRank,jdbcType=VARCHAR},
		PROBATION_RANK_3M=#{probationRank3m,jdbcType=VARCHAR},
		REGULAR_RANK=#{regularRank,jdbcType=VARCHAR},
		RANK_12M=#{rank12m,jdbcType=VARCHAR},
		JOIN_SSB=#{joinSsb,jdbcType=VARCHAR},
		PROBATION_SSB_3M=#{probationSsb3m,jdbcType=VARCHAR},
		REGULAR_SSB=#{regularSsb,jdbcType=VARCHAR},
		SSB_12M=#{ssb12m,jdbcType=VARCHAR},
		PROBATIONLEVEL_3M=#{probationLevel3m,jdbcType=VARCHAR},
		TESTLEVEL_12M=#{testLevel12m,jdbcType=VARCHAR},

		DT3M_FLAG = #{dt3mFlag,jdbcType=VARCHAR},
		DT12M_FLAG = #{dt12mFlag,jdbcType=VARCHAR},
		REGULARDT_FLAG = #{regulardtFlag,jdbcType=VARCHAR},
		NEXTTESTDATE_FLAG = #{nexttestdateFlag,jdbcType=VARCHAR},
		COUNTY_CODE = #{countyCode,jdbcType=VARCHAR},
		IN_TRANSIT_REMARK = #{inTransitRemark,jdbcType=VARCHAR},

		moddt=sysdate
	    where  userid = #{userid}
    </update>

	<update id="batchUpdateAudit">
		update CM_CONSULTANT_EXP
		set
		checkflag = #{checkFlag,jdbcType=VARCHAR},
		checkor = #{optUserId,jdbcType=VARCHAR},
		moddt=sysdate,
		modor = #{optUserId,jdbcType=VARCHAR}
		where  userid in
		<foreach collection="userIds" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>
    
    <update id="updateCmConsultantExpImport" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
    	update CM_CONSULTANT_EXP
	    <set>
            <if test="userid != null"> userid = #{userid}, </if>             
            <if test="checkflag != null"> checkflag = #{checkflag}, </if>             
            <if test="checkor != null"> checkor = #{checkor}, </if>        
            <if test="curmonthlevel != null"> curmonthlevel = #{curmonthlevel}, </if>       
            <if test="curmonthsalary != null"> curmonthsalary = #{curmonthsalary}, </if>

			<if test="nextTestPeriod != null"> NEXT_TEST_PERIOD = #{nextTestPeriod}, </if>
			<if test="nexttestdate != null">  nexttestdate = #{nexttestdate}, </if>
			<if test="startdt != null">  startdt = #{startdt}, </if>
			<if test="dt3m != null">  dt3m = #{dt3m}, </if>
			<if test="regulardt != null">  regulardt = #{regulardt}, </if>
			<if test="dt12m != null">  dt12m = #{dt12m}, </if>
			<if test="probationresult3m != null"> probationresult3m = #{probationresult3m}, </if>
			<if test="probationresult6m != null"> probationresult6m = #{probationresult6m}, </if>
			<if test="probationResult12m != null">  PROBATION_RESULT_12M = #{probationResult12m}, </if>
			<if test="startlevel != null">  startlevel = #{startlevel}, </if>
			<if test="probationLevel3m != null">  PROBATIONLEVEL_3M = #{probationLevel3m}, </if>
			<if test="regularlevel != null">  regularlevel = #{regularlevel}, </if>
			<if test="testLevel12m != null">  TESTLEVEL_12M = #{testLevel12m}, </if>
			<if test="joinRank != null">  JOIN_RANK = #{joinRank}, </if>
			<if test="probationRank3m != null">  PROBATION_RANK_3M = #{probationRank3m}, </if>
			<if test="regularRank != null">  REGULAR_RANK = #{regularRank}, </if>
			<if test="rank12m != null">  RANK_12M = #{rank12m}, </if>
			<if test="salary != null">  salary = #{salary}, </if>
			<if test="probationSalary3m != null">  PROBATION_SALARY_3M = #{probationSalary3m}, </if>
			<if test="regularsalary != null">  regularsalary = #{regularsalary}, </if>
			<if test="salary12m != null">  SALARY_12M = #{salary12m}, </if>
			<if test="joinSsb != null">  JOIN_SSB = #{joinSsb}, </if>
			<if test="probationSsb3m != null">  PROBATION_SSB_3M = #{probationSsb3m}, </if>
			<if test="regularSsb != null">  REGULAR_SSB = #{regularSsb}, </if>
			<if test="ssb12m != null">  SSB_12M = #{ssb12m}, </if>

			<if test="dt3mFlag != null">  DT3M_FLAG = #{dt3mFlag}, </if>
			<if test="dt12mFlag != null">  DT12M_FLAG = #{dt12mFlag}, </if>
			<if test="regulardtFlag != null">  REGULARDT_FLAG = #{regulardtFlag}, </if>
			<if test="nexttestdateFlag != null">  NEXTTESTDATE_FLAG = #{nexttestdateFlag}, </if>

			<if test="background != null">
				background = #{background},
			</if>

			<if test="beisenid != null">  beisenid = #{beisenid}, </if>
			<if test="beforepositionage != null">  BEFOREPOSITIONAGE = #{beforepositionage}, </if>
			<if test="centerOrg != null">  CENTER_ORG = #{centerOrg}, </if>
			<if test="modor != null">  modor = #{modor}, </if>
			<if test="moddt != null">  moddt = #{moddt}, </if>
		</set>
          where 
          <if test="userid != null"> userid = #{userid} </if> 
    </update>
    
    <delete id="delCmConsultantExp" parameterType="String">
	    DELETE from CM_CONSULTANT_EXP
	    where userid = #{userid}
	</delete>

	<select id="getUserCount" parameterType="map" resultType="int" useCache="false">
    	select count(*) from CM_CONSULTANT_EXP where  userid = #{userid}
    </select>
    
    <select id="getCmConsultantExpByConscode" parameterType="map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp" useCache="false">
	  	SELECT
		       T1.CONSNAME,
		       T1.EMAIL,
		       T1.OUTLETCODE,
		       T1.TEAMCODE,
		       T1.USERID CONSCODE,
		       T1.USERID,
		       T1.USERNO,
		       T1.PROVCODE,
		       T1.CITYCODE,
		       T1.GENDER,
		       T1.BIRTHDAY,
		       T1.EDULEVEL,
		       T1.WORKTYPE,
		       T1.WORKSTATE,
		       T1.CURMONTHLEVEL,
		       T1.STARTDT,
		       T1.STARTLEVEL,
		       T1.SALARY,
		       T1.PROBATIONENDDT,
		       T1.REGULARDT,
		       T1.REGULARLEVEL,
		       T1.REGULARSALARY,
		       T1.QUITDT,
		       T1.QUITLEVEL,
		       T1.QUITSALARY,
		       T1.QUITREASON,
		       T1.SERVINGAGE,
		       T1.CHECKFLAG,
		       T1.CHECKOR,
		       T1.CREATOR,
		       T1.CREATDT,
		       T1.MODOR,
		       T1.MODDT,
		       t1.jjcardno,
			   t1.attachtype,
			   t1.background,
			   t1.source,
			   t1.beforepositiontype,
			   t1.beforepositionage,
			   t1.recruit,
			   t1.recommend,
			   t1.recommenduserno,
			   t1.recommendtype,
			   t1.remark,

			   t1.curmonthsalary,
			   t1.nexttestdate,
			   t1.quitInfo,
			   t1.subpositions,
			   t1.probationresult3m,
			   t1.probationresult6m,
			   t1.promotedate,
			   t1.bxcommissionway,
			   t1.beisenid,
			   t1.PROBATION_RESULT_12M as probationResult12m,
		       t1.CENTER_ORG as centerOrg,
		       t1.ADJUST_SERVING_MONTH as adjustServingMonth,
			   t1.adjust_manage_serving_month as adjustManageServingMonth,
				T1.DT3M as dt3m,
				T1.DT12M as dt12m,
				T1.NEXT_TEST_PERIOD as nextTestPeriod,
				T1.PROBATION_SALARY_3M as probationSalary3m,
				T1.SALARY_12M as salary12m,
				T1.JOIN_RANK as joinRank,
				T1.PROBATION_RANK_3M as probationRank3m,
				T1.REGULAR_RANK as regularRank,
				T1.RANK_12M as rank12m,
				T1.JOIN_SSB as joinSsb,
				T1.PROBATION_SSB_3M as probationSsb3m,
				T1.REGULAR_SSB as regularSsb,
				T1.SSB_12M as ssb12m,
				T1.PROBATIONLEVEL_3M as probationLevel3m,
				T1.TESTLEVEL_12M as testLevel12m,

				T1.DT3M_FLAG as dt3mFlag,
				T1.DT12M_FLAG as dt12mFlag,
				T1.REGULARDT_FLAG as regulardtFlag,
				T1.NEXTTESTDATE_FLAG as nexttestdateFlag,
				T1.COUNTY_CODE countyCode,
			    T1.IN_TRANSIT_REMARK as inTransitRemark
		  FROM CM_CONSULTANT_EXP T1
		    WHERE T1.USERID = #{conscode}
	  </select>
	  
	  <select id="listCmConsultantExpLogByConscode" parameterType="map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp" useCache="false">
	  	SELECT T1.USERID,
				  T1.CONSNAME,
				  T1.EMAIL,
				  T1.OUTLETCODE,
				  T1.TEAMCODE,
				  T1.USERID,
				  T1.USERNO,
				  T1.PROVCODE,
				  T1.CITYCODE,
				  T1.GENDER,
				  T1.BIRTHDAY,
				  T1.EDULEVEL,
				  T1.WORKTYPE,
				  T1.WORKSTATE,
				  T1.CURMONTHLEVEL,
				  T1.STARTDT,
				  T1.STARTLEVEL,
				  T1.SALARY,
				  T1.PROBATIONENDDT,
				  T1.REGULARDT,
				  T1.REGULARLEVEL,
				  T1.REGULARSALARY,
				  T1.QUITDT,
				  T1.QUITLEVEL,
				  T1.QUITSALARY,
				  T1.QUITREASON,
				  T1.SERVINGAGE,
				  T1.CHECKFLAG,
				  T1.CHECKOR,
				  T1.CREATOR,
				  T1.CREATDT,
				  T1.MODOR,
				  T1.MODDT,
				  t1.jjcardno,
				  t1.attachtype,
				  t1.background,
				  t1.source,
				  t1.beforepositiontype,
				  t1.beforepositionage,
				  t1.recruit,
				  t1.recommend,
				  t1.recommenduserno,
				  t1.recommendtype,
				  t1.remark,
				  T1.SUBPOSITIONS,
				  T1.NEXTTESTDATE,
				  T1.PROBATIONRESULT3M,
				  T1.PROBATIONRESULT6M,
				  T1.CURMONTHSALARY,
				  T1.QUITINFO,
				  t1.promotedate,
				  T1.BXCOMMISSIONWAY,
				  T1.beisenid,
				  T1.center_org as centerOrg,
				  T1.PROBATION_RESULT_12M as probationResult12M,
				  T1.adjust_serving_month as adjustServingMonth,
				  T1.adjust_manage_serving_month as adjustManageServingMonth,

				  T1.DT3M as dt3m,
				  T1.DT12M as dt12m,
				  T1.NEXT_TEST_PERIOD as nextTestPeriod,
				  T1.PROBATION_SALARY_3M as probationSalary3m,
				  T1.SALARY_12M as salary12m,
				  T1.JOIN_RANK as joinRank,
				  T1.PROBATION_RANK_3M as probationRank3m,
				  T1.REGULAR_RANK as regularRank,
				  T1.RANK_12M as rank12m,
				  T1.JOIN_SSB as joinSsb,
				  T1.PROBATION_SSB_3M as probationSsb3m,
				  T1.REGULAR_SSB as regularSsb,
				  T1.SSB_12M as ssb12m,
				  T1.PROBATIONLEVEL_3M as probationLevel3m,
				  T1.TESTLEVEL_12M as testLevel12m,
				  T1.DT3M_FLAG as dt3mFlag,
				  T1.DT12M_FLAG as dt12mFlag,
				  T1.REGULARDT_FLAG as regulardtFlag,
				  T1.NEXTTESTDATE_FLAG as nexttestdateFlag,
				  T1.COUNTY_CODE countyCode,
				  T1.IN_TRANSIT_REMARK as inTransitRemark
		  FROM CM_CONSULTANT_EXP_CHECKLOG T1
		    WHERE T1.USERID = #{conscode}
		    order by T1.MODDT desc
	  </select>
	  
	  <select id="listCmConsultantExp" parameterType="map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp" useCache="false">
	  	select * from CM_CONSULTANT_EXP  where userid = #{userid}
	  </select>

	<select id="getOutletCodeByConscode" parameterType="string" resultType="string">
	   SELECT  T.OUTLETCODE FROM   CM_CONSULTANT T WHERE T.CONSCODE=#{consCode}
	</select>

	  <select id="listCmConsultantExpByExp" parameterType="map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp" useCache="false">
	  	SELECT T1.USERID as conscode,
		  		T1.CONSNAME,
		       T1.EMAIL,
		       T1.OUTLETCODE,
		       T1.TEAMCODE,
		       T1.USERID,
		       T1.USERNO,
		       T1.PROVCODE,
		       T1.CITYCODE,
		       T1.GENDER,
		       T1.BIRTHDAY,
		       T1.EDULEVEL,
		       T1.WORKTYPE,
		       T1.WORKSTATE,
		       T1.CURMONTHLEVEL,
		       T1.STARTDT,
		       T1.STARTLEVEL,
		       T1.SALARY,
		       T1.PROBATIONENDDT,
		       T1.REGULARDT,
		       T1.REGULARLEVEL,
		       T1.REGULARSALARY,
		       T1.QUITDT,
		       T1.QUITLEVEL,
		       T1.QUITSALARY,
		       T1.QUITREASON,
		       T1.SERVINGAGE,
		       T1.CHECKFLAG,
		       T1.CHECKOR,
		       T1.CREATOR,
		       T1.CREATDT,
		       T1.MODOR,
		       T1.MODDT,
		       t1.jjcardno,
			   t1.attachtype,
			   t1.background,
			   t1.source,
			   t1.beforepositiontype,
			   t1.beforepositionage,
			   t1.recruit,
			   t1.recommend,
			   t1.recommenduserno,
			   t1.recommendtype,
			   t1.remark,
			  T1.SUBPOSITIONS,
			  T1.NEXTTESTDATE,
			  T1.PROBATIONRESULT3M,
			  T1.PROBATIONRESULT6M,
			  T1.CURMONTHSALARY,
			  T1.QUITINFO,
			  t1.promotedate,
		  	  T1.BXCOMMISSIONWAY,
		  	  T1.beisenid,
		      T1.center_org as centerOrg,
		      T1.PROBATION_RESULT_12M as probationResult12M,
		      T1.adjust_serving_month as adjustServingMonth,
		      T1.adjust_manage_serving_month as adjustManageServingMonth,
			  T1.DT3M as dt3m,
			  T1.DT12M as dt12m,
			  T1.NEXT_TEST_PERIOD as nextTestPeriod,
			  T1.PROBATION_SALARY_3M as probationSalary3m,
			  T1.SALARY_12M as salary12m,
			  T1.JOIN_RANK as joinRank,
			  T1.PROBATION_RANK_3M as probationRank3m,
			  T1.REGULAR_RANK as regularRank,
			  T1.RANK_12M as rank12m,
			  T1.JOIN_SSB as joinSsb,
			  T1.PROBATION_SSB_3M as probationSsb3m,
			  T1.REGULAR_SSB as regularSsb,
			  T1.SSB_12M as ssb12m,
			  T1.PROBATIONLEVEL_3M as probationLevel3m,
			  T1.TESTLEVEL_12M as testLevel12m,
			  T1.COUNTY_CODE countyCode,
			  T1.IN_TRANSIT_REMARK as inTransitRemark
		  FROM   CM_CONSULTANT_EXP T1
		    <where>
		    <if test="conscode != null"> and T1.USERID = #{conscode,jdbcType=VARCHAR} </if>
		    <if test="consname != null"> and T1.CONSNAME like '%'||#{consname ,jdbcType=VARCHAR}||'%' </if>
		  	<if test="worktype != null and worktype !=''"> and T1.WORKTYPE in (${worktype}) </if>
		    <if test="beginStartDate != null"> and T1.STARTDT &gt;= #{beginStartDate,jdbcType=VARCHAR} </if>
		    <if test="endStartDate != null"> and T1.STARTDT &lt;= #{endStartDate,jdbcType=VARCHAR} </if>
		    <if test="areaf != null">
		    	and exists (select 1 from  CM_CONSULTANT_EXP_ORG a,CM_CONSULTANT_EXP b
							where a.userid = b.userid
								and b.CHECKFLAG = '2'
							   AND b.WORKTYPE != '2'
							   and  a.orgcode = T1.OUTLETCODE
							   and a.userid = #{areaf,jdbcType=VARCHAR})
		    </if>
		    <if test="orgcode != null"> 
		     	and ( T1.TEAMCODE = #{orgcode} OR
		     		T1.OUTLETCODE in (
		     			SELECT ORGCODE
						  FROM HB_ORGANIZATION HO
						 WHERE HO.STATUS = '0'
						 START WITH HO.ORGCODE = #{orgcode}
						CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		     		)
		     	)
		     </if>
		     <if test="userno != null"> and T1.USERNO = #{userno,jdbcType=VARCHAR} </if> 
		     <if test="islr == '1'.toString()"> and T1.USERNO is not null </if>
		     <if test="islr == '0'.toString()"> and T1.USERNO is null </if>
			  <if test="userlevel != null ">
				  and exists( (select regexp_substr( T1.CURMONTHLEVEL, '[^,]+', 1, level)
				  from dual
				  connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null )
				  intersect
				  (select regexp_substr( #{userlevel,jdbcType=VARCHAR}, '[^|]+', 1, level)
				  from dual
				  connect by regexp_substr(#{userlevel,jdbcType=VARCHAR}, '[^|]+', 1, level) is not null ) )			  </if>

		     <if test="beginRegulardt != null"> and T1.REGULARDT &gt;= #{beginRegulardt,jdbcType=VARCHAR} </if>
		     <if test="endRegulardt != null"> and T1.REGULARDT &lt;= #{endRegulardt,jdbcType=VARCHAR} </if>
		     <if test="beginQuitdt != null"> and T1.QUITDT &gt;= #{beginQuitdt,jdbcType=VARCHAR} </if>
		     <if test="endQuitdt != null"> and T1.QUITDT &lt;= #{endQuitdt,jdbcType=VARCHAR} </if>
			  <if test="workstate != null"> and T1.WORKSTATE = #{workstate,jdbcType=VARCHAR} </if>
			  <if test="checkflag != null"> and T1.CHECKFLAG = #{checkflag,jdbcType=VARCHAR} </if>

		  <if test="beginNexttestDate != null"> and T1.NEXTTESTDATE &gt;= #{beginNexttestDate,jdbcType=VARCHAR} </if>
		  <if test="endNexttestDate != null"> and T1.NEXTTESTDATE &lt;= #{endNexttestDate,jdbcType=VARCHAR} </if>

		  <if test="provCode != null"> and T1.PROVCODE = #{provCode,jdbcType=VARCHAR} </if>
		  <if test="cityCode != null"> and T1.CITYCODE = #{cityCode,jdbcType=VARCHAR} </if>

		  <if test="source!=null">and T1.source like '%'|| #{source} ||'%'</if>
		  <if test="subpositions != null"> and T1.SUBPOSITIONS = #{subpositions,jdbcType=VARCHAR} </if>
		  <if test="recommendtype != null"> and T1.RECOMMENDTYPE = #{recommendtype,jdbcType=VARCHAR} </if>
		  <if test="beisenid != null"> and T1.BEISENID = #{beisenid,jdbcType=VARCHAR} </if>

				<if test="userIds != null">
					and userid in
					<foreach collection="userIds" separator="," open="(" close=")" item="item">
						#{item}
					</foreach>
				</if>
		</where>
		  ORDER BY  T1.STARTDT desc nulls last
	  </select>
	  
	  <select id="listCmConsultantExpByPage" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp" useCache="false">
	  	select * from
	  	(
	  	SELECT  T1.USERID as conscode,
			  T1.CONSNAME,
			  T1.EMAIL,
			  T1.OUTLETCODE,
			  T1.TEAMCODE,
			  T1.USERID,
			  T1.USERNO,
			  T1.PROVCODE,
			  T1.CITYCODE,
		       T1.GENDER,
		       T1.BIRTHDAY,
		       T1.EDULEVEL,
		       T1.WORKTYPE,
		       T1.WORKSTATE,
		       T1.CURMONTHLEVEL,
		       T1.STARTDT,
		       T1.STARTLEVEL,
		       T1.SALARY,
		       T1.PROBATIONENDDT,
		       T1.REGULARDT,
		       T1.REGULARLEVEL,
		       T1.REGULARSALARY,
		       T1.QUITDT,
		       T1.QUITLEVEL,
		       T1.QUITSALARY,
		       T1.QUITREASON,
		       T1.SERVINGAGE,
		       T1.CHECKFLAG,
		       T1.CHECKOR,
		       T1.CREATOR,
		       T1.CREATDT,
		       T1.MODOR,
		       T1.MODDT,
		       t1.jjcardno,
			   t1.attachtype,
			   t1.background,
			   t1.source,
			   t1.beforepositiontype,
			   t1.beforepositionage,
			   t1.recruit,
			   t1.recommend,
			   t1.recommenduserno,
			   t1.recommendtype,
			   t1.remark,
			   T1.SUBPOSITIONS,
			   T1.NEXTTESTDATE,
			   T1.PROBATIONRESULT3M,
			   T1.PROBATIONRESULT6M,
			   T1.CURMONTHSALARY,
			   T1.QUITINFO,
			   T1.PROMOTEDATE,
			   T1.BXCOMMISSIONWAY,
		  	   T1.BEISENID,
		  	   T1.center_org as centerOrg,
		  	   T1.PROBATION_RESULT_12M as probationResult12M,
		  	   T1.ADJUST_SERVING_MONTH as adjustServingMonth,
		  	   T1.adjust_manage_serving_month as adjustManageServingMonth,
		  	   t2.constlevel,
		  	   T1.DT3M as dt3m,
		  		
		  	   T1.DT12M as dt12m,
		  	   T1.NEXT_TEST_PERIOD as nextTestPeriod,
		  	   T1.PROBATION_SALARY_3M as probationSalary3m,
		  	   T1.SALARY_12M as salary12m,
		  	   T1.JOIN_RANK as joinRank,
		  	   T1.PROBATION_RANK_3M as probationRank3m,
		  	   T1.REGULAR_RANK as regularRank,
		  	   T1.RANK_12M as rank12m,
		  	   T1.JOIN_SSB as joinSsb,
		  	   T1.PROBATION_SSB_3M as probationSsb3m,
		  	   T1.REGULAR_SSB as regularSsb,
		  	   T1.SSB_12M as ssb12m,
		  	   T1.PROBATIONLEVEL_3M as probationLevel3m,
		  	   T1.TESTLEVEL_12M as testLevel12m,
			   T1.COUNTY_CODE countyCode,
			   T1.IN_TRANSIT_REMARK as inTransitRemark
		  FROM CM_CONSULTANT_EXP T1
		   left join (SELECT userid, max(constlevel) as constlevel
						  FROM (select userid,
									   startdt,
									   regexp_substr(CURMONTHLEVEL, '[^,]+', 1, level) curmonthlevel
								from CM_CONSULTANT_EXP
									connect by level &lt;= regexp_count(curmonthlevel, ',') + 1
		  and userid = prior userid
		  and prior dbms_random.value is not null) consultexp
		  left join hb_constant hbconstant
		  on consultexp.curmonthlevel = hbconstant.constcode
		  and hbconstant.typecode = 'hrpositionslevel'
		  group by userid) t2
		  on t1.userid = t2.userid
		    <where>
		    <if test="param.conscode != null"> and T1.USERID = #{param.conscode,jdbcType=VARCHAR} </if>
		    <if test="param.consname != null"> and T1.CONSNAME like '%'||#{param.consname ,jdbcType=VARCHAR}||'%' </if>
		  	<if test="param.worktype != null and param.worktype !=''"> and T1.WORKTYPE in (${param.worktype}) </if>
		    <if test="param.beginStartDate != null"> and T1.STARTDT &gt;= #{param.beginStartDate,jdbcType=VARCHAR} </if>
		    <if test="param.endStartDate != null"> and T1.STARTDT &lt;= #{param.endStartDate,jdbcType=VARCHAR} </if>
		    <if test="param.areaf != null">
		    	and exists (select 1 from  CM_CONSULTANT_EXP_ORG a,CM_CONSULTANT_EXP b
							where a.userid = b.userid
								and b.CHECKFLAG = '2'
							   AND b.WORKTYPE != '2'
							   and  a.orgcode = T1.OUTLETCODE
							   and a.userid = #{param.areaf,jdbcType=VARCHAR})
		    </if>
		    <if test="param.orgcode != null">
		     	and ( T1.TEAMCODE = #{param.orgcode} OR
		     		T1.OUTLETCODE in (
		     			SELECT ORGCODE
						  FROM HB_ORGANIZATION HO
						 WHERE HO.STATUS = '0'
						 START WITH HO.ORGCODE = #{param.orgcode}
						CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		     		)
		     	)
		     </if>
		     <if test="param.userno != null"> and T1.USERNO = #{param.userno,jdbcType=VARCHAR} </if>
		     <if test="param.islr == '1'.toString()"> and T1.USERNO is not null </if>
		     <if test="param.islr == '0'.toString()"> and T1.USERNO is null </if> 
			  <if test="param.userlevel != null ">
				  and exists( (select regexp_substr( T1.CURMONTHLEVEL, '[^,]+', 1, level)
				  from dual
				  connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null )
				  intersect
				  (select regexp_substr( #{param.userlevel,jdbcType=VARCHAR}, '[^|]+', 1, level)
				  from dual
				  connect by regexp_substr(#{param.userlevel,jdbcType=VARCHAR}, '[^|]+', 1, level) is not null ) )
			  </if>

		  	<if test="param.beginRegulardt != null"> and T1.REGULARDT &gt;= #{param.beginRegulardt,jdbcType=VARCHAR} </if>
		     <if test="param.endRegulardt != null"> and T1.REGULARDT &lt;= #{param.endRegulardt,jdbcType=VARCHAR} </if>
		     <if test="param.beginQuitdt != null"> and T1.QUITDT &gt;= #{param.beginQuitdt,jdbcType=VARCHAR} </if>
		     <if test="param.endQuitdt != null"> and T1.QUITDT &lt;= #{param.endQuitdt,jdbcType=VARCHAR} </if>

		     <if test="param.workstate != null"> and T1.WORKSTATE = #{param.workstate,jdbcType=VARCHAR} </if>
		     <if test="param.checkflag != null"> and T1.CHECKFLAG = #{param.checkflag,jdbcType=VARCHAR} </if>

		  <if test="param.beginNexttestDate != null"> and T1.NEXTTESTDATE &gt;= #{param.beginNexttestDate,jdbcType=VARCHAR} </if>
		  <if test="param.endNexttestDate != null"> and T1.NEXTTESTDATE &lt;= #{param.endNexttestDate,jdbcType=VARCHAR} </if>

		  <if test="param.provCode != null"> and T1.PROVCODE = #{param.provCode,jdbcType=VARCHAR} </if>
		  <if test="param.cityCode != null"> and T1.CITYCODE = #{param.cityCode,jdbcType=VARCHAR} </if>

		  <if test="param.source!=null">and T1.source like '%'|| #{param.source} ||'%'</if>
		  <if test="param.subpositions != null"> and T1.SUBPOSITIONS = #{param.subpositions,jdbcType=VARCHAR} </if>
		  <if test="param.recommendtype != null"> and T1.RECOMMENDTYPE = #{param.recommendtype,jdbcType=VARCHAR} </if>
		  <if test="param.beisenid != null"> and T1.BEISENID = #{param.beisenid,jdbcType=VARCHAR} </if>
		     )
		  	<if test="param.sort != null and param.order != null" >
				<choose>
					<when test="param.sort == 'curmonthlevel'.toString()">
						ORDER BY constlevel ${param.order} nulls last
					</when>
					<otherwise>
						ORDER BY ${param.sort} ${param.order} nulls last
					</otherwise>
				</choose>
			</if>

             <if test="param.sort == null or param.order == null" > ORDER BY conscode asc nulls last</if>
			</where>
	  </select>
	  
	  <select id="listTeamLeadersByTeamCode" parameterType="Map" resultType="String" useCache="false">
	  	SELECT DISTINCT T1.CONSNAME
		  FROM CM_CONSULTANT_EXP T1
		 WHERE T1.CHECKFLAG = '2'
		   AND T1.TEAMCODE = #{teamcode}
		   and exists( (select regexp_substr( T1.CURMONTHLEVEL, '[^,]+', 1, level)
			   from dual
			   connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null )
			   intersect
			   (select regexp_substr( #{userlevel}, '[^|]+', 1, level)
			   from dual
			   connect by regexp_substr(#{userlevel}, '[^|]+', 1, level) is not null ) )
		   AND T1.WORKTYPE != '2'
	  </select>

	  <select id="listLeadersByOrgCodeAndLevel" parameterType="Map" resultType="String" useCache="false">
	  	SELECT DISTINCT T1.CONSNAME
		  FROM  CM_CONSULTANT_EXP T1
		 WHERE T1.CHECKFLAG = '2'
		   AND T1.WORKTYPE != '2'
		   AND T1.OUTLETCODE = #{orgcode}
		   <if test="userlevel != null">

			   and exists( (select regexp_substr( T1.CURMONTHLEVEL, '[^,]+', 1, level)
			   from dual
			   connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null )
			   intersect
			   (select regexp_substr( #{userlevel}, '[^|]+', 1, level)
			   from dual
			   connect by regexp_substr(#{userlevel}, '[^|]+', 1, level) is not null ) )

		   </if>
		   
	  </select>
	  
	  <select id="listLeadersByCityCode" parameterType="Map" resultType="String" useCache="false">
	  	SELECT DISTINCT T1.CONSNAME
		  FROM CM_CONSULTANT_EXP T1
		 WHERE T1.CHECKFLAG = '2'
		   AND T1.CITYCODE = #{citycode}
		   AND T1.CURMONTHLEVEL = '24'
		   AND T1.WORKTYPE != '2'
	  </select>

	<update id="syncOrgInfoFromCmConsultantExpToCmConsultant" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
    	update CM_CONSULTANT
	    set
		teamcode=#{teamcode,jdbcType=VARCHAR},
		outletcode=#{outletcode,jdbcType=VARCHAR},
		MODIFIER=#{modor,jdbcType=VARCHAR},
		MODDT=to_char(sysdate,'YYYYMMDD')
	    where  CONSCODE = #{userid}
    </update>

	<select id="consultantExpLoader" parameterType="map" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp" useCache="false">
	  	select nvl(consname,'') as consname,nvl(userno,'') as userno from CM_CONSULTANT_EXP  where CONSNAME like '%'||#{searchParam}||'%' or USERNO like '%'||#{searchParam}||'%'
	</select>
	<select id="ifOrgCodeIsDept" resultType="java.lang.String">
			select a.orgtype from hb_organization a where a.orgcode=#{orgcode}
	</select>
	<select id="getssOrgInfosByConscode" resultType="java.util.HashMap">
		select a.orgcode,b.orgname from CM_CONSULTANT_EXP_ORG a,hb_organization b
			where a.orgcode = b.orgcode
			and a.userid=#{conscode}
	</select>
    <delete id="delCmConsultantExpOrgByConscode">
		delete from CM_CONSULTANT_EXP_ORG a where a.userid = #{userid}
	</delete>
	<select id="listFLeadersByOrgCode" resultType="java.lang.String">
		select b.CONSNAME from  CM_CONSULTANT_EXP_ORG a,CM_CONSULTANT_EXP b
		where a.userid = b.userid
			and b.CHECKFLAG = '2'
		   AND b.WORKTYPE != '2'
		   and  a.orgcode =#{orgcode}
	</select>
	<select id="getCurmonthlevelNamesByCurmonthlevels" resultType="java.util.HashMap">
		select a.constcode,a.constdesc from Hb_Constant a where a.typecode='hrpositionslevel'
		and a.constcode in
		<foreach collection="curmonthlevels" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		order by decode(a.constcode
		<foreach collection="curmonthlevels" index="index" item="item" open="," separator="," close=",'')">
			#{item},#{index}
		</foreach>
	</select>

	<update id="batchUpdateCmConsultantExp" parameterType="map">
		update CM_CONSULTANT_EXP
		<set>
			<if test="dto.worktype != null">
				worktype=#{dto.worktype,jdbcType=VARCHAR},
			</if>
			<if test="dto.workstate != null">
				workstate = #{dto.workstate,jdbcType=VARCHAR},
			</if>
			<if test="dto.probationresult3m != null">
				probationresult3m=#{dto.probationresult3m,jdbcType=VARCHAR},
			</if>
			<if test="dto.probationresult6m != null">
				probationresult6m=#{dto.probationresult6m,jdbcType=VARCHAR},
			</if>
			<if test="dto.nexttestdate != null">
				nexttestdate=#{dto.nexttestdate,jdbcType=VARCHAR},
			</if>
			<if test="dto.regulardt != null">
				regulardt=#{dto.regulardt,jdbcType=VARCHAR},
			</if>
			<if test="dto.regularlevel != null">
				regularlevel=#{dto.regularlevel,jdbcType=VARCHAR},
			</if>
			<if test="dto.quitdt != null">
				quitdt=#{dto.quitdt,jdbcType=VARCHAR},
			</if>
			<if test="dto.quitlevel != null">
				quitlevel=#{dto.quitlevel,jdbcType=VARCHAR},
			</if>
			<if test="dto.quitreason != null">
				quitreason=#{dto.quitreason,jdbcType=VARCHAR},
			</if>
			<if test="dto.quitInfo != null">
				quitInfo=#{dto.quitInfo,jdbcType=VARCHAR},
			</if>
			<if test="dto.curmonthlevel != null">
				curmonthlevel = #{dto.curmonthlevel,jdbcType=VARCHAR},
			</if>
			<if test="dto.curmonthsalary != null">
				curmonthsalary = #{dto.curmonthsalary,jdbcType=VARCHAR},
			</if>
			<if test="dto.promotedate != null">
				promotedate = #{dto.promotedate,jdbcType=VARCHAR},
			</if>
			<if test="dto.bxcommissionway != null">
				bxcommissionway = #{dto.bxcommissionway,jdbcType=VARCHAR},
			</if>
			modor = #{dto.modor,jdbcType=VARCHAR},
			moddt = sysdate
		</set>
		where USERID in
		<foreach collection="ids" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</update>
	
	<select id="listAllAreafLeaders" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp" useCache="false">
		SELECT DISTINCT B.USERID CONSCODE, B.CONSNAME
		  FROM CM_CONSULTANT_EXP_ORG A, CM_CONSULTANT_EXP B
		 WHERE A.USERID = B.USERID
		   AND B.CHECKFLAG = '2'
		   AND B.WORKTYPE != '2'
		 ORDER BY NLSSORT(B.CONSNAME, 'NLS_SORT = SCHINESE_PINYIN_M')
	</select>

	<select id="selectUserCountByUserNo" resultType="int">
		select count(1) from CM_CONSULTANT_EXP
		where USERNO = #{exp.userno,jdbcType=VARCHAR}
		and userno is not null
		<if test="userid != null and userid != ''">
			and userid != #{userid,jdbcType=VARCHAR}
		</if>
	</select>

	<insert id="mergeCmConsultantExpByBeisen" parameterType="list">
		merge into CM_CONSULTANT_EXP t1
			using (
				<foreach collection="list" item="item" index="index" open="(" close=")" separator="union all">
					select
						#{item.userno,jdbcType=VARCHAR} as Userno,
						#{item.beisenid,jdbcType=VARCHAR} as Beisenid,
						#{item.consname,jdbcType=VARCHAR} as Consname,
						#{item.countyCode,jdbcType=VARCHAR} as County_Code,
						#{item.email,jdbcType=VARCHAR} as Email,
						#{item.outletcode,jdbcType=VARCHAR} as Outletcode,
						#{item.gender,jdbcType=VARCHAR} as Gender,
						#{item.birthday,jdbcType=VARCHAR} as Birthday,
						#{item.edulevel,jdbcType=VARCHAR} as Edulevel,
						#{item.centerOrg,jdbcType=VARCHAR} as Center_Org,
						#{item.worktype,jdbcType=VARCHAR} as Worktype,
						#{item.workstate,jdbcType=VARCHAR} as Workstate,
						#{item.curmonthlevel,jdbcType=VARCHAR} as Curmonthlevel,
						#{item.curmonthsalary,jdbcType=VARCHAR} as Curmonthsalary,
						#{item.subpositions,jdbcType=VARCHAR} as Subpositions,
						#{item.startdt,jdbcType=VARCHAR} as Startdt,
						#{item.startlevel,jdbcType=VARCHAR} as Startlevel,
						#{item.joinRank,jdbcType=VARCHAR} as Join_Rank,
						#{item.salary,jdbcType=VARCHAR} as Salary,
						#{item.joinSsb,jdbcType=VARCHAR} as Join_Ssb,
						#{item.dt3m,jdbcType=VARCHAR} as Dt3m,
						#{item.regulardt,jdbcType=VARCHAR} as Regulardt,
						#{item.regularlevel,jdbcType=VARCHAR} as Regularlevel,
						#{item.regularRank,jdbcType=VARCHAR} as Regular_Rank,
						#{item.regularsalary,jdbcType=VARCHAR} as Regularsalary,
						#{item.regularSsb,jdbcType=VARCHAR} as Regular_Ssb,
						#{item.dt12m,jdbcType=VARCHAR} as Dt12m,
						#{item.quitdt,jdbcType=VARCHAR} as Quitdt,
						#{item.quitlevel,jdbcType=VARCHAR} as Quitlevel,
						#{item.quitsalary,jdbcType=VARCHAR} as Quitsalary,
						#{item.servingage,jdbcType=VARCHAR} as Servingage,
						#{item.jjcardno,jdbcType=VARCHAR} as Jjcardno,
						#{item.attachtype,jdbcType=VARCHAR} as Attachtype,
						#{item.background,jdbcType=VARCHAR} as Background,
						#{item.source,jdbcType=VARCHAR} as Source,
						#{item.beforepositionage,jdbcType=VARCHAR} as Beforepositionage,
						#{item.recruit,jdbcType=VARCHAR} as Recruit,
						#{item.recommend,jdbcType=VARCHAR} as Recommend,
						#{item.recommenduserno,jdbcType=VARCHAR} as Recommenduserno,
						#{item.recommendtype,jdbcType=VARCHAR} as Recommendtype
					   from dual
				</foreach>
			)t2
			on (t1.Userno=t2.Userno)
			when matched then
				update set
					t1.Beisenid          = t2.Beisenid
					,t1.Consname          = t2.Consname
					,t1.County_Code       = t2.County_Code
					,t1.Email             = t2.Email
					,t1.Outletcode        = t2.Outletcode
					,t1.Gender            = t2.Gender
					,t1.Birthday          = t2.Birthday
					,t1.Edulevel          = t2.Edulevel
					,t1.Center_Org        = t2.Center_Org
					,t1.Worktype          = t2.Worktype
					,t1.Workstate         = t2.Workstate
					,t1.Curmonthlevel     = t2.Curmonthlevel
					,t1.Curmonthsalary    = t2.Curmonthsalary
					,t1.Subpositions      = t2.Subpositions
					,t1.Startdt           = t2.Startdt
					,t1.Startlevel        = t2.Startlevel
					,t1.Join_Rank         = t2.Join_Rank
					,t1.Salary            = t2.Salary
					,t1.Join_Ssb          = t2.Join_Ssb
					,t1.Dt3m              = t2.Dt3m
					,t1.Regulardt         = t2.Regulardt
					,t1.Regularlevel      = t2.Regularlevel
					,t1.Regular_Rank      = t2.Regular_Rank
					,t1.Regularsalary     = t2.Regularsalary
					,t1.Regular_Ssb       = t2.Regular_Ssb
					,t1.Dt12m             = t2.Dt12m
					,t1.Quitdt            = t2.Quitdt
					,t1.Quitlevel         = t2.Quitlevel
					,t1.Quitsalary        = t2.Quitsalary
					,t1.Servingage        = t2.Servingage
					,t1.Jjcardno          = t2.Jjcardno
					,t1.Attachtype        = t2.Attachtype
					,t1.Background        = t2.Background
					,t1.Source            = t2.Source
					,t1.Beforepositionage = t2.Beforepositionage
					,t1.Recruit           = t2.Recruit
					,t1.Recommend         = t2.Recommend
					,t1.Recommenduserno   = t2.Recommenduserno
					,t1.Recommendtype     = t2.Recommendtype
			when not matched then
				insert (t1.Userno,t1.Beisenid,t1.Consname,t1.County_Code,t1.Email,t1.Outletcode,t1.Gender,t1.Birthday,
						t1.Edulevel,t1.Center_Org,t1.Worktype,t1.Workstate,t1.Curmonthlevel,t1.Curmonthsalary,t1.Subpositions,
						t1.Startdt,t1.Startlevel,t1.Join_Rank,t1.Salary,t1.Join_Ssb,t1.Dt3m,t1.Regulardt,t1.Regularlevel,t1.Regular_Rank,
						t1.Regularsalary,t1.Regular_Ssb,t1.Dt12m,t1.Quitdt,t1.Quitlevel,t1.Quitsalary,t1.Servingage,t1.Jjcardno,
						t1.Attachtype,t1.Background,t1.Source,t1.Beforepositionage,t1.Recruit,t1.Recommend,t1.Recommenduserno,t1.Recommendtype)
				values(t2.Userno,t2.Beisenid,t2.Consname,t2.County_Code,t2.Email,t2.Outletcode,t2.Gender,t2.Birthday,
					   t2.Edulevel,t2.Center_Org,t2.Worktype,t2.Workstate,t2.Curmonthlevel,t2.Curmonthsalary,t2.Subpositions,
					   t2.Startdt,t2.Startlevel,t2.Join_Rank,t2.Salary,t2.Join_Ssb,t2.Dt3m,t2.Regulardt,t2.Regularlevel,t2.Regular_Rank,
					   t2.Regularsalary,t2.Regular_Ssb,t2.Dt12m,t2.Quitdt,t2.Quitlevel,t2.Quitsalary,t2.Servingage,t2.Jjcardno,
					   t2.Attachtype,t2.Background,t2.Source,t2.Beforepositionage,t2.Recruit,t2.Recommend,t2.Recommenduserno,t2.Recommendtype)
	</insert>

	<select id="listCmConsultantExpByUserNo" parameterType="List" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
		SELECT  T1.USERID as conscode,
		T1.CONSNAME,
		T1.EMAIL,
		T1.OUTLETCODE,
		T1.TEAMCODE,
		T1.USERID,
		T1.USERNO,
		T1.PROVCODE,
		T1.CITYCODE,
		T1.GENDER,
		T1.BIRTHDAY,
		T1.EDULEVEL,
		T1.WORKTYPE,
		T1.WORKSTATE,
		T1.CURMONTHLEVEL,
		T1.STARTDT,
		T1.STARTLEVEL,
		T1.SALARY,
		T1.PROBATIONENDDT,
		T1.REGULARDT,
		T1.REGULARLEVEL,
		T1.REGULARSALARY,
		T1.QUITDT,
		T1.QUITLEVEL,
		T1.QUITSALARY,
		T1.QUITREASON,
		T1.SERVINGAGE,
		T1.CHECKFLAG,
		T1.CHECKOR,
		T1.CREATOR,
		T1.CREATDT,
		T1.MODOR,
		T1.MODDT,
		t1.jjcardno,
		t1.attachtype,
		t1.background,
		t1.source,
		t1.beforepositiontype,
		t1.beforepositionage,
		t1.recruit,
		t1.recommend,
		t1.recommenduserno,
		t1.recommendtype,
		t1.remark,
		T1.SUBPOSITIONS,
		T1.NEXTTESTDATE,
		T1.PROBATIONRESULT3M,
		T1.PROBATIONRESULT6M,
		T1.CURMONTHSALARY,
		T1.QUITINFO,
		T1.PROMOTEDATE,
		T1.BXCOMMISSIONWAY,
		T1.BEISENID,
		T1.center_org as centerOrg,
		T1.PROBATION_RESULT_12M as probationResult12M,
		T1.ADJUST_SERVING_MONTH as adjustServingMonth,
		T1.adjust_manage_serving_month as adjustManageServingMonth,
		t2.constlevel,
		T1.DT3M as dt3m,
		T1.DT12M as dt12m,
		T1.NEXT_TEST_PERIOD as nextTestPeriod,
		T1.PROBATION_SALARY_3M as probationSalary3m,
		T1.SALARY_12M as salary12m,
		T1.JOIN_RANK as joinRank,
		T1.PROBATION_RANK_3M as probationRank3m,
		T1.REGULAR_RANK as regularRank,
		T1.RANK_12M as rank12m,
		T1.JOIN_SSB as joinSsb,
		T1.PROBATION_SSB_3M as probationSsb3m,
		T1.REGULAR_SSB as regularSsb,
		T1.SSB_12M as ssb12m,
		T1.PROBATIONLEVEL_3M as probationLevel3m,
		T1.TESTLEVEL_12M as testLevel12m,
		T1.COUNTY_CODE countyCode,
		T1.IN_TRANSIT_REMARK as inTransitRemark
		FROM CM_CONSULTANT_EXP T1
		left join (SELECT userid, max(constlevel) as constlevel
		FROM (select userid,
		startdt,
		regexp_substr(CURMONTHLEVEL, '[^,]+', 1, level) curmonthlevel
		from CM_CONSULTANT_EXP
		connect by level &lt;= regexp_count(curmonthlevel, ',') + 1
		and userid = prior userid
		and prior dbms_random.value is not null) consultexp
		left join hb_constant hbconstant
		on consultexp.curmonthlevel = hbconstant.constcode
		and hbconstant.typecode = 'hrpositionslevel'
		group by userid) t2
		on t1.userid = t2.userid
		where 1=1
		<if test="userNos != null and userNos.size()>0 ">
			and t1.userNo in
			<foreach collection="userNos" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="listCmConsultantExpByReportUserId" parameterType="List" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
		SELECT T1.USERID as conscode,
		T1.CONSNAME,
		T1.EMAIL,
		T1.OUTLETCODE,
		T1.TEAMCODE,
		T1.USERID,
		T1.USERNO,
		T1.PROVCODE,
		T1.CITYCODE,
		T1.GENDER,
		T1.BIRTHDAY,
		T1.EDULEVEL,
		T1.WORKTYPE,
		T1.WORKSTATE,
		T1.CURMONTHLEVEL,
		T1.STARTDT,
		T1.STARTLEVEL,
		T1.SALARY,
		T1.PROBATIONENDDT,
		T1.REGULARDT,
		T1.REGULARLEVEL,
		T1.REGULARSALARY,
		T1.QUITDT,
		T1.QUITLEVEL,
		T1.QUITSALARY,
		T1.QUITREASON,
		T1.SERVINGAGE,
		T1.CHECKFLAG,
		T1.CHECKOR,
		T1.CREATOR,
		T1.MODOR,
		t1.jjcardno,
		t1.attachtype,
		t1.background,
		t1.source,
		t1.beforepositiontype,
		t1.beforepositionage,
		t1.recruit,
		t1.recommend,
		t1.recommenduserno,
		t1.recommendtype,
		t1.remark,
		T1.SUBPOSITIONS,
		T1.NEXTTESTDATE,
		T1.PROBATIONRESULT3M,
		T1.PROBATIONRESULT6M,
		T1.CURMONTHSALARY,
		T1.QUITINFO,
		T1.PROMOTEDATE,
		T1.BXCOMMISSIONWAY,
		T1.BEISENID,
		T1.center_org as centerOrg,
		T1.PROBATION_RESULT_12M as probationResult12M,
		T1.ADJUST_SERVING_MONTH as adjustServingMonth,
		T1.adjust_manage_serving_month as adjustManageServingMonth,
		t1.curmonthlevel,
		T1.DT3M as dt3m,
		T1.DT12M as dt12m,
		T1.NEXT_TEST_PERIOD as nextTestPeriod,
		T1.PROBATION_SALARY_3M as probationSalary3m,
		T1.SALARY_12M as salary12m,
		T1.JOIN_RANK as joinRank,
		T1.PROBATION_RANK_3M as probationRank3m,
		T1.REGULAR_RANK as regularRank,
		T1.RANK_12M as rank12m,
		T1.JOIN_SSB as joinSsb,
		T1.PROBATION_SSB_3M as probationSsb3m,
		T1.REGULAR_SSB as regularSsb,
		T1.SSB_12M as ssb12m,
		T1.PROBATIONLEVEL_3M as probationLevel3m,
		T1.TESTLEVEL_12M as testLevel12m,
		T1.COUNTY_CODE countyCode,
		T1.IN_TRANSIT_REMARK as inTransitRemark
		FROM CM_CONSULTANT_EXP T1
		where 1=1
		<if test="userIds != null and userIds.size()>0 ">
			and t1.userid in
			<foreach collection="userIds" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	</select>


	<select id="positionsLevelAndUserLevel" resultType="com.howbuy.crm.hb.domain.system.HbConstantExpBO">
		SELECT t1.constcode positionsLevelCode,
		       t1.constdesc positionsLevelName,
		       t2.constcode userLevelCode,
		       t2.constdesc userLevelName
		FROM HB_CONSTANT t1
		left join HB_CONSTANT t2 on t1.constext2 = t2.constcode and t2.typecode='userlevel'
		where t1.typecode='hrpositionslevel'
	</select>

	<select id="getConsCodeInTeamCodeOrOutletCode" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
		select t1.teamcode, t1.outletcode, t1.consname, t1.curmonthlevel
		from cm_consultant_exp t1
		where t1.checkflag = '2'
		and t1.worktype != '2'
	</select>

	<select id="getConsNameInOrgCode" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExp">
		select a.orgcode, b.consname
		from cm_consultant_exp_org a, cm_consultant_exp b
		where a.userid = b.userid
		and b.checkflag = '2'
		and b.worktype != '2'
	</select>

</mapper>