## 测试的类
> com.howbuy.crm.hb.service.custinfo.impl.ConscustServiceImpl
## 测试的方法 
> getOrgCodeByTopGd(String topGd, String outletCode, String teamCode, String loginOrgCode)

## 分支伪代码
``` java
验证过程 {
    if (深度配置 == 11) {
        返回 组织结构范围为:0-全部
    }
    if (深度配置 == 12) {
       返回 组织结构范围为:0-全部
    }    
    if (深度配置 == 13) {
        返回 组织结构范围为所属架构及子部门客户
    }
    if (深度配置 == 14) {
        返回 组织结构范围为所属团队下客户
    }
    if (深度配置 == 15) {
        返回 组织结构范围自己客户
    }
}


## 测试案例
1、模拟广度配置值的为：11-所有客户（包括未分配）：则查看的组织结构范围为:0-全部
### test01
2、模拟广度配置值的为：12-所有客户（不包括未分配）：则查看的组织结构范围为:0-全部
### test02
3、模拟广度配置值的为：13-所属组织架构及下属子部门客户：则查看的组织结构范围为所属架构及子部门客户
### test03
4、模拟广度配置值的为：14-所属团队客户：则查看的组织结构范围为所属团队下客户
### test04
5、模拟广度配置值的为：15-分配给自己客户：则查看的组织结构范围自己客户
### test05