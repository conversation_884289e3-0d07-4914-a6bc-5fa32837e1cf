<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CrmWageAccountingDateMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>

    <update id="createOrUpdateAccountingDate" parameterType="map">
        DECLARE
        UPDATOR_V VARCHAR2(32);
        accountingDate_V VARCHAR2(8);
        BEGIN
        UPDATOR_V:= #{updator,jdbcType=VARCHAR};
        accountingDate_V:= #{accountingDate,jdbcType=VARCHAR} ;
        <foreach item="item" index="index" collection="ids">
            update CM_WAGE_ACCOUNTING_DATE A
            set UPDATE_STATUS ='0',
            updator = UPDATOR_V,
            update_time = sysdate
            where A.ID= #{item,jdbcType=VARCHAR};

            INSERT INTO CM_WAGE_ACCOUNTING_DATE (ID, ACCOUNTING_DATE, UPDATE_STATUS ,CREATOR)
            VALUES (#{item,jdbcType=VARCHAR},accountingDate_V,'1',UPDATOR_V);

        </foreach>
        commit;
        END;
    </update>

    <select id="selectAccountingDateByPage" parameterType="Map" resultType="Map" useCache="false">
        select TXX.*
        from (select A.ID,
        A.EXPECTTRADEDT,
        case
        when B.ACCOUNTING_DATE is null then
        A.EXPECTTRADEDT
        else
        B.ACCOUNTING_DATE
        end as ACCOUNTING_DATE,
        A.CONSCUSTNO,
        A.CONSCUSTNAME,
        A.CREATOR,
        A.PCODE,
        CC.PNAME,
        A.TRADE_TYPE,
        C.OUTLETCODE，
        C.CONSNAME
        from CM_PREBOOKPRODUCTINFO A
        left join CM_WAGE_ACCOUNTING_DATE B
        ON A.id = B.ID and B.UPDATE_STATUS = '1'
        left join CM_CONSULTANT C
        on A.CREATOR = C.CONSCODE
        LEFT JOIN CM_PRODUCTINFO CC
        ON A.PCODE = CC.PCODE AND CC.RECSTAT = '0'
        where (A.TRADE_TYPE = '1' OR A.TRADE_TYPE = '2') AND A.PAYSTATE='3'
        AND A.PREBOOKSTATE='2' and A.RECSTAT = '0'
        AND NOT EXISTS (SELECT 1 FROM cm_cust_transfervol t WHERE t.fcclpreid =A.ID and t.fcclflag='1')
        AND NOT EXISTS (SELECT 1 FROM cm_zt_orderinfo t WHERE t.mbusicode = '1120' and t.appointmentdealno = A.ID AND A.tradestate = '0')
        <if test="param.orgcode!=null">
            and A.CREATOR in ( select cct.conscode
            from CM_CONSULTANT CCT
            where exists(
            select 1
            from (select orgcode
            from HB_ORGANIZATION
            start with orgcode = #{param.orgcode}
            connect by prior orgcode = parentorgcode) dat
            where CCT.OUTLETCODE = dat.orgcode
            OR CCt.TEAMCODE = dat.ORGCODE)
            <if test="param.conscode!=null">
            and cct.CONSCODE = #{param.conscode}
            </if>
            )
        </if>



        AND NOT EXISTS (SELECT 1 FROM CM_NOSTAT_REPORT CNR WHERE CNR.PREID = A.ID)
        <if test="param.pcode!=null">
            AND A.PCODE = #{param.pcode}
        </if>
        <if test="param.startDate!=null">
            AND A.EXPECTTRADEDT <![CDATA[ >= ]]> #{param.startDate}
        </if>
        <if test="param.endDate!=null">
            AND A.EXPECTTRADEDT <![CDATA[ <= ]]> #{param.endDate}
        </if>
        <if test="param.custname != null">
            AND A.CONSCUSTNAME like '%'||#{param.custname}||'%'
        </if>
        ) TXX
        where 1 = 1
        <if test="param.accstartDate!=null">
            AND ACCOUNTING_DATE <![CDATA[ >= ]]> #{param.accstartDate}
        </if>
        <if test="param.accendDate!=null">
            AND ACCOUNTING_DATE <![CDATA[ <= ]]> #{param.accendDate}
        </if>
    </select>

    <select id="selectAccountingDate" parameterType="Map" resultType="Map" useCache="false">
        select TXX.*
        from (select A.ID,
        A.EXPECTTRADEDT,
        case
        when B.ACCOUNTING_DATE is null then
        A.EXPECTTRADEDT
        else
        B.ACCOUNTING_DATE
        end as ACCOUNTING_DATE,
        A.CONSCUSTNO,
        A.CONSCUSTNAME,
        A.CREATOR,
        A.PCODE,
        CC.PNAME,
        A.TRADE_TYPE,
        C.OUTLETCODE，
        C.CONSNAME
        from CM_PREBOOKPRODUCTINFO A
        left join CM_WAGE_ACCOUNTING_DATE B
        ON A.id = B.ID and B.UPDATE_STATUS = '1'
        left join CM_CONSULTANT C
        on A.CREATOR = C.CONSCODE
        LEFT JOIN CM_PRODUCTINFO CC
        ON A.PCODE = CC.PCODE AND CC.RECSTAT = '0'
        where (A.TRADE_TYPE = '1' OR A.TRADE_TYPE = '2') AND A.PAYSTATE='3'
        AND A.PREBOOKSTATE='2' and A.RECSTAT = '0'
        AND NOT EXISTS (SELECT 1 FROM cm_cust_transfervol t WHERE t.fcclpreid =A.ID and t.fcclflag='1')
        AND NOT EXISTS (SELECT 1 FROM cm_zt_orderinfo t WHERE t.mbusicode = '1120' and t.appointmentdealno = A.ID AND A.tradestate = '0')
        <if test="param.orgcode!=null">
            and A.CREATOR in ( select cct.conscode
            from CM_CONSULTANT CCT
            where exists(
            select 1
            from (select orgcode
            from HB_ORGANIZATION
            start with orgcode = #{param.orgcode}
            connect by prior orgcode = parentorgcode) dat
            where CCT.OUTLETCODE = dat.orgcode
            OR CCt.TEAMCODE = dat.ORGCODE)
            <if test="param.conscode!=null">
                and cct.CONSCODE = #{param.conscode}
            </if>
            )
        </if>
        AND NOT EXISTS (SELECT 1 FROM CM_NOSTAT_REPORT CNR WHERE CNR.PREID = A.ID)
        <if test="param.pcode!=null">
            AND A.PCODE = #{param.pcode}
        </if>
        <if test="param.startDate!=null">
            AND A.EXPECTTRADEDT <![CDATA[ >= ]]> #{param.startDate}
        </if>
        <if test="param.endDate!=null">
            AND A.EXPECTTRADEDT <![CDATA[ <= ]]> #{param.endDate}
        </if>
        <if test="param.custname != null">
            AND A.CONSCUSTNAME like '%'||#{param.custname}||'%'
        </if>
        ) TXX
        where 1 = 1
        <if test="param.accstartDate!=null">
            AND ACCOUNTING_DATE <![CDATA[ >= ]]> #{param.accstartDate}
        </if>
        <if test="param.accendDate!=null">
            AND ACCOUNTING_DATE <![CDATA[ <= ]]> #{param.accendDate}
        </if>
    </select>

</mapper>



