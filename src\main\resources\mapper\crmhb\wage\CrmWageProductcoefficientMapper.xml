<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.wage.CrmWageProductcoefficientMapper">
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	

	<insert id="insertCrmWageProductcoefficient" parameterType="CrmWageProductcoefficient">
		INSERT INTO CM_WAGE_PRODUCTCOEFFICIENT (
		<trim suffix="" suffixOverrides=",">
			id,
			<if test="fundcode != null"> fundcode, </if>
			<if test="orgCode != null"> org_code, </if>
			<if test="coefficientType != null"> coefficient_Type, </if>
			<if test="backstepratio != null"> backstepratio, </if>
			<if test="commissionratio != null"> COMMISSIONRATIO, </if>
			<if test="performanceratio != null"> performanceratio, </if>
			<if test="startdt != null"> startdt, </if>
			<if test="enddt != null"> enddt, </if>
			creator,
			credt
		</trim>
		) values (
		<trim suffix="" suffixOverrides=",">
			seq_SALARY_PRODUCTCOEFFICIENT.Nextval,
			<if test="fundcode != null"> #{fundcode}, </if>
			<if test="orgCode != null"> #{orgCode}, </if>
			<if test="coefficientType != null"> #{coefficientType}, </if>
			<if test="backstepratio != null"> #{backstepratio}, </if>
			<if test="commissionratio != null"> #{commissionratio}, </if>
			<if test="performanceratio != null"> #{performanceratio}, </if>
			<if test="startdt != null"> #{startdt}, </if>
			<if test="enddt != null"> #{enddt}, </if>
			#{creator},
			sysdate
		</trim>
		)
	</insert>
	  
	  
	  <update id="updateCrmWageProductcoefficient" parameterType="CrmWageProductcoefficient">
			UPDATE CM_WAGE_PRODUCTCOEFFICIENT
			<set>
				<if test="fundcode != null"> fundcode = #{fundcode}, </if>
				<if test="orgCode != null"> org_code = #{orgCode}, </if>
				<if test="coefficientType != null"> coefficient_Type = #{coefficientType}, </if>
				<if test="backstepratio != null"> backstepratio = #{backstepratio}, </if>
				<if test="commissionratio != null"> commissionratio = #{commissionratio}, </if>
				<if test="performanceratio != null"> performanceratio = #{performanceratio}, </if>
				<if test="performanceratio == null"> performanceratio = null, </if>
				<if test="startdt != null"> startdt = #{startdt}, </if>
				<if test="enddt != null"> enddt = #{enddt}, </if>
				<if test="enddt == null"> enddt = null, </if>
				modifier = #{modifier},
				moddt = sysdate
			</set>
			where id = #{id}
		</update>
	  
	  
	  <delete id="delCrmWageProductcoefficient" parameterType="String">
	    DELETE  from CM_WAGE_PRODUCTCOEFFICIENT
	    where id = #{id}
	  </delete>

	  <select id="listCrmWageProductcoefficient" parameterType="Map" resultType="CrmWageProductcoefficient" useCache="false">
		  SELECT t1.*,t2.jjjc as FUNDNAME ,t3.ORGNAME
		  FROM CM_WAGE_PRODUCTCOEFFICIENT t1
		  left join jjxx1 t2
		  ON t1.fundcode = t2.jjdm
		  left join HB_ORGANIZATION t3
		  ON t1.ORG_CODE = t3.ORGCODE
		  where  1=1
			  <if test="fundcode != null"> AND fundcode = #{fundcode} </if>
			  <if test="orgCode != null"> AND t1.org_code in (select orgcode from  hb_organization  connect by prior orgcode = parentorgcode start with orgcode =#{orgCode})</if>
			  <if test="coefficientType != null"> AND coefficient_type = #{coefficientType} </if>
			  <if test="startdt != null and enddt != null">
				  and (startdt <![CDATA[ >= ]]> #{startdt} and enddt <![CDATA[<=]]> #{enddt})
			  </if>
			  <if test="startdt == null and enddt != null"    >
				  and startdt <![CDATA[<=]]> #{enddt}
			  </if>
			  <if test="startdt != null and enddt == null"   >
				  and enddt <![CDATA[ >= ]]> #{startdt}
			  </if>
		  order by id desc
      </select>
	  
	  <select id="listCmrWageProductcoefficientByPage" parameterType="Map" resultType="CrmWageProductcoefficient" useCache="false">
		  SELECT t1.*,t2.jjjc as FUNDNAME ,t3.ORGNAME
		  FROM CM_WAGE_PRODUCTCOEFFICIENT t1
		  left join jjxx1 t2
		  ON t1.fundcode = t2.jjdm
		  left join HB_ORGANIZATION t3
		  ON t1.ORG_CODE = t3.ORGCODE
		  where  1=1
	              <if test="param.fundcode != null"> AND fundcode = #{param.fundcode} </if>
                  <if test="param.orgCode != null"> AND t1.org_code in (select orgcode from  hb_organization  connect by prior orgcode = parentorgcode start with orgcode =#{param.orgCode})</if>
                  <if test="param.coefficientType != null">AND coefficient_type = #{param.coefficientType}</if>
				  <if test="param.startdt != null and param.enddt != null">
					  and (startdt <![CDATA[ >= ]]> #{param.startdt} and enddt <![CDATA[<=]]> #{param.enddt})
				  </if>
				  <if test="param.startdt == null and param.enddt != null"    >
					  and startdt <![CDATA[<=]]> #{param.enddt}
				  </if>
				  <if test="param.startdt != null and param.enddt == null"   >
					  and enddt <![CDATA[ >= ]]> #{param.startdt}
				  </if>
                  order by id desc
        </select>
</mapper>



