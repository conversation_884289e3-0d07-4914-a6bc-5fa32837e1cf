<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.ClAsignActiveConfigMapper">
	<insert id="insertClAsignActiveConfig" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig">
        INSERT INTO CM_CLASIGN_ACTIVE_CONFIG (
       <trim suffix="" suffixOverrides=",">   
                <if test="id != null"> id, </if>
                <if test="type != null"> type, </if>
				<if test="validflag != null"> validflag, </if>
				<if test="manager != null"> manager, </if>
				<if test="oldmanager != null"> oldmanager, </if>
				<if test="conscode != null"> conscode, </if>
				<if test="oldconscode != null"> oldconscode, </if>
				<if test="custnos != null"> custnos, </if>
				<if test="activetype !=null"> activetype, </if>
				<if test="activeflag !=null"> activeflag, </if>
				<if test="startdt !=null"> startdt, </if>
				<if test="enddt !=null"> enddt, </if>
				<if test="activedt !=null"> activedt, </if>
				<if test="calrate !=null"> calrate, </if>
				<if test="calratetwo !=null"> calratetwo, </if>
				<if test="remark !=null"> remark, </if>
				<if test="allcustnos != null"> allcustnos, </if>
				<if test="creator !=null"> creator, </if>
				<if test="creatdt !=null"> creatdt, </if>
				<if test="modor !=null"> modor, </if>
				<if test="moddt !=null"> moddt, </if>
        </trim>
           ) values (
        <trim suffix="" suffixOverrides=",">
                <if test="id != null"> #{id}, </if>
				<if test="type != null"> #{type}, </if>
				<if test="validflag != null"> #{validflag}, </if>
				<if test="manager != null"> #{manager}, </if>
				<if test="oldmanager != null"> #{oldmanager}, </if>
				<if test="conscode != null"> #{conscode}, </if>
				<if test="oldconscode != null"> #{oldconscode}, </if>
				<if test="custnos != null"> #{custnos}, </if>
				<if test="activetype != null"> #{activetype}, </if>
				<if test="activeflag !=null"> #{activeflag}, </if>
				<if test="startdt !=null"> #{startdt}, </if>
				<if test="enddt !=null"> #{enddt}, </if>
				<if test="activedt !=null"> #{activedt}, </if>
				<if test="calrate !=null">  #{calrate}, </if>
				<if test="calratetwo !=null">  #{calratetwo}, </if>
				<if test="remark !=null">  #{remark}, </if>
				<if test="allcustnos != null"> #{allcustnos}, </if>
				<if test="creator !=null"> #{creator}, </if>
				<if test="creatdt !=null"> #{creatdt}, </if>
				<if test="modor !=null"> #{modor}, </if>
				<if test="moddt !=null"> #{moddt}, </if>
        </trim>  
         )
    </insert>

	<select id="getClAsignActiveConfigByConditions" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig">
		select * from CM_CLASIGN_ACTIVE_CONFIG
		where 1=1
		<if test="type != null">and TYPE = #{type} </if>
		<if test="validflag != null">and validflag = #{validflag} </if>
		<if test="manager != null">and manager = #{manager} </if>
		<if test="oldmanager != null">and oldmanager = #{oldmanager} </if>
		<if test="conscode != null">and conscode = #{conscode} </if>
		<if test="oldconscode != null">and oldconscode = #{oldconscode} </if>
		<if test="startdt != null">and startdt &lt;= #{enddt} </if>
		<if test="enddt != null">and enddt  &gt;= #{startdt} </if>
		<if test="id != null">and id <![CDATA[<>]]> #{id} </if>
	</select>

	<update id="updateClAsignActiveConfig" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig">
        UPDATE CM_CLASIGN_ACTIVE_CONFIG	  
    	set
            validflag = #{validflag,jdbcType=VARCHAR}, 
			manager = #{manager,jdbcType=VARCHAR},
			oldmanager = #{oldmanager,jdbcType=VARCHAR},
			conscode = #{conscode,jdbcType=VARCHAR},
			oldconscode = #{oldconscode,jdbcType=VARCHAR},
			custnos = #{custnos,jdbcType=VARCHAR},
			activetype = #{activetype,jdbcType=VARCHAR},
			activeflag = #{activeflag ,jdbcType=VARCHAR},
			activedt = #{activedt ,jdbcType=VARCHAR},
			startdt = #{startdt,jdbcType=VARCHAR},
			enddt = #{enddt,jdbcType=VARCHAR}, 
			calrate = #{calrate ,jdbcType=NUMERIC},
			calratetwo = #{calratetwo ,jdbcType=NUMERIC},
			remark = #{remark,jdbcType=VARCHAR},
			modor = #{modor,jdbcType=VARCHAR},
			moddt = sysdate  
          where id = #{id}   
    </update>
    
    <update id="delClAsignActiveConfig" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig">
        delete from CM_CLASIGN_ACTIVE_CONFIG where id = #{id}   
    </update>
    
    <select id="getClAsignActiveConfig" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig" useCache="false">
	  	SELECT t1.*,t2.consname managername,t3.consname,t2.outletcode manageOrgCode,t3.outletcode consOrgCode,
	  		t4.consname oldmanagername,t5.consname oldconsname,t4.outletcode oldmanageOrgCode,t5.outletcode oldconsOrgCode
		  FROM CM_CLASIGN_ACTIVE_CONFIG t1
		  left join CM_CONSULTANT_EXP t2
		  on t1.manager = t2.userid
		  left join CM_CONSULTANT_EXP t3
		  on t1.conscode = t3.userid
		  left join CM_CONSULTANT_EXP t4
		  on t1.oldmanager = t4.userid
		  left join CM_CONSULTANT_EXP t5
		  on t1.oldconscode = t5.userid
		  where 1=1
		  <if test="id != null"> and t1.id = #{id} </if> 
	  </select>
	  
	  <select id="listClAsignActiveConfigByPage" parameterType="Map" resultType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig" useCache="false">
	  	SELECT t1.*,t2.consname managername,t3.consname,t2.outletcode manageOrgCode,t3.outletcode consOrgCode,
	  			t4.consname oldmanagername,t5.consname oldconsname,t4.outletcode oldmanageOrgCode,t5.outletcode oldconsOrgCode
		  FROM CM_CLASIGN_ACTIVE_CONFIG t1
		  left join CM_CONSULTANT_EXP t2
		  on t1.manager = t2.userid
		  left join CM_CONSULTANT_EXP t3
		  on t1.conscode = t3.userid
		  left join CM_CONSULTANT_EXP t4
		  on t1.oldmanager = t4.userid
		  left join CM_CONSULTANT_EXP t5
		  on t1.oldconscode = t5.userid
		  where 1=1
		  <if test="param.startdt != null"> and t1.STARTDT &gt;= #{param.startdt,jdbcType=VARCHAR} </if>
		  <if test="param.enddt != null"> and t1.ENDDT &lt;= #{param.enddt,jdbcType=VARCHAR} </if>
		  <if test="param.activestartdt != null"> and t1.activedt &gt;= #{param.activestartdt,jdbcType=VARCHAR} </if>
		  <if test="param.activeenddt != null"> and t1.activedt &lt;= #{param.activeenddt,jdbcType=VARCHAR} </if>
		  <if test="param.id != null"> and t1.id = #{param.id} </if>
		  <if test="param.type != null"> and t1.type = #{param.type} </if> 
		  <if test="param.conscustno != null"> and t1.custnos like '%'||#{param.conscustno}||'%' </if> 
		  <if test="param.validflag != null"> and t1.validflag = #{param.validflag} </if>
		  <!-- 原投顾 -->
		  <if test="param.oldconscode != null">and t1.oldconscode = #{param.oldconscode}</if>
		  <if test="param.oldconscode == null and param.oldconsOrgCode != null">
			  and (t5.TEAMCODE = #{param.oldconsOrgCode} OR
			  t5.OUTLETCODE in (
				  SELECT ORGCODE
				  FROM HB_ORGANIZATION T
				  WHERE T.STATUS = '0'
				  START WITH T.ORGCODE = #{param.oldconsOrgCode}
				  CONNECT BY PRIOR ORGCODE = PARENTORGCODE
			  ))
		  </if>
		  <!-- 新投顾 -->
		  <if test="param.conscode != null">and t1.conscode = #{param.conscode}</if>
		  <if test="param.conscode == null and param.consOrgCode != null">
			  and (t3.TEAMCODE = #{param.consOrgCode} OR
			  t3.OUTLETCODE in (
				  SELECT ORGCODE
				  FROM HB_ORGANIZATION T
				  WHERE T.STATUS = '0'
				  START WITH T.ORGCODE = #{param.consOrgCode}
				  CONNECT BY PRIOR ORGCODE = PARENTORGCODE
			  ))
		  </if>
		  <!-- 原管理层 -->
		  <if test="param.oldmanager != null">and t1.oldmanager = #{param.oldmanager}</if>
		  <if test="param.oldmanager == null and param.oldmanageOrgCode != null">
			  and (t4.TEAMCODE = #{param.oldmanageOrgCode} OR
			  t4.OUTLETCODE in (
				  SELECT ORGCODE
				  FROM HB_ORGANIZATION T
				  WHERE T.STATUS = '0'
				  START WITH T.ORGCODE = #{param.oldmanageOrgCode}
				  CONNECT BY PRIOR ORGCODE = PARENTORGCODE
			  ))
		  </if>
		  <!-- 新管理层 -->
		  <if test="param.manager != null">and t1.manager = #{param.manager}</if>
		  <if test="param.manager == null and param.manageOrgCode != null">
			  and (t2.TEAMCODE = #{param.manageOrgCode} OR
			  t2.OUTLETCODE in (
				  SELECT ORGCODE
				  FROM HB_ORGANIZATION T
				  WHERE T.STATUS = '0'
				  START WITH T.ORGCODE = #{param.manageOrgCode}
				  CONNECT BY PRIOR ORGCODE = PARENTORGCODE
			  ))
		  </if>
		  <if test="param.activetype != null"> and t1.activetype = #{param.activetype} </if>
		  <if test="param.activeflag != null"> and t1.activeflag = #{param.activeflag} </if> 
		  <if test="param.sort != null and param.order != null" > ORDER BY  ${param.sort} ${param.order} nulls last</if>
	  </select>
	  
	  <select id="selectOverJgCount" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig" resultType="int" useCache="false">
			select count(*)
			from CM_CLASIGN_ACTIVE_CONFIG
			where type = '1'
			and manager = #{manager}
			and oldconscode = #{oldconscode}
			<if test="id != null">
				and id != #{id}
			</if>
		</select>
		
		<select id="selectOverOtherCount" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig" resultType="int" useCache="false">
			select count(*)
			from CM_CLASIGN_ACTIVE_CONFIG
			where type = #{type}
			<if test="manager != null">
			and manager = #{manager}
			</if>
			<if test="oldmanager != null">
			and oldmanager = #{oldmanager}
			</if>
			<if test="conscode != null">
			and conscode = #{conscode}
			</if>
			<if test="oldconscode != null">
			and oldconscode = #{oldconscode}
			</if>
			<if test="enddt == null or enddt == ''">
				and enddt >= #{startdt}
			</if>
			<if test="enddt != null and enddt != ''">
				and startdt &lt;= #{enddt}
				and enddt >= #{startdt}
			</if>
			<if test="id != null">
				and id != #{id}
			</if>
		</select>
	<insert id="insertClAsignActiveCustno" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveCustno">
        INSERT INTO CM_CLASIGN_ACTIVE_CUSTNO (
       <trim suffix="" suffixOverrides=",">   
                <if test="id != null"> id, </if>
                <if test="configid != null"> configid, </if>
				<if test="custno != null"> custno, </if>
        </trim>
           ) values (
        <trim suffix="" suffixOverrides=",">
                <if test="id != null"> #{id}, </if>
				<if test="configid != null"> #{configid}, </if>
				<if test="custno != null"> #{custno}, </if>
        </trim>  
         )
    </insert>

	<insert id="batchInsertClAsignActiveCustno" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveCustno">
		insert into CM_CLASIGN_ACTIVE_CUSTNO (
		<trim suffix="" suffixOverrides=",">
			id, configid, custno,
		</trim>
		)
		SELECT SEQ_HMC_ID.NEXTVAL, cc.* FROM (
		<foreach collection="list" item="item" index="index" separator="UNION ALL" open="" close="">select
			#{item.configid},
			#{item.custno}
			from dual
		</foreach>)cc
	</insert>

	<delete id="truncateClAsignActiveCustno">
		truncate table CM_CLASIGN_ACTIVE_CUSTNO
	</delete>

    <update id="delClAsignActiveCustno" parameterType="com.howbuy.crm.hb.domain.system.ClAsignActiveConfig">
        delete from CM_CLASIGN_ACTIVE_CUSTNO where configid = #{id}   
    </update>
		
</mapper>