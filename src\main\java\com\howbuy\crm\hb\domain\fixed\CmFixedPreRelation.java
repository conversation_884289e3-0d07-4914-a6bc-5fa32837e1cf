package com.howbuy.crm.hb.domain.fixed;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmFixedPreRelation implements Serializable{
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private BigDecimal planid;
	private BigDecimal preid;
	private String firstflag;
	private String isdel;
	private String creator;
	private Date creattime;
	private String modifier;
	private Date modtime;
	
}
