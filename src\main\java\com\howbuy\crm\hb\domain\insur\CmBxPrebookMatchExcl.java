package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxPrebookMatchExcl.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxPrebookMatchExcl  extends PageVo implements Serializable {
	private static final long serialVersionUID = 1L;
	private String insurid;
	private String fundname;
	private String custname;
	private String insurname;
	private String relation;
	private String insuridno;
	private String insurage;
	private String compname;
	private String prodtype;
	private String yearamk;
	private String payyears;
	private String ensureyears;
	private String insurstate;
	private String passdt;
	private String caltime;
	
}
