package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxPrebookSigninfo.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxPrebookMatch  extends PageVo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private BigDecimal id;
	private String insurid;
	private String fundname;
	private String custname;
	private String custnameflag;
	private String insurname;
	private String insurnameflag;
	private String relation;
	private String relationflag;
	private String insuridno;
	private String insuridnoDigest;
	private String insuridnoCipher;
	private String insuridnoMask;
	private String insuridnoMaskflag;
	private BigDecimal insurage;
	private String insurageflag;
	private String compname;
	private String compnameflag;
	private String prodtype;
	private String prodtypeflag;
	private BigDecimal yearamk;
	private String yearamkflag;
	private String payyears;
	private String payyearsflag;
	private String ensureyears;
	private String ensureyearsflag;
	private String insurstate;
	private String insurstateflag;
	private String passdt;
	private String passdtflag;
	private String caltime;
	private String caltimeflag;
	private String creator;
	private Date creatdt;
	private String modor;
	private Date moddt;
	/**
	 * 下面是查询用到的参数
	 */
	private String startdt;
	private String enddt;
	private String prestate;
	private String paystate;
	private String checkstate;
	private String onlydt;
	
	/**
	 * 0:已匹配；1：未匹配
	 */
	private String matchflag;
	/**
	 * 1:一致；0：不一致；2：未匹配情况
	 */
	private String comparflag;
	/**
	 * 1：表示核保管理为主；0：导入为主
	 */
	private String mainflag;
	
	private String  sort;

	private String  order;
	
	/**
	 * 操作类型时对列表处理还是上传页处理，上传页处理的值是：load
	 */
	private String opttype;
}
