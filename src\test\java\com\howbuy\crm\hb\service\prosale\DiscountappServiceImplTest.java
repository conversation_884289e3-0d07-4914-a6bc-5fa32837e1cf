package com.howbuy.crm.hb.service.prosale;

import com.howbuy.crm.hb.persistence.common.CommonMapper;
import com.howbuy.crm.hb.persistence.prosale.CmDiscountLogMapper;
import com.howbuy.crm.hb.persistence.prosale.DiscountappMapper;
import com.howbuy.crm.hb.service.prosale.impl.DiscountappServiceImpl;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.dto.Discountapp;
import com.howbuy.crm.prosale.response.GetPrebookByIdResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.Test;



/**
 * 单元测试CustprivatefundServiceImpl.checktrade检查导入交易是否符合条件
 * <AUTHOR>
 * 20220712
 */
@PowerMockIgnore("javax.management.*")
@Test
public class DiscountappServiceImplTest extends PowerMockTestCase {

    @InjectMocks
    private DiscountappServiceImpl serviceMock;
    
    @Mock
    private DiscountappMapper discountappMapperMock;
    
    @Mock
    private CmDiscountLogMapper cmDiscountLogMapperMock;
    
    @Mock
    private CommonMapper commonMapperMock;
    
    @Mock
    private QueryPreBookService queryPreBookServiceMock;
    
    //类型
    private static String NORMAL_TYPE="";
    private static String FIXED_TYPE="fixed";
    private static String AUDIT_TYPE = "auditDiscount";
    //用户ID
    private static String TEST_USERID="TEST_USERID";
    
    private static String TEST_PREBOOKID="1";
    

    
    
    
    

    /**
     * 测试_没有查询到折扣信息的情况
     */
    @Test
    public void testNotFindDiscount() {
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	//mock根据预约id没有查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(null);
    	//执行方法
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,NORMAL_TYPE);
        Assert.assertTrue(result.contains("查询不到折扣信息"),"查询不到折扣信息");
    	
    }
    
    /**
     * 测试_定投意向单折扣撤销情况
     */
    @Test
    public void testFixedCancelDiscount() {
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
    	
        //mock折扣更新成功
        PowerMockito.doNothing().when(discountappMapperMock).updateDiscountapp(Mockito.any());
        //设置查询序列
        PowerMockito.when(commonMapperMock.getSeqValue(Mockito.any())).thenReturn("1");
        //mock插入日志
        PowerMockito.doNothing().when(cmDiscountLogMapperMock).insert(Mockito.any());
        //执行方法
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,FIXED_TYPE);
        
        Assert.assertTrue(result.contains("success"),"success");
    }
    
    /**
     * 测试_预约取消折扣未查到预约情况
     */
    @Test
    public void testPreCancelDiscountNotPre() {
    	GetPrebookByIdResponse preResponse = new GetPrebookByIdResponse();
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
        //mock没有查到预约的情况
        PowerMockito.when(queryPreBookServiceMock.getPrebookById(Mockito.any())).thenReturn(preResponse);
        
    	//执行方法
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,NORMAL_TYPE);
        Assert.assertTrue(result.contains("查询不到预约信息"),"查询不到预约信息");
    	
    }
    
    /**
     * 测试_预约取消折扣在审核页面撤销
     */
    @Test
    public void testPreCancelDiscountAtAudit() {
    	CmPrebookproductinfo cmPrebookproductinfo = new CmPrebookproductinfo();
    	GetPrebookByIdResponse preResponse = new GetPrebookByIdResponse();
    	preResponse.success();
    	preResponse.setPreinfo(cmPrebookproductinfo);
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
        //mock查到预约的情况
        PowerMockito.when(queryPreBookServiceMock.getPrebookById(Mockito.any())).thenReturn(preResponse);
        //mock折扣更新成功
        PowerMockito.doNothing().when(discountappMapperMock).updateDiscountapp(Mockito.any());
        //设置查询序列
        PowerMockito.when(commonMapperMock.getSeqValue(Mockito.any())).thenReturn("1");
        //mock插入日志
        PowerMockito.doNothing().when(cmDiscountLogMapperMock).insert(Mockito.any());
    	//执行方法(在折扣审核页面处理)
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,AUDIT_TYPE);
        Assert.assertTrue(result.contains("success"),"success");
    	
    }
    
    /**
     * 测试_预约取消初审通过折扣在非审核页面撤销
     */
    @Test
    public void testPreCancelDiscountNotAuditCsPassd() {
    	CmPrebookproductinfo cmPrebookproductinfo = new CmPrebookproductinfo();
    	GetPrebookByIdResponse preResponse = new GetPrebookByIdResponse();
    	preResponse.success();
    	preResponse.setPreinfo(cmPrebookproductinfo);
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//审核状态是初审通过
    	discountapp.setDiscountState("3");
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
        //mock查到预约的情况
        PowerMockito.when(queryPreBookServiceMock.getPrebookById(Mockito.any())).thenReturn(preResponse);
       
    	//执行方法(在非折扣审核页面处理)
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,NORMAL_TYPE);
        Assert.assertTrue(result.contains("不符合撤销条件，不允许撤销"),"不符合撤销条件，不允许撤销");
    	
    }
    
    /**
     * 测试_预约取消终审通过折扣在非审核页面撤销
     */
    @Test
    public void testPreCancelDiscountNotAuditZsPassd() {
    	CmPrebookproductinfo cmPrebookproductinfo = new CmPrebookproductinfo();
    	GetPrebookByIdResponse preResponse = new GetPrebookByIdResponse();
    	preResponse.success();
    	preResponse.setPreinfo(cmPrebookproductinfo);
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//审核状态是终审通过
    	discountapp.setDiscountState("5");
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
        //mock查到预约的情况
        PowerMockito.when(queryPreBookServiceMock.getPrebookById(Mockito.any())).thenReturn(preResponse);
       
    	//执行方法(在非折扣审核页面处理)
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,NORMAL_TYPE);
        Assert.assertTrue(result.contains("不符合撤销条件，不允许撤销"),"不符合撤销条件，不允许撤销");
    	
    }
    
    /**
     * 测试_预约取消非审核通过的中台已经下单的非直接少汇的折扣在非审核页面撤销
     */
    @Test
    public void testPreCancelDiscountNotAuditHasZtNotSh() {
    	CmPrebookproductinfo cmPrebookproductinfo = new CmPrebookproductinfo();
    	//中台已下单
    	cmPrebookproductinfo.setDealno("1");
    	GetPrebookByIdResponse preResponse = new GetPrebookByIdResponse();
    	preResponse.success();
    	preResponse.setPreinfo(cmPrebookproductinfo);
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//审核状态是已申请
    	discountapp.setDiscountState("2");
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
        //mock查到预约的情况
        PowerMockito.when(queryPreBookServiceMock.getPrebookById(Mockito.any())).thenReturn(preResponse);
        //mock折扣更新成功
        PowerMockito.doNothing().when(discountappMapperMock).updateDiscountapp(Mockito.any());
        //设置查询序列
        PowerMockito.when(commonMapperMock.getSeqValue(Mockito.any())).thenReturn("1");
        //mock插入日志
        PowerMockito.doNothing().when(cmDiscountLogMapperMock).insert(Mockito.any());
    	//执行方法(在非折扣审核页面处理)
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,NORMAL_TYPE);
        Assert.assertTrue(result.contains("success"),"success");
    	
    }
    
    /**
     * 测试_预约取消非审核通过的中台已经下单的直接少汇的折扣在非审核页面撤销
     */
    @Test
    public void testPreCancelDiscountNotAuditHasZtAndSh() {
    	CmPrebookproductinfo cmPrebookproductinfo = new CmPrebookproductinfo();
    	//中台已下单
    	cmPrebookproductinfo.setDealno("1");
    	GetPrebookByIdResponse preResponse = new GetPrebookByIdResponse();
    	preResponse.success();
    	preResponse.setPreinfo(cmPrebookproductinfo);
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//审核状态是已申请
    	discountapp.setDiscountState("2");
    	//折扣方式是直接少汇
    	discountapp.setDiscountWay("1");
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
        //mock查到预约的情况
        PowerMockito.when(queryPreBookServiceMock.getPrebookById(Mockito.any())).thenReturn(preResponse);
       
    	//执行方法(在非折扣审核页面处理)
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,NORMAL_TYPE);
        Assert.assertTrue(result.contains("不符合撤销条件，不允许撤销"),"不符合撤销条件，不允许撤销");
    	
    }
    
    /**
     * 测试_预约取消非审核通过的中台未下单的折扣在非审核页面撤销
     */
    @Test
    public void testPreCancelDiscountNotAuditNotZt() {
    	CmPrebookproductinfo cmPrebookproductinfo = new CmPrebookproductinfo();
    	GetPrebookByIdResponse preResponse = new GetPrebookByIdResponse();
    	preResponse.success();
    	preResponse.setPreinfo(cmPrebookproductinfo);
    	DiscountappServiceImpl spy = PowerMockito.spy(serviceMock);
    	Discountapp discountapp = new Discountapp();
    	//审核状态是已申请
    	discountapp.setDiscountState("2");
    	//mock根据预约id查到折扣信息
        PowerMockito.when(discountappMapperMock.getDiscountapp(Mockito.any())).thenReturn(discountapp);
        //mock查到预约的情况
        PowerMockito.when(queryPreBookServiceMock.getPrebookById(Mockito.any())).thenReturn(preResponse);
        //mock折扣更新成功
        PowerMockito.doNothing().when(discountappMapperMock).updateDiscountapp(Mockito.any());
        //设置查询序列
        PowerMockito.when(commonMapperMock.getSeqValue(Mockito.any())).thenReturn("1");
        //mock插入日志
        PowerMockito.doNothing().when(cmDiscountLogMapperMock).insert(Mockito.any());
    	//执行方法(在非折扣审核页面处理)
        String result = (String)ReflectionUtils.invokeMethod(MemberModifier.methods(DiscountappServiceImpl.class, "cancelDiscount")[0], spy, TEST_PREBOOKID,TEST_USERID,NORMAL_TYPE);
        Assert.assertTrue(result.contains("success"),"success");
    	
    }

}
