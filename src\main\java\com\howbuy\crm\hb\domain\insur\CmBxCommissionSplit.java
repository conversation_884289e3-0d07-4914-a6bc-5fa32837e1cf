package com.howbuy.crm.hb.domain.insur;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/26 15:33
 * @since JDK 1.8
 */
/**
 * 创新产品预约佣金拆单明细表
 */
@Data
public class CmBxCommissionSplit {
    /**
    * 主键
    */
    private BigDecimal id;

    /**
    * 佣金明细表ID
    */
    private BigDecimal commissionId;

    /**
    * 预约购买明细id
    */
    private BigDecimal buyId;

    /**
    * 预约id
    */
    private BigDecimal preId;

    /**
    * 外键，对应预约单缴款计划明细表id
    */
    private BigDecimal payListId;

    /**
    * 序号
    */
    private Integer seqNo;

    /**
    * 应收日期
    */
    private String gatheringDt;

    /**
    * 公司佣金率
    */
    private BigDecimal commissionRatio;

    /**
    * 应收佣金-内
    */
    private BigDecimal gatherCommIn;

    /**
    * 应收佣金-外
    */
    private BigDecimal gatherCommOut;

    /**
    * 开票日期
    */
    private String ticketDt;

    /**
    * 实收佣金
    */
    private BigDecimal realCommission;

    /**
    * 结算日期
    */
    private String accountDt;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createDt;

    /**
    * 更新人
    */
    private String modifier;

    /**
    * 更新时间
    */
    private Date modifyDt;

    /**
    * 应收单结算状态1待结算、2已开票、3已结算；
    */
    private String commState;

    /**
    * 币种
    */
    private String currency;

    /**
    * 结算外币
    */
    private String settleCurrency;

    /**
    * 结算汇率
    */
    private BigDecimal settleRate;

    /**
    * 应收佣金-内(结算外币)
    */
    private BigDecimal settleGatherCommIn;

    /**
    * 应收佣金-外(结算外币)
    */
    private BigDecimal settleGatherCommOut;

    /**
    * 实收佣金(结算外币)
    */
    private BigDecimal settleRealCommission;

    /**
    * 备注
    */
    private String settleRemark;


    /**
     *佣金结算状态：1-待结算、2-已开票、3-已结算
     */
    private String commissionState;

    /**
    * 是否有效标记： 1-是 0-否
    */
    private String recStat;
}