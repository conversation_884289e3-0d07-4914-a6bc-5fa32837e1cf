package com.howbuy.crm.hb.domain.conference;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmConferenceCustInfomation implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 会议主键(外键) */
    private String conferenceid;

    /** 投顾客户号(外键) */
    private String conscustno;

    /** 参会部门 */
    private String field;

    /** 参会部门名称 */
    private String fieldvalue;

    /** 创建日期 */
    private String creatdt;

    /** 创建人 */
    private String creater;

    /** 修改日期 */
    private String modifydt;

    /** 修改人 */
    private String modifier;

    /** 默认 */
    private String defaultfilevalue;

    private List<String> fieldvalueList;
    
    private List<String> conscustnoList;

    public String getConferenceid() {
        return conferenceid;
    }

    public void setConferenceid(String conferenceid) {
        this.conferenceid = conferenceid;
    }

    public String getConscustno() {
        return conscustno;
    }

    public void setConscustno(String conscustno) {
        this.conscustno = conscustno;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getFieldvalue() {
        return fieldvalue;
    }

    public void setFieldvalue(String fieldvalue) {
        this.fieldvalue = fieldvalue;
    }

    public String getCreatdt() {
        return creatdt;
    }

    public void setCreatdt(String creatdt) {
        this.creatdt = creatdt;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getModifydt() {
        return modifydt;
    }

    public void setModifydt(String modifydt) {
        this.modifydt = modifydt;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getDefaultfilevalue() {
        return defaultfilevalue;
    }

    public void setDefaultfilevalue(String defaultfilevalue) {
        this.defaultfilevalue = defaultfilevalue;
    }

    public List<String> getFieldvalueList() {
        return fieldvalueList;
    }

    public void setFieldvalueList(List<String> fieldvalueList) {
        this.fieldvalueList = fieldvalueList;
    }

	public List<String> getConscustnoList() {
		return conscustnoList;
	}

	public void setConscustnoList(List<String> conscustnoList) {
		this.conscustnoList = conscustnoList;
	}
    
}
