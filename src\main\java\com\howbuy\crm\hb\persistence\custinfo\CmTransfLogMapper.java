package com.howbuy.crm.hb.persistence.custinfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.custinfo.CmTransfCustconsultant;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.custinfo.CmTransfLog;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmTransfLogMapper {

	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmTransfLog> listCmTransfLogByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	 /**
	  * 得到单个数据对象
	  * @param param
	  * @return
	  */
	CmTransfLog getCmTransfLog(Map<String, String> param);
	
	/**
	 * 单条修改数据对象
	 * @param cmTransfLog
	 */
	void updateCmTransfLog(CmTransfLog cmTransfLog);
	
	/**
	 * 更新
	 * @param cmTransfLog
	 */
	void updatePartMsg(CmTransfLog cmTransfLog);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmTransfLog(BigDecimal id);
	
	/**
	 * 新增单条数据对象
	 * @param cmTransfLog
	 */
	void insertCmTransfLog(CmTransfLog cmTransfLog);
	
	/**
	 * 根据条件查询符合条件的划转信息
	 * @param param
	 * @return
	 */
	List<CmTransfLog> listCmTransfLog(Map<String, String> param);

	/**
	 * 根据条件查询符合条件的划转信息
	 * @param param
	 * @return
	 */
	List<CmTransfLog> exportCmTransfLog(Map<String, String> param);

	/**
	 * 获取划转日志中原投顾及关联出的当前投顾
	 * @param id
	 * @return
	 */
	CmTransfCustconsultant findConsultantByTransfId(BigDecimal id);
}
