package com.howbuy.crm.hb.domain.conference;

import lombok.Data;

import java.io.Serializable;

/**
 * 扫码参会客户
 * <AUTHOR> on 2021/6/17 16:05
 */
@Data
public class CmConferenceScan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键 */
    private String id;

    /** 会议主键(外键) */
    private String conferenceId;

    /** 会议名称 */
    private String conferenceName;

    /** 手机号 */
    private String mobile;
    private String mobileMask;
    private String mobileDigest;
    private String mobileCipher;

    /** 记录创建日期 */
    private String credt;
    /** 创建人 */
    private String creator;
    
    private String conscustno;
    
    private String custname;
    
    private String consname;
    
    private String conscode;
    
    /** 是否投顾预约参会 */
    private String isconspre;
    
    /** 预约参会人数 */
    private int appointmentsnub;
    
    /** 所属区域 */
    private String uporgname;
    
    /** 所属部门 */
    private String orgname;
    
    /** 客户姓名(输入) */
    private String custnamesr;
    
    /** 投顾姓名(输入) */
    private String consnamesr;
    
    /** 实际人数(输入) */
    private int meetingnumber;



    /**
     * 扫码参会时，手机号码判断客户信息状态：
     0-不存在客户 1-存在唯一客户 2-存在多个客户
     */
    private String scanCustState;

    /**
     * 实时手机号码判断客户信息状态：
     0-不存在客户 1-存在唯一客户 2-存在多个客户
     */
    private String custState;
}
