package com.howbuy.crm.hb.manager;

import java.net.URLEncoder;
import java.util.List;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.howbuy.crm.hb.domain.custinfo.CmConsBookingCust;

import crm.howbuy.base.utils.StringUtil;

/**
 * 预约客户报表导出管理类
 * <AUTHOR>
 *
 */
public class BookingCustExportManager {
	/**
	 * 最近预约客户列表数据导出方法
	 * @param response
	 * @param listBookingCust
	 * @throws Exception
	 */
	public void exportBookingCustByList(HttpServletResponse response,
			List<CmConsBookingCust> listBookingCust) throws Exception {
		try {
			// 设置导出参数和标题
			String excelName = "".equals("BookingCustReport.xls") ? URLEncoder
					.encode("最近预约客户报表", "utf-8") : URLEncoder.encode("最近预约客户报表.xls", "utf-8");
			// 清空输出流
			response.reset();
			response.setHeader("Content-Disposition", "attachment;filename="
					+ new String((excelName).getBytes(), "iso8859-1"));
			ServletOutputStream os = response.getOutputStream();

			// 默认每个sheet只存储60000条数据，如果记录条数超过60000，则分成多个sheet进行导出
			// sheet也数据范围标识
			int x = 0;
			// 当前页数
			int curSheet = 1;
			// 每页sheet显示记录数
			int sheetSize = 60000;
			// 总sheet数
			int totalSheet = 0;
			if(listBookingCust!=null && listBookingCust.size()>0){
				totalSheet = (listBookingCust.size()+sheetSize-1)/sheetSize;
			}
			
			// 建立excel文件
			WritableWorkbook wbook = Workbook.createWorkbook(os);
			//当前sheet索引
			int sheetNum = 1;
			for (int j = 0; j < totalSheet; j++) {
				// sheet标题
				String sheetName = "第"+curSheet+"页数据";
				
				// sheet名称
				WritableSheet wsheet = wbook.createSheet(sheetName, sheetNum);
				sheetNum++;
				WritableFont fontCss = new WritableFont(WritableFont.ARIAL, 12,
						WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,
						Colour.BLACK);
				WritableCellFormat formatTitle = new WritableCellFormat(fontCss);
				wsheet.mergeCells(0, 0, 5, 0);
				formatTitle.setBackground(Colour.WHITE);
				formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);
				formatTitle.setAlignment(Alignment.CENTRE);
				wsheet.addCell(new Label(0, 0, "最近预约客户报表", formatTitle));
				fontCss = new jxl.write.WritableFont(WritableFont.ARIAL, 9,
						WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,
						Colour.BLACK);
				formatTitle = new WritableCellFormat(fontCss);
				formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);

				wsheet.setColumnView(0, 10);
				wsheet.setColumnView(1, 15);
				wsheet.setColumnView(2, 15);
				wsheet.setColumnView(3, 25);
				wsheet.setColumnView(4, 15);
				wsheet.setColumnView(5, 15);

				// 设置标题：列（从0开始），行（从1开始），显示内容
				wsheet.addCell(new Label(0, 1, "客户名称", formatTitle));
				wsheet.addCell(new Label(1, 1, "预约时间", formatTitle));
				wsheet.addCell(new Label(2, 1, "预约时段", formatTitle));
				wsheet.addCell(new Label(3, 1, "预约事项", formatTitle));
				wsheet.addCell(new Label(4, 1, "所属投顾", formatTitle));
				wsheet.addCell(new Label(5, 1, "预约状态", formatTitle));

				// 控制行数
				int rows = 2;
				// 对合并后的单元格进行样式设置，包括垂直居中显示
				WritableFont contentFont = new WritableFont(WritableFont.ARIAL,
						9, WritableFont.NO_BOLD, false,
						UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
				WritableCellFormat formatContent = new WritableCellFormat(
						contentFont);
				formatContent.setBackground(Colour.WHITE);
				formatContent.setBorder(Border.ALL, BorderLineStyle.THIN);
				formatContent.setVerticalAlignment(VerticalAlignment.CENTRE);

				// 设置导出sheet的起点行索引位置
				int start = curSheet * sheetSize - sheetSize;
				// 设置导出sheet的结束行索引位置
				int end = curSheet * sheetSize;
				if(end > listBookingCust.size()){
					end = listBookingCust.size();
				}
				for (int i = 0; i < listBookingCust.size(); i++) {
					if (x >= start && x < end) {
						// 循环写入内容，同时进行合并单元格操作
						CmConsBookingCust cmConsBookingCust = listBookingCust.get(x);
						wsheet.addCell(new Label(0, rows, (cmConsBookingCust.getCustName() == null ? "" : cmConsBookingCust.getCustName()), formatContent));
						wsheet.addCell(new Label(1, rows, (cmConsBookingCust.getBookingDt() == null ? "" : cmConsBookingCust.getBookingDt()), formatContent));
						String startEndTime="";
						if(StringUtil.isNotNullStr(cmConsBookingCust.getBookingStartTime())){
							if(StringUtil.isNotNullStr(cmConsBookingCust.getBookingEndTime())){
								startEndTime=cmConsBookingCust.getBookingStartTime()+"-"+cmConsBookingCust.getBookingEndTime();
							}else{
								startEndTime=cmConsBookingCust.getBookingStartTime();
							}
						}
						
						wsheet.addCell(new Label(2, rows, (startEndTime == null ? "" : startEndTime), formatContent));
						wsheet.addCell(new Label(3, rows, (cmConsBookingCust.getContent() == null ? "" : cmConsBookingCust.getContent()),formatContent));
						wsheet.addCell(new Label(4, rows, (cmConsBookingCust.getConsName() == null ? "" : cmConsBookingCust.getConsName()),formatContent));
						
						String bookingStatus="";
						if(StringUtil.isNotNullStr(cmConsBookingCust.getBookingStatus())){
							if("0".equals(cmConsBookingCust.getBookingStatus())){
								bookingStatus= "未完成";
							}else if("1".equals(cmConsBookingCust.getBookingStatus())){
								bookingStatus= "已完成";
							}else if("2".equals(cmConsBookingCust.getBookingStatus())){
								bookingStatus= "已取消";
							}
						}						
						wsheet.addCell(new Label(5, rows, (bookingStatus == null ? "" : bookingStatus), formatContent));
						rows++;
					} else {
						break;
					}
					x++;
				}
				curSheet++;
			}
			// 主体内容生成结束
			wbook.write(); // 写入文件
			if(wbook!=null){
				wbook.close();
			}
			os.close(); // 关闭流
			System.out.println("报表导出成功！");
		} catch (Exception ex) {
			ex.printStackTrace();
			System.out.println("报表导出异常！");
		}

	}
	
	
}
