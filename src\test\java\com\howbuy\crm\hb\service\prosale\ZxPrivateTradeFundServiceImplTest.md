## 测试的类
> com.howbuy.crm.hb.service.prosale.impl.ZxPrivateTradeFundServiceImpl
## 测试的方法
> dealsignQs(String custno,String fundcode,String tradedt,BigDecimal nav,String userid,String ishaiwai)

## 分支伪代码
```java
if (客户号或产品代码或交易日期或净值为空) {
    直接返回，提示"参数错误！客户号、产品代码、交易日期、净值必填！"
} else {
	if (根据客户号和产品代码没有查询到持仓) {
   		 直接返回，提示"参数错误！没有查询到持仓！"
	} else {
		if(查询到的持仓的份额大于0){
			插入数据，返回"success"
		}else{
			直接返回，提示"持仓份额有问题！"
		}
	}
}
```

## 测试案例
##### 1、testEmptyparam：客户号或产品代码或交易日期或净值为空
##### 2、testNotFindFund：根据客户号和产品代码没有查询到持仓
##### 3、testFundVolZero：根据客户号和产品代码没有查询到持仓,且持仓份额为0
##### 4、testFundVolOne：根据客户号和产品代码没有查询到持仓,且持仓份额为1
##### 5、testFundVolMoreOne：根据客户号和产品代码没有查询到持仓,且持仓份额大于1