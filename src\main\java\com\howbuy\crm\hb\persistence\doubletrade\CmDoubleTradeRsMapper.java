package com.howbuy.crm.hb.persistence.doubletrade;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleTradeRs;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmDoubleTradeRsMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmDoubleTradeRs getCmDoubleTradeRs(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmDoubleTradeRs
      */
	void insertCmDoubleTradeRs(CmDoubleTradeRs cmDoubleTradeRs);

	/**
	 * @description: 从trade库同步订单数据
	 * @param contractNo
	 * @return void
	 * @author: hongdong.xie
	 * @date: 2024/10/31 14:09
	 * @since JDK 1.8
	 */
	void syncTpDisTradeAppRec(@Param("contractNo") String contractNo);

	/**
	 * @description: 查询sync_tp_dis_trade_app_rec表中是否存在该合同号
	 * @param contractNo
	 * @return void
	 */
	int countSyncTpDisTradeAppRec(@Param("contractNo") String contractNo);
	/**
	 * 单条修改数据对象
	 * @param cmDoubleTradeRs
	 */
	void updateCmDoubleTradeRs(CmDoubleTradeRs cmDoubleTradeRs);
	
	/**
	 * 批量修改数据对象
	 * @param cmDoubleTradeRs
	 */
	void updateBatchCmDoubleTradeRs(CmDoubleTradeRs cmDoubleTradeRs);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmDoubleTradeRs(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmDoubleTradeRs(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleTradeRs> listCmDoubleTradeRs(Map<String, String> param);
	

	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmDoubleTradeRs> listCmDoubleTradeRsByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
