package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class CsPotentialUploadLog implements Serializable{
	private static final long serialVersionUID = 1L;
	private long id;
	private String batchID;
	private String fileName;
	private int custNum;
	private int succNum;
	private int errorNum;
	private String uploader;
	private String uploadDate;
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getBatchID() {
		return batchID;
	}
	public void setBatchID(String batchID) {
		this.batchID = batchID;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public int getCustNum() {
		return custNum;
	}
	public void setCustNum(int custNum) {
		this.custNum = custNum;
	}
	public int getSuccNum() {
		return succNum;
	}
	public void setSuccNum(int succNum) {
		this.succNum = succNum;
	}
	public int getErrorNum() {
		return errorNum;
	}
	public void setErrorNum(int errorNum) {
		this.errorNum = errorNum;
	}
	public String getUploader() {
		return uploader;
	}
	public void setUploader(String uploader) {
		this.uploader = uploader;
	}
	public String getUploadDate() {
		return uploadDate;
	}
	public void setUploadDate(String uploadDate) {
		this.uploadDate = uploadDate;
	}
	
	
}
