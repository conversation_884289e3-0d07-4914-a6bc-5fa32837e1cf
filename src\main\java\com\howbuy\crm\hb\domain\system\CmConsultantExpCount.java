package com.howbuy.crm.hb.domain.system;

import lombok.Data;

import java.io.Serializable;

/**
 * 花名册员工状态统计报表
 * <AUTHOR>
 */
@Data
public class CmConsultantExpCount implements Serializable  {
	private static final long serialVersionUID = 1L;

	/**
	 * 查询部门
	 */
	private String queryOrgCode;
	/**
	 * 查询开始时间
	 */
	private String queryStartDate;
	/**
	 * 查询结束时间
	 */
	private String queryEndDate  ;
	  
	/**
	 * 结果集  中心
	 */
	private String centerName     ;
	/**
	 * 结果集  区域
	 */
	private String areaName  ;
	  
	/**
	 * 结果集  分公司
	 */
	private String orgName     ;
	/**
	 * 结果集  入职人数
	 */
	private Integer startCount  ;
	  
	/**
	 * 结果集  转正人数
	 */
	private Integer regularCount     ;
	/**
	 * 结果集  离职人数
	 */
	private Integer quitCount     ;
	/**
	 * 结果集  月末在职人数
	 */
	private Integer monthEndCount     ;


}
