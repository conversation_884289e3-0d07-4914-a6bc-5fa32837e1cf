package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxPrebookBuyinfo.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxPrebookCommission implements Serializable {
	/**
	 * 主键
	 */
	private BigDecimal id;

	/**
	 * 预约购买明细id
	 */
	private BigDecimal buyid;

	/**
	 * 序号
	 */
	private BigDecimal seqno;

	/**
	 * 应收日期
	 */
	private String gatheringdt;

	/**
	 * 公司佣金率
	 */
	private BigDecimal commissionratio;

	/**
	 * 应收佣金-内
	 */
	private BigDecimal gathercommin;

	/**
	 * 应收佣金-外
	 */
	private BigDecimal gathercommout;

	/**
	 * 开票日期
	 */
	private String ticketdt;

	/**
	 * 实收佣金
	 */
	private BigDecimal realcommission;

	/**
	 * 结算日期
	 */
	private String accountdt;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private Date creatdt;

	/**
	 * 更新人
	 */
	private String modifier;

	/**
	 * 更新时间
	 */
	private Date modifydt;

	/**
	 * 应收单结算状态1待结算、2已开票、3已结算；
	 */
	private String commstate;

	/**
	 * 手工修改应收佣金-外
	 */
	private BigDecimal expgathercommout;

	/**
	 * 手工修改开票日期
	 */
	private String expticketdt;

	/**
	 * 手工修改实收佣金
	 */
	private BigDecimal exprealcommission;

	/**
	 * 手工修改结算日期
	 */
	private String expaccountdt;

	/**
	 * 手工修改人
	 */
	private String expcreator;

	/**
	 * 手工修改时间
	 */
	private Date expcreatdt;

	/**
	 * 开票项目
	 */
	private String ticketproject;

	/**
	 * 发票信息
	 */
	private String ticketmsg;

	/**
	 * 邮寄地址
	 */
	private String addr;

	/**
	 * 佣金率调整
	 */
	private BigDecimal expcommissionratio;

	/**
	 * 外键，对应预约单缴款计划明细表id
	 */
	private BigDecimal paylistid;

	/**
	 * 结算外币
	 */
	private String settleCurrency;

	/**
	 * 结算汇率
	 */
	private BigDecimal settleRate;

	/**
	 * 手工修改应收佣金-外(结算外币)
	 */
	private BigDecimal expSettleGathercommout;

	/**
	 * 手工修改实收佣金(结算外币)
	 */
	private BigDecimal expSettleRealcommission;

	/**
	 *结算备注
	 */
	private String settleRemark;

	/**
	 *是否已拆单 1-是 0-否
	 */
	private String  splitFlag;
}
