package com.howbuy.crm.hb.persistence.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationRepeatMail;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 协会邮件-多个投顾客户号
 * <AUTHOR>
 */
public interface AssociationRepeatMailMapper {

    /**
     * 分页查询邮件列表
     * @param param 查询参数
     * @param pageBean 分页参数
     * @return
     */
    List<AssociationRepeatMail> listAssociationRepeatMailByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 查询邮件列表
     * @param param 查询参数
     * @return
     */
    List<AssociationRepeatMail> listAssociationRepeatMail(@Param("param") Map<String, String> param);

    /**
     * 根据主键获取多个投顾客户号邮件
     * @param ids 多个主键的值以逗号分隔
     * @return
     */
    AssociationRepeatMail findAssociationRepeatMailByIds(String ids);

    /**
     * 执行关联客户操作
     * @param associationRepeatMail
     * （ids 多个主键的值以逗号分隔；id 实际选中的主键；creator 创建人；modifier 修改人；conscustno 实际选中的投顾客户号）
     */
    void relateConscustno(AssociationRepeatMail associationRepeatMail);

    /**
     * 根据IDS批量修改处理状态
     * @param list 待修改记录id的集合
     * @param handleStatus 处理状态
     * @param modifier 修改人
     * @param remark 备注
     * slist里是ids,ids 多个主键的值以逗号分隔；id 实际选中的主键；creator 创建人；modifier 修改人；conscustno 实际选中的投顾客户号）
     */
    void batchUpdateHandleStatusRemarkForRepeat(@Param("list") List<String> list, @Param("handleStatus") String handleStatus,
                                       @Param("modifier") String modifier, @Param("remark") String remark);
}
