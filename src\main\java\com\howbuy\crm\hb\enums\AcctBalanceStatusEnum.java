package com.howbuy.crm.hb.enums;

/**
 * <AUTHOR>
 * @description: 高端持仓持仓状态枚举
 * @date 2023/6/6 9:44
 * @since JDK 1.8
 */
public enum AcctBalanceStatusEnum {

    CLEAR_BALANCE("0", "已清仓"),
    HAVE_BALANCE("1", "持仓"),
    ALL_BALANCE("2", "全部");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    AcctBalanceStatusEnum(String code, String description){
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
