package com.howbuy.crm.hb.domain.pushmsg;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**  
    * @description: 自定义企微任务
    * <AUTHOR>
    * @date 2023/10/13 17:59
    */
@Getter
@Setter
@ToString
public class CmWechatPushTask {
    private Integer id;

    /**
    * 消息类型
    */
    private String msgType;

    /**
    * 推送类型 1-只推送企微任务 2-均推送卡片 3-针对添加客户推任务，针对未添加客户推卡片消息
    */
    private String pushType;

    /**
    * 群发任务标题
    */
    private String taskTitle;

    /**
    * 群发任务内容
    */
    private String taskContent;

    /**
    * 群发任务链接
    */
    private String taskUrl;

    /**
    * 群发任务链接标题
    */
    private String taskUrlTitle;

    /**
    * 群发任务链接描述
    */
    private String taskUrlDesc;

    /**
    * 卡片标题
    */
    private String cardTitle;

    /**
    * 卡片内容
    */
    private String cardContent;

    /**
    * 卡片链接
    */
    private String cardUrl;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTime;
}