//package com.howbuy.crm.hb.service.conference.cmconferencescanserviceimpl;
//
//import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
//import com.howbuy.auth.facade.response.CodecSingleResponse;
//import com.howbuy.crm.hb.domain.conference.CmConferenceScan;
//import com.howbuy.crm.hb.persistence.conference.CmConferenceScanMapper;
//import com.howbuy.crm.hb.service.callout.CsCalloutMyTaskService;
//import com.howbuy.crm.hb.service.conference.impl.CmConferenceScanServiceImpl;
//import crm.howbuy.base.db.PageData;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.testng.PowerMockTestCase;
//import org.testng.annotations.Test;
//import java.lang.reflect.Method;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import static org.junit.Assert.assertEquals;
//
///**
// * @description: 单元测试:验证前端参数是否符合要求
// * @author: jianyi.tao
// * @create: 2022/07/18 10:35
// * @since: JDK 1.8
// */
//@PowerMockIgnore("javax.management.*")
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({CmConferenceScanServiceImpl.class, CsCalloutMyTaskService.class, DecryptSingleFacade.class})
//public class TestCheckPageParam extends PowerMockTestCase {
//    @Mock
//    private CmConferenceScanMapper mockScanMapper;
//
//    @Mock
//    private DecryptSingleFacade decryptSingleFacade;
//
//    @Mock
//    private CsCalloutMyTaskService mockMyTaskService;
//
//    @InjectMocks
//    private CmConferenceScanServiceImpl mockServiceImpl;
//
//    // 模拟页面传入参数
//    private static Map<String, String> pageParam = new HashMap<>();
//
//    // 模拟客户列表返回数据
//    private static Map<String, Object> custMap = new HashMap<>();
//
//    // 参数常量
//    private final static String TEST_TELNO = "02136696277";
//    private final static String TEST_MOBILE2 = "15021286907";
//    private final static String TEST_EMAIL = "<EMAIL>";
//    private final static String MOBILE_CIPHER = "0jS3zUKFEFOVfEPlNcjiaQ==02";
//    private final static String MOBILE_DIGEST = "fa6b9346cff8d2f214a7aead19066906";
//
//    // 预设调用返回数据
//    static {
//        // 填充预设参数值
//        pageParam.put("scanId", "21");
//        custMap.put("8000001234", "jianyi.tao");
//    }
//
//    /**
//     * 1、手机号码1已存在
//     */
//    @Test
//    public void test01() throws Exception {
//        // mock查询参会信息方法
//        CmConferenceScan cmConferenceScan = new CmConferenceScan();
//        cmConferenceScan.setMobileDigest(MOBILE_DIGEST);
//        PowerMockito.when(mockScanMapper.findCmConferenceScanById(Mockito.any())).thenReturn(cmConferenceScan);
//
//        // mock查询结果数据
//        List<Map<String, Object>> custList = new ArrayList<>();
//        custList.add(custMap);
//        PageData<Map<String, Object>> pageData = new PageData<>();
//        pageData.setListData(custList);
//
//        // mock手机号查询方法
//        PowerMockito.when(mockMyTaskService.isExsitCustByMobileByPage(Mockito.any())).thenReturn(pageData);
//
//        // mock目标方法
//        Method method = PowerMockito.method(CmConferenceScanServiceImpl.class, "checkPageParam", HashMap.class);
//
//        // 调用目标方法获取返回结果
//        Object result = method.invoke(mockServiceImpl, pageParam);
//
//        // 比对预期结果
//        assertEquals("手机号码1已存在！", ((HashMap) result).get("message"));
//    }
//
//    /**
//     * 2、手机号码1不存在，数据有误！
//     */
//    @Test
//    public void test02() throws Exception {
//        // mock查询参会信息方法
//        CmConferenceScan cmConferenceScan = new CmConferenceScan();
//        cmConferenceScan.setMobileDigest(null);
//        PowerMockito.when(mockScanMapper.findCmConferenceScanById(Mockito.any())).thenReturn(cmConferenceScan);
//
//        // mock查询结果数据
//        List<Map<String, Object>> custList = new ArrayList<>();
//        custList.add(custMap);
//        PageData<Map<String, Object>> pageData = new PageData<>();
//        pageData.setListData(custList);
//
//        // mock手机号查询方法
//        PowerMockito.when(mockMyTaskService.isExsitCustByMobileByPage(Mockito.any())).thenReturn(pageData);
//
//        // mock目标方法
//        Method method = PowerMockito.method(CmConferenceScanServiceImpl.class, "checkPageParam", HashMap.class);
//
//        // 调用目标方法获取返回结果
//        Object result = method.invoke(mockServiceImpl, pageParam);
//
//        // 比对预期结果
//        assertEquals("手机号码1不存在，数据有误！", ((HashMap) result).get("message"));
//    }
//
//    /**
//     * 3、外部接口调用失败！
//     */
//    @Test
//    public void test03() throws Exception {
//        // mock查询参会信息方法
//        CmConferenceScan cmConferenceScan = new CmConferenceScan();
//        cmConferenceScan.setMobileCipher(MOBILE_CIPHER);
//        cmConferenceScan.setMobileDigest(MOBILE_DIGEST);
//        PowerMockito.when(mockScanMapper.findCmConferenceScanById(Mockito.any())).thenReturn(cmConferenceScan);
//
//        // mock查询结果数据
//        List<Map<String, Object>> custList = new ArrayList<>();
//        PageData<Map<String, Object>> pageData = new PageData<>();
//        pageData.setListData(custList);
//
//        // mock手机号查询方法
//        PowerMockito.when(mockMyTaskService.isExsitCustByMobileByPage(Mockito.any())).thenReturn(pageData);
//
//        // mock调用解密接口方法
//        PowerMockito.when(decryptSingleFacade.decrypt(Mockito.any())).thenReturn(null);
//
//        // mock目标方法
//        Method method = PowerMockito.method(CmConferenceScanServiceImpl.class, "checkPageParam", HashMap.class);
//
//        // 调用目标方法获取返回结果
//        Object result = method.invoke(mockServiceImpl, pageParam);
//
//        // 比对预期结果
//        assertEquals("外部接口调用失败！", ((HashMap) result).get("message"));
//    }
//
//    /**
//     * 4、手机号码2（15021286907）已存在，请修改后再试！
//     */
//    @Test
//    public void test04() throws Exception {
//        // mock查询参会信息方法
//        CmConferenceScan cmConferenceScan = new CmConferenceScan();
//        cmConferenceScan.setMobileCipher(MOBILE_CIPHER);
//        PowerMockito.when(mockScanMapper.findCmConferenceScanById(Mockito.any())).thenReturn(cmConferenceScan);
//
//        // mock查询结果数据
//        List<Map<String, Object>> custList = new ArrayList<>();
//        custList.add(custMap);
//        PageData<Map<String, Object>> pageData = new PageData<>();
//        pageData.setListData(custList);
//
//        // mock手机号查询方法
//        PowerMockito.when(mockMyTaskService.isExsitCustByMobileByPage(Mockito.any())).thenReturn(pageData);
//        pageParam.clear();
//        pageParam.put("mobile2", TEST_MOBILE2);
//
//        // mock调用解密接口方法
//        CodecSingleResponse codecSingleResponse = new CodecSingleResponse();
//        codecSingleResponse.setCodecText(TEST_MOBILE2);
//        PowerMockito.when(decryptSingleFacade.decrypt(Mockito.any())).thenReturn(codecSingleResponse);
//
//        // mock目标方法
//        Method method = PowerMockito.method(CmConferenceScanServiceImpl.class, "checkPageParam", HashMap.class);
//
//        // 调用目标方法获取返回结果
//        Object result = method.invoke(mockServiceImpl, pageParam);
//
//        // 比对预期结果
//        assertEquals(null, ((HashMap) result).get("message"));
//    }
//
//    /**
//     * 5、电话号码已存在，请修改后再试！
//     */
//    @Test
//    public void test05() throws Exception {
//        // mock查询参会信息方法
//        CmConferenceScan cmConferenceScan = new CmConferenceScan();
//        cmConferenceScan.setMobileCipher(MOBILE_CIPHER);
//        PowerMockito.when(mockScanMapper.findCmConferenceScanById(Mockito.any())).thenReturn(cmConferenceScan);
//
//        // mock查询结果数据
//        List<Map<String, Object>> custList = new ArrayList<>();
//        custList.add(custMap);
//        PageData<Map<String, Object>> pageData = new PageData<>();
//        pageData.setListData(custList);
//
//        // mock手机号查询方法
//        PowerMockito.when(mockMyTaskService.isExsitCustByMobileByPage(Mockito.any())).thenReturn(pageData);
//
//        pageParam.clear();
//        pageParam.put("telNo", TEST_TELNO);
//
//        // mock调用解密接口方法
//        CodecSingleResponse codecSingleResponse = new CodecSingleResponse();
//        codecSingleResponse.setCodecText(TEST_TELNO);
//        PowerMockito.when(decryptSingleFacade.decrypt(Mockito.any())).thenReturn(codecSingleResponse);
//
//        // mock目标方法
//        Method method = PowerMockito.method(CmConferenceScanServiceImpl.class, "checkPageParam", HashMap.class);
//
//        // 调用目标方法获取返回结果
//        Object result = method.invoke(mockServiceImpl, pageParam);
//
//        // 比对预期结果
//        assertEquals(null, ((HashMap) result).get("message"));
//    }
//
//    /**
//     * 6、电子邮箱已存在，请修改后再试！
//     */
//    @Test
//    public void test06() throws Exception {
//        // mock查询参会信息方法
//        CmConferenceScan cmConferenceScan = new CmConferenceScan();
//        cmConferenceScan.setMobileCipher(MOBILE_CIPHER);
//        PowerMockito.when(mockScanMapper.findCmConferenceScanById(Mockito.any())).thenReturn(cmConferenceScan);
//
//        // mock查询结果数据
//        List<Map<String, Object>> custList = new ArrayList<>();
//        custList.add(custMap);
//        PageData<Map<String, Object>> pageData = new PageData<>();
//        pageData.setListData(custList);
//
//        // mock手机号查询方法
//        PowerMockito.when(mockMyTaskService.isExsitCustByMobileByPage(Mockito.any())).thenReturn(pageData);
//        pageParam.clear();
//        pageParam.put("email", TEST_EMAIL);
//
//        // mock调用解密接口方法
//        CodecSingleResponse codecSingleResponse = new CodecSingleResponse();
//        codecSingleResponse.setCodecText(TEST_EMAIL);
//        PowerMockito.when(decryptSingleFacade.decrypt(Mockito.any())).thenReturn(codecSingleResponse);
//
//        // mock目标方法
//        Method method = PowerMockito.method(CmConferenceScanServiceImpl.class, "checkPageParam", HashMap.class);
//
//        // 调用目标方法获取返回结果
//        Object result = method.invoke(mockServiceImpl, pageParam);
//
//        // 比对预期结果
//        assertEquals(null, ((HashMap) result).get("message"));
//    }
//
//    /**
//     * 7、正常数据，校验成功
//     */
//    @Test
//    public void test07() throws Exception {
//        // mock查询参会信息方法
//        CmConferenceScan cmConferenceScan = new CmConferenceScan();
//        cmConferenceScan.setMobileCipher(MOBILE_CIPHER);
//        PowerMockito.when(mockScanMapper.findCmConferenceScanById(Mockito.any())).thenReturn(cmConferenceScan);
//
//        // mock查询结果数据
//        List<Map<String, Object>> custList = new ArrayList<>();
//        custList.add(custMap);
//        PageData<Map<String, Object>> pageData = new PageData<>();
//        pageData.setListData(custList);
//
//        // mock手机号查询方法
//        PowerMockito.when(mockMyTaskService.isExsitCustByMobileByPage(Mockito.any())).thenReturn(pageData);
//
//        // mock调用解密接口方法
//        CodecSingleResponse codecSingleResponse = new CodecSingleResponse();
//        codecSingleResponse.setCodecText("成功");
//        PowerMockito.when(decryptSingleFacade.decrypt(Mockito.any())).thenReturn(codecSingleResponse);
//
//        // mock目标方法
//        Method method = PowerMockito.method(CmConferenceScanServiceImpl.class, "checkPageParam", HashMap.class);
//
//        // 调用目标方法获取返回结果
//        pageParam.clear();
//        Object result = method.invoke(mockServiceImpl, pageParam);
//
//        // 比对预期结果
//        assertEquals("0000", ((HashMap) result).get("errorCode"));
//    }
//
//}