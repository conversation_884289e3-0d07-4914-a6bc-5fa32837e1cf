/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.custinfo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.howbuy.crm.configuration.annotation.ExcelValidIsNull;
import com.howbuy.crm.configuration.annotation.ExcelValidType;
import lombok.Data;

/**
 * @description: (上传划转客户实体类)
 * <AUTHOR>
 * @date 2023/4/10 10:43
 * @since JDK 1.8
 */
@Data
public class TransferCustInfo {

    @ExcelProperty(value = "投顾客户号",index = 0)
    @ExcelValidIsNull(message = "投顾客户号字段数据不符合要求，请修改后重新上传")
    private String concustno;

    @ExcelProperty(value = "客户姓名",index = 1)
    @ExcelValidIsNull(message = "客户姓名字段数据不符合要求，请修改后重新上传")
    private String custName;

    @ExcelProperty(value = "原投顾",index = 2)
    @ExcelValidIsNull(message = "原投顾字段数据不符合要求，请修改后重新上传")
    private String custconsName;

    @ExcelProperty(value = "原投顾编号",index = 3)
    @ExcelValidIsNull(message = "原投顾编号字段数据不符合要求，请修改后重新上传")
    private String custconscode;

    @ExcelProperty(value = "分配投顾",index = 4)
    @ExcelValidIsNull(message = "分配投顾字段数据不符合要求，请修改后重新上传")
    private String disCustconsName;

    @ExcelProperty(value = "分配投顾编号",index = 5)
    @ExcelValidIsNull(message = "分配投顾编号字段数据不符合要求，请修改后重新上传")
    private String disCustconsCode;

    @ExcelProperty(value = "分配原因",index = 6)
    @ExcelValidType(message = "分配原因不存在")
    @ExcelValidIsNull(message = "分配原因字段数据不符合要求，请修改后重新上传")
    private String reason;

    @ExcelProperty(value = "备注", index = 7)
    private String remarks;

}