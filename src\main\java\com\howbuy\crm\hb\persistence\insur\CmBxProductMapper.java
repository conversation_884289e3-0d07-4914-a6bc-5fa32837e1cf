package com.howbuy.crm.hb.persistence.insur;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.insur.CmBxProduct;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxProductMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxProduct getCmBxProduct(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmBxProduct
      */
	void insertCmBxProduct(CmBxProduct cmBxProduct);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxProduct
	 */
	void updateCmBxProduct(CmBxProduct cmBxProduct);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxProduct> listCmBxProduct(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmBxProduct> listCmBxProductByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
