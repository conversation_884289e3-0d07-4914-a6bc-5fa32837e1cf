/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.system;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: (花名册职级对应层级类)
 * @date 2025/5/8 15:34
 * @since JDK 1.8
 */
@Setter
@Getter
public class HbConstantExpBO {
    /**
     * 职级代码
     */
    private String positionsLevelCode;

    /**
     * 职级名称
     */
    private String positionsLevelName;

    /**
     * 层级代码
     */
    private String userLevelCode;

    /**
     * 层级名称
     */
    private String userLevelName;


}