package com.howbuy.crm.hb.persistence.doubletrade;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleFile;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmDoubleFileMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmDoubleFile getCmDoubleFile(Map<String, String> param);
    
     /**
      * 文件表中新增数据对象
      * @param cmDoubleFile
      */
	void insertCmDoubleFile(CmDoubleFile cmDoubleFile);
	
	/**
	 * 单条修改数据对象
	 * @param cmDoubleFile
	 */
	void updateCmDoubleFile(CmDoubleFile cmDoubleFile);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmDoubleFile(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmDoubleFile(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleFile> listCmDoubleFile(Map<String, String> param);

	/**
	 * 根据fileId 查找 上传成功的 双录回访文件列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleFile> getSuccessFileListByFileId(@Param("fileId") String fileId);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmDoubleFileCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmDoubleFile> listCmDoubleFileByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
