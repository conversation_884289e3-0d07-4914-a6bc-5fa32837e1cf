/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.pushmsg;


import com.howbuy.crm.base.CurrencyEnum;
import com.howbuy.crm.base.HbProductLineEnum;
import com.howbuy.crm.base.PreBookArchTypeEnum;
import com.howbuy.crm.hb.domain.pushmsg.CmMarketReportMessage;
import com.howbuy.crm.hb.domain.pushmsg.CmMarketReportMessageDTO;
import com.howbuy.crm.hb.service.pushmsg.CmMarketReportMessageService;
import com.howbuy.crm.hb.web.dto.pushmsg.CmMarketReportMessageVO;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.DisCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 营销喜报消息推送controller
 * <AUTHOR>
 * @date 2024/3/19 10:13
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/marketReportMessage")
public class CmMarketReportMessageController {

    /**
     * 分销渠道：海外
     */
    private static final String HW_DIS_CODE_NAME = "海外";

    @Autowired
    private CmMarketReportMessageService marketReportMessageService;

    /**
     * @api {GET} /marketReportMessage/list.do list()
     * @apiVersion 1.0.0
     * @apiGroup MarketReportMessageController
     * @apiName list()
     * @apiDescription 营销喜报页面
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"IM_USED"}
     */
    @GetMapping("/list.do")
    public ModelAndView list(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();

        modelAndView.setViewName("/pushmsg/marketReportMessage");
        return modelAndView;
    }


    /**
     * @api {POST} /marketReportMessage/messageList messageList()
     * @apiVersion 1.0.0
     * @apiGroup CmMarketReportMessageController
     * @apiName messageList()
     * @apiDescription 分页查询营销喜报
     * @apiParam (请求参数) {String} isSend
     * @apiParam (请求参数) {String} orgCode
     * @apiParam (请求参数) {String} consCode
     * @apiParam (请求参数) {String} prodCode
     * @apiParam (请求参数) {String} hbType
     * @apiParam (请求参数) {String} payDateStart
     * @apiParam (请求参数) {String} payDateEnd
     * @apiParam (请求参数) {Number} page
     * @apiParam (请求参数) {Number} rows
     * @apiParamExample 请求参数示例
     * prodCode=2O8ymWOU0x&hbType=mtxlF&orgCode=HMsOInhi9&payDateStart=ebQgy6&payDateEnd=Pyy&isSend=7bmg4nX&page=6826&rows=1451&consCode=LyvP
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @PostMapping("/messageList")
    public Map<String, Object> messageList(CmMarketReportMessageDTO dto) {
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("total", 0);
        resultMap.put("rows", new ArrayList<>());

        PageData<CmMarketReportMessage> messagePage = marketReportMessageService.getMarketReportMessageByPage(dto);
        if (CollectionUtils.isEmpty(messagePage.getListData())) {
            return resultMap;
        }
        ConsOrgCache orgcache = ConsOrgCache.getInstance();
        List<CmMarketReportMessageVO> rows = new ArrayList<>(messagePage.getListData().size());
        // 部分字段转义
        for (CmMarketReportMessage message : messagePage.getListData()) {
            CmMarketReportMessageVO messageVO = new CmMarketReportMessageVO();
            BeanUtils.copyProperties(message, messageVO);
            // 投顾所属的部门和区域
            String consCode = message.getConsCode();
            messageVO.setOrgName(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(consCode)));
            String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(consCode));
            if ("0".equals(uporgcode)) {
                messageVO.setUpOrgName(messageVO.getOrgName());
            } else {
                messageVO.setUpOrgName(orgcache.getAllOrgMap().get(uporgcode));
            }
            // 好买产品线
            messageVO.setHbType(HbProductLineEnum.getDescription(message.getHbType()));
            // 币种
            if (StringUtils.isNotBlank(message.getCurrency())) {
                messageVO.setCurrency(CurrencyEnum.getDescription(message.getCurrency()));
            }
            // 产品分销渠道
            String disCodeName;
            if (PreBookArchTypeEnum.HW.getCode().equals(message.getArchType())) {
                disCodeName = HW_DIS_CODE_NAME;
            } else {
                DisCodeEnum disCodeEnum = DisCodeEnum.getEnum(message.getPreDisCode());
                disCodeName = disCodeEnum == null ? "" : disCodeEnum.getChannelEnum().getDescription();
            }
            messageVO.setDisCodeName(disCodeName);

            rows.add(messageVO);
        }

        resultMap.put("total", messagePage.getPageBean().getTotalNum());
        resultMap.put("rows", rows);
        return resultMap;
    }


    /**
     * @api {GET} /marketReportMessage/sendMsg sendMsg()
     * @apiVersion 1.0.0
     * @apiGroup CmMarketReportMessageController
     * @apiName sendMsg()
     * @apiDescription 发送营销喜报
     * @apiParam (请求参数) {String} ids
     * @apiParamExample 请求参数示例
     * ids=jS8dUeuuJr
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @GetMapping("/sendMsg")
    public Map<String, Object> sendMsg(@RequestParam("ids") String ids, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("success", false);
        resultMap.put("msg", "发送失败");

        User user = (User) request.getSession().getAttribute("loginUser");
        String userId = user.getUserId();

        try {
            marketReportMessageService.sendMsg(ids, userId);
            resultMap.put("success", true);
            resultMap.put("msg", "发送成功");
        } catch (Exception e) {
            log.error("发送营销喜报消息失败", e);
        }
        return resultMap;
    }
}