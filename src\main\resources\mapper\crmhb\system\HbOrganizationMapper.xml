<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.HbOrganizationMapper">
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	  
    <resultMap id="hbOrganizationParentcode" type="com.howbuy.crm.hb.domain.system.HbOrganization">
		<result column="parentorgcode" property="parentorgcode" />		
	</resultMap>	
	
	<resultMap id="maxSort" type="com.howbuy.crm.hb.domain.system.HbOrganization">
		<result column="sort" property="sort" />		
	</resultMap>  
	
	<resultMap id="orgChildByparentorgcode" type="com.howbuy.crm.hb.domain.system.HbOrganization">
		<result column="orgcode" property="orgcode" />
		<result column="orgname" property="orgname" />
		<result column="parentorgcode" property="parentorgcode" />
		<result column="parentorgname" property="parentorgname" />		
	</resultMap>
	
	 <select id="getHbOrganization" parameterType="Map" resultType="HbOrganization">
	    SELECT
	          *
	    FROM HB_ORGANIZATION
	    where 1=1  
	              <if test="orgcode != null"> AND orgcode = #{orgcode} </if>             
                  <if test="orgname != null"> AND orgname = #{orgname} </if>             
                  <if test="englishname != null"> AND englishname = #{englishname} </if>             
                  <if test="parentorgcode != null"> AND parentorgcode = #{parentorgcode} </if>
                  <if test="parentorgcoderoot != null"> AND parentorgcode is null </if>             
                  <if test="sort != null"> AND sort = #{sort} </if>             
                  <if test="orgtype != null"> AND orgtype = #{orgtype} </if>             
                  <if test="status != null"> AND status = #{status} </if>             
                  <if test="showflag != null"> AND showflag = #{showflag} </if>             
                  <if test="province != null"> AND province = #{province} </if>             
                  <if test="provincename != null"> AND provincename = #{provincename} </if>             
                  <if test="city != null"> AND city = #{city} </if>             
                  <if test="cityname != null"> AND cityname = #{cityname} </if>             
                  <if test="area != null"> AND area = #{area} </if>             
                  <if test="areaname != null"> AND areaname = #{areaname} </if>             
                  <if test="address != null"> AND address = #{address} </if>             
                  <if test="recstat != null"> AND recstat = #{recstat} </if>             
                  <if test="checkflag != null"> AND checkflag = #{checkflag} </if>             
                  <if test="telno != null"> AND telno = #{telno} </if>             
                  <if test="fax != null"> AND fax = #{fax} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="checker != null"> AND checker = #{checker} </if>             
                  <if test="credate != null"> AND credate = #{credate} </if>             
                  <if test="moddate != null"> AND moddate = #{moddate} </if>             
                  <if test="checkdate != null"> AND checkdate = #{checkdate} </if>             
        	  </select>
	  
	  
	  <insert id="insertHbOrganization" parameterType="HbOrganization" >
	    INSERT INTO HB_ORGANIZATION (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="orgcode != null"> orgcode, </if> 
	      	      <if test="orgname != null"> orgname, </if> 
	      	      <if test="englishname != null"> englishname, </if> 
	      	      <if test="parentorgcode != null"> parentorgcode, </if> 
	      	      <if test="sort != null"> sort, </if> 
	      	      <if test="orgtype != null"> orgtype, </if> 
	      	      <if test="status != null"> status, </if> 
	      	      <if test="showflag != null"> showflag, </if> 
	      	      <if test="province != null"> province, </if> 
	      	      <if test="provincename != null"> provincename, </if> 
	      	      <if test="city != null"> city, </if> 
	      	      <if test="cityname != null"> cityname, </if> 
	      	      <if test="area != null"> area, </if> 
	      	      <if test="areaname != null"> areaname, </if> 
	      	      <if test="address != null"> address, </if> 
	      	      <if test="recstat != null"> recstat, </if> 
	      	      <if test="checkflag != null"> checkflag, </if> 
	      	      <if test="telno != null"> telno, </if> 
	      	      <if test="fax != null"> fax, </if> 
	      	      <if test="creator != null"> creator, </if> 
	      	      <if test="modifier != null"> modifier, </if> 
	      	      <if test="checker != null"> checker, </if> 
	      	      <if test="credate != null"> credate, </if> 
	      	      <if test="moddate != null"> moddate, </if> 
	      	      <if test="checkdate != null"> checkdate, </if> 
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="orgcode != null"> #{orgcode}, </if> 
	      	      <if test="orgname != null"> #{orgname}, </if> 
	      	      <if test="englishname != null"> #{englishname}, </if> 
	      	      <if test="parentorgcode != null"> #{parentorgcode}, </if> 
	      	      <if test="sort != null"> #{sort}, </if> 
	      	      <if test="orgtype != null"> #{orgtype}, </if> 
	      	      <if test="status != null"> #{status}, </if> 
	      	      <if test="showflag != null"> #{showflag}, </if> 
	      	      <if test="province != null"> #{province}, </if> 
	      	      <if test="provincename != null"> #{provincename}, </if> 
	      	      <if test="city != null"> #{city}, </if> 
	      	      <if test="cityname != null"> #{cityname}, </if> 
	      	      <if test="area != null"> #{area}, </if> 
	      	      <if test="areaname != null"> #{areaname}, </if> 
	      	      <if test="address != null"> #{address}, </if> 
	      	      <if test="recstat != null"> #{recstat}, </if> 
	      	      <if test="checkflag != null"> #{checkflag}, </if> 
	      	      <if test="telno != null"> #{telno}, </if> 
	      	      <if test="fax != null"> #{fax}, </if> 
	      	      <if test="creator != null"> #{creator}, </if> 
	      	      <if test="modifier != null"> #{modifier}, </if> 
	      	      <if test="checker != null"> #{checker}, </if> 
	      	      <if test="credate != null"> #{credate}, </if> 
	      	      <if test="moddate != null"> #{moddate}, </if> 
	      	      <if test="checkdate != null"> #{checkdate}, </if> 
	               </trim>	
         )      
	  </insert>
	  
	  
	  <update id="updateHbOrganization" parameterType="HbOrganization" >
	    UPDATE HB_ORGANIZATION	    
	    <set>
	                <if test="orgcode != null"> orgcode = #{orgcode}, </if>             
                    <if test="orgname != null"> orgname = #{orgname}, </if>             
                    <if test="englishname != null"> englishname = #{englishname}, </if>             
                    <if test="parentorgcode != null"> parentorgcode = #{parentorgcode}, </if>             
                    <if test="sort != null"> sort = #{sort}, </if>             
                    <if test="orgtype != null"> orgtype = #{orgtype}, </if>             
                    <if test="status != null"> status = #{status}, </if>             
                    <if test="showflag != null"> showflag = #{showflag}, </if>             
                    <if test="province != null"> province = #{province}, </if>             
                    <if test="provincename != null"> provincename = #{provincename}, </if>             
                    <if test="city != null"> city = #{city}, </if>             
                    <if test="cityname != null"> cityname = #{cityname}, </if>             
                    <if test="area != null"> area = #{area}, </if>             
                    <if test="areaname != null"> areaname = #{areaname}, </if>             
                    <if test="address != null"> address = #{address}, </if>             
                    <if test="recstat != null"> recstat = #{recstat}, </if>             
                    <if test="checkflag != null"> checkflag = #{checkflag}, </if>             
                    <if test="telno != null"> telno = #{telno}, </if>             
                    <if test="fax != null"> fax = #{fax}, </if>             
                    <if test="creator != null"> creator = #{creator}, </if>             
                    <if test="modifier != null"> modifier = #{modifier}, </if>             
                    <if test="checker != null"> checker = #{checker}, </if>             
                    <if test="credate != null"> credate = #{credate}, </if>             
                    <if test="moddate != null"> moddate = #{moddate}, </if>             
                    <if test="checkdate != null"> checkdate = #{checkdate}, </if>             
                 </set>
          where orgcode = #{orgcode}
	  </update>
	  
	  <update id="updateHbOrganizationChildren" parameterType="HbOrganization" >
	    UPDATE HB_ORGANIZATION tt	    
	    <set>                                       
                    <if test="showflag != null"> showflag = #{showflag}, </if>                                                                          
                    <if test="modifier != null"> modifier = #{modifier}, </if>                                           
                    <if test="moddate != null"> moddate = #{moddate}, </if>                                           
         </set>
          where tt.orgcode in (select orgcode 
                               from HB_ORGANIZATION t 
                               where  t.status = '0' 
                               start with t.parentorgcode = #{orgcode} 
                               connect by PRIOR orgcode = parentorgcode
                               )
	  </update>
	  
	  
	  <delete id="delHbOrganization" parameterType="String">
	    DELETE  from HB_ORGANIZATION
	    where orgcode = #{orgcode}
	  </delete>
	  
	  
	  <delete id="delListHbOrganization" parameterType="String">
	    DELETE  from HB_ORGANIZATION
	    where orgcode in ($orgcode$)
	        
	  </delete>
	  
	  
	  <select id="listHbOrganization" parameterType="Map" resultType="HbOrganization" useCache="false">
	    SELECT
	          *
	    FROM HB_ORGANIZATION
	    where 1=1  
	              <if test="orgcode != null"> AND orgcode = #{orgcode} </if>             
                  <if test="orgname != null"> AND orgname = #{orgname} </if>             
                  <if test="englishname != null"> AND englishname = #{englishname} </if>             
                  <if test="parentorgcode != null"> AND parentorgcode = #{parentorgcode} </if>
                  <if test="parentorgcodenotroot != null"> AND parentorgcode is not null </if>             
                  <if test="sort != null"> AND sort = #{sort} </if>             
                  <if test="orgtype != null"> AND orgtype = #{orgtype} </if>             
                  <if test="status != null"> AND status = #{status} </if>             
                  <if test="showflag != null"> AND showflag = #{showflag} </if>             
                  <if test="province != null"> AND province = #{province} </if>             
                  <if test="provincename != null"> AND provincename = #{provincename} </if>             
                  <if test="city != null"> AND city = #{city} </if>             
                  <if test="cityname != null"> AND cityname = #{cityname} </if>             
                  <if test="area != null"> AND area = #{area} </if>             
                  <if test="areaname != null"> AND areaname = #{areaname} </if>             
                  <if test="address != null"> AND address = #{address} </if>             
                  <if test="recstat != null"> AND recstat = #{recstat} </if>             
                  <if test="checkflag != null"> AND checkflag = #{checkflag} </if>             
                  <if test="telno != null"> AND telno = #{telno} </if>             
                  <if test="fax != null"> AND fax = #{fax} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="checker != null"> AND checker = #{checker} </if>             
                  <if test="credate != null"> AND credate = #{credate} </if>             
                  <if test="moddate != null"> AND moddate = #{moddate} </if>             
                  <if test="checkdate != null"> AND checkdate = #{checkdate} </if>             
        	  </select>
        	  
        <select id="listHbOrganizationParentCode" parameterType="Map" resultMap="hbOrganizationParentcode" useCache="false">
	    SELECT parentorgcode         
        FROM HB_ORGANIZATION
        where parentorgcode is not null and status = #{status}
        GROUP BY parentorgcode                            
        </select>
        
        <select id="listHbOrganizationTree" parameterType="Map" resultType="HbOrganization" useCache="false">
	    select level,t.* 
	    from HB_ORGANIZATION t 
	    where t.status = #{status}
	    start with t.parentorgcode = '0' 
	    connect by PRIOR orgcode = parentorgcode
        order by level,sort                          
        </select>
        
        <select id="listHbOrganizationTreeByorgcode" parameterType="Map" resultType="HbOrganization" useCache="false">
	    SELECT LEVEL, T.*,(CASE WHEN ORGCODE IN (SELECT PARENTORGCODE FROM HB_ORGANIZATION) THEN  'N' ELSE 'Y' END) isleaf
		  FROM HB_ORGANIZATION T
		where t.status = '0'
		<if test="showflag != null"> AND t.showflag = #{showflag} </if>
		 START WITH ORGCODE = #{orgcode}
		CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		 ORDER SIBLINGS BY T.SORT                       
        </select>
        
        <select id="listHbOrganizationTreeByBusinessCenter" parameterType="Map" resultType="HbOrganization" useCache="false">
	    SELECT LEVEL, T.*,(CASE WHEN ORGCODE IN (SELECT PARENTORGCODE FROM HB_ORGANIZATION) THEN  'N' ELSE 'Y' END) isleaf
		  FROM HB_ORGANIZATION T
		where t.status = '0'  and LEVEL <![CDATA[<=]]> 2
		<if test="showflag != null"> AND t.showflag = #{showflag} </if>    
		 START WITH ORGCODE = #{orgcode}
		CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		 ORDER SIBLINGS BY T.SORT                       
        </select>
        
        <select id="listHbOrganizationOutByCode" parameterType="Map" resultType="HbOrganization" useCache="false">
	        SELECT LEVEL, T.*
			  FROM HB_ORGANIZATION T
			 WHERE T.STATUS = '0'
			   AND T.ORGTYPE = '0'
			 START WITH T.ORGCODE = #{orgcode}
			CONNECT BY PRIOR T.PARENTORGCODE = T.ORGCODE
			 ORDER BY LEVEL
		</select>
		
		<select id="listOrgChildByparentorgcode" parameterType="Map" resultMap="orgChildByparentorgcode" useCache="false">
	    select tt.orgcode as orgcode,tt.orgname as orgname,t1.orgcode as parentorgcode,t1.orgname as parentorgname from 
        (
	       select LEVEL, T.* 
           from HB_ORGANIZATION t 
           where  t.status = '0' 
           <if test="orgtype != null"> AND orgtype = #{orgtype} </if>  
           start with t.parentorgcode = #{parentorgcode} 
           connect by PRIOR orgcode = parentorgcode
           ORDER SIBLINGS BY T.SORT 
        ) tt left join HB_ORGANIZATION T1
        on t1.orgcode = tt.parentorgcode          
       </select>
        
        <select id="getMaxSort" parameterType="Map" resultMap="maxSort" useCache="false">
	    select max(sort) as sort
	    from HB_ORGANIZATION t 
	    where t.parentorgcode = #{parentorgcode}                          
        </select>
        
        <update id="updateHbOrganizationByStatus" parameterType="String">
	    UPDATE HB_ORGANIZATION t set status = '1'
	    where t.parentorgcode = #{id} OR t.orgcode = #{id}
	  </update>
	  
	  <select id="listHbOrganizationByPage" parameterType="Map" resultType="HbOrganization" useCache="false">
	    SELECT
	          *
	    FROM HB_ORGANIZATION
	    where 1=1   
	              <if test="param.orgcode != null"> AND orgcode = #{param.orgcode} </if>             
                  <if test="param.orgname != null"> AND orgname = #{param.orgname} </if>             
                  <if test="param.englishname != null"> AND englishname = #{param.englishname} </if>             
                  <if test="param.parentorgcode != null"> AND parentorgcode = #{param.parentorgcode} </if>             
                  <if test="param.sort != null"> AND sort = #{param.sort} </if>             
                  <if test="param.orgtype != null"> AND orgtype = #{param.orgtype} </if>             
                  <if test="param.status != null"> AND status = #{param.status} </if>             
                  <if test="param.showflag != null"> AND showflag = #{param.showflag} </if>             
                  <if test="param.province != null"> AND province = #{param.province} </if>             
                  <if test="param.provincename != null"> AND provincename = #{param.provincename} </if>             
                  <if test="param.city != null"> AND city = #{param.city} </if>             
                  <if test="param.cityname != null"> AND cityname = #{param.cityname} </if>             
                  <if test="param.area != null"> AND area = #{param.area} </if>             
                  <if test="param.areaname != null"> AND areaname = #{param.areaname} </if>             
                  <if test="param.address != null"> AND address = #{param.address} </if>             
                  <if test="param.recstat != null"> AND recstat = #{param.recstat} </if>             
                  <if test="param.checkflag != null"> AND checkflag = #{param.checkflag} </if>             
                  <if test="param.telno != null"> AND telno = #{param.telno} </if>             
                  <if test="param.fax != null"> AND fax = #{param.fax} </if>             
                  <if test="param.creator != null"> AND creator = #{param.creator} </if>             
                  <if test="param.modifier != null"> AND modifier = #{param.modifier} </if>             
                  <if test="param.checker != null"> AND checker = #{param.checker} </if>             
                  <if test="param.credate != null"> AND credate = #{param.credate} </if>             
                  <if test="param.moddate != null"> AND moddate = #{param.moddate} </if>             
                  <if test="param.checkdate != null"> AND checkdate = #{param.checkdate} </if>             
        	  </select>
	  
	  <select id="getHbOrganizationCount" parameterType="Map" resultType="int">
	    SELECT
	          COUNT(*)
	    FROM HB_ORGANIZATION
	    where 1=1  
	              <if test="orgcode != null"> AND orgcode = #{orgcode} </if>             
                  <if test="orgname != null"> AND orgname = #{orgname} </if>             
                  <if test="englishname != null"> AND englishname = #{englishname} </if>             
                  <if test="parentorgcode != null"> AND parentorgcode = #{parentorgcode} </if>             
                  <if test="sort != null"> AND sort = #{sort} </if>             
                  <if test="orgtype != null"> AND orgtype = #{orgtype} </if>             
                  <if test="status != null"> AND status = #{status} </if>             
                  <if test="showflag != null"> AND showflag = #{showflag} </if>             
                  <if test="province != null"> AND province = #{province} </if>             
                  <if test="provincename != null"> AND provincename = #{provincename} </if>             
                  <if test="city != null"> AND city = #{city} </if>             
                  <if test="cityname != null"> AND cityname = #{cityname} </if>             
                  <if test="area != null"> AND area = #{area} </if>             
                  <if test="areaname != null"> AND areaname = #{areaname} </if>             
                  <if test="address != null"> AND address = #{address} </if>             
                  <if test="recstat != null"> AND recstat = #{recstat} </if>             
                  <if test="checkflag != null"> AND checkflag = #{checkflag} </if>             
                  <if test="telno != null"> AND telno = #{telno} </if>             
                  <if test="fax != null"> AND fax = #{fax} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="checker != null"> AND checker = #{checker} </if>             
                  <if test="credate != null"> AND credate = #{credate} </if>             
                  <if test="moddate != null"> AND moddate = #{moddate} </if>             
                  <if test="checkdate != null"> AND checkdate = #{checkdate} </if>             
        	  </select>
        	  
       <select id="getOrgChildShowCount" parameterType="Map" resultType="int">
	    SELECT
	    COUNT(*)
	    FROM HB_ORGANIZATION tt
	    where tt.orgcode in (select orgcode 
                               from HB_ORGANIZATION t 
                               where  t.status = '0' AND T.showflag = '0'
                               start with t.parentorgcode = #{orgcode} 
                               connect by PRIOR orgcode = parentorgcode
                               )             
       </select>
       
       <select id="getOrgFatherShowCount" parameterType="Map" resultType="int">
	    select count(*)
        from HB_ORGANIZATION t
        where t.orgcode = (select tt.parentorgcode
                           from HB_ORGANIZATION tt
                           where tt.orgcode = #{orgcode})
              and t.status =0 and t.showflag = 0            
       </select>

        <select id="getParentOrgcode" parameterType="String" resultType="String">
            SELECT ORGCODE FROM HB_ORGANIZATION
            WHERE PARENTORGCODE='0'
            AND ORGCODE IN
             (
            SELECT ORGCODE
              FROM HB_ORGANIZATION T
             WHERE T.STATUS = '0'
             START WITH T.ORGCODE = #{OUTLETCODE}
            CONNECT BY PRIOR  PARENTORGCODE= ORGCODE)
        </select>

        <select id="getIsBelong" parameterType="Map" resultType="String" useCache="false">
            select (case
             when count(1) > 0 then
              'TRUE'
             else
              'FALSE'
           end)
          from (select *
                  from hb_organization ho
                 start with ho.orgcode = (select orgcode
                                            from cm_sales_task
                                           where task = #{task}
                                             and salesno = #{task})
                connect by prior ho.orgcode = ho.parentorgcode) m
         where m.orgcode = #{orgcode}
        </select>

    <select id="getFirstLevelOrgCode" parameterType="Map" resultType="CmNewOrgCons" useCache="false">
        select orgcode,orgname from hb_organization where parentorgcode=#{parentorgcode} and status='0'
    </select>
    
    <select id="getTwoLevelOrgCode" parameterType="Map" resultType="CmNewOrgCons" useCache="false">
			SELECT distinct CASE
			         WHEN CC.TEAMCODE IS NULL THEN 
			          #{parentorgcode}||'@'||CC.CONSCODE 
			         WHEN CC.TEAMCODE IS NOT NULL THEN
			          HO.ORGCODE
			       END AS ORGCODE,
			       CASE
			         WHEN CC.TEAMCODE IS NULL THEN
			          CC.CONSNAME
			         WHEN CC.TEAMCODE IS NOT NULL THEN
			          HO.ORGNAME
			       END AS ORGNAME
			  FROM CM_CONSULTANT CC
			  LEFT JOIN HB_ORGANIZATION HO
			    ON CC.TEAMCODE = HO.ORGCODE
			 WHERE CC.OUTLETCODE = #{parentorgcode} and cc.consstatus='1' and (ho.status='0' or ho.status is null)  and cc.ISVIRTUAL='0'
    </select>
    

    <select id="getConsCode" parameterType="Map" resultType="CmNewOrgCons" useCache="false">
        select cc.conscode,cc.consname from cm_consultant cc where cc.consstatus='1' and teamcode=#{teamcode}  and cc.ISVIRTUAL='0'
    </select>

    <select id="getBelongOrg" parameterType="String" resultType="Map" useCache="false">
       SELECT *
      FROM (SELECT LEVEL AS L, HO.ORGNAME, HO.ORGCODE
              FROM HB_ORGANIZATION HO
             START WITH HO.ORGCODE = '0'
            CONNECT BY PRIOR HO.ORGCODE = HO.PARENTORGCODE) M
     WHERE M.ORGCODE IN
           (SELECT ORGCODE
              FROM HB_ORGANIZATION HOO
             START WITH HOO.ORGCODE = #{outletcode}
            CONNECT BY PRIOR HOO.PARENTORGCODE = HOO.ORGCODE)
    </select>
	  
	<select id="getHbOrganizationChildCount" parameterType="Map" resultType="int" useCache="false">
	    select count(t.orgcode)
		  from hb_organization t
		 where t.status = '0'
		   and t.showflag = '0'
		   and t.orgcode=#{porgcode}
		 start with t.parentorgcode = #{orgcode}
		connect by parentorgcode = prior orgcode
     </select>
     
     <select id="getHbOrgChildConsCount" parameterType="Map" resultType="int" useCache="false">
	    SELECT COUNT(*)
		  FROM CM_CONSULTANT T
		  LEFT JOIN HB_ORGANIZATION T1
		    ON T.OUTLETCODE = T1.ORGCODE
		  LEFT JOIN HB_ORGANIZATION T2
		    ON T.TEAMCODE = T2.ORGCODE
		 WHERE T1.ORGCODE = #{orgcode}
		    OR T2.ORGCODE = #{orgcode}
     </select>

    <select id="getSubOrgCodeList" resultType="string" parameterType="string">
        SELECT T.ORGCODE FROM HB_ORGANIZATION T
        <where>
            AND T.STATUS='0'
            AND T.PARENTORGCODE=#{orgCode, jdbcType=VARCHAR}
        </where>
    </select>
</mapper>



