package com.howbuy.crm.hb.domain.report;

import java.io.Serializable;

/**
 * @Description: 实体类SyncBpFundMan.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class SyncBpFundMan implements Serializable {

private static final long serialVersionUID = 1L;

	private String fundManCode;
	
	private String fundManName;
	
	private String fundManAbbr;
	
	private String fundManStat;
	
	private String addr;
	
	private String postCode;
	
	private String contact;
	
	private String telNo;
	
	private String fax;
	
	private String establishDt;
	
	private String homePage;
	
	private String summary;
	
	private String recStat;
	
	private String checkFlag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String creDt;
	
	private String modDt;
	
	private String syncDate;
	
	private String fundManAttrPinyin;
	
	public String getFundManCode() {
		return this.fundManCode;
	}

	public void setFundManCode(String fundManCode) {
		this.fundManCode = fundManCode;
	}
	
	public String getFundManName() {
		return this.fundManName;
	}

	public void setFundManName(String fundManName) {
		this.fundManName = fundManName;
	}
	
	public String getFundManAbbr() {
		return this.fundManAbbr;
	}

	public void setFundManAbbr(String fundManAbbr) {
		this.fundManAbbr = fundManAbbr;
	}
	
	public String getFundManStat() {
		return this.fundManStat;
	}

	public void setFundManStat(String fundManStat) {
		this.fundManStat = fundManStat;
	}
	
	public String getAddr() {
		return this.addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}
	
	public String getPostCode() {
		return this.postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	
	public String getContact() {
		return this.contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}
	
	public String getTelNo() {
		return this.telNo;
	}

	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}
	
	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}
	
	public String getEstablishDt() {
		return this.establishDt;
	}

	public void setEstablishDt(String establishDt) {
		this.establishDt = establishDt;
	}
	
	public String getHomePage() {
		return this.homePage;
	}

	public void setHomePage(String homePage) {
		this.homePage = homePage;
	}
	
	public String getSummary() {
		return this.summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}
	
	public String getRecStat() {
		return this.recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}
	
	public String getCheckFlag() {
		return this.checkFlag;
	}

	public void setCheckFlag(String checkFlag) {
		this.checkFlag = checkFlag;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getCreDt() {
		return this.creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}
	
	public String getModDt() {
		return this.modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}
	
	public String getSyncDate() {
		return this.syncDate;
	}

	public void setSyncDate(String syncDate) {
		this.syncDate = syncDate;
	}
	
	public String getFundManAttrPinyin() {
		return this.fundManAttrPinyin;
	}

	public void setFundManAttrPinyin(String fundManAttrPinyin) {
		this.fundManAttrPinyin = fundManAttrPinyin;
	}
	

   public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
