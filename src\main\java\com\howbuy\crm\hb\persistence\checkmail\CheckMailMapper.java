package com.howbuy.crm.hb.persistence.checkmail;

import com.howbuy.crm.hb.domain.checkmail.CmCheckMailConMain;
import com.howbuy.crm.hb.domain.checkmail.CmCheckMailConSub;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户定制对账单
 * <AUTHOR>
 *
 */
public interface CheckMailMapper {

	/**
	 * 分页查询
	 * @param param
	 * @param pageBean
	 * @return
	 */
    List<CmCheckMailConMain> listCheckMailByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 获取全部
     * @return
     */
    List<CmCheckMailConMain> getAll();

    /**
     * 获取全部
     * @param id
     * @return
     */
    List<CmCheckMailConSub> getAllSubByMid(String id);

    /**
     * 获取单个
     * @param id
     * @return
     */
    CmCheckMailConMain getConMainById(String id);

    /**
     * 获取全部
     * @param id
     * @param productType
     * @return
     */
    List<CmCheckMailConSub> getAllSubByMidAndProType(@Param("mid") String id, @Param("producttype") String productType);
}
