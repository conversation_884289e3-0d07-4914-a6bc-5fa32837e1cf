package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxOptLog.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxOptLog implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private BigDecimal preid;
	private String opttype;
	private String des;
	private String creator;
	private Date creatdt;
}
