package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.CmConsultant;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmConsultantMapper {

	/**
	 * 查询某部门或某团队下的所有投顾
	 * @param param
	 * @return
	 */
	List<CmConsultant> listOrgConsByOrgCode(Map<String, String> param);

	/**
	 * 查询某部门或某团队下的所有投顾
	 * @param param
	 * @return
	 */
	List<CmConsultant> listConsCodeByConsCode(Map<String, String> param);

	/**
	 * 获得投顾的分机号
	 * 
	 * @param paramMap
	 * @return List<String>
	 */
	List<String> listCmConsultantsTel(Map<String, String> paramMap);

	/**
	 * 显示
	 * @param param
	 * @return
	 */
	List<CmConsultant> showConsCodeList(Map<String, List> param);

}
