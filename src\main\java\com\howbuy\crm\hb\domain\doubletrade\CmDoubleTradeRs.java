package com.howbuy.crm.hb.domain.doubletrade;

import java.io.Serializable;

/**
 * @Description: 实体类CmDoubleTradeRs.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmDoubleTradeRs implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String contractNo;

	/**
	 * 交易日期
	 */
	private String appDt;

	/**
	 * 公募客户号
	 */
	private String custNo;

	/**
	 * 客户姓名
	 */
	private String custName;

	/**
	 * 客户风险等级
	 */
	private String riskLevel;

	private String mobile;
	
	private String mobileCipher;

	private String gender;

	private String fundCode;

	private String fundName;

	/**
	 * 基金类型
	 */
	private String fundType;

	/**
	 * 产品风险等级
	 */
	private String fundRiskLevel;

	/**
	 * 购买金额
	 */
	private String appAmt;

	private String handleFlag;

	private String remark;

	private String creator;

	private String creDt;

	private String modifier;

	private String modDt;

	private String uploader;

	private String uploadDt;

	private String uploadType;

	private String fileId;

	private String filePath;

	private String fileName;

	private String conscustNo;

	private String handleCount;

	private String hboneNo;

	public String getContractNo() {
		return contractNo;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	public String getAppDt() {
		return appDt;
	}

	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getRiskLevel() {
		return riskLevel;
	}

	public void setRiskLevel(String riskLevel) {
		this.riskLevel = riskLevel;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getFundType() {
		return fundType;
	}

	public void setFundType(String fundType) {
		this.fundType = fundType;
	}

	public String getFundRiskLevel() {
		return fundRiskLevel;
	}

	public void setFundRiskLevel(String fundRiskLevel) {
		this.fundRiskLevel = fundRiskLevel;
	}

	public String getAppAmt() {
		return appAmt;
	}

	public void setAppAmt(String appAmt) {
		this.appAmt = appAmt;
	}

	public String getHandleFlag() {
		return handleFlag;
	}

	public void setHandleFlag(String handleFlag) {
		this.handleFlag = handleFlag;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreDt() {
		return creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModDt() {
		return modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}

	public String getUploader() {
		return uploader;
	}

	public void setUploader(String uploader) {
		this.uploader = uploader;
	}

	public String getUploadDt() {
		return uploadDt;
	}

	public void setUploadDt(String uploadDt) {
		this.uploadDt = uploadDt;
	}

	public String getUploadType() {
		return uploadType;
	}

	public void setUploadType(String uploadType) {
		this.uploadType = uploadType;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getHandleCount() {
		return handleCount;
	}

	public void setHandleCount(String handleCount) {
		this.handleCount = handleCount;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getMobileCipher() {
		return mobileCipher;
	}

	public void setMobileCipher(String mobileCipher) {
		this.mobileCipher = mobileCipher;
	}

	public String getHboneNo() {
		return hboneNo;
	}

	public void setHboneNo(String hboneNo) {
		this.hboneNo = hboneNo;
	}
}
