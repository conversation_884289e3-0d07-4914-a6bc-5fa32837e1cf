package com.howbuy.crm.hb.domain.system;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.howbuy.crm.hb.domain.insur.PageVo;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmConsultantZjSalArch extends PageVo implements Serializable  {
	private static final long serialVersionUID = 1L;
	private String conscode;
	private String consname;
	private String centerName;
	private String areacode;
	private String areaname;
	private String outletcode;
	private String orgname;
	private String teamcode;
	private String teamname;
	private String userid;
	private String orgcode;
	/**
	 * 工号
	 */
	private String userno;
	private String provcode;
	private String citycode;
	private String cityname;
	/**
	 * 层级	 */
	private String userlevel;
	/**
	 * 当月职级
	 */
	private String curmonthlevel;
    /**
     * 员工状态
     */
    private String worktype       ;
	/**
	 * 在职状态
	 */
    private String workstate      ;
	  
	/**
	 *入职日期
	*/
	private String startdt        ;
	  
	/**
	 * 转正日期
	 */
	private String regulardt      ;
	/**
	 * 离职日期
	 */
	private String quitdt         ;
	  
	private String years;
	
	private String ranklevel1m;
	
	private BigDecimal salary1m;
	
	private String ranklevel2m;
	
	private BigDecimal salary2m;
	
	private String ranklevel3m;
	
	private BigDecimal salary3m;
	
	private String ranklevel4m;
	
	private BigDecimal salary4m;
	
	private String ranklevel5m;
	
	private BigDecimal salary5m;
	
	private String ranklevel6m;
	
	private BigDecimal salary6m;
	
	private String ranklevel7m;
	
	private BigDecimal salary7m;
	
	private String ranklevel8m;
	
	private BigDecimal salary8m;
	
	private String ranklevel9m;
	
	private BigDecimal salary9m;
	
	private String ranklevel10m;
	
	private BigDecimal salary10m;
	
	private String ranklevel11m;
	
	private BigDecimal salary11m;
	
	private String ranklevel12m;
	
	private BigDecimal salary12m;
	
	private Date creatdt;
    private String modor;
    private Date moddt;
	/**
	 * 审核状态
	 */
	private String checkflag;
	private String beginQuitdt;
	private String endQuitdt;
	private String beginStartDate;
	private String endStartDate;
	private List<String> conscodes;

	public List<String> getConscodes() {
		return conscodes;
	}

	public void setConscodes(List<String> conscodes) {
		this.conscodes = conscodes;
	}

	public String getBeginQuitdt() {
		return beginQuitdt;
	}

	public void setBeginQuitdt(String beginQuitdt) {
		this.beginQuitdt = beginQuitdt;
	}

	public String getEndQuitdt() {
		return endQuitdt;
	}

	public void setEndQuitdt(String endQuitdt) {
		this.endQuitdt = endQuitdt;
	}

	public String getBeginStartDate() {
		return beginStartDate;
	}

	public void setBeginStartDate(String beginStartDate) {
		this.beginStartDate = beginStartDate;
	}

	public String getEndStartDate() {
		return endStartDate;
	}

	public void setEndStartDate(String endStartDate) {
		this.endStartDate = endStartDate;
	}

	public String getCheckflag() {
		return checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}

	private String  sort;

	private String  order;

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

	public String getCenterName() {
		return centerName;
	}

	public void setCenterName(String centerName) {
		this.centerName = centerName;
	}

	public String getAreacode() {
		return areacode;
	}

	public void setAreacode(String areacode) {
		this.areacode = areacode;
	}

	public String getAreaname() {
		return areaname;
	}

	public void setAreaname(String areaname) {
		this.areaname = areaname;
	}

	public String getOutletcode() {
		return outletcode;
	}

	public void setOutletcode(String outletcode) {
		this.outletcode = outletcode;
	}

	public String getOrgname() {
		return orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}

	public String getTeamcode() {
		return teamcode;
	}

	public void setTeamcode(String teamcode) {
		this.teamcode = teamcode;
	}

	public String getTeamname() {
		return teamname;
	}

	public void setTeamname(String teamname) {
		this.teamname = teamname;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getUserno() {
		return userno;
	}

	public void setUserno(String userno) {
		this.userno = userno;
	}

	public String getProvcode() {
		return provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public String getCityname() {
		return cityname;
	}

	public void setCityname(String cityname) {
		this.cityname = cityname;
	}

	public String getWorktype() {
		return worktype;
	}

	public void setWorktype(String worktype) {
		this.worktype = worktype;
	}

	public String getWorkstate() {
		return workstate;
	}

	public void setWorkstate(String workstate) {
		this.workstate = workstate;
	}

	public String getStartdt() {
		return startdt;
	}

	public void setStartdt(String startdt) {
		this.startdt = startdt;
	}

	public String getRegulardt() {
		return regulardt;
	}

	public void setRegulardt(String regulardt) {
		this.regulardt = regulardt;
	}

	public String getQuitdt() {
		return quitdt;
	}

	public void setQuitdt(String quitdt) {
		this.quitdt = quitdt;
	}

	public String getYears() {
		return years;
	}

	public void setYears(String years) {
		this.years = years;
	}

	public String getRanklevel1m() {
		return ranklevel1m;
	}

	public void setRanklevel1m(String ranklevel1m) {
		this.ranklevel1m = ranklevel1m;
	}

	public BigDecimal getSalary1m() {
		return salary1m;
	}

	public void setSalary1m(BigDecimal salary1m) {
		this.salary1m = salary1m;
	}

	public String getRanklevel2m() {
		return ranklevel2m;
	}

	public void setRanklevel2m(String ranklevel2m) {
		this.ranklevel2m = ranklevel2m;
	}

	public BigDecimal getSalary2m() {
		return salary2m;
	}

	public void setSalary2m(BigDecimal salary2m) {
		this.salary2m = salary2m;
	}

	public String getRanklevel3m() {
		return ranklevel3m;
	}

	public void setRanklevel3m(String ranklevel3m) {
		this.ranklevel3m = ranklevel3m;
	}

	public BigDecimal getSalary3m() {
		return salary3m;
	}

	public void setSalary3m(BigDecimal salary3m) {
		this.salary3m = salary3m;
	}

	public String getRanklevel4m() {
		return ranklevel4m;
	}

	public void setRanklevel4m(String ranklevel4m) {
		this.ranklevel4m = ranklevel4m;
	}

	public BigDecimal getSalary4m() {
		return salary4m;
	}

	public void setSalary4m(BigDecimal salary4m) {
		this.salary4m = salary4m;
	}

	public String getRanklevel5m() {
		return ranklevel5m;
	}

	public void setRanklevel5m(String ranklevel5m) {
		this.ranklevel5m = ranklevel5m;
	}

	public BigDecimal getSalary5m() {
		return salary5m;
	}

	public void setSalary5m(BigDecimal salary5m) {
		this.salary5m = salary5m;
	}

	public String getRanklevel6m() {
		return ranklevel6m;
	}

	public void setRanklevel6m(String ranklevel6m) {
		this.ranklevel6m = ranklevel6m;
	}

	public BigDecimal getSalary6m() {
		return salary6m;
	}

	public void setSalary6m(BigDecimal salary6m) {
		this.salary6m = salary6m;
	}

	public String getRanklevel7m() {
		return ranklevel7m;
	}

	public void setRanklevel7m(String ranklevel7m) {
		this.ranklevel7m = ranklevel7m;
	}

	public BigDecimal getSalary7m() {
		return salary7m;
	}

	public void setSalary7m(BigDecimal salary7m) {
		this.salary7m = salary7m;
	}

	public String getRanklevel8m() {
		return ranklevel8m;
	}

	public void setRanklevel8m(String ranklevel8m) {
		this.ranklevel8m = ranklevel8m;
	}

	public BigDecimal getSalary8m() {
		return salary8m;
	}

	public void setSalary8m(BigDecimal salary8m) {
		this.salary8m = salary8m;
	}

	public String getRanklevel9m() {
		return ranklevel9m;
	}

	public void setRanklevel9m(String ranklevel9m) {
		this.ranklevel9m = ranklevel9m;
	}

	public BigDecimal getSalary9m() {
		return salary9m;
	}

	public void setSalary9m(BigDecimal salary9m) {
		this.salary9m = salary9m;
	}

	public String getRanklevel10m() {
		return ranklevel10m;
	}

	public void setRanklevel10m(String ranklevel10m) {
		this.ranklevel10m = ranklevel10m;
	}

	public BigDecimal getSalary10m() {
		return salary10m;
	}

	public void setSalary10m(BigDecimal salary10m) {
		this.salary10m = salary10m;
	}

	public String getRanklevel11m() {
		return ranklevel11m;
	}

	public void setRanklevel11m(String ranklevel11m) {
		this.ranklevel11m = ranklevel11m;
	}

	public BigDecimal getSalary11m() {
		return salary11m;
	}

	public void setSalary11m(BigDecimal salary11m) {
		this.salary11m = salary11m;
	}

	public String getRanklevel12m() {
		return ranklevel12m;
	}

	public void setRanklevel12m(String ranklevel12m) {
		this.ranklevel12m = ranklevel12m;
	}

	public BigDecimal getSalary12m() {
		return salary12m;
	}

	public void setSalary12m(BigDecimal salary12m) {
		this.salary12m = salary12m;
	}

	public Date getCreatdt() {
		return creatdt;
	}

	public void setCreatdt(Date creatdt) {
		this.creatdt = creatdt;
	}

	public String getModor() {
		return modor;
	}

	public void setModor(String modor) {
		this.modor = modor;
	}

	public Date getModdt() {
		return moddt;
	}

	public void setModdt(Date moddt) {
		this.moddt = moddt;
	}

	public String getSort() {
		return sort;
	}

	public void setSort(String sort) {
		this.sort = sort;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public String getUserlevel() {
		return userlevel;
	}

	public void setUserlevel(String userlevel) {
		this.userlevel = userlevel;
	}

	public String getCurmonthlevel() {
		return curmonthlevel;
	}

	public void setCurmonthlevel(String curmonthlevel) {
		this.curmonthlevel = curmonthlevel;
	}

	public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}
	
}
