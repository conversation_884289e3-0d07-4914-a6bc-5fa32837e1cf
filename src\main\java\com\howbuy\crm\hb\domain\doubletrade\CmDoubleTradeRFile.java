package com.howbuy.crm.hb.domain.doubletrade;

import java.io.Serializable;

/**
 * @Description: 实体类CmDoubleTraderfile.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmDoubleTradeRFile implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String tid;

	private String fileId;

	private String fileName;

	private String recStat;

	private String creator;

	private String creDt;

	private String modifier;

	private String modDt;

	private String uploader;

	private String uploadDt;

	private String dirDt;

	private String uploadType;

	private String filePath;

	private String videoDownStatus;

	private String videoRoomId;

	private String isLegal;

	/**
	 * 文件相对路径
	 */
	private String relativeFilePath;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getRecStat() {
		return recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreDt() {
		return creDt;
	}

	public void setCreDt(String creDt) {
		this.creDt = creDt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModDt() {
		return modDt;
	}

	public void setModDt(String modDt) {
		this.modDt = modDt;
	}

	public String getUploader() {
		return uploader;
	}

	public void setUploader(String uploader) {
		this.uploader = uploader;
	}

	public String getUploadDt() {
		return uploadDt;
	}

	public void setUploadDt(String uploadDt) {
		this.uploadDt = uploadDt;
	}

	public String getDirDt() {
		return dirDt;
	}

	public void setDirDt(String dirDt) {
		this.dirDt = dirDt;
	}

	public String getUploadType() {
		return uploadType;
	}

	public void setUploadType(String uploadType) {
		this.uploadType = uploadType;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getVideoDownStatus() {
		return videoDownStatus;
	}

	public void setVideoDownStatus(String videoDownStatus) {
		this.videoDownStatus = videoDownStatus;
	}

	public String getVideoRoomId() {
		return videoRoomId;
	}

	public void setVideoRoomId(String videoRoomId) {
		this.videoRoomId = videoRoomId;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getIsLegal() {
		return isLegal;
	}

	public void setIsLegal(String isLegal) {
		this.isLegal = isLegal;
	}

	public String getRelativeFilePath() {
		return relativeFilePath;
	}

	public void setRelativeFilePath(String relativeFilePath) {
		this.relativeFilePath = relativeFilePath;
	}
}
