package com.howbuy.crm.hb.persistence.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationOrgname;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * shucheng
 * <AUTHOR>
 *
 */
public interface AssociationOrgnameMapper {

    /**
     * 查询机构别名列表
     * @param param 查询参数
     * @param pageBean 分页参数
     * @return
     */
    List<AssociationOrgname> listAssociationOrgnameByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 根据主键获取机构别名
     * @param id 主键
     * @return
     */
    AssociationOrgname findAssociationOrgnameById(String id);

    /**
     * 插入机构别名
     * @param associationOrgname
     */
    void insertAssociationOrgname(AssociationOrgname associationOrgname);

    /**
     * 修改机构别名
     * @param associationOrgname
     */
    void updateAssociationOrgname(AssociationOrgname associationOrgname);

    /**
     * 删除机构别名
     * @param id 主键
     */
    void deleteAssociationOrgname(String id);

    /**
     * 判断是否存在机构名、机构别名完全相同的记录
     * @param map
     * @return
     */
    boolean checkRecordExist(Map<String, String> map);
}
