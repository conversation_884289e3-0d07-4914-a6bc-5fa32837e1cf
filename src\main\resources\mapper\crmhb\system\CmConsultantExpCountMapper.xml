<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpCountMapper">
	<select id="listAllNeedDealOrg" resultType="java.util.HashMap">
		SELECT * FROM (
			 SELECT LEVEL ORGLEVEL,SYS_CONNECT_BY_PATH(ORGNAME,'/') ORGPATH,
			  T.ORGCODE,T.ORGNAME,T.PARENTORGCODE
				  FROM HB_ORGANIZATION T
				where t.status = '0'
					 START WITH ORGCODE  in('1','10')
				CONNECT BY PRIOR ORGCODE = PARENTORGCODE
				 ORDER SIBLINGS BY T.SORT) x
		 where x.orgcode in (
						SELECT ORGCODE
							  FROM HB_ORGANIZATION HO
							 WHERE HO.STATUS = '0'
							 START WITH HO.ORGCODE = #{queryOrgCode}
							CONNECT BY PRIOR ORGCODE = PARENTORGCODE
					) and x.orglevel  &lt;= 3
	</select>
	<select id="queryCmConsultantExpCountByDate" resultType="com.howbuy.crm.hb.domain.system.CmConsultantExpCount"
		parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpCount">
		SELECT
		(SELECT count(1)
		FROM cm_consultant_exp s
		where s.userid in ((
		SELECT userid
		FROM cm_consultant_exp a where a.outletcode in (  SELECT ORGCODE
		FROM HB_ORGANIZATION HO
		WHERE HO.STATUS = '0'
		START WITH HO.ORGCODE =#{queryOrgCode}
		CONNECT BY PRIOR ORGCODE = PARENTORGCODE)))
		and s.startdt>=#{queryStartDate} and s.startdt &lt;=#{queryEndDate}) as startCount,

		(SELECT count(1)
		FROM cm_consultant_exp s
		where s.userid in ((
		SELECT userid
		FROM cm_consultant_exp a where a.outletcode in (  SELECT ORGCODE
		FROM HB_ORGANIZATION HO
		WHERE HO.STATUS = '0'
		START WITH HO.ORGCODE =#{queryOrgCode}
		CONNECT BY PRIOR ORGCODE = PARENTORGCODE)))
		and s.regulardt>=#{queryStartDate} and s.regulardt &lt;=#{queryEndDate}) as regularCount,

		(SELECT count(1)
		FROM cm_consultant_exp s
		where s.userid in ((
		SELECT userid
		FROM cm_consultant_exp a where a.outletcode in (  SELECT ORGCODE
		FROM HB_ORGANIZATION HO
		WHERE HO.STATUS = '0'
		START WITH HO.ORGCODE =#{queryOrgCode}
		CONNECT BY PRIOR ORGCODE = PARENTORGCODE)))
		and s.quitdt>=#{queryStartDate} and s.quitdt &lt;=#{queryEndDate}) as quitCount,

		(SELECT count(1)
		FROM cm_consultant_exp s
		where s.userid in ((
		SELECT userid
		FROM cm_consultant_exp a where a.outletcode in (  SELECT ORGCODE
		FROM HB_ORGANIZATION HO
		WHERE HO.STATUS = '0'
		START WITH HO.ORGCODE =#{queryOrgCode}
		CONNECT BY PRIOR ORGCODE = PARENTORGCODE)))
		and s.startdt &lt;=#{queryEndDate} and nvl(s.quitdt,'99999999') >=#{queryEndDate}) as monthEndCount

		FROM  dual
	</select>
</mapper>