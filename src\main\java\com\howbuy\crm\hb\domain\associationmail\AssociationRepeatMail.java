package com.howbuy.crm.hb.domain.associationmail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 协会解析同名邮件
 * <AUTHOR> on 2021/6/4 18:11
 */
@Data
public class AssociationRepeatMail implements Serializable {

    /** 主键 */
    private String id;

    /** 邮件日期 */
    private Date mailDate;

    /** 邮件日期（展示用的） */
    private String mailDateStr;

    /** 来源邮箱 */
    private String sourceMail;

    /** 投顾客户代码 */
    private String conscustno;

    /** 客户名称 */
    private String custName;

    /** 机构名称 */
    private String orgName;

    /** 投资者账号 */
    private String investorAccount;

    /** 初始密码 */
    private String initPassword;

    /** 管理人登记编码 */
    private String managerRegno;

    /** 登录链接 */
    private String loginHref;

    /** 创建人 */
    private String creator;

    /** 记录创建日期 */
    private String credt;

    /** 修改人 */
    private String modifier;

    /** 修改日期 */
    private String moddt;

    /** 邮件主题 */
    private String subject;

    /** 收件人 */
    private String toMail;

    /** 删除标志（0 正常；1 删除） */
    private String delFlag;

    /** 邮件uid（线上邮件的唯一标识） */
    private String mailUid;

    /** 备注 */
    private String remark;

    /** 处理状态（0 待处理；1 暂缓处理；2 已忽略） */
    private String handleStatus;

    // 以下是数据合并展示时，用到的字段
    /** 数据合并时，多个主键的值以逗号分隔 */
    private String ids;

    /** 数据合并时，多个投顾客户代码以逗号分隔 */
    private String conscustnos;

    /** 数据合并时，多个一账通号以逗号分隔 */
    private String hboneNos;

    /** 数据合并时，多个备注以逗号分隔 */
    private String remarks;
}
