/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.outersevice;

import com.howbuy.crm.account.client.facade.custinfo.ConscustInfoFacade;
import com.howbuy.crm.account.client.request.custinfo.QueryCustBindRelationRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustBindRelationVO;
import com.howbuy.crm.hb.domain.qywechat.CmWechatCustRelationInfo;
import com.howbuy.crm.hb.domain.qywechat.HboneNoAndConsCodeVo;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.HbOneAndConsCodeDto;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.QueryWechatRelationBatchRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationBatchResponse;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.RelationInfo;
import com.howbuy.crm.wechat.client.facade.wechatrelation.WechatRelationFacade;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/5/23 11:54
 * @since JDK 1.8
 */
@Service
public class CmWechatOuterService {

    @Autowired
    private ConscustInfoFacade conscustInfoFacade;


    @Autowired
    private WechatRelationFacade wechatRelationFacade;

    public CmCustBindRelationVO queryBindRelation(String hboneNo, String consCode) {
        QueryCustBindRelationRequest queryCustSimpleRequest = new QueryCustBindRelationRequest();
        queryCustSimpleRequest.setHboneNo(hboneNo);
        queryCustSimpleRequest.setConsCode(consCode);
        Response<CmCustBindRelationVO> cmCustBindRealtionVOResponse = conscustInfoFacade.queryBindRelationByHboneNo(queryCustSimpleRequest);
        if (cmCustBindRealtionVOResponse.isSuccess()) {
            return cmCustBindRealtionVOResponse.getData();
        }
        return null;
    }

    /**
     * 批量根据一账通与投顾号查询绑定的企微信息
     */
    public List<CmWechatCustRelationInfo> queryWechatRelationBatch(List<HboneNoAndConsCodeVo> hboneNoAndConsCodeVoList) {
        if (CollectionUtils.isEmpty(hboneNoAndConsCodeVoList)) {
            return new ArrayList<>();
        }
        List<HbOneAndConsCodeDto> dtoList = hboneNoAndConsCodeVoList.stream().map(hboneNoAndConsCodeVo -> {
            HbOneAndConsCodeDto hbOneAndConsCodeDto = new HbOneAndConsCodeDto();
            hbOneAndConsCodeDto.setConsCode(hboneNoAndConsCodeVo.getConscode());
            hbOneAndConsCodeDto.setHbOneNo(hboneNoAndConsCodeVo.getHboneNo());
            return hbOneAndConsCodeDto;
        }).collect(Collectors.toList());
        QueryWechatRelationBatchRequest queryWechatRelationBatchRequest = new QueryWechatRelationBatchRequest();
        queryWechatRelationBatchRequest.setHbOneAndConsCodeDtoList(dtoList);
        com.howbuy.crm.wechat.client.base.Response<QueryWechatRelationBatchResponse> queryWechatRelationBatchResponseResponse = wechatRelationFacade.queryWechatRelationBatch(queryWechatRelationBatchRequest);
        if (queryWechatRelationBatchResponseResponse.isSuccess() && CollectionUtils.isNotEmpty(queryWechatRelationBatchResponseResponse.getReturnObject().getRelationInfoList())) {
            List<RelationInfo> relationInfoList = queryWechatRelationBatchResponseResponse.getReturnObject().getRelationInfoList();
            return relationInfoList.stream().map(relationInfo -> {
                CmWechatCustRelationInfo cmWechatCustRelationInfo = new CmWechatCustRelationInfo();
                cmWechatCustRelationInfo.setConsCode(relationInfo.getConsCode());
                cmWechatCustRelationInfo.setHbOneNo(relationInfo.getHbOneNo());
                cmWechatCustRelationInfo.setGnUnionId(relationInfo.getGnUnionId());
                cmWechatCustRelationInfo.setHwUnionId(relationInfo.getHkUnionId());
                return cmWechatCustRelationInfo;
            }).collect(Collectors.toList());

        }
        return new ArrayList<>();
    }

    ;

}