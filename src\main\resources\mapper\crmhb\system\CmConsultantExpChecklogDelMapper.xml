<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpChecklogDelMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.hb.domain.system.CmConsultantExpChecklogDel">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT_EXP_CHECKLOG_DEL-->
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="USERNO" jdbcType="VARCHAR" property="userno" />
    <result column="PROVCODE" jdbcType="VARCHAR" property="provcode" />
    <result column="CITYCODE" jdbcType="VARCHAR" property="citycode" />
    <result column="GENDER" jdbcType="VARCHAR" property="gender" />
    <result column="BIRTHDAY" jdbcType="VARCHAR" property="birthday" />
    <result column="EDULEVEL" jdbcType="VARCHAR" property="edulevel" />
    <result column="WORKTYPE" jdbcType="VARCHAR" property="worktype" />
    <result column="WORKSTATE" jdbcType="VARCHAR" property="workstate" />
    <result column="USERLEVEL" jdbcType="VARCHAR" property="userlevel" />
    <result column="CURMONTHLEVEL" jdbcType="VARCHAR" property="curmonthlevel" />
    <result column="STARTDT" jdbcType="VARCHAR" property="startdt" />
    <result column="STARTLEVEL" jdbcType="VARCHAR" property="startlevel" />
    <result column="SALARY" jdbcType="DECIMAL" property="salary" />
    <result column="PROBATIONENDDT" jdbcType="VARCHAR" property="probationenddt" />
    <result column="REGULARDT" jdbcType="VARCHAR" property="regulardt" />
    <result column="REGULARLEVEL" jdbcType="VARCHAR" property="regularlevel" />
    <result column="REGULARSALARY" jdbcType="DECIMAL" property="regularsalary" />
    <result column="QUITDT" jdbcType="VARCHAR" property="quitdt" />
    <result column="QUITLEVEL" jdbcType="VARCHAR" property="quitlevel" />
    <result column="QUITSALARY" jdbcType="DECIMAL" property="quitsalary" />
    <result column="QUITREASON" jdbcType="VARCHAR" property="quitreason" />
    <result column="SERVINGAGE" jdbcType="DECIMAL" property="servingage" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CHECKOR" jdbcType="VARCHAR" property="checkor" />
    <result column="JJCARDNO" jdbcType="VARCHAR" property="jjcardno" />
    <result column="ATTACHTYPE" jdbcType="VARCHAR" property="attachtype" />
    <result column="BACKGROUND" jdbcType="VARCHAR" property="background" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
    <result column="BEFOREPOSITIONTYPE" jdbcType="VARCHAR" property="beforepositiontype" />
    <result column="BEFOREPOSITIONAGE" jdbcType="VARCHAR" property="beforepositionage" />
    <result column="RECRUIT" jdbcType="VARCHAR" property="recruit" />
    <result column="RECOMMEND" jdbcType="VARCHAR" property="recommend" />
    <result column="RECOMMENDTYPE" jdbcType="VARCHAR" property="recommendtype" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATDT" jdbcType="TIMESTAMP" property="creatdt" />
    <result column="MODOR" jdbcType="VARCHAR" property="modor" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="TEAMCODE" jdbcType="VARCHAR" property="teamcode" />
    <result column="OUTLETCODE" jdbcType="VARCHAR" property="outletcode" />
    <result column="CONSNAME" jdbcType="VARCHAR" property="consname" />
    <result column="RECOMMENDUSERNO" jdbcType="VARCHAR" property="recommenduserno" />
    <result column="SUBPOSITIONS" jdbcType="VARCHAR" property="subpositions" />
    <result column="NEXTTESTDATE" jdbcType="VARCHAR" property="nexttestdate" />
    <result column="PROBATIONRESULT3M" jdbcType="VARCHAR" property="probationresult3m" />
    <result column="PROBATIONRESULT6M" jdbcType="VARCHAR" property="probationresult6m" />
    <result column="CURMONTHSALARY" jdbcType="DECIMAL" property="curmonthsalary" />
    <result column="QUITINFO" jdbcType="VARCHAR" property="quitinfo" />
    <result column="PROMOTEDATE" jdbcType="VARCHAR" property="promotedate" />
    <result column="BXCOMMISSIONWAY" jdbcType="VARCHAR" property="bxcommissionway" />
    <result column="BEISENID" jdbcType="VARCHAR" property="beisenid" />
    <result column="PROBATION_RESULT_12M" jdbcType="VARCHAR" property="probationResult12m" />
    <result column="CENTER_ORG" jdbcType="VARCHAR" property="centerOrg" />
    <result column="ADJUST_SERVING_MONTH" jdbcType="DECIMAL" property="adjustServingMonth" />
    <result column="ADJUST_MANAGE_SERVING_MONTH" jdbcType="DECIMAL" property="adjustManageServingMonth" />
    <result column="DT3M" jdbcType="VARCHAR" property="dt3m" />
    <result column="DT3M_FLAG" jdbcType="VARCHAR" property="dt3mFlag" />
    <result column="DT12M" jdbcType="VARCHAR" property="dt12m" />
    <result column="DT12M_FLAG" jdbcType="VARCHAR" property="dt12mFlag" />
    <result column="REGULARDT_FLAG" jdbcType="VARCHAR" property="regulardtFlag" />
    <result column="NEXTTESTDATE_FLAG" jdbcType="VARCHAR" property="nexttestdateFlag" />
    <result column="NEXT_TEST_PERIOD" jdbcType="VARCHAR" property="nextTestPeriod" />
    <result column="PROBATION_SALARY_3M" jdbcType="DECIMAL" property="probationSalary3m" />
    <result column="SALARY_12M" jdbcType="DECIMAL" property="salary12m" />
    <result column="JOIN_RANK" jdbcType="VARCHAR" property="joinRank" />
    <result column="PROBATION_RANK_3M" jdbcType="VARCHAR" property="probationRank3m" />
    <result column="REGULAR_RANK" jdbcType="VARCHAR" property="regularRank" />
    <result column="RANK_12M" jdbcType="VARCHAR" property="rank12m" />
    <result column="JOIN_SSB" jdbcType="VARCHAR" property="joinSsb" />
    <result column="PROBATION_SSB_3M" jdbcType="VARCHAR" property="probationSsb3m" />
    <result column="REGULAR_SSB" jdbcType="VARCHAR" property="regularSsb" />
    <result column="SSB_12M" jdbcType="VARCHAR" property="ssb12m" />
    <result column="PROBATIONLEVEL_3M" jdbcType="VARCHAR" property="probationlevel3m" />
    <result column="TESTLEVEL_12M" jdbcType="VARCHAR" property="testlevel12m" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    USERID, USERNO, PROVCODE, CITYCODE, GENDER, BIRTHDAY, EDULEVEL, WORKTYPE, WORKSTATE, 
    USERLEVEL, CURMONTHLEVEL, STARTDT, STARTLEVEL, SALARY, PROBATIONENDDT, REGULARDT, 
    REGULARLEVEL, REGULARSALARY, QUITDT, QUITLEVEL, QUITSALARY, QUITREASON, SERVINGAGE, 
    CHECKFLAG, CHECKOR, JJCARDNO, ATTACHTYPE, BACKGROUND, "SOURCE", BEFOREPOSITIONTYPE, 
    BEFOREPOSITIONAGE, RECRUIT, RECOMMEND, RECOMMENDTYPE, REMARK, CREATOR, CREATDT, MODOR, 
    MODDT, EMAIL, TEAMCODE, OUTLETCODE, CONSNAME, RECOMMENDUSERNO, SUBPOSITIONS, NEXTTESTDATE, 
    PROBATIONRESULT3M, PROBATIONRESULT6M, CURMONTHSALARY, QUITINFO, PROMOTEDATE, BXCOMMISSIONWAY, 
    BEISENID, PROBATION_RESULT_12M, CENTER_ORG, ADJUST_SERVING_MONTH, ADJUST_MANAGE_SERVING_MONTH, 
    DT3M, DT3M_FLAG, DT12M, DT12M_FLAG, REGULARDT_FLAG, NEXTTESTDATE_FLAG, NEXT_TEST_PERIOD, 
    PROBATION_SALARY_3M, SALARY_12M, JOIN_RANK, PROBATION_RANK_3M, REGULAR_RANK, RANK_12M, 
    JOIN_SSB, PROBATION_SSB_3M, REGULAR_SSB, SSB_12M, PROBATIONLEVEL_3M, TESTLEVEL_12M
  </sql>
  <insert id="insertSelective" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpChecklogDel">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_EXP_CHECKLOG_DEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        USERID,
      </if>
      <if test="userno != null">
        USERNO,
      </if>
      <if test="provcode != null">
        PROVCODE,
      </if>
      <if test="citycode != null">
        CITYCODE,
      </if>
      <if test="gender != null">
        GENDER,
      </if>
      <if test="birthday != null">
        BIRTHDAY,
      </if>
      <if test="edulevel != null">
        EDULEVEL,
      </if>
      <if test="worktype != null">
        WORKTYPE,
      </if>
      <if test="workstate != null">
        WORKSTATE,
      </if>
      <if test="userlevel != null">
        USERLEVEL,
      </if>
      <if test="curmonthlevel != null">
        CURMONTHLEVEL,
      </if>
      <if test="startdt != null">
        STARTDT,
      </if>
      <if test="startlevel != null">
        STARTLEVEL,
      </if>
      <if test="salary != null">
        SALARY,
      </if>
      <if test="probationenddt != null">
        PROBATIONENDDT,
      </if>
      <if test="regulardt != null">
        REGULARDT,
      </if>
      <if test="regularlevel != null">
        REGULARLEVEL,
      </if>
      <if test="regularsalary != null">
        REGULARSALARY,
      </if>
      <if test="quitdt != null">
        QUITDT,
      </if>
      <if test="quitlevel != null">
        QUITLEVEL,
      </if>
      <if test="quitsalary != null">
        QUITSALARY,
      </if>
      <if test="quitreason != null">
        QUITREASON,
      </if>
      <if test="servingage != null">
        SERVINGAGE,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="checkor != null">
        CHECKOR,
      </if>
      <if test="jjcardno != null">
        JJCARDNO,
      </if>
      <if test="attachtype != null">
        ATTACHTYPE,
      </if>
      <if test="background != null">
        BACKGROUND,
      </if>
      <if test="source != null">
        "SOURCE",
      </if>
      <if test="beforepositiontype != null">
        BEFOREPOSITIONTYPE,
      </if>
      <if test="beforepositionage != null">
        BEFOREPOSITIONAGE,
      </if>
      <if test="recruit != null">
        RECRUIT,
      </if>
      <if test="recommend != null">
        RECOMMEND,
      </if>
      <if test="recommendtype != null">
        RECOMMENDTYPE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatdt != null">
        CREATDT,
      </if>
      <if test="modor != null">
        MODOR,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="teamcode != null">
        TEAMCODE,
      </if>
      <if test="outletcode != null">
        OUTLETCODE,
      </if>
      <if test="consname != null">
        CONSNAME,
      </if>
      <if test="recommenduserno != null">
        RECOMMENDUSERNO,
      </if>
      <if test="subpositions != null">
        SUBPOSITIONS,
      </if>
      <if test="nexttestdate != null">
        NEXTTESTDATE,
      </if>
      <if test="probationresult3m != null">
        PROBATIONRESULT3M,
      </if>
      <if test="probationresult6m != null">
        PROBATIONRESULT6M,
      </if>
      <if test="curmonthsalary != null">
        CURMONTHSALARY,
      </if>
      <if test="quitinfo != null">
        QUITINFO,
      </if>
      <if test="promotedate != null">
        PROMOTEDATE,
      </if>
      <if test="bxcommissionway != null">
        BXCOMMISSIONWAY,
      </if>
      <if test="beisenid != null">
        BEISENID,
      </if>
      <if test="probationResult12m != null">
        PROBATION_RESULT_12M,
      </if>
      <if test="centerOrg != null">
        CENTER_ORG,
      </if>
      <if test="adjustServingMonth != null">
        ADJUST_SERVING_MONTH,
      </if>
      <if test="adjustManageServingMonth != null">
        ADJUST_MANAGE_SERVING_MONTH,
      </if>
      <if test="dt3m != null">
        DT3M,
      </if>
      <if test="dt3mFlag != null">
        DT3M_FLAG,
      </if>
      <if test="dt12m != null">
        DT12M,
      </if>
      <if test="dt12mFlag != null">
        DT12M_FLAG,
      </if>
      <if test="regulardtFlag != null">
        REGULARDT_FLAG,
      </if>
      <if test="nexttestdateFlag != null">
        NEXTTESTDATE_FLAG,
      </if>
      <if test="nextTestPeriod != null">
        NEXT_TEST_PERIOD,
      </if>
      <if test="probationSalary3m != null">
        PROBATION_SALARY_3M,
      </if>
      <if test="salary12m != null">
        SALARY_12M,
      </if>
      <if test="joinRank != null">
        JOIN_RANK,
      </if>
      <if test="probationRank3m != null">
        PROBATION_RANK_3M,
      </if>
      <if test="regularRank != null">
        REGULAR_RANK,
      </if>
      <if test="rank12m != null">
        RANK_12M,
      </if>
      <if test="joinSsb != null">
        JOIN_SSB,
      </if>
      <if test="probationSsb3m != null">
        PROBATION_SSB_3M,
      </if>
      <if test="regularSsb != null">
        REGULAR_SSB,
      </if>
      <if test="ssb12m != null">
        SSB_12M,
      </if>
      <if test="probationlevel3m != null">
        PROBATIONLEVEL_3M,
      </if>
      <if test="testlevel12m != null">
        TESTLEVEL_12M,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="userno != null">
        #{userno,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="edulevel != null">
        #{edulevel,jdbcType=VARCHAR},
      </if>
      <if test="worktype != null">
        #{worktype,jdbcType=VARCHAR},
      </if>
      <if test="workstate != null">
        #{workstate,jdbcType=VARCHAR},
      </if>
      <if test="userlevel != null">
        #{userlevel,jdbcType=VARCHAR},
      </if>
      <if test="curmonthlevel != null">
        #{curmonthlevel,jdbcType=VARCHAR},
      </if>
      <if test="startdt != null">
        #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="startlevel != null">
        #{startlevel,jdbcType=VARCHAR},
      </if>
      <if test="salary != null">
        #{salary,jdbcType=DECIMAL},
      </if>
      <if test="probationenddt != null">
        #{probationenddt,jdbcType=VARCHAR},
      </if>
      <if test="regulardt != null">
        #{regulardt,jdbcType=VARCHAR},
      </if>
      <if test="regularlevel != null">
        #{regularlevel,jdbcType=VARCHAR},
      </if>
      <if test="regularsalary != null">
        #{regularsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitdt != null">
        #{quitdt,jdbcType=VARCHAR},
      </if>
      <if test="quitlevel != null">
        #{quitlevel,jdbcType=VARCHAR},
      </if>
      <if test="quitsalary != null">
        #{quitsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitreason != null">
        #{quitreason,jdbcType=VARCHAR},
      </if>
      <if test="servingage != null">
        #{servingage,jdbcType=DECIMAL},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="checkor != null">
        #{checkor,jdbcType=VARCHAR},
      </if>
      <if test="jjcardno != null">
        #{jjcardno,jdbcType=VARCHAR},
      </if>
      <if test="attachtype != null">
        #{attachtype,jdbcType=VARCHAR},
      </if>
      <if test="background != null">
        #{background,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="beforepositiontype != null">
        #{beforepositiontype,jdbcType=VARCHAR},
      </if>
      <if test="beforepositionage != null">
        #{beforepositionage,jdbcType=VARCHAR},
      </if>
      <if test="recruit != null">
        #{recruit,jdbcType=VARCHAR},
      </if>
      <if test="recommend != null">
        #{recommend,jdbcType=VARCHAR},
      </if>
      <if test="recommendtype != null">
        #{recommendtype,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatdt != null">
        #{creatdt,jdbcType=TIMESTAMP},
      </if>
      <if test="modor != null">
        #{modor,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="teamcode != null">
        #{teamcode,jdbcType=VARCHAR},
      </if>
      <if test="outletcode != null">
        #{outletcode,jdbcType=VARCHAR},
      </if>
      <if test="consname != null">
        #{consname,jdbcType=VARCHAR},
      </if>
      <if test="recommenduserno != null">
        #{recommenduserno,jdbcType=VARCHAR},
      </if>
      <if test="subpositions != null">
        #{subpositions,jdbcType=VARCHAR},
      </if>
      <if test="nexttestdate != null">
        #{nexttestdate,jdbcType=VARCHAR},
      </if>
      <if test="probationresult3m != null">
        #{probationresult3m,jdbcType=VARCHAR},
      </if>
      <if test="probationresult6m != null">
        #{probationresult6m,jdbcType=VARCHAR},
      </if>
      <if test="curmonthsalary != null">
        #{curmonthsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitinfo != null">
        #{quitinfo,jdbcType=VARCHAR},
      </if>
      <if test="promotedate != null">
        #{promotedate,jdbcType=VARCHAR},
      </if>
      <if test="bxcommissionway != null">
        #{bxcommissionway,jdbcType=VARCHAR},
      </if>
      <if test="beisenid != null">
        #{beisenid,jdbcType=VARCHAR},
      </if>
      <if test="probationResult12m != null">
        #{probationResult12m,jdbcType=VARCHAR},
      </if>
      <if test="centerOrg != null">
        #{centerOrg,jdbcType=VARCHAR},
      </if>
      <if test="adjustServingMonth != null">
        #{adjustServingMonth,jdbcType=DECIMAL},
      </if>
      <if test="adjustManageServingMonth != null">
        #{adjustManageServingMonth,jdbcType=DECIMAL},
      </if>
      <if test="dt3m != null">
        #{dt3m,jdbcType=VARCHAR},
      </if>
      <if test="dt3mFlag != null">
        #{dt3mFlag,jdbcType=VARCHAR},
      </if>
      <if test="dt12m != null">
        #{dt12m,jdbcType=VARCHAR},
      </if>
      <if test="dt12mFlag != null">
        #{dt12mFlag,jdbcType=VARCHAR},
      </if>
      <if test="regulardtFlag != null">
        #{regulardtFlag,jdbcType=VARCHAR},
      </if>
      <if test="nexttestdateFlag != null">
        #{nexttestdateFlag,jdbcType=VARCHAR},
      </if>
      <if test="nextTestPeriod != null">
        #{nextTestPeriod,jdbcType=VARCHAR},
      </if>
      <if test="probationSalary3m != null">
        #{probationSalary3m,jdbcType=DECIMAL},
      </if>
      <if test="salary12m != null">
        #{salary12m,jdbcType=DECIMAL},
      </if>
      <if test="joinRank != null">
        #{joinRank,jdbcType=VARCHAR},
      </if>
      <if test="probationRank3m != null">
        #{probationRank3m,jdbcType=VARCHAR},
      </if>
      <if test="regularRank != null">
        #{regularRank,jdbcType=VARCHAR},
      </if>
      <if test="rank12m != null">
        #{rank12m,jdbcType=VARCHAR},
      </if>
      <if test="joinSsb != null">
        #{joinSsb,jdbcType=VARCHAR},
      </if>
      <if test="probationSsb3m != null">
        #{probationSsb3m,jdbcType=VARCHAR},
      </if>
      <if test="regularSsb != null">
        #{regularSsb,jdbcType=VARCHAR},
      </if>
      <if test="ssb12m != null">
        #{ssb12m,jdbcType=VARCHAR},
      </if>
      <if test="probationlevel3m != null">
        #{probationlevel3m,jdbcType=VARCHAR},
      </if>
      <if test="testlevel12m != null">
        #{testlevel12m,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertByUserId" parameterType="string">
    insert into CM_CONSULTANT_EXP_CHECKLOG_DEL (<include refid="Base_Column_List"/>)
    select
    <include refid="Base_Column_List"/>
    from CM_CONSULTANT_EXP_CHECKLOG
    where userid = #{userid,jdbcType=VARCHAR}
  </insert>
</mapper>