package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.custinfo.ProductShareBonus;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ProductShareBonusMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    ProductShareBonus getProductShareBonus(Map<String, String> param);
    
    /**
     * 得到单个数据对象
     * @param param
     * @return
     */
    ProductShareBonus getNextTimeBonusByCode(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param productShareBonus
      */
	void insertProductShareBonus(ProductShareBonus productShareBonus);
	
	/**
	 * 单条修改数据对象
	 * @param productShareBonus
	 */
	void updateProductShareBonus(ProductShareBonus productShareBonus);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delProductShareBonus(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListProductShareBonus(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<ProductShareBonus> listTimeEndBonus(Map<String, String> param);
	
	/**
	 * 查询部门团队列表数据对象
	 * @param param
	 * @return
	 */
	List<ProductShareBonus> listTimeEndBonusDept(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getTimeEndBonusCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<ProductShareBonus> listTimeEndBonusByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<ProductShareBonus> listTimeEndBonusDeptByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<ProductShareBonus> listProductShareBonus(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getProductShareBonusCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<ProductShareBonus> listProductShareBonusByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<ProductShareBonus> listProductHolding(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getProductHoldingCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<ProductShareBonus> listProductHoldingByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
