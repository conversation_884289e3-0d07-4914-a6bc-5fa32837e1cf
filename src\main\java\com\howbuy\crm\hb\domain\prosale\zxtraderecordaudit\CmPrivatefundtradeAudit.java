package com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 直销交易记录审核实体
 * <AUTHOR>
 * @date 2022/6/1 17:22
 */
@Data
public class CmPrivatefundtradeAudit implements Serializable {

    /** 审核记录id（主键） */
    private String auditid;

    /** 投顾客户号 */
    private String conscustno;
    /** 客户姓名 */
    private String custname;

    /**
     * 交易类型（importZxTradeType）1：股权回款；2：分红；3：强减；5：强增；6：强赎
     * StaticVar.LOAD_TRADETYPE_*
     */
    private String tradetype;
    private String tradetypeName;

    /** 产品代码 */
    private String fundcode;

    /** 产品名称 */
    private String fundname;

    /** 确认日期 */
    private String tradedt;

    /** 确认净值 */
    private BigDecimal nav;

    /** 确认金额 */
    private BigDecimal ackamt;

    /** 确认份额 */
    private BigDecimal ackvol;
    /**
     * 转让价格
     */
    private BigDecimal transferprice;

    /** 是否海外特殊处理（是、否） */
    private String ishaiwai;

    /** 是否发送短信（是、否） */
    private String ismessage;

    /** 备注 */
    private String discsummary;

    /** 修改人 */
    private String modifier;
    private String modifierName;

    /** 修改时间 */
    private String moddt;

    /** 审核人 */
    private String auditor;
    private String auditorName;

    /** 审核时间 */
    private String auditdt;

    /**
     * 审核状态（zxTradeAuditStatus）1：待审核；2：审核通过；3：审核不通过；4：作废
     * StaticVar.ZX_TRADE_AUDIT_*
     */
    private String auditstatus;
    private String auditstatusName;

    /**
     * 分红方式 0：红利再投；1：现金分红
     */
    private String dividendMode;

    /** 合规修改人 */
    private String hgmodifier;
    /** 合规审核人 */
    private String hgauditor;
}
