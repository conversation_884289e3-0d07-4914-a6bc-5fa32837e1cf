package com.howbuy.crm.hb.persistence.conscust;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.conscust.CsPotentialUploadLog;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CsPotentialUploadLogMapper {

	/**
	 * 插入潜在客户上传日志
	 * @param csPotentialUploadLog
	 */
	public abstract void addPotentialUploadLog(CsPotentialUploadLog csPotentialUploadLog);
	/**
	 * 分页查询上传日志
	 * @param param
	 * @param pageBean
	 * @return
	 */
	public abstract List<CsPotentialUploadLog> listPotentialCustByPage(@Param("param") Map<String, String> param,
			@Param("page") CommPageBean pageBean);
}
