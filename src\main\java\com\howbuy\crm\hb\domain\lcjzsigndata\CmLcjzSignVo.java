/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.lcjzsigndata;

import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/7/27 19:35
 * @since JDK 1.8
 */
@Data
public class CmLcjzSignVo {
    private String conscustno;
    private String custname;
    private String istg;
    private String consname;
    private String orgcenter;
    private String orgcode;
    private String orgname;
    private String courseid;
    private String coursename;
    private String sponsor;
    private String citycode;
    private String coursedt;
    private String teacherName;
    private String coursearea;
    private String coursetime;
    private String coursestatus;
    private String conferenceid;
    private String conferencename;
    private String sponsororgname;
    private String conferencetype;
    private String applysource;
    private String applystatus;
    private String applytime;
    private String issign;
    private String signdt;
    private String dealdt;
}