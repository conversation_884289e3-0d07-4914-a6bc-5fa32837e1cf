package com.howbuy.crm.hb.enums.birthdaygift;

/**
 * <AUTHOR>
 * @description: 申请人类型(1:客户，2:投顾)
 * @date 2025/02/21 9:44
 * @since JDK 1.8
 */
public enum WhiteListApplicantTypeEnum {

    /**
     * 1-客户
     */
    CUST("1", "客户"),

    /**
     * 2-投顾
     */
    CONS("2", "投顾");

    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    WhiteListApplicantTypeEnum(String code, String description){
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
