package com.howbuy.crm.hb.domain.insur;

import crm.howbuy.base.db.CommPageBean;

/**
 * @description:分页公共参数
 * <AUTHOR> @version 1.0
 * @created 
 */
public class PageVo{

	protected static final  String ALL="ALL";
	/** 第几页 */
	private Integer page = 1;
	/** 每页显示多少条记录 */
	private Integer rows = 50;

	/** 第几页 */
	public Integer getPage() {
		return page;
	}

	/** 第几页 */
	public void setPage(Integer page) {
		this.page = page;
	}

	/** 每页显示多少条记录 */
	public Integer getRows() {
		return rows;
	}

	/** 每页显示多少条记录 */
	public void setRows(Integer rows) {
		this.rows = rows;
	}

	/**
	 * 构建pageBean
	 * @return
	 */
	public CommPageBean getPageBeanByVo(){
		CommPageBean commPageBean = new CommPageBean();
		commPageBean.setCurPage(getPage());
		commPageBean.setPageSize(getRows());
		return commPageBean;

	}

}
