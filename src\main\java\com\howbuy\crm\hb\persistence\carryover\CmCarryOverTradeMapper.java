package com.howbuy.crm.hb.persistence.carryover;

import com.howbuy.crm.hb.domain.carryover.CmCarryOverTradeInfo;
import crm.howbuy.base.db.CommPageBean;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * @author: yu.zhang
 * @description:
 * @CreatDate: 2020-09-18 14:21
 * @since: JDK1.8
 */
public interface CmCarryOverTradeMapper {

    /**
     * 查询推送消息
     * @param param
     * @return
     */
    List<CmCarryOverTradeInfo> queryCarryOverTradeList(Map<String, Object> param);

    /**
     * 查询结转子份额大于当前持仓的客户
     * @param param
     * @return
     */
    List<String> querySubshareCustList(Map<String, Object> param);

    /**
     * 查询子代码对应的实收费用记录
     * @param param
     * @return
     */
    List<String> querySubNafeeCount(Map<String, Object> param);

    /**
     * 查询主代码的应收记录
     * @param param
     * @return
     */
    List<String> queryHostNafeeCount(Map<String, Object> param);

    /**
     * updateCmCarryOverTrade
     * @param cmcarryovertradeinfo
     * @return void
     * @Author: yu.zhang on 2020/9/24 11:43
     */
    void updateCmCarryOverTrade(CmCarryOverTradeInfo cmcarryovertradeinfo);
    
    /**
     * listCmCarryOverTradeInfoByPage
     * @param param
     * @param pageBean
     * @return java.util.List<com.howbuy.crm.hb.domain.carryover.CmCarryOverTradeInfo>
     * @Author: yu.zhang on 2020/9/24 11:43
     */
	List<CmCarryOverTradeInfo> listCmCarryOverTradeInfoByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * listCmCarryOverTradeInfo
	 * @param param
	 * @return java.util.List<com.howbuy.crm.hb.domain.carryover.CmCarryOverTradeInfo>
	 * @Author: yu.zhang on 2020/9/24 11:43
	 */
	List<CmCarryOverTradeInfo> listCmCarryOverTradeInfo(Map<String, String> param);
	
	/**
	 * getCmCarryOverTrade
	 * @param param
	 * @return com.howbuy.crm.hb.domain.carryover.CmCarryOverTradeInfo
	 * @Author: yu.zhang on 2020/9/24 11:43
	 */
	CmCarryOverTradeInfo getCmCarryOverTrade(Map<String, Object> param);

	/**
	 * getCmCarryOverTradeById
	 * @param carryid
	 * @return com.howbuy.crm.hb.domain.carryover.CmCarryOverTradeInfo
	 * @Author: yu.zhang on 2020/9/24 11:43
	 */
    CmCarryOverTradeInfo getCmCarryOverTradeById(String carryid);

    /**
     * batchInsertCmCarryOverTrade
     * @param cmCarryOverTradeInfoList
     * @return void
     * @Author: yu.zhang on 2020/9/24 11:43
     */
	void batchInsertCmCarryOverTrade(List<CmCarryOverTradeInfo> cmCarryOverTradeInfoList);

}
