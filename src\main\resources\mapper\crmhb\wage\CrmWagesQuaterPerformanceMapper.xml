<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CrmWageQuaterPerformanceMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>


    <insert id="insertCrmWageQuaterPerformance" parameterType="CrmWageQuaterPerformance">
        INSERT INTO CM_WAGE_QUATER_PERFORMANCE (
        credt,
        ID,
        <trim suffix="" suffixOverrides=",">
            <if test="orgCode!= null"> org_code, </if>
            <if test="quaterPerformanceMin!= null"> QUATER_PERFORMANCE_MIN, </if>
            <if test="quaterPerformanceMax!= null"> QUATER_PERFORMANCE_MAX, </if>
            <if test="performanceRatio!= null"> PERFORMANCE_RATIO, </if>
            <if test="startdt!= null"> startdt, </if>
            <if test="enddt!= null"> enddt, </if>
            <if test="creator!= null"> creator, </if>
        </trim>
        ) values (
        sysdate,
        SEQ_CRM_WAGE_QUATER_PFM.nextval ,
        <trim suffix="" suffixOverrides=",">
            <if test="orgCode!= null"> #{orgCode}, </if>
            <if test="quaterPerformanceMin!= null"> #{quaterPerformanceMin}, </if>
            <if test="quaterPerformanceMax!= null"> #{quaterPerformanceMax}, </if>
            <if test="performanceRatio!= null"> #{performanceRatio}, </if>
            <if test="startdt!= null"> #{startdt}, </if>
            <if test="enddt!= null"> #{enddt}, </if>
            <if test="creator!= null">  #{creator}, </if>
        </trim>
        )
    </insert>

    <delete id="deleteData"  parameterType="String" >
        delete from CM_WAGE_QUATER_PERFORMANCE
        <where>
            ID = #{id}
        </where>
    </delete>

    <select id="selectCrmWageQuaterPerformanceByPage" parameterType="Map" resultType="Map" useCache="false">
        SELECT CN.ID ,
        CN.ORG_CODE, HO.ORGNAME,
        CN.QUATER_PERFORMANCE_MAX,CN.QUATER_PERFORMANCE_MIN,
        CN.PERFORMANCE_RATIO ,CN.STARTDT,CN.ENDDT
        FROM CM_WAGE_QUATER_PERFORMANCE CN
        LEFT JOIN HB_ORGANIZATION HO
        ON CN.ORG_CODE = HO.ORGCODE
        <where> CN.ORG_CODE IN
            (SELECT ORGCODE
            FROM HB_ORGANIZATION
            CONNECT BY PRIOR ORGCODE = PARENTORGCODE
            START WITH ORGCODE = #{param.orgcode})
        </where>
    </select>


    <update id="updateCrmWageQuaterPerformance" parameterType="CrmWageQuaterPerformance">
        UPDATE CM_WAGE_QUATER_PERFORMANCE
        <set >
            moddt = sysdate,
            <trim suffix="" suffixOverrides=",">
                <if test="quaterPerformanceMin!= null"> QUATER_PERFORMANCE_MIN = #{quaterPerformanceMin}, </if>
                <if test="quaterPerformanceMax!= null"> QUATER_PERFORMANCE_Max = #{quaterPerformanceMax}, </if>
                <if test="quaterPerformanceMax== null"> QUATER_PERFORMANCE_Max = null, </if>
                <if test="performanceRatio!= null"> PERFORMANCE_RATIO = #{performanceRatio}, </if>
                <if test="startdt!= null"> startdt = #{startdt}, </if>
                <if test="enddt!= null"> enddt = #{enddt}, </if>
                <if test="enddt== null"> enddt = null, </if>
                <if test="modifier!= null"> MODIFIER = #{modifier}, </if>
            </trim>
        </set>
        <where>
            id = #{id}
        </where>
    </update>
</mapper>



