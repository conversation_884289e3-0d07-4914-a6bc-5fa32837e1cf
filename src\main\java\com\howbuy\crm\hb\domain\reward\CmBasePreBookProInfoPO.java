/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.reward;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (改造PRO_REWARD_STAT_TRADE_NUM时产生的预约信息表对应的PO对象)
 * <AUTHOR>
 * @date 2023/10/8 2:59 PM
 * @since JDK 1.8
 */
@Data
public class CmBasePreBookProInfoPO implements Serializable {
    private Long id;
    private String tradeType;
    private String currency;
    private BigDecimal realpayamt;
    private BigDecimal realpayamtRmb;
    private String conscustno;
    private BigDecimal fee;
    private String paystate;
    private String creator;
    private String prebookstate;
    private String expecttradedt;
    private String accountDt;
    private String pcode;
}