package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.HboneCancelFlushUnbind;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/5/16 13:11
 * @since JDK 1.8
 */
public interface HboneCancelFlushUnbindMapper {
    int deleteByPrimaryKey(String hboneNo);

    int insert(HboneCancelFlushUnbind record);

    int insertSelective(HboneCancelFlushUnbind record);

    HboneCancelFlushUnbind selectByPrimaryKey(String hboneNo);

    int updateByPrimaryKeySelective(HboneCancelFlushUnbind record);

    int updateByPrimaryKey(HboneCancelFlushUnbind record);


    /**
     * 查找未处理的数据
     * @return
     */
    List<HboneCancelFlushUnbind> selectUnDealList();


    /**
     * @description:(根据一账通查找未处理的数据)
     * @param hboneNo
     * @return com.howbuy.crm.hb.domain.custinfo.HboneCancelFlushUnbind
     * @author: haoran.zhang
     * @date: 2025/5/16 13:32
     * @since JDK 1.8
     */
    HboneCancelFlushUnbind selectUnDealByHboneNo(@Param("hboneNo") String hboneNo);
}