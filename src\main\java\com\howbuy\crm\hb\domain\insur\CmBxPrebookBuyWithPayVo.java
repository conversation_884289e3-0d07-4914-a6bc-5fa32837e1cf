package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * @Description: 保险产品购买信息 && 缴费信息-查询Vo
 * <AUTHOR> @version 1.0
 * @created 
 */
public class CmBxPrebookBuyWithPayVo  extends  PageVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/***************************prebookInfo信息  begin **************************************/
	/**
	 * 预约Id
	 */
	private BigDecimal preId;


	private String fundCode;


	/**
	 * 预约状态
	 */
	private String preState;

	/**
	 * 复核状态1待复核、2已复核；
	 */
	private String checkState;

	/**
	 * 保单状态
	 */
	private String insurState;

	private String orgCode;

	private String consCode;


	/**
	 * 投顾客户号
	 */
	private String conscustno;

	/**
	 * 投保人
	 */
	private String custName;





	/**
     * 是否变为退保 1是；0否
	 */
	private String isChangeReturn;


	          /*******signInfo************/

	/**
	 * 保单号
 	 */
	private String insurId;
	/**
	 * 签单日期
	 */
	private String signBeginDt;

	private String signEndDt;


	/**
     *核保通过日期
     */
    private String passBeginDt;
    private String passEndDt;

    /**
     * 冷静期截止日
     */
    private String caltimeBeginDt;
    private String caltimeEndDt;
    /**
     * 佣金核算日期
     */
    private String commissionCalStartDt;
    private String commissionCalEndDt;

    /*******signInfo************/


    /***************************prebookInfo信息  end **************************************/


    /***************************buyInfo信息  begin **************************************/
	/**
	 * 购买Id
	 */
	private BigDecimal buyId;


	/***************************buyInfo信息  end **************************************/




	/***************************endPay 缴费信息  begin **************************************/
	/**
	 * 缴款Id
	 */
	private BigDecimal endPayId;

	/**
     * 缴款Id列表
     */
    private List<BigDecimal> endPayIdList;

    /**
     * 缴款状态
     */
    private String payState;

    /***************************endPay 缴费信息  end **************************************/


    public String getCommissionCalStartDt() {
        return commissionCalStartDt;
    }

    public void setCommissionCalStartDt(String commissionCalStartDt) {
        this.commissionCalStartDt = commissionCalStartDt;
    }

    public String getCommissionCalEndDt() {
        return commissionCalEndDt;
    }

    public void setCommissionCalEndDt(String commissionCalEndDt) {
        this.commissionCalEndDt = commissionCalEndDt;
    }


    public String getConsCode() {
        return ALL.equals(consCode) ? null : consCode;
    }

    public BigDecimal getPreId() {
        return preId;
    }

    public void setPreId(BigDecimal preId) {
		this.preId = preId;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getPreState() {
		return preState;
	}

	public void setPreState(String preState) {
		this.preState = preState;
	}

	public String getCheckState() {
		return checkState;
	}

	public void setCheckState(String checkState) {
		this.checkState = checkState;
	}

	public String getInsurState() {
		return insurState;
	}

	public void setInsurState(String insurState) {
		this.insurState = insurState;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getIsChangeReturn() {
		return isChangeReturn;
	}

	public void setIsChangeReturn(String isChangeReturn) {
		this.isChangeReturn = isChangeReturn;
	}

	public String getInsurId() {
		return insurId;
	}

	public void setInsurId(String insurId) {
		this.insurId = insurId;
	}

	public String getSignBeginDt() {
		return signBeginDt;
	}

	public void setSignBeginDt(String signBeginDt) {
		this.signBeginDt = signBeginDt;
	}

	public String getSignEndDt() {
		return signEndDt;
	}

	public void setSignEndDt(String signEndDt) {
		this.signEndDt = signEndDt;
	}

	public String getPassBeginDt() {
		return passBeginDt;
	}

	public void setPassBeginDt(String passBeginDt) {
		this.passBeginDt = passBeginDt;
	}

	public String getPassEndDt() {
		return passEndDt;
	}

	public void setPassEndDt(String passEndDt) {
		this.passEndDt = passEndDt;
	}

	public String getCaltimeBeginDt() {
		return caltimeBeginDt;
	}

	public void setCaltimeBeginDt(String caltimeBeginDt) {
		this.caltimeBeginDt = caltimeBeginDt;
	}

	public String getCaltimeEndDt() {
		return caltimeEndDt;
	}

	public void setCaltimeEndDt(String caltimeEndDt) {
		this.caltimeEndDt = caltimeEndDt;
	}

	public BigDecimal getBuyId() {
		return buyId;
	}

	public void setBuyId(BigDecimal buyId) {
		this.buyId = buyId;
	}

	public BigDecimal getEndPayId() {
		return endPayId;
	}

	public void setEndPayId(BigDecimal endPayId) {
		this.endPayId = endPayId;
	}

	public List<BigDecimal> getEndPayIdList() {
		return endPayIdList;
	}

	public void setEndPayIdList(List<BigDecimal> endPayIdList) {
		this.endPayIdList = endPayIdList;
	}

	public String getPayState() {
		return payState;
	}

	public void setPayState(String payState) {
		this.payState = payState;
	}
}
