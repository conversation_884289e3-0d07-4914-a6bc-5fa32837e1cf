package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;
import com.howbuy.crm.hb.domain.custinfo.OrgCommunicateVisit;


/**
 * 
 * <AUTHOR>
 *
 */
public interface OrgCommunicateVisitMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
	OrgCommunicateVisit getOrgCommunicateVisit(Map<String, Object> param);

	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delOrgCommunicateVisit(long id);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<OrgCommunicateVisit> listOrgCommunicateVisit(Map<String, String> param);


	/**
	 * 查询产品中心、其他、机构、后台中心四个一级部门下的所有非虚拟投顾:
	 * @param
	* <AUTHOR>
	* @date 2020/6/10
	*/
	List<Map> listConsForAttend();

   
	/**
	 * 插入
	 * @param orgCommunicateVisit
	 */
	void insert(OrgCommunicateVisit orgCommunicateVisit);

	/**
	 * 更新
	 * @param orgCommunicateVisit
	 */
	void update(OrgCommunicateVisit orgCommunicateVisit);
}
