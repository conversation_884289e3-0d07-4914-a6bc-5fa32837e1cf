package com.howbuy.crm.hb.domain.system;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 实体类HbOrganization.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class HbOrganization implements Serializable {

private static final long serialVersionUID = 1L;

	private String orgcode;
	
	private String orgname;
	
	private String englishname;
	
	private String parentorgcode;
	
	private String parentorgname;
	
	private Integer sort;
	
	private String orgtype;
	
	private String status;
	
	private String showflag;
	
	private String province;
	
	private String provincename;
	
	private String city;
	
	private String cityname;
	
	private String area;
	
	private String areaname;
	
	private String address;
	
	private String recstat;
	
	private String checkflag;
	
	private String telno;
	
	private String fax;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private Date credate;
	
	private Date moddate;
	
	private Date checkdate;
	
	private Integer level;
	
	private String isleaf;
	
	
	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public String getOrgcode() {
		return this.orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}
	
	public String getOrgname() {
		return this.orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}
	
	public String getEnglishname() {
		return this.englishname;
	}

	public void setEnglishname(String englishname) {
		this.englishname = englishname;
	}
	
	public String getParentorgcode() {
		return this.parentorgcode;
	}

	public void setParentorgcode(String parentorgcode) {
		this.parentorgcode = parentorgcode;
	}
	
	public Integer getSort() {
		return this.sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}
	
	public String getOrgtype() {
		return this.orgtype;
	}

	public void setOrgtype(String orgtype) {
		this.orgtype = orgtype;
	}
	
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
	public String getShowflag() {
		return this.showflag;
	}

	public void setShowflag(String showflag) {
		this.showflag = showflag;
	}
	
	public String getProvince() {
		return this.province;
	}

	public void setProvince(String province) {
		this.province = province;
	}
	
	public String getProvincename() {
		return this.provincename;
	}

	public void setProvincename(String provincename) {
		this.provincename = provincename;
	}
	
	public String getCity() {
		return this.city;
	}

	public void setCity(String city) {
		this.city = city;
	}
	
	public String getCityname() {
		return this.cityname;
	}

	public void setCityname(String cityname) {
		this.cityname = cityname;
	}
	
	public String getArea() {
		return this.area;
	}

	public void setArea(String area) {
		this.area = area;
	}
	
	public String getAreaname() {
		return this.areaname;
	}

	public void setAreaname(String areaname) {
		this.areaname = areaname;
	}
	
	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	
	public String getRecstat() {
		return this.recstat;
	}

	public void setRecstat(String recstat) {
		this.recstat = recstat;
	}
	
	public String getCheckflag() {
		return this.checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}
	
	public String getTelno() {
		return this.telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}
	
	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}
	
	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	
	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public Date getCredate() {
		return this.credate;
	}

	public void setCredate(Date credate) {
		this.credate = credate;
	}
	
	public Date getModdate() {
		return this.moddate;
	}

	public void setModdate(Date moddate) {
		this.moddate = moddate;
	}
	
	public Date getCheckdate() {
		return this.checkdate;
	}

	public void setCheckdate(Date checkdate) {
		this.checkdate = checkdate;
	}
	
	
	

   public String getParentorgname() {
		return parentorgname;
	}

	public void setParentorgname(String parentorgname) {
		this.parentorgname = parentorgname;
	}

public static long getSerialversionuid() {
		return serialVersionUID;
	}

public String getIsleaf() {
	return isleaf;
}

public void setIsleaf(String isleaf) {
	this.isleaf = isleaf;
}
}
