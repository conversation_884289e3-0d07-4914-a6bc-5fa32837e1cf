package com.howbuy.crm.hb.domain.associationmail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 协会解析异常邮件
 * <AUTHOR> on 2021/5/17 18:14
 */
@Data
public class AssociationExpMail implements Serializable {

    /** 主键 */
    private String id;

    /** 邮件日期 */
    private Date mailDate;

    /** 邮件日期（展示用的） */
    private String mailDateStr;

    /** 来源邮箱 */
    private String sourceMail;

    /** 投顾客户代码 */
    private String conscustno;

    /** 客户名称 */
    private String custName;

    /** 机构名称 */
    private String orgName;

    /** 投资者账号 */
    private String investorAccount;

    /** 初始密码 */
    private String initPassword;

    /** 管理人登记编码 */
    private String managerRegno;

    /** 登录链接 */
    private String loginHref;

    /** 创建人 */
    private String creator;

    /** 记录创建日期 */
    private String credt;

    /** 修改人 */
    private String modifier;

    /** 修改日期 */
    private String moddt;

    /** 邮件主题 */
    private String subject;

    /** 收件人 */
    private String toMail;

    /** 删除标志（0 正常；1 删除） */
    private String delFlag;

    /** 邮件uid（线上邮件的唯一标识） */
    private String mailUid;

    /** 处理状态（0 待处理；1 暂缓处理；2 已忽略） */
    private String handleStatus;

    /** 异常状态（0 其他；1 解析异常；2 重复邮件；3 无匹配账号；4 重复匹配） */
    private String expStatus;

    /** 备注 */
    private String remark;

    /** BDP推送过来的数据（排查错误用的） */
    private String bdpPushData;

    // 以下是关联出来的字段
    /** 一账通号 */
    private String hboneNo;
}
