/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.constants;

/**
 * @description: CRM账户服务接口路径常量类，用于存放CRM账户服务接口路径常量
 * <AUTHOR>
 * @date 2023/12/14 13:39
 * @since JDK 1.8
 */
public class CrmAccountPathConstant {

    /**
     * 根据香港客户号查询香港客户信息接口路径
     */
    public static final String QUERY_HK_CUST_INFO_BY_HK_TX_ACCT_NO = "/hkcustinfo/queryhkcustinfo";

    /**
     * 根据投顾客户号查询香港客户信息接口路径
     */
    public static final String QUERY_HK_CUST_INFO_BY_CUST_NO = "/hkcustinfo/queryHkCustInfoByCustNo";

    /**
     * 分页查询香港账户中心香港客户信息列表
     */
    public static final String QUERY_HK_CUST_INFO_BY_PAGE = "/hkcustinfo/queryhkcustinfopage";

    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String RELATE_CONSCUST_AND_HKTXACCTNO = "/hkcustinfo/bindhktxacct";

    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String DISRELATE_CONSCUST_AND_HKTXACCTNO = "/hkcustinfo/unbindhktxacct";

    /**
     * 关联投顾客户号和一账通号
     */
    public static final String DISRELATE_CONSCUST_AND_HBNOE = "/hbonecustinfo/unbindhbone";
    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String MERGE_CONSCUST_LIST_AND_HKTXACCTNO = "/hkcustinfo/mergehktxacct";

    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String CREATE_HK_TX_ACCT_NO = "/hkcustinfo/createhktxacctinfo";


    /**
     * 根据投顾客户号查询一账通账户详细信息
     */
    public static final String QUERY_HBONE_CUST_DETAIL_INFO_BY_CUSTNO = "/hbonecustinfo/queryhbonecustdetailinfobycustno";



    /**
     * 根据香港交易账号查询香港客户风险测评问卷流水
     */
    public static final String QUERY_HK_KYC_ANSWER_LIST_BY_HKTXACCTNO = "/hkcustinfo/queryHkKycAnswerListByHkTxAcctNo";


    /**
     * 根据一账通号 查询账户中心客户信息
     */
    public static final String QUERY_HBONE_CUST_DETAILINFO_BY_HBONENO = "/hbonecustinfo/queryhbonecustdetailinfobyhboneno";


    /**
     * 分页查询香港客户异常信息列表
     */
    public static final String QUERY_HK_CUST_ABNORMAL_INFO_BY_PAGE = "/hkabnormal/queryhkabmoamlpage";

    /**
     * 分页查询一账通客户异常信息列表
     */
    public static final String QUERY_HBONE_CUST_ABNORMAL_INFO_BY_PAGE = "/hboneabnormal/queryhboneabmoamlpage";

    /**
     * 处理香港客户异常信息
     */
    public static final String DEAL_HK_CUST_ABNORMAL_INFO = "/hkabnormal/dealabnormal";

    /**
     * 香港异常客户页-解除异常主表关联的投顾客户号
     */
    public static final String DEAL_HK_CUST_UNBIND_RELATED_CUST_NO = "/hkabnormal/unbindrelatedcustno";

    /**
     * 香港异常客户页-新增投顾客户
     */
    public static final String DEAL_HK_CUST_CREATE_CONS_CUST_NO = "/hkabnormal/createcustinfobyhk";

    /**
     * 香港异常客户页- 子表 投顾客户号 绑定香港客户号
     */
    public static final String DEAL_HK_CUST_SUB_TABLE_BIND_HK_CUST_NO = "/hkabnormal/subtablebbindhkcustno";

    /**
     * 香港异常客户页- 子表 使用香港开户信息更新投顾客户 前置校验
     */
    public static final String DEAL_HK_CUST_SUB_TABLE_VALIDATE_BEFORE_UPDATE_CUST_INFO_BY_HK = "/hkabnormal/validatebeforeupdatecustinfobyhk";

    /**
     * 香港异常客户页- 子表 使用香港开户信息更新投顾客户
     */
    public static final String DEAL_HK_CUST_SUB_TABLE_UPDATE_CUST_INFO_BY_HK = "/hkabnormal/updatecustinfobyhk";

    /**
     * 处一账通客户异常信息
     */
    public static final String DEAL_HBONE_CUST_ABNORMAL_INFO = "/hboneabnormal/dealabnormal";


    /**
     *查询 香港客户异常信息 明细列表信息
     */
    public static final String QUERY_HK_CUST_ABNORMAL_DETAIL_INFO_ = "/hkabnormal/queryhkabmoamldetaillist";

    /**
     * 查询 一账通客户异常信息 明细列表信息
     */
    public static final String QUERY_HBONE_CUST_ABNORMAL_DETAIL_INFO_ = "/hboneabnormal/queryhboneabmoamldetaillist";

    /**
     * 批量处理香港客户异常信息
     */
    public static final String BATCH_DEAL_HK_CUST_ABNORMAL_INFO = "/hkabnormal/batchdealabnormal";
    /**
     * 批量处理一账通客户异常信息
     */
    public static final String BATCH_DEAL_HBONE_CUST_ABNORMAL_INFO = "/hboneabnormal/batchdealabnormal";



    /**
     * 一账通异常客户页，按钮：新建客户
     */
    public static final String HBONE_ABNORMAL_CREATE_CUST_INFO = "/hboneabnormal/createcustinfobyhbone";


    /**
     * 关联异常主表一账通并更新信息
     */
    public static final String ASSOCIATE_HBONE_CUST_ABNORMAL_INFO = "/hboneabnormal/associateabnormalhbone";


    /**
     * 创建投顾客户信息
     */
    public static final String CREATE_CONS_CUST = "/custinfo/createCustInfo";

    /**
     * 查询手机号码归属地
     */
    public static final String QUERY_MOBILE_AREA_CODE = "/dictionary/getmobileareacodelist";

}