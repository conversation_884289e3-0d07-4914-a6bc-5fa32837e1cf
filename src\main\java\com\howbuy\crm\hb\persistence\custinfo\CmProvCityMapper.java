package com.howbuy.crm.hb.persistence.custinfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmProvCityMapper {

    /**
     * 根据省份城市中文名获取对应的CRM省份城市代码
     * @param pmap
     * @return
     */
     Map<String, Object> qryProCityCodeByAreaName(Map<String, String> pmap);

     /**
      * 查询
      * @param pmap
      * @return
      */
     List<Map<String, Object>> qryProCity(HashMap<String, String> pmap);
}
