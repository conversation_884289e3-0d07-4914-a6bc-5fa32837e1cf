package com.howbuy.crm.hb.domain.wage;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 实体类CmSalaryProductcoefficient.java
 * @created
 */
@Data
public class CrmWageProductcoefficient implements Serializable {

    private static final long serialVersionUID = 6492492069633590457L;

    private long id;
    /** 产品代码 */
    private String fundcode;
    /**  产品名称 */
    private String fundname;
    /** 折标系数 */
    private BigDecimal backstepratio;
    /** 佣金率 */
    private BigDecimal commissionratio;
    /** 绩效系数 */
    private BigDecimal performanceratio;
    /**  所属部门编号 */
    private String orgCode;
    /**  所属部门名称 */
    private String orgName;
    /** 产品核算类型 */
    private String coefficientType;
    /** 产品核算类型名称 */
    private String coefficientTypeName;

    private String startdt;

    private String enddt;

    private String creator;

    private String modifier;

    private String credt;

    private String moddt;

}
