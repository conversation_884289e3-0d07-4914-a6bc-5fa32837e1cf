package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 划转历史数据中当前投顾与原投顾对比
 * <AUTHOR>
 */
@Data
public class CmTransfCustconsultant implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /** id */
    private BigDecimal id;
    /** 投顾客户号 */
    private String conscustno;
    /** 客户姓名 */
    private String custname;
    /** 原投顾 */
    private String oldconscode;
    /** 当前投顾 */
    private String conscode;
    private boolean same;

    public boolean getSame() {
        return (oldconscode != null) && (oldconscode.equals(conscode));
    }
}
