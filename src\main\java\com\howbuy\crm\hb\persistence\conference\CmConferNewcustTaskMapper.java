package com.howbuy.crm.hb.persistence.conference;


import com.howbuy.crm.hb.domain.conference.CmConferNewcustTask;
import com.howbuy.crm.hb.domain.conference.CmconferNewCustTaskVO;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:(扫码参会新课分配任务表Mapper接口)
 * @return
 * @author: xufanchao
 * @date: 2023/11/7 20:01
 * @since JDK 1.8
 */
public interface CmConferNewcustTaskMapper {
    /**
     * @description:(根据id删除)
     * @param id
     * @return int
     * @author: xufanchao
     * @date: 2023/11/8 18:23
     * @since JDK 1.8
     */
    int deleteByPrimaryKey(String id);

    /**
     * @description:(新增)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/8 18:23
     * @since JDK 1.8
     */
    int insert(CmConferNewcustTask record);


    /**
     * @description:(新增数据)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/8 18:23
     * @since JDK 1.8
     */
    int insertSelective(CmConferNewcustTask record);

    /**
     * @description:(新增数据)
     * @param id
     * @return com.howbuy.crm.hb.domain.conference.CmConferNewcustTask
     * @author: xufanchao
     * @date: 2023/11/8 18:23
     * @since JDK 1.8
     */
    CmConferNewcustTask selectByPrimaryKey(String id);

    /**
     * @description:(更新数据)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/8 18:23
     * @since JDK 1.8
     */
    int updateByPrimaryKeySelective(CmConferNewcustTask record);

    /**
     * @description:(更新数据)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/8 18:24
     * @since JDK 1.8
     */
    int updateByPrimaryKey(CmConferNewcustTask record);

    /**
     * @description:(查询新客分配处理数据)
     * @param cmconferNewCustTaskVO
     * @return java.util.List<com.howbuy.crm.hb.domain.conference.CmConferNewcustTask>
     * @author: xufanchao
     * @date: 2023/11/14 10:56
     * @since JDK 1.8
     */
    List<CmConferNewcustTask> listCmConferNewCustByPage(@Param("taskVo") CmconferNewCustTaskVO cmconferNewCustTaskVO,@Param("page") CommPageBean pageBean);
}