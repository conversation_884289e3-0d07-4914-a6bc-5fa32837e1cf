package com.howbuy.crm.hb.domain.prosale;

import com.howbuy.crm.hb.domain.insur.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预约单查询Vo
 * <AUTHOR>
 * NOTICE: 替换 {mybatis :id=listZxPrivateTradeFundByPage} 参数
 */
@Data
public class ZxPrivateTradeFundVo extends PageVo implements Serializable {

	private static final long serialVersionUID = 1L;

	private String  fundcode;

	private String  conscode;

	private String  orgcode;

	private String  custname;

	private String  conscustno;

	private String  hboneno;
	
	private String busicode;

	private String sort;
	
	private String  order;

	private BigDecimal balancevol;

	private String modifier;

	private String moddt;

}
