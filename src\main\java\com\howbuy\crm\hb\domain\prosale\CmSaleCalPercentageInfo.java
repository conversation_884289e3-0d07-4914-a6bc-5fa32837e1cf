package com.howbuy.crm.hb.domain.prosale;

import com.howbuy.crm.hb.enums.PreCalculateTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销控日历期内-占比的统计
 * <AUTHOR>
 * @time 2021-11-11 16:10:37
 */
@Data
public class CmSaleCalPercentageInfo implements Serializable {
    private static final long serialVersionUID = 1L;


    public CmSaleCalPercentageInfo() {

    }

    public CmSaleCalPercentageInfo(PreCalculateTypeEnum typeEnum) {
        this.typeEnum=typeEnum;
        this.description = typeEnum.getDescription();
    }

    /**
     * 排序号码  从1开始
     */
    private int sortNumber;


    /**
     * 产品代码
     */
    private String  pcode;

    /**
     * 产品名称
     */
    private String pname;
    
    /**
     * 描述
     */
    private String  description;

    /**
     * 汇总金额-字符串-展示
     */
    private String sumBalance;

    /**
     * 汇总计算的 金额/人数
     */
    private Number calculateBalance;
    /**
     * 额度配置的 金额/人数
     */
    private Number benchMarkBalance;

    /**
     * 占比-
     */
    private BigDecimal percentNum;

    /**
     * 占比-字符串-展示
     */
    private String percentage;

    /**
     * 统计维度
     */
    private PreCalculateTypeEnum typeEnum;


}
