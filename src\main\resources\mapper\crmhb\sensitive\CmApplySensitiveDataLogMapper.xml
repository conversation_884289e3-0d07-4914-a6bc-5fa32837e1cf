<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.sensitive.CmApplySensitiveDataLogMapper">
  <cache type="org.mybatis.caches.oscache.OSCache"/>
  <insert id="insertCmApplySensitiveDataLog" parameterType="CmApplySensitiveDataLog">
		INSERT INTO CM_APPLY_SENSITIVE_DATA_LOG (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="applyid != null">applyid,</if>
			<if test="sourceval != null">sourceval,</if>
		</trim>
		) VALUES (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="applyid != null">#{applyid},</if>
			<if test="sourceval != null">#{sourceval},</if>
		</trim>
		)
	</insert>
	
	<select id="listCmApplySensitiveDataLog" parameterType="Map" resultType="CmApplySensitiveDataLog" useCache="false">
		SELECT * FROM CM_APPLY_SENSITIVE_DATA_LOG
		WHERE 1=1
		<if test="applyid != null">AND applyid = #{applyid}</if>
	</select>
</mapper>