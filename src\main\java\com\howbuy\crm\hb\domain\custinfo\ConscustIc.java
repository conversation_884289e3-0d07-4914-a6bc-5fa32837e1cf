package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;


/**
 * @Description: 实体类ConscustIc.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class ConscustIc implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustno;
	
	private String conscustlvl;
	
	private Integer conscustgrade;
	
	private String conscuststatus;
	
	private String idtype;
	
	private String idno;
	
	private String custname;
	
	private String provcode;
	
	private String citycode;
	
	private String edulevel;
	
	private String vocation;
	
	private String inclevel;
	
	private String birthday;
	
	private String gender;
	
	private String married;
	
	private String pincome;
	
	private String fincome;
	
	private String decisionflag;
	
	private String interests;
	
	private String familycondition;
	
	private String contacttime;
	
	private String contactmethod;
	
	private String sendinfoflag;
	
	private String recvtelflag;
	
	private String recvemailflag;
	
	private String recvmsgflag;
	
	private String company;
	
	private String risklevel;
	
	private String selfrisklevel;
	
	private String addr;
	
	private String postcode;
	
	private String mobile;
	
	private String telno;
	
	private String fax;
	
	private String email;
	
	private String hometelno;
	
	private String officetelno;
	
	private String actcode;
	
	private String intrcustno;
	
	private String source;
	
	private String knowchan;
	
	private String otherchan;
	
	private String otherinvest;
	
	private String salon;
	
	private String beforeinvest;
	
	private String im;
	
	private String msn;
	
	private String ainvestamt;
	
	private String ainvestfamt;
	
	private String selfdefflag;
	
	private String visitfqcy;
	
	private String devdirection;
	
	private String saledirection;
	
	private String subsource;
	
	private String subsourcetype;
	
	private String saleprocess;
	
	private String mergedconscust;
	
	private String addr2;
	
	private String postcode2;
	
	private String mobile2;
	
	private String email2;
	
	private String knowhowbuy;
	
	private String subknow;
	
	private String subknowtype;
	
	private String buyingprod;
	
	private String buyedprod;
	
	private String freeprod;
	
	private String specialflag;
	
	private String dlvymode;
	
	private String remark;
	
	private String regdt;
	
	private String uddt;
	
	private String pririsklevel;
	
	private String linkman;
	
	private String linktel;
	
	private String linkmobile;
	
	private String linkemail;
	
	private String linkpostcode;
	
	private String linkaddr;
	
	private String capacity;
	
	private String activityno;
	
	private String partnerno;
	
	private String gpsinvestlevel;
	
	private String gpsrisklevel;
	
	private String isboss;
	
	private String financeneed;
	
	private String isjoinclub;
	
	private String tmpBeforeinvest;
	
	private String tmpOtherinvest;
	
	private String isrisktip;
	
	private Integer tempemailflag;
	
	private String custSourceRemark;
	
	private String custSourceRemarkno;
	
	private String checkflag;
	
	private String checkremark;
	
	private String checkman;
	
	private String creator;
	
	private String outletname;
	
	private String invsttype;

	/** 3.5.1 新增wechatcode */
	private String wechatcode;

	private String newsourceno;
	
	private String newsourcename;
	
	private String newsubsourcename;
	
	private String newsubsourcetypename;
	
	private String sourcename;

	/** 3.7.1证件有限期 */
	private String validity;

	/** 3.7.1证件有限期日期 */
	private String validitydt;

	/** 3.7.1性质 */
	private String nature;

	/** 3.7.1资质 */
	private String aptitude;

	/** 3.7.1经营范围 */
	private String scopebusiness;

	/** 3.6.2 期望交易类型 added  by jingya.xu */
	private String hopetradetype;

	/** 7.3增加 */
	private String isorg;

	/** 7.3增加 */
	private String canedit;

	/** 8.1yu.zhang 是否申请划转 0-否 1-是 */
	private String transferapply;

	/** 8.1yu.zhang 处理状态:1-待处理;2-已处理-驳回;3-已处理-同意' 等同划转日志表cm_transf_log */
	private String opstatus;
	
	/**
	 * 以Digest结尾的是脱敏信息的摘要，以Mask结尾的是掩码，以Cipher结尾的是密文
	 */
	private String idnoDigest;
	private String idnoMask;
	private String idnoCipher;
	
	private String addrDigest;
	private String addrMask;
	private String addrCipher;
	
	private String mobileDigest;
	private String mobileMask;
	private String mobileCipher;
	
	private String telnoDigest;
	private String telnoMask;
	private String telnoCipher;
	
	private String emailDigest;
	private String emailMask;
	private String emailCipher;
	
	private String addr2Digest;
	private String addr2Mask;
	private String addr2Cipher;
	
	private String mobile2Digest;
	private String mobile2Mask;
	private String mobile2Cipher;
	
	private String email2Digest;
	private String email2Mask;
	private String email2Cipher;
	
	private String linkaddrDigest;
	private String linkaddrMask;
	private String linkaddrCipher;
	
	private String linkmobileDigest;
	private String linkmobileMask;
	private String linkmobileCipher;
	
	private String linktelDigest;
	private String linktelMask;
	private String linktelCipher;
	
	private String linkemailDigest;
	private String linkemailMask;
	private String linkemailCipher;

}
