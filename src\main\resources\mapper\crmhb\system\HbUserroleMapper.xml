<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.hb.persistence.system.HbUserroleMapper">   
	<cache type="org.mybatis.caches.oscache.OSCache"/>
	<resultMap id="userrole" type="com.howbuy.crm.hb.domain.system.HbUserrole">
		<result column="usercode" property="usercode" />
		<result column="rolecode" property="rolecode" />		
		<result column="rolename" property="rolename" />
	</resultMap>
	
	<resultMap id="roleuser" type="com.howbuy.crm.hb.domain.system.HbRole">
		<result column="userId" property="userId" />
		<result column="rolecode" property="rolecode" />		
		<result column="rolename" property="rolename" />
	</resultMap>
	
	 <select id="getHbUserrole" parameterType="Map" resultType="HbUserrole">
	    SELECT
	          *
	    FROM HB_USERROLE
	    where 1=1  
	              <if test="id != null"> AND id = #{id} </if>             
                  <if test="usercode != null"> AND usercode = #{usercode} </if>             
                  <if test="rolecode != null"> AND rolecode = #{rolecode} </if>             
                  <if test="recstat != null"> AND recstat = #{recstat} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="credt != null"> AND credt = #{credt} </if>             
                  <if test="moddt != null"> AND moddt = #{moddt} </if>             
        	  </select>
	  
	  
	  <insert id="insertHbUserrole" parameterType="HbUserrole" >
	    INSERT INTO HB_USERROLE (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="id == null"> id, </if> 
	      	      <if test="usercode != null"> usercode, </if> 
	      	      <if test="rolecode != null"> rolecode, </if> 
	      	      <if test="recstat != null"> recstat, </if> 
	      	      <if test="creator != null"> creator, </if> 
	      	      <if test="modifier != null"> modifier, </if> 
	      	      <if test="credt != null"> credt, </if> 
	      	      <if test="moddt != null"> moddt, </if> 
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="id == null"> SEQ_PERMISSION.Nextval, </if> 
	      	      <if test="usercode != null"> #{usercode}, </if> 
	      	      <if test="rolecode != null"> #{rolecode}, </if> 
	      	      <if test="recstat != null"> #{recstat}, </if> 
	      	      <if test="creator != null"> #{creator}, </if> 
	      	      <if test="modifier != null"> #{modifier}, </if> 
	      	      <if test="credt != null"> #{credt}, </if> 
	      	      <if test="moddt != null"> #{moddt}, </if> 
	               </trim>	
         )      
	  </insert>
	  
	  
	  <update id="updateHbUserrole" parameterType="HbUserrole" >
	    UPDATE HB_USERROLE	    
	    <set>
	                <if test="id != null"> id = #{id}, </if>             
                    <if test="usercode != null"> usercode = #{usercode}, </if>             
                    <if test="rolecode != null"> rolecode = #{rolecode}, </if>             
                    <if test="recstat != null"> recstat = #{recstat}, </if>             
                    <if test="creator != null"> creator = #{creator}, </if>             
                    <if test="modifier != null"> modifier = #{modifier}, </if>             
                    <if test="credt != null"> credt = #{credt}, </if>             
                    <if test="moddt != null"> moddt = #{moddt}, </if>             
                 </set>
          where id = #id
	  </update>
	  
	  <delete id="delHbUserroleByRoleCode" parameterType="String">
	    DELETE  from HB_USERROLE
	    where ROLECODE = #{id}
	  </delete>
	  
	  <delete id="delHbUserroleByUserId" parameterType="String">
	    DELETE  from HB_USERROLE
	    where USERCODE = #{userId}
	  </delete>
	  
	  <select id="listHbRoleAndUser" parameterType="Map" resultMap="roleuser" useCache="false">
	    SELECT t1.rolecode,t1.rolename,t2.usercode userId FROM hb_role t1 LEFT JOIN hb_userrole t2 ON t1.rolecode = t2.rolecode 
	    <if test="userId != null"> AND t2.usercode = #{userId} </if>
		  <if test="rolename != null"> where t1.rolename like '%'||#{rolename}||'%' </if>
	  </select>
	  
	  <select id="listHbUserrole" parameterType="Map" resultType="HbUserrole" useCache="false">
	    SELECT * FROM HB_USERROLE 
	    where 1=1  
	      <if test="id != null"> AND id = #{id} </if>             
          <if test="usercode != null"> AND usercode = #{usercode} </if>             
          <if test="rolecode != null"> AND rolecode = #{rolecode} </if>             
          <if test="recstat != null"> AND recstat = #{recstat} </if>             
          <if test="creator != null"> AND creator = #{creator} </if>             
          <if test="modifier != null"> AND modifier = #{modifier} </if>             
          <if test="credt != null"> AND credt = #{credt} </if>             
          <if test="moddt != null"> AND moddt = #{moddt} </if>             
      </select>
      
      <!-- 加载角色对应的用户信息 -->
      <select id="listHbRoleUser" parameterType="Map" resultType="HbUserrole" useCache="false">
      	SELECT B.consname AS username, B.conscode AS usercode 
			  FROM HB_USERROLE A, CM_CONSULTANT B
			 where A.usercode = B.CONSCODE AND B.CONSSTATUS = 1 
			 <if test="rolecode != null"> AND A.rolecode = #{rolecode} </if>
			 <if test="username != null"> AND (B.Consname like '%'||#{username}||'%' OR B.Conscode like '%'||#{username}||'%') </if> 
      </select>
      
      <!-- 根据投顾编码获取用户角色 -->
      <select id="listHbRolecodeByConscode" parameterType="Map" resultType="HbUserrole" useCache="false">
      	select b.conscode as usercode , a.rolecode
		  from hb_userrole a, cm_consultant b
		 where a.usercode = b.conscode
		   and b.consstatus = 1
		   and b.conscode = #{consCode}
		   and a.rolecode in ('ROLE_PS_HEAD',
		                      'ROLE_PS',
		                      'ROLE_IC_ASSISTANT',
		                      'ROLE_IC_ASSISTANT_II',
		                      'ROLE_CD_HEAD',
		                      'ROLE_CD_CD',
		                      'ROLE_IC_OPDB',
		                      'ROLE_IC_HEAD')
      </select>
        	  
      <select id="listHbUserroleByUserCode" parameterType="Map" resultMap="userrole" useCache="false">
	    SELECT T1.USERCODE, T1.ROLECODE, T2.ROLENAME
  			FROM HB_USERROLE T1, HB_ROLE T2
 		WHERE T1.ROLECODE = T2.ROLECODE
	        <if test="conscode != null"> AND t1.usercode = #{conscode} </if>         
      </select>
      
      <select id="listUserByCustReview" parameterType="Map" resultType="Map" useCache="false">
      	SELECT A.CONSCODE,A.CONSNAME
		  FROM CM_CONSULTANT A
		 WHERE A.CONSCODE IN
		       (SELECT T.USERCODE
		          FROM HB_USERROLE T
		         WHERE T.ROLECODE IN (SELECT T1.USER_ROLE_ID
		                                FROM HB_AUTH T1
		                               WHERE T1.AUTH_TYPE = '3'
		                                 AND T1.MENU_CODE = '050302'
		                                 AND T1.AUTH_PARA3 = '3'))
		   <if test="isvalid == '0'.toString()"> AND A.CONSSTATUS = '1' </if>                                    
	  </select>
	  
	  <select id="listHbUserroleByPage" parameterType="Map" resultType="HbUserrole" useCache="false">
	    SELECT
	          *
	    FROM HB_USERROLE
	    where 1=1   
	              <if test="param.id != null"> AND id = #{param.id} </if>             
                  <if test="param.usercode != null"> AND usercode = #{param.usercode} </if>             
                  <if test="param.rolecode != null"> AND rolecode = #{param.rolecode} </if>             
                  <if test="param.recstat != null"> AND recstat = #{param.recstat} </if>             
                  <if test="param.creator != null"> AND creator = #{param.creator} </if>             
                  <if test="param.modifier != null"> AND modifier = #{param.modifier} </if>             
                  <if test="param.credt != null"> AND credt = #{param.credt} </if>             
                  <if test="param.moddt != null"> AND moddt = #{param.moddt} </if>             
        	  </select>
	  
	  <select id="getHbUserroleCount" parameterType="Map" resultType="int">
	    SELECT
	          COUNT(*)
	    FROM HB_USERROLE
	    where 1=1  
	              <if test="id != null"> AND id = #{id} </if>             
                  <if test="usercode != null"> AND usercode = #{usercode} </if>             
                  <if test="rolecode != null"> AND rolecode = #{rolecode} </if>             
                  <if test="recstat != null"> AND recstat = #{recstat} </if>             
                  <if test="creator != null"> AND creator = #{creator} </if>             
                  <if test="modifier != null"> AND modifier = #{modifier} </if>             
                  <if test="credt != null"> AND credt = #{credt} </if>             
                  <if test="moddt != null"> AND moddt = #{moddt} </if>             
        	  </select>
    <select id="getUserByCenterCodeAndRole" parameterType="Map" resultType="HbUserrole" useCache="false">
		select
		cc.consname AS username, hu.usercode AS usercode
		from hb_userrole hu,cm_consultant cc
		where hu.rolecode= #{rolecode}
		and hu.usercode = cc.conscode
		and cc.consstatus='1'
		and  (cc.outletcode  = #{centercode} or cc.outletcode in (
					select orgcode
					   from HB_ORGANIZATION t
					   where  t.status = '0'
					   start with t.parentorgcode = #{centercode}
					   connect by PRIOR orgcode = parentorgcode))
	</select>
	<select id="getAllRoleCodeByConsCode" resultType="java.lang.String"  useCache="false">
		select a.rolecode from hb_userrole a where a.usercode=#{conscode} and a.recstat='0'
	</select>

	<select id="listOperateByMenuUserCode" parameterType="string" resultType="string" useCache="false">
		SELECT auth_para3
		FROM hb_auth
		where menu_code = #{menuCode}
		and auth_type = '3'
		and user_role_id in
		(SELECT rolecode FROM hb_userrole where usercode = #{userId})
	</select>
</mapper>



