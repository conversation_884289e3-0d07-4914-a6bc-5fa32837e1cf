<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CrmWageMonthConslevelMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>


    <insert id="insertCrmWageMonthConslevel" parameterType="CrmWageMonthConslevel">
        INSERT INTO CM_WAGE_MONTH_CONSLEVEL (
        credt,
        ID,
        <trim suffix="" suffixOverrides=",">
            <if test="orgCode!= null"> org_code, </if>
            <if test="monthBackstepMin!= null"> MONTH_BACKSTEP_MIN, </if>
            <if test="monthBackstepMax!= null"> MONTH_BACKSTEP_MAX, </if>
            <if test="conslevel!= null"> conslevel, </if>
            <if test="startdt!= null"> startdt, </if>
            <if test="enddt!= null"> enddt, </if>
            <if test="creator!= null"> creator, </if>
        </trim>
        ) values (
        sysdate,
        SEQ_CRM_WAGE_CONSLEVEL.nextval ,
        <trim suffix="" suffixOverrides=",">
            <if test="orgCode!= null"> #{orgCode}, </if>
            <if test="monthBackstepMin!= null"> #{monthBackstepMin}, </if>
            <if test="monthBackstepMax!= null"> #{monthBackstepMax}, </if>
            <if test="conslevel!= null"> #{conslevel}, </if>
            <if test="startdt!= null"> #{startdt}, </if>
            <if test="enddt!= null"> #{enddt}, </if>
            <if test="creator!= null">  #{creator}, </if>
        </trim>
        )
    </insert>

    <delete id="deleteData"  parameterType="String" >
        delete from CM_WAGE_MONTH_CONSLEVEL
        <where>
            ID = #{id}
        </where>
    </delete>

    <select id="selectCrmWageMonthConslevelByPage" parameterType="Map" resultType="Map" useCache="false">
        SELECT CN.ID ,
        CN.ORG_CODE, HO.ORGNAME,cn.CONSLEVEL,
        cn.STARTDT,cn.ENDDT,cn.MONTH_BACKSTEP_MIN,cn.MONTH_BACKSTEP_MAX
        FROM CM_WAGE_MONTH_CONSLEVEL CN
        LEFT JOIN HB_ORGANIZATION HO
        ON CN.ORG_CODE = HO.ORGCODE
        <where> CN.ORG_CODE IN
            (SELECT ORGCODE
            FROM HB_ORGANIZATION
            CONNECT BY PRIOR ORGCODE = PARENTORGCODE
            START WITH ORGCODE = #{param.orgcode})
        </where>
         order by org_code  , startdt ,MONTH_BACKSTEP_MIN
    </select>

    <update id="updateCrmWageMonthConslevel" parameterType="CrmWageMonthConslevel">
        UPDATE CM_WAGE_MONTH_CONSLEVEL
        <set >
            moddt = sysdate,
            <trim suffix="" suffixOverrides=",">
                <if test="monthBackstepMin!= null"> MONTH_BACKSTEP_MIN = #{monthBackstepMin}, </if>
                <if test="monthBackstepMax!= null"> MONTH_BACKSTEP_Max = #{monthBackstepMax}, </if>
                <if test="monthBackstepMax== null"> MONTH_BACKSTEP_Max = null, </if>
                <if test="conslevel!= null"> conslevel = #{conslevel}, </if>
                <if test="startdt!= null"> startdt = #{startdt}, </if>
                <if test="enddt!= null"> enddt = #{enddt}, </if>
                <if test="enddt== null"> enddt = null, </if>
                <if test="modifier!= null"> MODIFIER = #{modifier}, </if>
            </trim>
        </set>
        <where>
            id = #{id}
        </where>
    </update>
</mapper>



