<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CrmWageSalesNumberMapper">
	<cache type="org.mybatis.caches.oscache.OSCache" />

	<insert id="insertCrmWageSalesNumber" parameterType="CrmWageSalesNumber">
		INSERT INTO CM_WAGE_SALES_NUMBER (
		create_time,
		<trim suffix="" suffixOverrides=",">
			<if test="orgCode!= null"> org_code, </if>
			<if test="salesNumber!= null"> sales_number, </if>
			<if test="creator!= null"> creator, </if>
		</trim>
		) values (
		sysdate,
		<trim suffix="" suffixOverrides=",">
			<if test="orgCode!= null">  #{orgCode}, </if>
			<if test="salesNumber!= null">#{salesNumber}, </if>
			<if test="creator!= null">  #{creator}, </if>
		</trim>
		)
	</insert>
	
	<select id="selectSalesNumber" parameterType="String" resultType="String" useCache="false">
		SELECT SALES_NUMBER  FROM CM_WAGE_SALES_NUMBER
		<where>
			ORG_CODE =#{orgCode}
		</where>
	</select>
	
	<select id="checkInsertData" parameterType="CrmWageSalesNumber" resultType="int" useCache="false">
		select count(1) from  CM_WAGE_SALES_NUMBER A
		<where >
			org_Code = #{orgCode}
		</where>
	</select>
	
	<delete id="deleteData"  parameterType="String" >
		delete from CM_WAGE_SALES_NUMBER
		<where>
			org_Code = #{orgCode}
		</where>
	</delete>
	
	<select id="selectCrmWageSalesNumberByPage" parameterType="Map" resultType="Map" useCache="false">
		SELECT CN.ORG_CODE, HO.ORGNAME, CN.SALES_NUMBER
		  FROM CM_WAGE_SALES_NUMBER CN
		  LEFT JOIN HB_ORGANIZATION HO
		    ON CN.ORG_CODE = HO.ORGCODE
		 <where> CN.ORG_CODE IN
		       (SELECT ORGCODE
		          FROM HB_ORGANIZATION
		        CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		         START WITH ORGCODE = #{param.orgcode})
		</where>
	</select>
	
	
	<update id="updateCrmWageSalesNumber" parameterType="CrmWageSalesNumber">
	  UPDATE CM_WAGE_SALES_NUMBER
		<set >
			update_time = sysdate,
			<trim suffix="" suffixOverrides=",">
				<if test="salesNumber != null"> sales_number = #{salesNumber}, </if>
				<if test="updator!= null"> updator = #{updator}, </if>
			</trim>
		</set>			
		<where>
			org_Code = #{orgCode}
		</where>
		
	</update>
	
</mapper>



