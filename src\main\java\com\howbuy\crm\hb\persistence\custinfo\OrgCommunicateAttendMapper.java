package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.OrgCommunicateAttend;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 参会人
 * @reason:
 * @Date: 2020/6/11 17:57
 */
public interface OrgCommunicateAttendMapper {

    void batchInsert(@Param("list") List<OrgCommunicateAttend> list);
    
    List<OrgCommunicateAttend> listOrgCommunicateAttend(Map<String,Object> param);

    void deleteAttendById(Long id);
}
