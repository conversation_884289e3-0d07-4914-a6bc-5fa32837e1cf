package com.howbuy.crm.hb.persistence.callout;

import com.howbuy.crm.hb.domain.conscust.Vconscust;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.report.SyncBpFundMan;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface VconscustMapper {

    /**
     * 根据投顾code获取其下所有的客户
     * @param param
     * @return
     */
    List<Conscust> listAutoConsCustByConscode(Map<String, String> param);

    /**
     * 通过客户号获得投顾conscode
     * @param conscustno
     * @return
     */
    String getConscodeFromConscustno(String conscustno);

    /**
     * 查询列表数据对象
     * @param param
     * @return
     */
    List<SyncBpFundMan> listSyncBpFundMan(Map<String, String> param);

    /**
     * 查询首页最新分配客户（部分）数据对象
     * @param param
     * @return
     */
    List<Vconscust> loadNewestAssignCust(Map<String, String> param);

    /**
     * 查询首页最新来电客户（部分）数据对象
     * @param param
     * @return
     */
    List<Vconscust> loadNewestIncomingCallCust(Map<String, String> param);

    /**
     * 查询首页最新联系客户（部分）数据对象
     * @param param
     * @return
     */
    List<Vconscust> loadNewestContactCust(Map<String, String> param);

    /**
     * 查询首页最近生日客户（部分）数据对象
     * @param param
     * @return
     */
    List<Vconscust> loadNearestBirthdayCust(Map<String, String> param);
}
