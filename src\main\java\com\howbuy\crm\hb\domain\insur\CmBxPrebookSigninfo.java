package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxPrebookSigninfo.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxPrebookSigninfo implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal preid;
	private String insurid;
	private String signdt;
	private String passdt;
	private String paystate;
	private String paydt;
	private String caltime;
	private String nextyearpaydt;
	private String ratiodt;
	private String cancelsurdt;
	private String insurremark;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	private String visitstate;
}
