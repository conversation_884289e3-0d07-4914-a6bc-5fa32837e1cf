/**
 * Copyright (c) 2025, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.conference;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (路演会议 上传文件)
 * <AUTHOR>
 * @date 2025/2/12 16:31
 * @since JDK 1.8
 */
@Data
public class SaveConferenceFileVO implements Serializable {

    //序列化
    private static final long serialVersionUID = 1L;

    /**
     * 会议ID
     */
    private String conferenceId;


    /**
     * 操作人员
     */
    private String operator;

    /**
     * 文件名
     * 历史逻辑： 文件名为：   会议ID-会议名称.文件后缀
     * 不开放外部修改
     */
//    private String fileName;


    /**
     * 文件后缀  Eg:txt doc  pdf  ppt  pptx  xls  xlsx
     */
    private String filesSuffix;

    /**
     * 文件相对路径
     * 历史逻辑： 文件相对路径为：  创建日期yyyyMM 格式化。
     * 不开放外部修改
     */
//    private String relativeFilePath;


    /**
     * 文件字节
     */
    private byte[] fileBytes;

}