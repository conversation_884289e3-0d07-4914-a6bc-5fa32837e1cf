<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CrmWageQuaterDetailMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>

	<select id="getCrmWageQuaterDetail" parameterType="Map" resultType="CrmWageQuaterDetail" useCache="false">
	 select id,
			yyyy,
			qu,
			depart,
			conscode,
			highratio,
			pubratio,
			outdetail,
			indetail,
			tranfvol,
			longserviceprize,
			selfbuy,
			manageamt,
			other,
			reissue,
			deduct,
			totalamt,
			remark,
			creator,
			creatdt,
			innovatecoeff,
			unqualifiedcoeff,
			quarterpreform,
			stockcall,
			finalcoeff,
			xjh,
			houseoffic,
			currpersonhz,
			currpersonhzeight,
			currpersonhztwo,
			cxa,
			cxb,
			mgmsal,
			needsal
	   from CM_WAGE_QUATER_DETAIL 
	   where id = #{id}
	</select>

    <insert id="batchInsertCrmWageQuaterDetail" parameterType="java.util.List">
		insert into CM_WAGE_QUATER_DETAIL
		   (id,
			yyyy,
			qu,
			depart,
			conscode,
			highratio,
			pubratio,
			tranfvol,
			selfbuy,
			manageamt,
			other,
			reissue,
			deduct,
			totalamt,
			remark,
			creator,
			innovatecoeff,
			stockcall,
			finalcoeff,
			houseoffic,
			cxb,
			mgmsal,
			needsal
			)
		<foreach collection="list" item="item" index="index" separator="UNION ALL" open="select to_char(SEQ_WAGE_DETAIL.NEXTVAL), a.* from (" close=") a">
	       SELECT   #{item.yyyy},
					#{item.qu},
					#{item.depart},
					#{item.conscode},
					#{item.highratio},
					#{item.pubratio},
					#{item.tranfvol},
					#{item.selfbuy},
					#{item.manageamt},
					#{item.other},
					#{item.reissue},
					#{item.deduct},
					#{item.totalamt},
					#{item.remark},
					#{item.creator},
					#{item.innovatecoeff},
					#{item.stockcall},
					#{item.finalcoeff},
					#{item.houseoffic},
					#{item.cxb},
					#{item.mgmsal},
					#{item.needsal}
			FROM DUAL
	    </foreach>
	</insert>
	
	<update id="updateCrmWageQuaterDetail" parameterType="CrmWageQuaterDetail">
        UPDATE CM_WAGE_QUATER_DETAIL	    
	   		<set>
   	      	  <if test="id != null"> id = #{id}, </if>             
      	      <if test="yyyy != null"> yyyy = #{yyyy}, </if>             
      	      <if test="qu != null"> qu = #{qu}, </if>             
      	      <if test="depart != null"> depart = #{depart}, </if>             
      	      <if test="conscode != null"> conscode = #{conscode}, </if>             
      	      <if test="highratio != null"> highratio = #{highratio}, </if>             
      	      <if test="pubratio != null"> pubratio = #{pubratio}, </if>              
      	      <if test="tranfvol != null"> tranfvol = #{tranfvol}, </if>             
      	      <if test="longserviceprize != null"> longserviceprize = #{longserviceprize}, </if>     
      	      <if test="selfbuy != null"> selfbuy = #{selfbuy}, </if> 
      	      <if test="manageamt != null"> manageamt = #{manageamt}, </if> 
      	      <if test="other != null"> other = #{other}, </if> 
      	      <if test="reissue != null"> reissue = #{reissue}, </if> 
      	      <if test="deduct != null"> deduct = #{deduct}, </if> 
      	      <if test="totalamt != null"> totalamt = #{totalamt}, </if> 
      	      <if test="remark != null"> remark = #{remark}, </if> 
      	      <if test="modifier != null"> modifier = #{modifier}, </if>             
      	      <if test="modifydt != null"> modifydt = #{modifydt}, </if> 
      	      <if test="innovatecoeff != null"> innovatecoeff = #{innovatecoeff}, </if> 
      	      <if test="unqualifiedcoeff != null"> unqualifiedcoeff = #{unqualifiedcoeff}, </if> 
      	      <if test="quarterpreform != null"> quarterpreform = #{quarterpreform}, </if>             
      	      <if test="stockcall != null"> stockcall = #{stockcall}, </if>     
      	      <if test="finalcoeff != null"> finalcoeff = #{finalcoeff}, </if>
      	      <if test="xjh != null"> xjh = #{xjh}, </if>   
      	      <if test="houseoffic != null"> houseoffic = #{houseoffic}, </if>   
      	      <if test="currpersonhz != null"> currpersonhz = #{currpersonhz}, </if>   
      	      <if test="currpersonhzeight != null"> currpersonhzeight = #{currpersonhzeight}, </if>   
      	      <if test="currpersonhztwo != null"> currpersonhztwo = #{currpersonhztwo}, </if>   
      	      <if test="cxa != null"> cxa = #{cxa}, </if>   
      	      <if test="cxb != null"> cxb = #{cxb}, </if>   
      	      <if test="mgmsal != null"> mgmsal = #{mgmsal}, </if>   
      	      <if test="needsal != null"> needsal = #{needsal}, </if>           
             </set>
       WHERE id = #{id}
    </update>

    <delete id="deleteCrmWageQuaterDetail"  parameterType="Map" >
        delete from CM_WAGE_QUATER_DETAIL
        where yyyy=#{yyyy} and qu=#{qu} and depart=#{depart}
    </delete>

    <select id="listCrmWageQuaterDetail" parameterType="Map" resultType="CrmWageQuaterDetail" useCache="false">
        select t.id,
			t.yyyy,
			t.qu,
			t.depart,
			t.conscode,
			t2.userno,
			t1.consname,
			t1.teamcode,
			t1.outletcode,
			t.highratio,
			t.pubratio,
			t.outdetail,
			t.indetail,
			t.tranfvol,
			t.longserviceprize,
			t.selfbuy,
			t.manageamt,
			t.other,
			t.reissue,
			t.deduct,
			t.totalamt,
			t.remark,
			t.creator,
			t.creatdt,
			t.innovatecoeff,
			t.unqualifiedcoeff,
			t.quarterpreform,
			t.stockcall,
			t.finalcoeff,
			t.xjh,
			t.houseoffic,
			t.currpersonhz,
			t.currpersonhzeight,
			t.currpersonhztwo,
			t.cxa,
			t.cxb,
			t.mgmsal,
			t.needsal
	   from CM_WAGE_QUATER_DETAIL T
		      LEFT JOIN CM_CONSULTANT T1
		        ON (T.CONSCODE = T1.CONSCODE)
		      left join CM_CONSULTANT_EXP T2
		       on T.CONSCODE = t2.userid
	   where 1=1          
              <if test="yyyy != null"> AND yyyy = #{yyyy} </if>             
              <if test="qu != null"> AND qu = #{qu} </if>             
              <if test="conscode != null"> and T.CONSCODE = #{conscode,jdbcType=VARCHAR} </if>
		     <if test="conscode == null and orgcode != null"> 
		     	and ( T1.TEAMCODE = #{orgcode} OR
		     		T1.OUTLETCODE in (
		     			SELECT ORGCODE
						  FROM HB_ORGANIZATION a
						 WHERE a.STATUS = '0'
						 START WITH a.ORGCODE = #{orgcode}
						CONNECT BY PRIOR ORGCODE = PARENTORGCODE
		     		)
		     	)
		     </if>
		     order by T1.OUTLETCODE,T1.TEAMCODE,t1.consname
    </select>

	<select id="getConsInfoByDeptType" parameterType="Map" resultType="CrmWageQuaterDetail" useCache="false">
		SELECT A.CONSCODE, A.CONSNAME, A.teamcode, A.OUTLETCODE, B.USERNO
		  FROM CM_CONSULTANT A
		  LEFT JOIN CM_CONSULTANT_EXP B
		    ON A.CONSCODE = B.USERID
		 WHERE A.CONSLEVEL IS NOT NULL
		   AND A.CONSSTATUS = '1'
		   AND A.ISVIRTUAL = '0'
		   AND A.OUTLETCODE IN
		       (SELECT HO.ORGCODE
		          FROM HB_ORGANIZATION HO
		          WHERE HO.STATUS = '0'
		         <if test="depttype != null and depttype =='IC'"> 
		         START WITH HO.ORGCODE = '1'
		         </if>
		         <if test="depttype != null and depttype =='HBC'"> 
		         START WITH HO.ORGCODE = '10'
		         </if>
		        CONNECT BY PRIOR HO.ORGCODE = HO.PARENTORGCODE)
		   ORDER BY A.OUTLETCODE,A.teamcode,A.CONSNAME
	</select>
    
</mapper>



