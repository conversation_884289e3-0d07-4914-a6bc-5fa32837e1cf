package com.howbuy.crm.hb.domain.associationmail;

import lombok.Data;

import java.io.Serializable;

/**
 * BDP匹配日志
 * <AUTHOR> on 2021/6/15 11:18
 */
@Data
public class BdpMatchLog implements Serializable {

    /** 主键 */
    private String id;

    /** 创建人 */
    private String creator;

    /** 记录创建日期 */
    private String credt;

    /** 总数据 */
    private Integer totalNum;

    /** 匹配成功 */
    private Integer matchSuccessNum;

    /** 匹配不到 */
    private Integer matchFailNum;

    /** 多个投顾客户号 */
    private Integer matchManyNum;

    /** 耗时 */
    private Long elapsedTime;
}
