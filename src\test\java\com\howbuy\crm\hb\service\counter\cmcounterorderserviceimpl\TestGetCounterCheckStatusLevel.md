## 测试的类
> com.howbuy.crm.hb.service.counter.impl.CmCounterOrderServiceImpl
## 测试的方法
> getCounterCheckStatusLevel(String conscustno, String bdid)

## 分支伪代码
```java
if (投顾客户号或bdid为空) {
    直接抛出异常，提示"投顾客户号或bdid不允许为空"
}
if (业务类型没取到) {
    直接抛出异常，提示“业务不存在，找不到相应的busiid”
}
if (关联账户或私募定投业务) {
    返回分部已审核的状态枚举
}
if (客户所属投顾的所属组织架构不属于IC/HBC) {
    返回分部已审核的状态枚举
}
if (上传人是地区投顾助理或高端柜台业务处理) {
    返回分部已审核的状态枚举
}
if (是香港业务) {
    返回分部已审核的状态枚举
}
返回待分部审核的状态枚举
```

## 测试案例
##### 1、testEmptyConscustnoOrBdid：投顾客户号或bdid为空
##### 2、testBusiidIsNull：业务类型没取到
##### 3、testIsRelateFixedBusiness：关联账户或私募定投业务
##### 4、testCustNotIcHbc：客户所属投顾的所属组织架构不属于IC/HBC
##### 5、testUploaderIsAssistantOP：上传人是地区投顾助理或高端柜台业务处理
##### 6、testIsHKBusiness：是香港业务
##### 7、testDefaultSituation：默认情况