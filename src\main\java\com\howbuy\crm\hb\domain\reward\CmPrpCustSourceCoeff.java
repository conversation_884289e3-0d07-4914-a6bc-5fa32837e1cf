package com.howbuy.crm.hb.domain.reward;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: shuai.zhang
 * @Date: 2021/9/1
 * @Description:配置-客户来源系数
 */
@Data
public class CmPrpCustSourceCoeff implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键id */
    private Long id;
    /** 来源类型 */
    private String sourceType;
    /** 来源系数起点(0.2、1) */
    private String startPoint;
    /** 客户折标系数 */
    private BigDecimal zbCoeff;
    /** 管理系数 */
    private BigDecimal manageCoeff;
    /**
     * 管理系数-区副
     */
    private BigDecimal manageCoeffRegionalSubTotal;
    /**
     * 管理系数-区总
     */
    private BigDecimal manageCoeffRegionalTotal;
    /** 存续A 可选项有：公司资源、投顾资源 */
    private String cxa;
    /** 存续B 可选项有：公司资源、投顾资源 */
    private String cxb;
    /**
     * 起始日期
     */
    private String startDt;
    /**
     * 结束日期
     */
    private String endDt;

    private String creator;

    private Date createTime;

    private String modor;

    private Date updateTime;

}
