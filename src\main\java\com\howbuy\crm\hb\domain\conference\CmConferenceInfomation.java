package com.howbuy.crm.hb.domain.conference;

import java.io.Serializable;
import java.util.List;

/**
 * (路演参会部门)
 * <AUTHOR>
 * @ClassName: CmConferenceDepartment
 * 											  
 * 创建日期    		                            修改人员   	        版本	 	     修改内容  
 * -------------------------------------------------  
 * 2017年8月14日 下午5:05:24   yu.zhang     1.0    	初始化创建
 *
 * 修改记录:
 * @since		JDK1.6
 */
public class CmConferenceInfomation implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 会议主键(外键) */
	private String conferenceid;
	
	/** 参会部门 */
	private String field;
	
	/** 参会部门名称 */
	private String fieldvalue;

	/** 创建日期 */
	private String creatdt;

	/** 创建人 */
	private String creater;
	
	/** 修改日期 */
	private String modifydt;
	
	/** 修改人 */
	private String modifier;

	public String getConferenceid() {
		return conferenceid;
	}

	public void setConferenceid(String conferenceid) {
		this.conferenceid = conferenceid;
	}

	public String getCreatdt() {
		return creatdt;
	}

	public void setCreatdt(String creatdt) {
		this.creatdt = creatdt;
	}

	public String getCreater() {
		return creater;
	}

	public void setCreater(String creater) {
		this.creater = creater;
	}

	public String getModifydt() {
		return modifydt;
	}

	public void setModifydt(String modifydt) {
		this.modifydt = modifydt;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getField() {
		return field;
	}

	public void setField(String field) {
		this.field = field;
	}

	public String getFieldvalue() {
		return fieldvalue;
	}

	public void setFieldvalue(String fieldvalue) {
		this.fieldvalue = fieldvalue;
	}

}
