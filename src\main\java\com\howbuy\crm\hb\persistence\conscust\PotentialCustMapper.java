package com.howbuy.crm.hb.persistence.conscust;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.conscust.CmPotentialCust;
import com.howbuy.crm.hb.domain.conscust.PotentialCust;

import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface PotentialCustMapper {

	/**
	 * 获取batchid在数据库中的次数
	 * @param param
	 * @return
	 */
	public abstract Map getBatchIDCount(Map<String,Object> param);
	
	/**
	 * 插入潜在客户表中
	 * @param potentialCust
	 */
	public abstract void addPotentialCust(PotentialCust potentialCust);
	
	
	
	
	
	
	
	
	
	
	/**
     * @Description:得到单个数据对象
     * @param Map
     * @return PotentialCust
     */
    CmPotentialCust getPotentialCust(Map<String, String> param);


	/**
	 * @Description:得到单个数据对象
	 * @param Map
	 * @return PotentialCust
	 */
	CmPotentialCust getPotentialById(@Param("pcustId") String pcustId);
    
	/**
     * @Description:单条修改数据对象
     * @param PotentialCust
     * @return 
     */
	int updatePotentialCust(CmPotentialCust potentialCust);
	
	/**
     * @Description:单条删除数据对象
     * @param String id
     * @return
     */
	void delPotentialCust(String id);
	
	/**
     * @Description:删除多条数据对象
     * @param po String ids
     * @return
     */
	void delListPotentialCust(String ids);	
	
	/**
     * @Description:查询列表数据对象
     * @param Map
     * @return List<PotentialCust>
     */
	List<CmPotentialCust> listPotentialCust(Map<String, String> param);
	
	/**
     * @Description:查询列表数据对象
     * @param Map
     * @return List<PotentialCust>
     */
	List<CmPotentialCust> listPotentialCustRepeat(Map<String, String> param);
	
	/**
     * @Description:查询总数
     * @param Map
     * @return int（总条数）
     */
	int getPotentialCustCount(Map<String, String> param);
	
	/**
     * @Description:查询总数
     * @param Map
     * @return int（总条数）
     */
	int getPotentialCustRepeatCount(Map<String, String> param);
	
	/**
     * @Description:查询列表（分页数据）
     * @param Map
     * @return List<PotentialCust>
     */
	List<CmPotentialCust> listPotentialCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);


	List<CmPotentialCust> listPotentialCust2(Map<String, String> param);

	List<Map<String,Object>> listPotentialcfCust(Map<String, String> param);
	/**
     * @Description:查询列表（分页数据）
     * @param Map
     * @return List<PotentialCust>
     */
	List<CmPotentialCust> listPotentialCustRepeatByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);


	/**
	 * @Description 查询列表(分页数据)
	 * @param param
	 * @param pageBean
     * @return
     */
	List<CmPotentialCust> listPotentialcfCustByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 判断手机号是否重复
	 * @param mobile
	 * @return
     */
	int isMobileRepeat(@Param("param") Map<String,String> param);
}
