package com.howbuy.crm.hb.persistence.conscust;

import com.howbuy.crm.hb.domain.callout.PubCustVO;
import com.howbuy.crm.hb.domain.conscust.Conscustrpubcust;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ConscustrpubcustMapper {
	/**
	 * 根据投顾客户号统计公募号个数
	 * @param param
	 * @return
	 */
	public abstract Map getPubCustCountByConsCustNO(Map<String, Object> param);
	
	/**
	 * 根据投顾客户号获取跟公募关系对象
	 * @param param
	 * @return
	 */
	public abstract Conscustrpubcust getPubCustByConscustNo(Map<String, Object> param);

	/**
	 * 获取单个
	 * @param param
	 * @return
	 */
	public abstract List<Conscustrpubcust> getConsCustByPubcustNo(Map<String, String> param);
	/**
	 * 根据公募客户号获取是否有数据
	 * @param conscustrpubcust
	 * @return
	 */
	public abstract int getPubCustByPubCustNo(Conscustrpubcust conscustrpubcust);
	/**
	 * 根据投顾客户号获取对应的公募客户号信息
	 * @param param
	 * @return
	 */
	public abstract Map getPubCustInfoByConscustNO(Map<String, Object> param);
	/**
	 * 功能描述：根据公募客户号和投顾客户号查询是否已经存在绑定数据
	 * @param param
	 * @return
	 */
	public abstract Map isExistRelateion(Map<String, Object> param);

	/**
	 * 功能描述：查询公募客户是否已经包含投顾
	 * @param param
	 * @return
	 */
	public abstract List<PubCustVO> isAssignedConstant(Map<String, Object> param);
	
	/**
	 * 查询公募客户交易记录
	 * @param param
	 * @param pageBean
	 * @return
	 */
	public List<Map<String,Object>> listPubCustByPage(@Param("p") Map<String, String> param, @Param("page") CommPageBean pageBean);
}
