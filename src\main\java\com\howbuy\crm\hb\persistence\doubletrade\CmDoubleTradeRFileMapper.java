package com.howbuy.crm.hb.persistence.doubletrade;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleTradeRFile;
import crm.howbuy.base.db.CommPageBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmDoubleTradeRFileMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmDoubleTradeRFile getCmDoubleTradeRFile(Map<String, String> param);
    
     /**
      * 增数据对象
      * @param cmDoubleTradeRFile
      */
	void insertCmDoubleTradeRFile(CmDoubleTradeRFile cmDoubleTradeRFile);
	
	/**
	 * 单条修改数据对象
	 * @param cmDoubleTradeRFile
	 */
	void updateCmDoubleTradeRFile(CmDoubleTradeRFile cmDoubleTradeRFile);
	
	/**
	 * 单条修改数据对象
	 * @param cmDoubleTradeRFile
	 */
	void updateCmDoubleTradeRFileByFileid(CmDoubleTradeRFile cmDoubleTradeRFile);
	
	/**
	 * 单条删除数据对象
	 * @param id
	 */
	void delCmDoubleTradeRFile(String id);
	
	/**
	 * 删除多条数据对象
	 * @param ids
	 */
	void delListCmDoubleTradeRFile(String ids);	
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleTradeRFile> listCmDoubleTradeRFile(Map<String, String> param);
	
	/**
	 * 查询总数
	 * @param param
	 * @return
	 */
	int getCmDoubleTradeRFileCount(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmDoubleTradeRFile> listCmDoubleTradeRFileByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

}
