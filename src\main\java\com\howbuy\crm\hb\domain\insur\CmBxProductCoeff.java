package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxProductCoeff.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxProductCoeff implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private String fundcode;
	private String detailfundcode;
	private String isdel;
	private String signbegdt;
	private String signenddt;
	private String passbegdt;
	private String passenddt;
	private String channcode;
	private String payyears;
	private String ensureyears;
	private BigDecimal yearamk;
	private BigDecimal yearamkend;
	private BigDecimal procoe;
	/**
	 * 投顾佣金率
	 */
	private BigDecimal proratio;
	/**
	 * 公司佣金率
	 */
	private BigDecimal gsproratio;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	private String detailfundname;
	private String channname;
	private String payyearsname;
	private String ensureyearsname;
	private String yearnum;
	/** 是否特殊处理:1-是;2-否 */
	private String isspecialhandle;

	/** 公司名称 */
	private String compname;
}
