package com.howbuy.crm.hb.service.prosale.prebookproductinfoserviceimpl;

import java.util.ArrayList;
import java.util.List;

import com.howbuy.crm.hb.service.prosale.impl.PrebookproductinfoServiceImpl;
import com.howbuy.crm.page.core.webapp.util.SessionUserManager;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.service.business.product.ComprehensiveService;


import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.annotations.Test;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.testng.Assert;


/**
 * 单元测试：获取一个月内对应货币的最近的一笔的人民币中间价
 * <AUTHOR>
 * @date 2022/12/14 16:18
 */
@PowerMockIgnore("javax.management.*")
@PrepareForTest({PrebookproductinfoServiceImpl.class, SessionUserManager.class})
public class TestGetCurLastZjj extends PowerMockTestCase {

    @InjectMocks
    private PrebookproductinfoServiceImpl serviceMock;
    
    @Mock
    private ComprehensiveService comprehensiveService;

    public static final String TEST_CURRENCY = "TEST_CURRENCY";
    
    public static final String TEST_ENDDT = "20221215";

    /**
     * 1.currency为空
     */
    @Test
    public void test1() {
    	PrebookproductinfoServiceImpl spy = PowerMockito.spy(serviceMock);
    	//执行方法
    	RmbhlzjjDto result = (RmbhlzjjDto)ReflectionUtils.invokeMethod(MemberModifier.methods(PrebookproductinfoServiceImpl.class, "getCurLastZjj")[0], spy, null,TEST_ENDDT);
        Assert.assertTrue(result==null,"币种为空");
    }
    
    /**
     * 2.enddt为空
     */
    @Test
    public void test2() {
    	PrebookproductinfoServiceImpl spy = PowerMockito.spy(serviceMock);
    	//执行方法
    	RmbhlzjjDto result = (RmbhlzjjDto)ReflectionUtils.invokeMethod(MemberModifier.methods(PrebookproductinfoServiceImpl.class, "getCurLastZjj")[0], spy, TEST_CURRENCY,null);
        Assert.assertTrue(result==null,"日期为空");
    }
    
    /**
     * 3.接口返回null
     */
    @Test
    public void test3() {
    	PrebookproductinfoServiceImpl spy = PowerMockito.spy(serviceMock);
    	//mock根据币种和日期调接口返回null
        PowerMockito.when(comprehensiveService.getRmbhlzjjList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
    	//执行方法
    	RmbhlzjjDto result = (RmbhlzjjDto)ReflectionUtils.invokeMethod(MemberModifier.methods(PrebookproductinfoServiceImpl.class, "getCurLastZjj")[0], spy, TEST_CURRENCY,TEST_ENDDT);
        Assert.assertTrue(result==null,"接口返回null");
    }
    
    /**
     * 4.接口返回的列表第一条是最近的日期，返回第一条数据
     */
    @Test
    public void test4() {
    	PrebookproductinfoServiceImpl spy = PowerMockito.spy(serviceMock);
    	List<RmbhlzjjDto> list = new ArrayList<>();
    	RmbhlzjjDto dto1 = new RmbhlzjjDto();
    	dto1.setJzrq("20221215");
    	RmbhlzjjDto dto2 = new RmbhlzjjDto();
    	dto2.setJzrq("20221214");
    	list.add(dto1);
    	list.add(dto2);
    	//mock根据预约id没有查到折扣信息
        PowerMockito.when(comprehensiveService.getRmbhlzjjList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(list);
    	//执行方法
    	RmbhlzjjDto result = (RmbhlzjjDto)ReflectionUtils.invokeMethod(MemberModifier.methods(PrebookproductinfoServiceImpl.class, "getCurLastZjj")[0], spy, TEST_CURRENCY,TEST_ENDDT);
        Assert.assertTrue("20221215".equals(result.getJzrq()),"接口返回第一条");
    }
    
    /**
     * 5.接口返回的列表第一条不是最近的日期，返回日期最近的数据
     */
    @Test
    public void test5() {
    	PrebookproductinfoServiceImpl spy = PowerMockito.spy(serviceMock);
    	List<RmbhlzjjDto> list = new ArrayList<>();
    	RmbhlzjjDto dto1 = new RmbhlzjjDto();
    	dto1.setJzrq("20221215");
    	RmbhlzjjDto dto2 = new RmbhlzjjDto();
    	dto2.setJzrq("20221214");
    	list.add(dto2);
    	list.add(dto1);
    	//mock根据预约id没有查到折扣信息
        PowerMockito.when(comprehensiveService.getRmbhlzjjList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(list);
    	//执行方法
    	RmbhlzjjDto result = (RmbhlzjjDto)ReflectionUtils.invokeMethod(MemberModifier.methods(PrebookproductinfoServiceImpl.class, "getCurLastZjj")[0], spy, TEST_CURRENCY,TEST_ENDDT);
        Assert.assertTrue("20221215".equals(result.getJzrq()),"接口返回日期最近的一条");
    }

    
}