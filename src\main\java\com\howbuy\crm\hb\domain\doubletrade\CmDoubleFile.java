package com.howbuy.crm.hb.domain.doubletrade;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 实体类CmDoubleFile.java
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class CmDoubleFile implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 文件编号
	 */
	private String fileId;
	/**
	 * 文件存储路径
	 */
	private String filePath;
	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 上传状态：0上传成功；1上传失败；
	 */
	private String uploadType;
	/**
	 * 记录状态：0正常；1已删除
	 */
	private String recStat;
	/**
	 * 上传文件人员名称
	 */
	private String uploader;
	/**
	 * 修改人
	 */
	private String modifier;
	/**
	 * 修改日期
	 */
	private String modDt;
	/**
	 * 上传文件日期
	 */
	private String uploadDt;

	/** 0：不存在；1：已存在 */
	private String isExsit;


	/**
	 * 视频录制开始时间
	 */
	private String videostarttime;

	/**
	 * 视频录制时长
	 */
	private String videoduration;

	/**
	 * 视频房间ID
	 */
	private String videoroomid;

	/**
	 * 视频下载状态 0 待下载 1 已下载
	 */
	private String videodownstatus;

	/**
	 * 云端视频缩略图地址
	 */
	private String imageurl;

	private String id;

	/**
	 * 是否规范 1-合规 0-不合规
	 */
	private String islegal;

	/**
	 * 文件相对路径
	 */
	private String relativeFilePath;

}
