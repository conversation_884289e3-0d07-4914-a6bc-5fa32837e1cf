package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.custinfo.CmCustLabel;


/**
 * 
 * <AUTHOR>
 *
 */
public interface CmCustLabelMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
	CmCustLabel getCmCustLabel(Map<String, String> param);
    
     /**
      * 新增数据对象
      * @param cmCustLabel
      */
	void insertCmCustLabel(CmCustLabel cmCustLabel);
	
	/**
	 * 移动投顾打标日志
	 * @param cmCustLabel
	 */
	void insertCmCustLabelLog(CmCustLabel cmCustLabel);
	
	/**
	 * 单条删除数据对象
	 * @param conscustno
	 */
	void delCmCustLabel(String conscustno);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmCustLabel> listCmCustLabelLog(Map<String, String> param);
	
	/**
	 * 更新投顾客户等级
	 * @param param
	 */
	void updateConscustlvlByCustno(Map<String, String> param);
	
}
