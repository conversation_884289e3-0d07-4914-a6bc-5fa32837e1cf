package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmCustfamily implements Serializable {
	private static final long serialVersionUID = 1L;

	private String conscustno;

	private String custname;
	
	private String orgname;

	private String uporgname;

	private String conscode;

	private String consname;

	private String checkflag;

	private String creator;

	private String modifier;

	private String checker;

	private String credt;

	private String moddt;

	private String checkdt;

	private String source;

	private String sourcename;

	private String subsource;

	private String subsourcetype;

	private String subnum;

	private String custsourceremark;

	private String regdt;

	private String remark;

	private String custfamilyremark;

}
