package com.howbuy.crm.hb.persistence.conference;

import com.howbuy.crm.hb.domain.conference.CmConferenceCustInfomation;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmConferenceCustInfomationMapper {

	/**
	 * 插入
	 * @param cmConferenceCustInfomation
	 */
    void insertCmConferenceCustInfomation(CmConferenceCustInfomation cmConferenceCustInfomation);

    /**
     * 删除
     * @param param
     */
    void deleteCustInfomation(Map<String, String> param);

    /**
     * 查询列表
     * @param param
     * @return
     */
    List<CmConferenceCustInfomation> listCmConferenceCustInfomation(Map<String, String> param);

    /**
     * 通过id查询
     * @param conferenceid
     * @return
     */
    List<CmConferenceCustInfomation> listCmConferenceCustInfomationByid(String conferenceid);
    
    /**
     * 更新
     * @param cmConferenceCustInfomation
     */
    void updateCmConferenceCustInfomationByConcustnos(CmConferenceCustInfomation cmConferenceCustInfomation);
}
