package com.howbuy.crm.hb.persistence.conference;


import com.howbuy.crm.hb.domain.conference.CmConferenceTheme;

/**
 * (路演会议属性)
 * <AUTHOR>
 *
 */
public interface CmConferenceThemeMapper {

	/**
	 * 插入
	 * @param cmConferenceTheme
	 */
	public void insertCmConferenceTheme(CmConferenceTheme cmConferenceTheme);
	
	/**
	 * 更新
	 * @param cmConferenceTheme
	 */
	public void updateCmConferenceTheme(CmConferenceTheme cmConferenceTheme);
	
	
	/**
	 * 删除
	 * @param conferenceid
	 */
	public void deleteCmConferenceTheme(String conferenceid);
	
	
}
