package com.howbuy.crm.hb.persistence.doubletrade;

import com.howbuy.crm.hb.domain.doubletrade.CmDoubleTrade;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmDoubleTradeMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmDoubleTrade getCmDoubleTrade(Map<String, String> param);

	/**
	 * 根据id查询对应记录
	 * @param idList
	 * @return
	 */
	List<CmDoubleTrade> getCmDoubleTradeList(@Param("idList") List<String> idList);
    
    /**
     * 得到单个客户数据对象
     * @param param
     * @return
     */
    CmDoubleTrade getCmTradeCustInfo(Map<String, String> param);
    
    /**
     * 得到单个公募基金数据对象
     * @param param
     * @return
     */
    CmDoubleTrade getCmTradePubFundInfo(Map<String, String> param);
    
    /**
     * 得到单个私募基金数据对象
     * @param param
     * @return
     */
    CmDoubleTrade getCmTradePriFundInfo(Map<String, String> param);
    

	/**
	 * 单条修改数据对象
	 * @param cmDoubleTrade
	 */
	void updateCmDoubleTrade(CmDoubleTrade cmDoubleTrade);
	
	/**
	 * 单条修改数据对象
	 * @param cmDoubleTrade
	 */
	void updateCmDoubleTradeCalmendDt(CmDoubleTrade cmDoubleTrade);
	
	/**
	 * 批量修改数据对象
	 * @param cmDoubleTrade
	 */
	void updateBatchCmDoubleTrade(CmDoubleTrade cmDoubleTrade);
	
	/**
	 * 修改记录为无需双录
	 * @param cmDoubleTrade
	 */
	void updateNoNeedDoubleTrade(CmDoubleTrade cmDoubleTrade);

	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleTrade> listCmDoubleTrade(Map<String, String> param);
	
	/**
	 * 查询导出列数据对象
	 * @param param
	 * @return
	 */
	List<CmDoubleTrade> exportCmDoubleTrade(Map<String, String> param);
	

	/**
	 * 修改产品数据为需双录
	 * @param exeParam
	 */
	void updateDoubleTask(Map<String, String> exeParam);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmDoubleTrade> listCmDoubleTradeByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 通过双录任务id查询对应预约:
	 * @param tid
	 * @return
	 */
	CmPrebookproductinfo getPreBookByTradeId(String tid);

	/**
	 * 获得待分配任务量
	 * @return
	 */
	int countToAssignedTasks();

	/**
	 * 获取已分配任务处理人
	 * @return
	 */
	List<String> getConductors();

	/**
	 * 根据条件查询已分配任务数量
	 * @param param
	 * @return
	 */
	int countDountTradeByContion(Map<String,String> param);

	/**
	 * 获取处理人下拉列表
	 * @param param
	 * @return
	 */
	List<Map<String,String>> getConductorList(Map<String, String> param);

	/**
	 * 查询发送回访状态修改提醒需要的数据
	 * @param tradeId 回访任务id
	 * @return
	 */
	List<Map<String,String>> queryHandleFlagUpdateNoticeData(String tradeId);

	/**
	 * 修改为“无需双录”
	 * @param tradeId 双录流水id
	 */
	int updateNotNeedDoubleTrade(String tradeId);

	/**
	 * @description:(请在此添加描述)
	 * @param tradeIds
	 * @return int
	 * @author: jin.wang03
	 * @date: 2025/3/18 10:38
	 * @since JDK 1.8
	 */
	int batchUpdateToNoNeed(@Param("tradeIds") List<String> tradeIds, @Param("modifier") String modifier);

	/**
	 * 获取同步中台订单需要的参数
	 * @param tradeId 双录流水id
	 * @return
	 */
	Map<String,String> getSyncZtOrderParams(String tradeId);
	
	/**
	 * 根据任务id和处理人查询出回访状态 = 待投顾处理/待客服处理 且 处理人 != 空 且 处理人 != 当前选择分配客服的客户号和处理人
	 * @param param
	 * @return
	 */
	List<Map<String,String>> getNeedHandTradeId(Map<String, String> param);
}
