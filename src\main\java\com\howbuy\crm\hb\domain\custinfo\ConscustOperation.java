package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;


/**
 * @Description: 实体类ConscustIc.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class ConscustOperation implements Serializable {

	private static final long serialVersionUID = 1L;

	private String operationid;

	private String conscustno;

	private String conscustlvl;

	private Integer conscustgrade;

	private String custname;

	private String mobile;

	private String mobileCipher;

	private String provcode;

	private String citycode;

	private String newsourceno;

	private String custSourceRemark;

	private String regdt;

	private String status;

	/** 8.1yu.zhang 是否申请划转 0-否 1-是 */
	private String transferapply; 

	/** 8.1yu.zhang 处理状态:1-待处理;2-已处理-驳回;3-已处理-同意' 等同划转日志表cm_transf_log */
	private String opstatus; 

	private String creator;

	private String creatdt;

	private String source;

	private String checkflag;

	/** 客户状态：针对审核通过的客户，取客户当前成交状态【若客户持有“高端成交”标签（标签id:21001），则取“成交”；否则取“潜客” */
	private String custStatus;

	private String uporgname;

	private String outletname;

	private String newsourcename;

	private String custSourceRemarkno;

	/** 7.3的需求增加的内容 */
	private String canedit;

	/** 7.3增加 */
	private String isorg;

	/** 操作来源：1-CRM，2-APP */
	private String opensource;
	
	private int checkStatus;

	/**
	 * CM_CUSTMGR表中配置
	 */
	private String custMgr;

	/**
	 * 投顾
	 */
	private String consCode;

	/**
	 * 是否可以查看 手机明文
	 * 当 客户状态 = 潜客（当前时间点）且 当前用户 = 创建者 时，可查看客户手机明文
	 */
	private boolean canSeeMobileInfo=false;

}
