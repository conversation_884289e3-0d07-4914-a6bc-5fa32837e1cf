package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.util.Date;

/**
 *  白名单查询
 * <AUTHOR>
 *
 */
public class ProductWhiteModel implements Serializable{

    private static final long serialVersionUID = -9154971435078347807L;
    /**
     * 用户名称
     */
    private String custName;
    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品名称
     */
    private String fundName;
    /**
     * 一账通号
     */
    private String hboneNo;
    
    /**
     * 所属投顾
     */
    private String consname;
    
    /**
	 * 所属区域
	 */
	private String uporgname;
	/**
	 * 所属部门
	 */
	private String outletName;
    /**
     * 创建时间
     */
    private Date createTimestamp;
    /**
     * 更新时间
     */
    private Date updatedTimestamp;

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(Date updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

	public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

	public String getUporgname() {
		return uporgname;
	}

	public void setUporgname(String uporgname) {
		this.uporgname = uporgname;
	}

	public String getOutletName() {
		return outletName;
	}

	public void setOutletName(String outletName) {
		this.outletName = outletName;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

}
