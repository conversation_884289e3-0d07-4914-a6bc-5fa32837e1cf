package com.howbuy.crm.hb.domain.uploadmodule;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;


/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmUploadModuleType implements Serializable {
	private static final long serialVersionUID = 1L;
	private String id;
	/**
	 * 模块id
	 */
	private String moduleid;
	/**
	 * 上传类型
	 */
	private String uptype;
	/**
	 * 上传限制大小（单位M）
	 */
	private BigDecimal maxsize;
	private String creator;
	/**
	 * 可以上传的格式
	 */
	private List<CmUploadModuleTypeSuffix> suffixs;
}
