package com.howbuy.crm.hb.persistence.conference;


import com.howbuy.crm.hb.domain.conference.CmConferNewcustDetail;
import com.howbuy.crm.hb.domain.conference.CmconferNewCustTaskDetailDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:(扫码参会新客分配任务明细表mapper接口)
 * @return
 * @author: xufanchao
 * @date: 2023/11/7 19:58
 * @since JDK 1.8
 */
public interface CmConferNewcustDetailMapper {
    /**
     * @description:(根据)
     * @param id
     * @return int
     * @author: xufanchao
     * @date: 2023/11/7 19:59
     * @since JDK 1.8
     */
    int deleteByPrimaryKey(String id);

    /**
     * @description:(新增数据)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/7 20:01
     * @since JDK 1.8
     */
    int insert(CmConferNewcustDetail record);

    /**
     * @description:(新增数据)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/7 20:02
     * @since JDK 1.8
     */
    int insertSelective(CmConferNewcustDetail record);

    /**
     * @description:(根据id进行查询数据)
     * @param id
     * @return com.howbuy.crm.hb.domain.conference.CmConferNewcustDetail
     * @author: xufanchao
     * @date: 2023/11/7 20:02
     * @since JDK 1.8
     */
    CmConferNewcustDetail selectByPrimaryKey(String id);

    /**
     * @description:(更新数据)
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/7 20:03
     * @since JDK 1.8
     */
    int updateByPrimaryKeySelective(CmConferNewcustDetail record);

    /**
     * @description:(     * @description:(更新数据))
     * @param record
     * @return int
     * @author: xufanchao
     * @date: 2023/11/7 20:03
     * @since JDK 1.8
     */
    int updateByPrimaryKey(CmConferNewcustDetail record);

    /**
     * @return int
     * @description:批量更新数据
     * @author: xufanchao
     * @date: 2023/11/17 11:07
     * @since JDK 1.8
     */
    int updateBatchSelective(List<CmConferNewcustDetail> list);

    /**
     * @description:(查询当前订单的详情数据)
     * @param id
     * @return java.util.List<com.howbuy.crm.hb.domain.conference.CmconferNewCustTaskDetailDTO>
     * @author: xufanchao
     * @date: 2023/11/15 15:12
     * @since JDK 1.8
     */
    List<CmconferNewCustTaskDetailDTO> listCmConferNewCust(@Param("id") String id);
}