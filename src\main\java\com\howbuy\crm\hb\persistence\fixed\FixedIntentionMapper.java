package com.howbuy.crm.hb.persistence.fixed;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.fixed.CmFixedIntention;

import crm.howbuy.base.db.CommPageBean;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

/**
 * 
 * <AUTHOR>
 *
 */
@MapperScan
public interface FixedIntentionMapper{
	
	/**
	 * 查询当前预约列表
	 * @param cmFixedIntention
	 */
	void updateFixedState(CmFixedIntention cmFixedIntention) ;
	
	/**
	 * 插入意向单表
	 * @param cmFixedIntention
	 */
	void insertCmFixedIntention(CmFixedIntention cmFixedIntention) ;

	/**
	 * 插入意向单变化历史表
	 * @param cmFixedIntention
	 */
	void insertCmFixedIntentionLog(CmFixedIntention cmFixedIntention) ;
	
	/**
	 * 获取意向单
	 * @param param
	 * @return
	 */
	CmFixedIntention getCmFixedIntention(Map<String,Object> param) ;
	
	/**
	 * 查询多条记录
	 * @param param
	 * @return
	 */
	List<CmFixedIntention> listCmFixedIntention(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmFixedIntention> listCmFixedIntentionByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 根据意向单id获取定投折扣率
	 * @param intentionid 意向单id
	 * @return
	 */
	BigDecimal getPlanDiscountByIntetionid(@Param("intentionid") String intentionid);
}
