package com.howbuy.crm.hb.domain.checkmail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 定制账单配置子表
 * <AUTHOR>
 */
@Data
public class CmCheckMailConSub implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;
    /** 配置主表主键 */
    private String mid;
    /** 产品代码 */
    private String productcode;
    /** 产品名称 */
    private String productname;
    /** 产品类型 4阳光私募/5股权 */
    private String producttype;
    /** 产品管理人 好买基金/好臻 */
    private String productmanager;
    /** 创建人 */
    private String creator;
    /** 创建日期 */
    private Date credt;
    /** 创建日期 */
    private String credtstr;
    /** 修改人 */
    private String modifier;
    /** 修改日期 */
    private Date moddt;
    /** 修改日期 */
    private String moddtstr;
}
