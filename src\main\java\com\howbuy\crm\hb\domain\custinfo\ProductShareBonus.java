package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 实体类ProductShareBonus.java
 * <AUTHOR>
 * @version 1.0
 */
public class ProductShareBonus implements Serializable {

	private static final long serialVersionUID = 1L;

	private String custName;

	private String consName;
	
	private String consCode;

	private String fundCode;

	private BigDecimal balanceVol;

	private BigDecimal nav;

	private BigDecimal totalCost;

	private BigDecimal yjsy;

	private String jjdm;

	private String jjjc;

	private String clrq;

	private String zzrq;

	private String fprq;

	private String cxqx2;

	private String cnt;
	
	private String amt;

	private String syfppl;

	private String syfpsm;
	
	private String yjdqr;

	public String getCustName() {
		return this.custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getConsName() {
		return this.consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getFundCode() {
		return this.fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public BigDecimal getBalanceVol() {
		return this.balanceVol;
	}

	public void setBalanceVol(BigDecimal balanceVol) {
		this.balanceVol = balanceVol;
	}

	public BigDecimal getNav() {
		return this.nav;
	}

	public void setNav(BigDecimal nav) {
		this.nav = nav;
	}

	public BigDecimal getTotalCost() {
		return this.totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public BigDecimal getYjsy() {
		return this.yjsy;
	}

	public void setYjsy(BigDecimal yjsy) {
		this.yjsy = yjsy;
	}

	public String getJjdm() {
		return this.jjdm;
	}

	public void setJjdm(String jjdm) {
		this.jjdm = jjdm;
	}

	public String getJjjc() {
		return this.jjjc;
	}

	public void setJjjc(String jjjc) {
		this.jjjc = jjjc;
	}

	public String getClrq() {
		return this.clrq;
	}

	public void setClrq(String clrq) {
		this.clrq = clrq;
	}

	public String getZzrq() {
		return this.zzrq;
	}

	public void setZzrq(String zzrq) {
		this.zzrq = zzrq;
	}

	public String getFprq() {
		return this.fprq;
	}

	public void setFprq(String fprq) {
		this.fprq = fprq;
	}

	public String getCxqx2() {
		return this.cxqx2;
	}

	public void setCxqx2(String cxqx2) {
		this.cxqx2 = cxqx2;
	}

	public String getYjdqr() {
		return yjdqr;
	}

	public void setYjdqr(String yjdqr) {
		this.yjdqr = yjdqr;
	}

	public String getCnt() {
		return this.cnt;
	}

	public void setCnt(String cnt) {
		this.cnt = cnt;
	}
	
	public String getAmt() {
		return amt;
	}

	public void setAmt(String amt) {
		this.amt = amt;
	}

	public String getSyfppl() {
		return this.syfppl;
	}

	public void setSyfppl(String syfppl) {
		this.syfppl = syfppl;
	}

	public String getSyfpsm() {
		return this.syfpsm;
	}

	public void setSyfpsm(String syfpsm) {
		this.syfpsm = syfpsm;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
