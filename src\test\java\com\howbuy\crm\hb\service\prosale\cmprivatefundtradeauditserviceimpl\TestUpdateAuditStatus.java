package com.howbuy.crm.hb.service.prosale.cmprivatefundtradeauditserviceimpl;

import com.google.common.collect.Lists;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit.CmPrivatefundtradeAudit;
import com.howbuy.crm.hb.persistence.prosale.CmPrivatefundtradeAuditMapper;
import com.howbuy.crm.hb.service.prosale.impl.CmPrivatefundtradeAuditServiceImpl;
import com.howbuy.crm.page.core.webapp.util.SessionUserManager;
import crm.howbuy.base.constants.StaticVar;
import lombok.SneakyThrows;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.testng.Assert;

import static org.mockito.ArgumentMatchers.anyList;

/**
 * 单元测试：修改直销交易记录审核记录的审核状态
 * <AUTHOR>
 * @date 2022/7/12 16:18
 */
@PowerMockIgnore("javax.management.*")
@PrepareForTest({CmPrivatefundtradeAuditServiceImpl.class, SessionUserManager.class})
public class TestUpdateAuditStatus extends PowerMockTestCase {

    @InjectMocks
    private CmPrivatefundtradeAuditServiceImpl serviceMock;
    @Mock
    private CmPrivatefundtradeAuditMapper cmPrivatefundtradeAuditMapper;

    private CmPrivatefundtradeAudit cmPrivatefundtradeAudit;
    public static final String TEST_AUDITID = "TEST_AUDITID";

    /**
     * 1.auditid为空
     */
    @Test
    public void testEmptyAuditid() {
        // mock auditid为空
        mockEmptyAuditid();
        ReturnMessageDto<Integer> returnMessageDto = serviceMock.updateAuditStatus(cmPrivatefundtradeAudit);
        Assert.assertFalse(returnMessageDto.isSuccess());
        Assert.assertEquals(returnMessageDto.getReturnMsg(), "请先选择需要审核的数据");
    }

    /**
     * 2.auditstatus为空
     */
    @Test
    public void testEmptyAuditstatus() {
        // mock auditstatus为空
        mockEmptyAuditstatus();
        ReturnMessageDto<Integer> returnMessageDto = serviceMock.updateAuditStatus(cmPrivatefundtradeAudit);
        Assert.assertFalse(returnMessageDto.isSuccess());
        Assert.assertEquals(returnMessageDto.getReturnMsg(), "审核状态不能为空");
    }

    /**
     * 3.该审核记录不是待审核状态
     */
    @Test
    public void testNotWaitCheck() {
        // mock审核记录不是待审核状态
        mockNotWaitCheck();
        ReturnMessageDto<Integer> returnMessageDto = serviceMock.updateAuditStatus(cmPrivatefundtradeAudit);
        Assert.assertFalse(returnMessageDto.isSuccess());
        Assert.assertEquals(returnMessageDto.getReturnMsg(), "该记录目前不是待审核状态，请刷新当前页面");
    }

    /**
     * 4.审核通过，验证持仓信息成功
     */
    @Test
    public void testValidateFundtradeSuccess() {
        CmPrivatefundtradeAuditServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock 审核通过，验证持仓信息成功
        mockValidateFundtradeSuccess(spy);
        ReturnMessageDto<Integer> returnMessageDto = spy.updateAuditStatus(cmPrivatefundtradeAudit);
        Assert.assertTrue(returnMessageDto.isSuccess());
        Assert.assertEquals((int)returnMessageDto.getReturnObject(), 1);
    }

    /**
     * 5.审核通过，验证持仓信息失败
     */
    @Test
    public void testValidateFundtradeFail() {
        CmPrivatefundtradeAuditServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock 审核通过，验证持仓信息失败
        mockValidateFundtradeFail(spy);
        ReturnMessageDto<Integer> returnMessageDto = spy.updateAuditStatus(cmPrivatefundtradeAudit);
        Assert.assertFalse(returnMessageDto.isSuccess());
        Assert.assertEquals((int)returnMessageDto.getReturnObject(), 0);
    }

    /**
     * 6.审核操作为"审核不通过"
     */
    @Test
    public void testAuditNotPass() {
        // mock 审核操作为"审核不通过"
        mockAuditNotPass();
        ReturnMessageDto<Integer> returnMessageDto = serviceMock.updateAuditStatus(cmPrivatefundtradeAudit);
        Assert.assertTrue(returnMessageDto.isSuccess());
        Assert.assertEquals((int)returnMessageDto.getReturnObject(), 1);
    }

    /**
     * mock auditid为空
     */
    private void mockEmptyAuditid() {
        cmPrivatefundtradeAudit = new CmPrivatefundtradeAudit();
    }

    /**
     * mock auditstatus为空
     */
    private void mockEmptyAuditstatus() {
        cmPrivatefundtradeAudit = new CmPrivatefundtradeAudit();
        cmPrivatefundtradeAudit.setAuditid(TEST_AUDITID);
    }

    /**
     * mock审核记录不是待审核状态
     */
    private void mockNotWaitCheck() {
        cmPrivatefundtradeAudit = new CmPrivatefundtradeAudit();
        cmPrivatefundtradeAudit.setAuditid(TEST_AUDITID);
        cmPrivatefundtradeAudit.setAuditstatus(StaticVar.ZX_TRADE_AUDIT_PASS);

        PowerMockito.when(cmPrivatefundtradeAuditMapper.hasNotAuditStatusData(Lists.newArrayList(TEST_AUDITID), StaticVar.ZX_TRADE_AUDIT_WAIT_CHECK))
            .thenReturn(true);
    }

    /**
     * mock 审核通过，验证持仓信息成功
     */
    @SneakyThrows
    private void mockValidateFundtradeSuccess(CmPrivatefundtradeAuditServiceImpl spy) {
        cmPrivatefundtradeAudit = new CmPrivatefundtradeAudit();
        cmPrivatefundtradeAudit.setAuditid(TEST_AUDITID);
        cmPrivatefundtradeAudit.setAuditstatus(StaticVar.ZX_TRADE_AUDIT_PASS);

        PowerMockito.when(cmPrivatefundtradeAuditMapper.hasNotAuditStatusData(Lists.newArrayList(TEST_AUDITID), StaticVar.ZX_TRADE_AUDIT_WAIT_CHECK))
                .thenReturn(false);

        PowerMockito.mockStatic(SessionUserManager.class);
        PowerMockito.when(SessionUserManager.getCurrentUserId()).thenReturn("chun.lin");
        PowerMockito.doReturn(ReturnMessageDto.ok()).when(spy, "validateAndInsertFundtrade", anyList());
        PowerMockito.when(cmPrivatefundtradeAuditMapper.updateAuditStatus(cmPrivatefundtradeAudit)).thenReturn(1);
    }

    /**
     * mock 审核通过，验证持仓信息失败
     */
    @SneakyThrows
    private void mockValidateFundtradeFail(CmPrivatefundtradeAuditServiceImpl spy) {
        cmPrivatefundtradeAudit = new CmPrivatefundtradeAudit();
        cmPrivatefundtradeAudit.setAuditid(TEST_AUDITID);
        cmPrivatefundtradeAudit.setAuditstatus(StaticVar.ZX_TRADE_AUDIT_PASS);

        PowerMockito.when(cmPrivatefundtradeAuditMapper.hasNotAuditStatusData(Lists.newArrayList(TEST_AUDITID), StaticVar.ZX_TRADE_AUDIT_WAIT_CHECK))
                .thenReturn(false);

        PowerMockito.mockStatic(SessionUserManager.class);
        PowerMockito.when(SessionUserManager.getCurrentUserId()).thenReturn("chun.lin");
        PowerMockito.doReturn(ReturnMessageDto.fail("审核失败", 0)).when(spy, "validateAndInsertFundtrade", anyList());
    }

    /**
     * mock 审核操作为"审核不通过"
     */
    @SneakyThrows
    private void mockAuditNotPass() {
        cmPrivatefundtradeAudit = new CmPrivatefundtradeAudit();
        cmPrivatefundtradeAudit.setAuditid(TEST_AUDITID);
        cmPrivatefundtradeAudit.setAuditstatus(StaticVar.ZX_TRADE_AUDIT_NOT_PASS);

        PowerMockito.when(cmPrivatefundtradeAuditMapper.hasNotAuditStatusData(Lists.newArrayList(TEST_AUDITID), StaticVar.ZX_TRADE_AUDIT_WAIT_CHECK))
                .thenReturn(false);

        PowerMockito.mockStatic(SessionUserManager.class);
        PowerMockito.when(SessionUserManager.getCurrentUserId()).thenReturn("chun.lin");
        PowerMockito.when(cmPrivatefundtradeAuditMapper.updateAuditStatus(cmPrivatefundtradeAudit)).thenReturn(1);
    }
}