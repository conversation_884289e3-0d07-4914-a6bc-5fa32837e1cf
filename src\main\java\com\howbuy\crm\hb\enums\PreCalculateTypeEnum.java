/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.hb.enums;
/**
 * 销控 统计类型的枚举
 * <AUTHOR>
 *
 */
public enum PreCalculateTypeEnum {

	/**
	 * 1-预约金额
	 */
	APP_BALANCE("1", "预约金额"),
	/**
	 * 2-预约人数
	 */
	APP_NUM("2", "预约人数"),
	/**
	 * 3-打款金额
	 */
	PAY_BALANCE("3", "打款金额"),
	/**
	 * 4-打款人数
	 */
	PAY_NUM("4", "打款人数"),
	/**
	 * 5-白名单预约金额
	 */
	WHITE_BALANCE("5", "白名单预约金额"),
	/**
	 * 6-白名单预约人数
	 */
	WHITE_NUM("6", "白名单预约人数")
	;
	/**
	 * 编码
	 */
	private String code; 
	/**
	 * 描述
	 */
	private String description; 

	private PreCalculateTypeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		PreCalculateTypeEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static PreCalculateTypeEnum getEnum(String code) {
		for(PreCalculateTypeEnum statusEnum : PreCalculateTypeEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
