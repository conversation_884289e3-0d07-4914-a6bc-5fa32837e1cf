package com.howbuy.crm.hb.persistence.insur;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.insur.*;
import org.apache.ibatis.annotations.Param;

import crm.howbuy.base.db.CommPageBean;

/**
 * <AUTHOR>
 * @Description: 接口
 * @version 1.0
 * @created 
 */
public interface CmBxPrebookinfoMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxPrebookinfo getCmBxPrebookinfo(Map<String, Object> param);
    
    /**
     * 得到单个数据对象
     * @param param
     * @return
     */
    CmBxPrebookinfo getViewCmBxPrebookinfo(Map<String, Object> param);
    
     /**
      * 新增数据对象
      * @param cmBxPrebookinfo
      */
	void insertCmBxPrebookinfo(CmBxPrebookinfo cmBxPrebookinfo);
	
	/**
	 * 插入密文
	 * @param cmBxPrebookinfo
	 */
	void insertCmBxPrebookinfoCipher(CmBxPrebookinfo cmBxPrebookinfo);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxPrebookinfo
	 */
	void updateCmBxPrebookinfo(CmBxPrebookinfo cmBxPrebookinfo);
	
	/**
	 * 更新预约密文
	 * @param cmBxPrebookinfo
	 */
	void updateCmBxPrebookinfoCipher(CmBxPrebookinfo cmBxPrebookinfo);
	
	/**
	 * 单条历史修改数据对象
	 * @param cmBxPrebookinfo
	 */
	void updateHisCmBxPrebookinfo(CmBxPrebookinfo cmBxPrebookinfo);
	
	/**
	 * 修改密文
	 * @param cmBxPrebookinfo
	 */
	void updateHisCmBxPrebookinfoCipher(CmBxPrebookinfo cmBxPrebookinfo);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxPrebookinfo> listCmBxPrebookinfo(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmBxPrebookinfo> listCmBxPrebookinfoByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

	/**
	 * 批量更新预约单信息的复核状态数据
	 */
	int updateCmBxPrebookListById(List<CmBxPrebookEndpayList> cmBxPrebookLists);

	/**
	 * 更新历史的保单数据
	 */
	int updateCmBxPrebookList();

	/**
	 * 导出
	 * @param param
	 * @return
	 */
	List<CmBxPrebookinfo> listCmBxPrebookinfoByExp(Map<String, String> param);
	
	/**
	 * 佣金明细-查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<InsureCommissionPageDto> listCmBxPrebookinfoCommByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 佣金明细-导出
	 * @param param
	 * @return
	 */
	List<InsureCommissionPageDto> listCmBxPrebookinfoCommByExp(Map<String, String> param);

	/**
	 * 查询缴款计划列表。附带buyIfo preInfo 信息
	 * @param vo
	 * @param pageBean
	 * @return
	 */
	List<CmBxPrebookBuyWithPayInfo> listCmBxPrebookBuyWithPayInfoByPage(@Param("param") CmBxPrebookBuyWithPayVo vo, @Param("page") CommPageBean pageBean);

	/**
	 * 查询保单的部门明细数据
	 */
	List<CmBxPrebookBuyWithPayInfo> listCmBxPrebookinfoByInsuid(String preid);
}
