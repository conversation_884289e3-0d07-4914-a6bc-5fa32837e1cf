<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.reward.CmPrpOrderinfoTypeCoeffMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>
	<insert id="insertCmPrpOrderinfoTypeCoeff" parameterType="CmPrpOrderinfoTypeCoeff">
		INSERT INTO CM_PRP_ORDERINFO_TYPE_COEFF (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="accountProductType != null">account_product_type,</if>
			<if test="orderInfoType != null">order_info_type,</if>
			<if test="bigOrderProductType != null">big_order_product_type,</if>
			<if test="commissionRate != null">commission_rate,</if>
			<if test="stockFeeDMin != null">stock_fee_d_min,</if>
			<if test="stockFeeDMax != null">stock_fee_d_max,</if>
			<if test="startDt != null">start_dt,</if>
			<if test="endDt != null">end_dt,</if>
			<if test="creator != null">creator,</if>
			<if test="modor != null">modor,</if>
			<if test="updateTime != null">update_time,</if>
		</trim>
		) VALUES (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="accountProductType != null">#{accountProductType},</if>
			<if test="orderInfoType != null">#{orderInfoType},</if>
			<if test="bigOrderProductType != null">#{bigOrderProductType},</if>
			<if test="commissionRate != null">#{commissionRate},</if>
			<if test="stockFeeDMin != null">#{stockFeeDMin},</if>
			<if test="stockFeeDMax != null">#{stockFeeDMax},</if>
			<if test="startDt != null">#{startDt},</if>
			<if test="endDt != null">#{endDt},</if>
			<if test="creator != null">#{creator},</if>
			<if test="modor != null">#{modor},</if>
			<if test="updateTime != null">#{updateTime},</if>
		</trim>
		)
	</insert>
	
	<update id="updateCmPrpOrderinfoTypeCoeff" parameterType="CmPrpOrderinfoTypeCoeff" >
	    UPDATE CM_PRP_ORDERINFO_TYPE_COEFF	    
	    <set>
			big_order_product_type = #{bigOrderProductType,jdbcType=VARCHAR},
			stock_fee_d_min = #{stockFeeDMin,jdbcType=NUMERIC},
			stock_fee_d_max = #{stockFeeDMax,jdbcType=NUMERIC},
			commission_rate = #{commissionRate,jdbcType=NUMERIC},
			start_dt = #{startDt,jdbcType=VARCHAR},
			end_dt = #{endDt,jdbcType=VARCHAR}, 
			modor = #{modor,jdbcType=VARCHAR},                  
            update_time = sysdate,           
         </set>
          where id = #{id}
	  </update>
	  
	  <delete id="delCmPrpOrderinfoTypeCoeff" parameterType="BigDecimal">
	    delete from CM_PRP_ORDERINFO_TYPE_COEFF where id = #{id}
	  </delete>
	  
	  <select id="listCmPrpOrderinfoTypeCoeffByPage" parameterType="Map" resultType="CmPrpOrderinfoTypeCoeff" useCache="false">
	    SELECT id,
	           account_product_type accountProductType,
	           order_info_type orderInfoType,
		  	   big_order_product_type bigOrderProductType,
	           commission_rate commissionRate,
	    	   start_dt startDt,
	    	   end_dt endDt,
	    	   creator,
	    	   create_time createTime,
	    	   modor,
	    	   update_time updateTime,
		  stock_fee_d_min stockFeeDMin,
		  stock_fee_d_max stockFeeDMax
		  FROM CM_PRP_ORDERINFO_TYPE_COEFF
	    where 1=1   
            <if test="param.id != null"> AND id = #{param.id} </if>    
            <if test="param.accountProductType != null"> AND account_product_type = #{param.accountProductType} </if>    
            <if test="param.orderInfoType != null"> AND order_info_type = #{param.orderInfoType} </if>         
          order by create_time desc
      </select>
      
      <select id="getCmPrpOrderinfoTypeCoeff" parameterType="Map" resultType="CmPrpOrderinfoTypeCoeff" useCache="false">
	    SELECT id,
	           account_product_type accountProductType,
	           order_info_type orderInfoType,
		  	   big_order_product_type bigOrderProductType,
	           commission_rate commissionRate,
	    	   start_dt startDt,
	    	   end_dt endDt,
	    	   creator,
	    	   create_time createTime,
	    	   modor,
	    	   update_time updateTime,
		  	   stock_fee_d_min stockFeeDMin,
		   	   stock_fee_d_max stockFeeDMax,
			  sale_fold_coeff saleFoldCoeff
		 FROM CM_PRP_ORDERINFO_TYPE_COEFF
	     where 1=1  
         <if test="id != null"> AND id = #{id} </if>  
         <if test="accountProductType != null"> AND account_product_type = #{accountProductType} </if> 
         <if test="orderInfoType != null"> AND order_info_type = #{orderInfoType} </if>            
         <if test="startDt != null"> AND start_dt = #{startDt} </if>             
         <if test="endDt != null"> AND end_dt = #{endDt} </if>                             
  	  </select>
  	  
  	  <select id="listCmPrpOrderinfoTypeCoeff" parameterType="Map" resultType="CmPrpOrderinfoTypeCoeff" useCache="false">
	    SELECT id,
	           account_product_type accountProductType,
	           order_info_type orderInfoType,
		  	   big_order_product_type bigOrderProductType,
		  	   stock_fee_d_min stockFeeDMin,
		  	   stock_fee_d_max stockFeeDMax,
	           commission_rate commissionRate,
	    	   start_dt startDt,
	    	   end_dt endDt,
	    	   creator,
	    	   create_time createTime,
	    	   modor,
	    	   update_time updateTime
		 FROM CM_PRP_ORDERINFO_TYPE_COEFF
	     where 1=1  
         <if test="id != null"> AND id = #{id} </if>  
         <if test="accountProductType != null"> AND account_product_type = #{accountProductType} </if>
         <if test="orderInfoType != null"> AND order_info_type = #{orderInfoType} </if>              
         <if test="stockFeeDMin != null or stockFeeDMax != null">
			 AND nvl(#{stockFeeDMin,jdbcType=NUMERIC}, 0) &lt; nvl(stock_fee_d_max,10000)
			 and nvl(#{stockFeeDMax,jdbcType=NUMERIC}, 10000) &gt; nvl(stock_fee_d_min, 0)
		 </if>
         <if test="bigOrderProductType != null and bigOrderProductType != ''"> AND big_order_product_type = #{bigOrderProductType,jdbcType=VARCHAR} </if>
         <if test="startDt != null"> AND start_dt = #{startDt} </if>
         <if test="endDt != null"> AND end_dt = #{endDt} </if>      
         <if test="checkStartDt != null">  
         AND ( #{checkStartDt} BETWEEN START_DT AND NVL(END_DT, '********') 
         	   OR #{checkEndDt} BETWEEN START_DT AND NVL(END_DT, '********')
         	   OR START_DT BETWEEN #{checkStartDt} AND #{checkEndDt}
         	   OR NVL(END_DT, '********') BETWEEN #{checkStartDt} AND #{checkEndDt}
         	  ) 
         </if>
         <if test="notId != null"> AND id != #{notId} </if>            
      </select>
      
</mapper>