package com.howbuy.crm.hb.domain.prosale;

import com.howbuy.crm.base.PreBookTradeTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 白名单校验使用的-用来验证的dto
 * @author: haoran.zhang
 * @create: 2022-02-21 19:50
 **/
@Data
public class CmPreForWhiteValidateDto {

    /**
     * 预约id
     */
    private BigDecimal preId ;
    /**
     * 预约交易类型 {@link com.howbuy.crm.base.PreBookTradeTypeEnum}
     */
    private String tradeType;

    /**
     * 客户号
     */
    private String concustNo;

    /**
     * 金额
     */
    private BigDecimal buyAmt;

    /**
     * 分次call预约 的 callId .
     * 不为分次Call预约。 为空
     */
    private BigDecimal callId;

    /**
     * 分次call预约 所属callId 对应的 首次预约Id
     */
    private BigDecimal firstPreId;


    /**
     * 是否计入-人数的统计
     * @return
     */
    public boolean isNumCalculate(){
    	//购买 才计入人数统计
        if(!PreBookTradeTypeEnum.BUY.getCode().equals(getTradeType())){ 
            return  false;
        }
      //callId 为空，标识 [不是分次call]
        if(callId==null){ 
            return  true;
        }else{
            //分次call. 当前预约为首次预约，才计入
            return preId != null && preId.equals(firstPreId);
        }
    }
}
