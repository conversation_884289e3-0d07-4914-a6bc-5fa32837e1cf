package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/5/16 13:11
 * @since JDK 1.8
 */
/**
 * 已注销一账通解绑crm客户处理表-一次性
 */
public class HboneCancelFlushUnbind implements Serializable {
    /**
    * 一账通号
    */
    private String hboneNo;

    /**
    * 执行解绑的客户号
    */
    private String ubindCustNo;

    /**
    * 处理状态 0-未处理 1-已处理 2-无需处理
    */
    private String dealStatus;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getUbindCustNo() {
        return ubindCustNo;
    }

    public void setUbindCustNo(String ubindCustNo) {
        this.ubindCustNo = ubindCustNo;
    }

    public String getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus;
    }
}