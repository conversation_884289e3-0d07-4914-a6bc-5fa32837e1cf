<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CmWageQuaterReportMapper">
	<cache type="org.mybatis.caches.oscache.OSCache" />
	<!--配置返回游标中别名对应的resultMap -->
	<resultMap type ="java.util.HashMap" id= "quatercollectCursorMap">
	</resultMap >

	<resultMap type ="java.util.HashMap" id= "quatercountCursorMap">
	</resultMap >

	<select id="queryWageQuaterCollect" statementType="CALLABLE"  parameterType="Map" useCache="false">
		<![CDATA[
		{call PRO_WAGE_QUATER_COLLECT(
		    #{P_START_DATE ,mode=IN ,jdbcType=VARCHAR},
			#{P_END_DATE ,mode=IN ,jdbcType=VARCHAR},
			#{P_YEAR_MONTH ,mode=IN ,jdbcType=VARCHAR},
			#{P_TYPE ,mode=IN,jdbcType=VARCHAR},
			#{P_ORGCODE ,mode=IN, jdbcType=VARCHAR},
			#{P_PAGE_SIZE ,mode=IN, jdbcType=INTEGER},
			#{P_PAGE ,mode=IN ,jdbcType=INTEGER},
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} ,
			#{P_TOTAL,mode=OUT ,jdbcType=INTEGER},
			#{V_DATA_CURSOR ,mode=OUT, jdbcType=CURSOR,javaType=ResultSet,resultMap=quatercollectCursorMap})}
		]]>
	</select>


	<select id="queryWageQuaterCount" statementType="CALLABLE"  parameterType="Map" useCache="false">
		<![CDATA[
		{call PRO_WAGE_QUATER_COUNT(
			#{P_YEAR_MONTH ,mode=IN ,jdbcType=VARCHAR},
			#{P_TYPE ,mode=IN,jdbcType=VARCHAR},
			#{P_ORGCODE ,mode=IN, jdbcType=VARCHAR},
			#{P_CONSCODE ,mode=IN ,jdbcType=VARCHAR},
			#{P_PCODE ,mode=IN, jdbcType=VARCHAR},
			#{P_PRODTYPE ,mode=IN, jdbcType=VARCHAR},
			#{P_CUSTNAME ,mode=IN, jdbcType=VARCHAR},
			#{P_PAGE_SIZE ,mode=IN, jdbcType=INTEGER},
			#{P_PAGE ,mode=IN ,jdbcType=INTEGER},
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} ,
			#{P_TOTAL,mode=OUT ,jdbcType=INTEGER},
			#{V_DATA_CURSOR ,mode=OUT, jdbcType=CURSOR,javaType=ResultSet,resultMap=quatercountCursorMap})}
		]]>
	</select>
	
	<select id="publishWageQuaterCount" statementType="CALLABLE"  parameterType="Map" useCache="false">
		<![CDATA[
		{call PRO_WAGE_QUATER_COUNT_PUBLISH(
			#{P_YEAR_MONTH ,mode=IN ,jdbcType=VARCHAR},
			#{P_ORGCODE ,mode=IN, jdbcType=VARCHAR},
			#{P_CONSCODE ,mode=IN ,jdbcType=VARCHAR},
			#{P_PCODE ,mode=IN, jdbcType=VARCHAR},
			#{P_PRODTYPE ,mode=IN ,jdbcType=VARCHAR},
			#{P_CUSTNAME ,mode=IN ,jdbcType=VARCHAR},
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR})}
		]]>
	</select>
	
	<select id="listCmWageQuaterCountResultMap" parameterType="Map" resultType="Map" useCache="false">
	   SELECT A.*
       FROM CM_WAGE_QUATER_COUNT_RESULT A
       LEFT JOIN CM_CONSULTANT C
         ON A.CONSCODE = C.CONSCODE
      WHERE (A.ORGCODE IN (SELECT ORGCODE
                             FROM HB_ORGANIZATION HJ
                           CONNECT BY PRIOR HJ.ORGCODE = HJ.PARENTORGCODE
                            START WITH ORGCODE = #{orgcode}) OR C.TEAMCODE = #{orgcode})
        AND A.YEAR_MONTH = #{yearMonth}
        <if test="conscode != null"> AND A.CONSCODE = #{conscode} </if>
        <if test="prodtype != null"> AND A.PRO_TYPE = #{prodtype} </if>
        <if test="custname != null"> AND A.CONSCUSTNAME LIKE '%'||#{custname}||'%' </if>
        <if test="pcode != null"> AND A.PCODE = #{pcode} </if>
     </select>
</mapper>



