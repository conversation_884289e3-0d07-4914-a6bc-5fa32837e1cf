/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.outersevice;

import com.howbuy.crm.callstat.service.CallStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 通话状态外部服务类，用于调用外部接口，获取通话状态信息
 * <AUTHOR>
 * @date 2023/12/20 15:00
 * @since JDK 1.8
 */
@Service
public class CallStatOuterService {
    @Autowired
    private CallStatService callStatService;

    /**
     * @description: 获取通话状态
     * @param key 通话 key
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    public String getState(String key) {
        return callStatService.getState(key);
    }

    /**
     * @description: 增加通话状态
     * @param key 通话 key
     * @return java.lang.Boolean
     * @throws
     * @since JDK 1.8
     */
    public Boolean addCall(String key) {
        return callStatService.addCall(key);
    }

    /**
     * @description: 删除通话状态
     * @param key 通话 key
     * @return java.lang.Boolean
     * @throws
     * @since JDK 1.8
     */
    public Boolean removeCall(String key) {
        return callStatService.removeCall(key);
    }


    /**
     * @description: 更新通话状态
     * @param key  通话 key
     * @param value 需要更新的值
     * @return java.lang.Boolean
     * @throws
     * @since JDK 1.8
     */
    public Boolean updateCall(String key,String value) {
        return callStatService.updateCall(key,value);
    }
}