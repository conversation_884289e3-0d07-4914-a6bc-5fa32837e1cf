package com.howbuy.crm.hb.domain.prosale.balancefactor;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/20 18:34
 */
@Data
public class BalanceCustFundVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户号
     */
    private String conscustno;
    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 所属投顾
     */
    private String consCode;

    /**
     * 所属投顾
     */
    private String consName;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 产品名称
     */
    private String fundName;

    /**
     * 所属部门
     */
    private String orgName;

    /**
     * 所属区域
     */
    private String regionName;

    /**
     * 持仓份额
     */
    private String balanceVol;

    /**
     * 参考市值
     */
    private String balanceAmt;

    /**
     * 当前净值
     */
    private String nav;

    /**
     * 净值日期
     */
    private String navDt;
    /**
     * 平衡因子
     */
    private BigDecimal balanceFactor;
    /**
     * 是否参与计算
     */
    private String isCal;
    /**
     * id
     */
    private String id;
    
    /**
     * 是否转换完成1是0否
     */
    private String convertFinish;
}
