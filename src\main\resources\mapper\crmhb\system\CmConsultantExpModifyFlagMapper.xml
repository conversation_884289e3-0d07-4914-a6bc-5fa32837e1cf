<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.system.CmConsultantExpModifyFlagMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.hb.domain.system.CmConsultantExpModifyFlag">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT_EXP_MODIFY_FLAG-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="CONSNAME_FLAG" jdbcType="VARCHAR" property="consnameFlag" />
    <result column="CITYCODE_FLAG" jdbcType="VARCHAR" property="citycodeFlag" />
    <result column="CENTER_ORG_FLAG" jdbcType="VARCHAR" property="centerOrgFlag" />
    <result column="WORKTYPE_FLAG" jdbcType="VARCHAR" property="worktypeFlag" />
    <result column="WORKSTATE_FLAG" jdbcType="VARCHAR" property="workstateFlag" />
    <result column="CURMONTHLEVEL_FLAG" jdbcType="VARCHAR" property="curmonthlevelFlag" />
    <result column="USERLEVEL_FLAG" jdbcType="VARCHAR" property="userlevelFlag" />
    <result column="CURMONTHSALARY_FLAG" jdbcType="VARCHAR" property="curmonthsalaryFlag" />
    <result column="SUBPOSITIONS_FLAG" jdbcType="VARCHAR" property="subpositionsFlag" />
    <result column="STARTDT_FLAG" jdbcType="VARCHAR" property="startdtFlag" />
    <result column="STARTLEVEL_FLAG" jdbcType="VARCHAR" property="startlevelFlag" />
    <result column="SALARY_FLAG" jdbcType="VARCHAR" property="salaryFlag" />
    <result column="REGULARDT_FLAG" jdbcType="VARCHAR" property="regulardtFlag" />
    <result column="REGULARLEVEL_FLAG" jdbcType="VARCHAR" property="regularlevelFlag" />
    <result column="REGULARSALARY_FLAG" jdbcType="VARCHAR" property="regularsalaryFlag" />
    <result column="QUITDT_FLAG" jdbcType="VARCHAR" property="quitdtFlag" />
    <result column="QUITLEVEL_FLAG" jdbcType="VARCHAR" property="quitlevelFlag" />
    <result column="QUITSALARY_FLAG" jdbcType="VARCHAR" property="quitsalaryFlag" />
    <result column="BACKGROUND_FLAG" jdbcType="VARCHAR" property="backgroundFlag" />
    <result column="SOURCE_FLAG" jdbcType="VARCHAR" property="sourceFlag" />
    <result column="BEFOREPOSITIONAGE_FLAG" jdbcType="VARCHAR" property="beforepositionageFlag" />
    <result column="RECRUIT_FLAG" jdbcType="VARCHAR" property="recruitFlag" />
    <result column="RECOMMEND_FLAG" jdbcType="VARCHAR" property="recommendFlag" />
    <result column="RECOMMENDUSERNO_FLAG" jdbcType="VARCHAR" property="recommendusernoFlag" />
    <result column="RECOMMENDTYPE_FLAG" jdbcType="VARCHAR" property="recommendtypeFlag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
    <result column="DT3M_FLAG" jdbcType="VARCHAR" property="dt3mFlag" />
    <result column="DT12M_FLAG" jdbcType="VARCHAR" property="dt12mFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, USERID, CONSNAME_FLAG, CITYCODE_FLAG, CENTER_ORG_FLAG, WORKTYPE_FLAG, WORKSTATE_FLAG, 
    CURMONTHLEVEL_FLAG, USERLEVEL_FLAG, CURMONTHSALARY_FLAG, SUBPOSITIONS_FLAG, STARTDT_FLAG, 
    STARTLEVEL_FLAG, SALARY_FLAG, REGULARDT_FLAG, REGULARLEVEL_FLAG, REGULARSALARY_FLAG, 
    QUITDT_FLAG, QUITLEVEL_FLAG, QUITSALARY_FLAG, BACKGROUND_FLAG, SOURCE_FLAG, BEFOREPOSITIONAGE_FLAG, 
    RECRUIT_FLAG, RECOMMEND_FLAG, RECOMMENDUSERNO_FLAG, RECOMMENDTYPE_FLAG, CREATOR, 
    MODIFIER, CREDT, MODDT, DT3M_FLAG, DT12M_FLAG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_MODIFY_FLAG
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from CM_CONSULTANT_EXP_MODIFY_FLAG
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpModifyFlag">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_EXP_MODIFY_FLAG (ID, USERID, CONSNAME_FLAG, 
      CITYCODE_FLAG, CENTER_ORG_FLAG, WORKTYPE_FLAG, 
      WORKSTATE_FLAG, CURMONTHLEVEL_FLAG, USERLEVEL_FLAG, 
      CURMONTHSALARY_FLAG, SUBPOSITIONS_FLAG, STARTDT_FLAG, 
      STARTLEVEL_FLAG, SALARY_FLAG, REGULARDT_FLAG, 
      REGULARLEVEL_FLAG, REGULARSALARY_FLAG, QUITDT_FLAG, 
      QUITLEVEL_FLAG, QUITSALARY_FLAG, BACKGROUND_FLAG, 
      SOURCE_FLAG, BEFOREPOSITIONAGE_FLAG, RECRUIT_FLAG, 
      RECOMMEND_FLAG, RECOMMENDUSERNO_FLAG, RECOMMENDTYPE_FLAG, 
      CREATOR, MODIFIER, CREDT, 
      MODDT, DT3M_FLAG, DT12M_FLAG
      )
    values (#{id,jdbcType=DECIMAL}, #{userid,jdbcType=VARCHAR}, #{consnameFlag,jdbcType=VARCHAR}, 
      #{citycodeFlag,jdbcType=VARCHAR}, #{centerOrgFlag,jdbcType=VARCHAR}, #{worktypeFlag,jdbcType=VARCHAR}, 
      #{workstateFlag,jdbcType=VARCHAR}, #{curmonthlevelFlag,jdbcType=VARCHAR}, #{userlevelFlag,jdbcType=VARCHAR}, 
      #{curmonthsalaryFlag,jdbcType=VARCHAR}, #{subpositionsFlag,jdbcType=VARCHAR}, #{startdtFlag,jdbcType=VARCHAR}, 
      #{startlevelFlag,jdbcType=VARCHAR}, #{salaryFlag,jdbcType=VARCHAR}, #{regulardtFlag,jdbcType=VARCHAR}, 
      #{regularlevelFlag,jdbcType=VARCHAR}, #{regularsalaryFlag,jdbcType=VARCHAR}, #{quitdtFlag,jdbcType=VARCHAR}, 
      #{quitlevelFlag,jdbcType=VARCHAR}, #{quitsalaryFlag,jdbcType=VARCHAR}, #{backgroundFlag,jdbcType=VARCHAR}, 
      #{sourceFlag,jdbcType=VARCHAR}, #{beforepositionageFlag,jdbcType=VARCHAR}, #{recruitFlag,jdbcType=VARCHAR}, 
      #{recommendFlag,jdbcType=VARCHAR}, #{recommendusernoFlag,jdbcType=VARCHAR}, #{recommendtypeFlag,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{credt,jdbcType=TIMESTAMP}, 
      #{moddt,jdbcType=TIMESTAMP}, #{dt3mFlag,jdbcType=VARCHAR}, #{dt12mFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpModifyFlag">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_EXP_MODIFY_FLAG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="userid != null">
        USERID,
      </if>
      <if test="consnameFlag != null">
        CONSNAME_FLAG,
      </if>
      <if test="citycodeFlag != null">
        CITYCODE_FLAG,
      </if>
      <if test="centerOrgFlag != null">
        CENTER_ORG_FLAG,
      </if>
      <if test="worktypeFlag != null">
        WORKTYPE_FLAG,
      </if>
      <if test="workstateFlag != null">
        WORKSTATE_FLAG,
      </if>
      <if test="curmonthlevelFlag != null">
        CURMONTHLEVEL_FLAG,
      </if>
      <if test="userlevelFlag != null">
        USERLEVEL_FLAG,
      </if>
      <if test="curmonthsalaryFlag != null">
        CURMONTHSALARY_FLAG,
      </if>
      <if test="subpositionsFlag != null">
        SUBPOSITIONS_FLAG,
      </if>
      <if test="startdtFlag != null">
        STARTDT_FLAG,
      </if>
      <if test="startlevelFlag != null">
        STARTLEVEL_FLAG,
      </if>
      <if test="salaryFlag != null">
        SALARY_FLAG,
      </if>
      <if test="regulardtFlag != null">
        REGULARDT_FLAG,
      </if>
      <if test="regularlevelFlag != null">
        REGULARLEVEL_FLAG,
      </if>
      <if test="regularsalaryFlag != null">
        REGULARSALARY_FLAG,
      </if>
      <if test="quitdtFlag != null">
        QUITDT_FLAG,
      </if>
      <if test="quitlevelFlag != null">
        QUITLEVEL_FLAG,
      </if>
      <if test="quitsalaryFlag != null">
        QUITSALARY_FLAG,
      </if>
      <if test="backgroundFlag != null">
        BACKGROUND_FLAG,
      </if>
      <if test="sourceFlag != null">
        SOURCE_FLAG,
      </if>
      <if test="beforepositionageFlag != null">
        BEFOREPOSITIONAGE_FLAG,
      </if>
      <if test="recruitFlag != null">
        RECRUIT_FLAG,
      </if>
      <if test="recommendFlag != null">
        RECOMMEND_FLAG,
      </if>
      <if test="recommendusernoFlag != null">
        RECOMMENDUSERNO_FLAG,
      </if>
      <if test="recommendtypeFlag != null">
        RECOMMENDTYPE_FLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="dt3mFlag != null">
        DT3M_FLAG,
      </if>
      <if test="dt12mFlag != null">
        DT12M_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="consnameFlag != null">
        #{consnameFlag,jdbcType=VARCHAR},
      </if>
      <if test="citycodeFlag != null">
        #{citycodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="centerOrgFlag != null">
        #{centerOrgFlag,jdbcType=VARCHAR},
      </if>
      <if test="worktypeFlag != null">
        #{worktypeFlag,jdbcType=VARCHAR},
      </if>
      <if test="workstateFlag != null">
        #{workstateFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthlevelFlag != null">
        #{curmonthlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="userlevelFlag != null">
        #{userlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthsalaryFlag != null">
        #{curmonthsalaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="subpositionsFlag != null">
        #{subpositionsFlag,jdbcType=VARCHAR},
      </if>
      <if test="startdtFlag != null">
        #{startdtFlag,jdbcType=VARCHAR},
      </if>
      <if test="startlevelFlag != null">
        #{startlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="salaryFlag != null">
        #{salaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="regulardtFlag != null">
        #{regulardtFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularlevelFlag != null">
        #{regularlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularsalaryFlag != null">
        #{regularsalaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitdtFlag != null">
        #{quitdtFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitlevelFlag != null">
        #{quitlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitsalaryFlag != null">
        #{quitsalaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="backgroundFlag != null">
        #{backgroundFlag,jdbcType=VARCHAR},
      </if>
      <if test="sourceFlag != null">
        #{sourceFlag,jdbcType=VARCHAR},
      </if>
      <if test="beforepositionageFlag != null">
        #{beforepositionageFlag,jdbcType=VARCHAR},
      </if>
      <if test="recruitFlag != null">
        #{recruitFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendFlag != null">
        #{recommendFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendusernoFlag != null">
        #{recommendusernoFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendtypeFlag != null">
        #{recommendtypeFlag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="dt3mFlag != null">
        #{dt3mFlag,jdbcType=VARCHAR},
      </if>
      <if test="dt12mFlag != null">
        #{dt12mFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpModifyFlag">
    <!--@mbg.generated-->
    update CM_CONSULTANT_EXP_MODIFY_FLAG
    <set>
      <if test="userid != null">
        USERID = #{userid,jdbcType=VARCHAR},
      </if>
      <if test="consnameFlag != null">
        CONSNAME_FLAG = #{consnameFlag,jdbcType=VARCHAR},
      </if>
      <if test="citycodeFlag != null">
        CITYCODE_FLAG = #{citycodeFlag,jdbcType=VARCHAR},
      </if>
      <if test="centerOrgFlag != null">
        CENTER_ORG_FLAG = #{centerOrgFlag,jdbcType=VARCHAR},
      </if>
      <if test="worktypeFlag != null">
        WORKTYPE_FLAG = #{worktypeFlag,jdbcType=VARCHAR},
      </if>
      <if test="workstateFlag != null">
        WORKSTATE_FLAG = #{workstateFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthlevelFlag != null">
        CURMONTHLEVEL_FLAG = #{curmonthlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="userlevelFlag != null">
        USERLEVEL_FLAG = #{userlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="curmonthsalaryFlag != null">
        CURMONTHSALARY_FLAG = #{curmonthsalaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="subpositionsFlag != null">
        SUBPOSITIONS_FLAG = #{subpositionsFlag,jdbcType=VARCHAR},
      </if>
      <if test="startdtFlag != null">
        STARTDT_FLAG = #{startdtFlag,jdbcType=VARCHAR},
      </if>
      <if test="startlevelFlag != null">
        STARTLEVEL_FLAG = #{startlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="salaryFlag != null">
        SALARY_FLAG = #{salaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="regulardtFlag != null">
        REGULARDT_FLAG = #{regulardtFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularlevelFlag != null">
        REGULARLEVEL_FLAG = #{regularlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="regularsalaryFlag != null">
        REGULARSALARY_FLAG = #{regularsalaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitdtFlag != null">
        QUITDT_FLAG = #{quitdtFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitlevelFlag != null">
        QUITLEVEL_FLAG = #{quitlevelFlag,jdbcType=VARCHAR},
      </if>
      <if test="quitsalaryFlag != null">
        QUITSALARY_FLAG = #{quitsalaryFlag,jdbcType=VARCHAR},
      </if>
      <if test="backgroundFlag != null">
        BACKGROUND_FLAG = #{backgroundFlag,jdbcType=VARCHAR},
      </if>
      <if test="sourceFlag != null">
        SOURCE_FLAG = #{sourceFlag,jdbcType=VARCHAR},
      </if>
      <if test="beforepositionageFlag != null">
        BEFOREPOSITIONAGE_FLAG = #{beforepositionageFlag,jdbcType=VARCHAR},
      </if>
      <if test="recruitFlag != null">
        RECRUIT_FLAG = #{recruitFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendFlag != null">
        RECOMMEND_FLAG = #{recommendFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendusernoFlag != null">
        RECOMMENDUSERNO_FLAG = #{recommendusernoFlag,jdbcType=VARCHAR},
      </if>
      <if test="recommendtypeFlag != null">
        RECOMMENDTYPE_FLAG = #{recommendtypeFlag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="dt3mFlag != null">
        DT3M_FLAG = #{dt3mFlag,jdbcType=VARCHAR},
      </if>
      <if test="dt12mFlag != null">
        DT12M_FLAG = #{dt12mFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.hb.domain.system.CmConsultantExpModifyFlag">
    <!--@mbg.generated-->
    update CM_CONSULTANT_EXP_MODIFY_FLAG
    set USERID = #{userid,jdbcType=VARCHAR},
      CONSNAME_FLAG = #{consnameFlag,jdbcType=VARCHAR},
      CITYCODE_FLAG = #{citycodeFlag,jdbcType=VARCHAR},
      CENTER_ORG_FLAG = #{centerOrgFlag,jdbcType=VARCHAR},
      WORKTYPE_FLAG = #{worktypeFlag,jdbcType=VARCHAR},
      WORKSTATE_FLAG = #{workstateFlag,jdbcType=VARCHAR},
      CURMONTHLEVEL_FLAG = #{curmonthlevelFlag,jdbcType=VARCHAR},
      USERLEVEL_FLAG = #{userlevelFlag,jdbcType=VARCHAR},
      CURMONTHSALARY_FLAG = #{curmonthsalaryFlag,jdbcType=VARCHAR},
      SUBPOSITIONS_FLAG = #{subpositionsFlag,jdbcType=VARCHAR},
      STARTDT_FLAG = #{startdtFlag,jdbcType=VARCHAR},
      STARTLEVEL_FLAG = #{startlevelFlag,jdbcType=VARCHAR},
      SALARY_FLAG = #{salaryFlag,jdbcType=VARCHAR},
      REGULARDT_FLAG = #{regulardtFlag,jdbcType=VARCHAR},
      REGULARLEVEL_FLAG = #{regularlevelFlag,jdbcType=VARCHAR},
      REGULARSALARY_FLAG = #{regularsalaryFlag,jdbcType=VARCHAR},
      QUITDT_FLAG = #{quitdtFlag,jdbcType=VARCHAR},
      QUITLEVEL_FLAG = #{quitlevelFlag,jdbcType=VARCHAR},
      QUITSALARY_FLAG = #{quitsalaryFlag,jdbcType=VARCHAR},
      BACKGROUND_FLAG = #{backgroundFlag,jdbcType=VARCHAR},
      SOURCE_FLAG = #{sourceFlag,jdbcType=VARCHAR},
      BEFOREPOSITIONAGE_FLAG = #{beforepositionageFlag,jdbcType=VARCHAR},
      RECRUIT_FLAG = #{recruitFlag,jdbcType=VARCHAR},
      RECOMMEND_FLAG = #{recommendFlag,jdbcType=VARCHAR},
      RECOMMENDUSERNO_FLAG = #{recommendusernoFlag,jdbcType=VARCHAR},
      RECOMMENDTYPE_FLAG = #{recommendtypeFlag,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=TIMESTAMP},
      MODDT = #{moddt,jdbcType=TIMESTAMP},
      DT3M_FLAG = #{dt3mFlag,jdbcType=VARCHAR},
      DT12M_FLAG = #{dt12mFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectByUserid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_MODIFY_FLAG
    where userid = #{userid,jdbcType=VARCHAR}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_MODIFY_FLAG
  </select>

  <insert id="mergeExpModifyFlagByList" parameterType="list">
    merge into CM_CONSULTANT_EXP_MODIFY_FLAG t1
    using (
    <foreach collection="list" item="item" index="index" open="(" close=")" separator="union all">
      select
      #{item.userid,jdbcType=VARCHAR} as userid,
      #{item.consnameFlag,jdbcType=VARCHAR} as consname_Flag,
      #{item.citycodeFlag,jdbcType=VARCHAR} as citycode_Flag,
      #{item.centerOrgFlag,jdbcType=VARCHAR} as center_Org_Flag,
      #{item.worktypeFlag,jdbcType=VARCHAR} as worktype_Flag,
      #{item.workstateFlag,jdbcType=VARCHAR} as workstate_Flag,
      #{item.subpositionsFlag,jdbcType=VARCHAR} as subpositions_Flag,
      #{item.curmonthsalaryFlag,jdbcType=VARCHAR} as curmonthsalary_Flag,
      #{item.regulardtFlag,jdbcType=VARCHAR} as regulardt_Flag,
      #{item.regularlevelFlag,jdbcType=VARCHAR} as regularlevel_Flag,
      #{item.startdtFlag,jdbcType=VARCHAR} as startdt_Flag,
      #{item.startlevelFlag,jdbcType=VARCHAR} as startlevel_Flag,
      #{item.salaryFlag,jdbcType=VARCHAR} as salary_Flag,
      #{item.dt3mFlag,jdbcType=VARCHAR} as dt3m_Flag,
      #{item.dt12mFlag,jdbcType=VARCHAR} as dt12m_Flag,
      #{item.quitdtFlag,jdbcType=VARCHAR} as quitdt_Flag,
      #{item.quitlevelFlag,jdbcType=VARCHAR} as quitlevel_Flag,
      #{item.backgroundFlag,jdbcType=VARCHAR} as background_Flag,
      #{item.sourceFlag,jdbcType=VARCHAR} as source_Flag,
      #{item.beforepositionageFlag,jdbcType=VARCHAR} as beforepositionage_Flag,
      #{item.recruitFlag,jdbcType=VARCHAR} as recruit_Flag,
      #{item.recommendFlag,jdbcType=VARCHAR} as recommend_Flag,
      #{item.recommendusernoFlag,jdbcType=VARCHAR} as recommenduserno_Flag,
      #{item.recommendtypeFlag,jdbcType=VARCHAR} as recommendtype_Flag,
      #{item.curmonthlevelFlag,jdbcType=VARCHAR} as curmonthlevel_Flag,
      #{item.regularsalaryFlag,jdbcType=VARCHAR} as regularsalary_flag
      from dual
    </foreach>
    )t2
    on (t1.userid=t2.userid)
    when matched then
    update set
      t1.consname_Flag = t2.consname_Flag
      ,t1.citycode_Flag = t2.citycode_Flag
      ,t1.center_Org_Flag = t2.center_Org_Flag
    ,t1.worktype_Flag = t2.worktype_Flag
    ,t1.workstate_Flag = t2.workstate_Flag
    ,t1.subpositions_Flag = t2.subpositions_Flag
    ,t1.curmonthsalary_Flag = t2.curmonthsalary_Flag
    ,t1.regulardt_Flag = t2.regulardt_Flag
    ,t1.regularlevel_Flag = t2.regularlevel_Flag
      ,t1.startdt_Flag = t2.startdt_Flag
      ,t1.startlevel_Flag = t2.startlevel_Flag
      ,t1.salary_Flag = t2.salary_Flag
      ,t1.dt3m_Flag = t2.dt3m_Flag
      ,t1.dt12m_Flag = t2.dt12m_Flag
      ,t1.quitdt_Flag = t2.quitdt_Flag
      ,t1.quitlevel_Flag = t2.quitlevel_Flag
      ,t1.background_Flag = t2.background_Flag
      ,t1.source_Flag = t2.source_Flag
      ,t1.beforepositionage_Flag = t2.beforepositionage_Flag
      ,t1.recruit_Flag = t2.recruit_Flag
      ,t1.recommend_Flag = t2.recommend_Flag
      ,t1.recommenduserno_Flag = t2.recommenduserno_Flag
      ,t1.recommendtype_Flag = t2.recommendtype_Flag
      ,t1.curmonthlevel_Flag = t2.curmonthlevel_Flag
    ,t1.regularsalary_flag = t2.regularsalary_flag
      ,t1.modifier = 'auto'
      ,t1.moddt = sysdate
    when not matched then
      insert (t1.userid,t1.consname_Flag,t1.citycode_Flag,t1.center_Org_Flag,t1.startdt_Flag,t1.startlevel_Flag,t1.salary_Flag,
            t1.dt3m_Flag,t1.regularlevel_Flag,t1.dt12m_Flag,t1.quitdt_Flag,t1.quitlevel_Flag,t1.worktype_Flag,t1.workstate_Flag
    ,t1.subpositions_Flag,t1.curmonthsalary_Flag,t1.regulardt_Flag ,
            t1.background_Flag,t1.source_Flag,t1.beforepositionage_Flag,t1.recruit_Flag,t1.recommend_Flag,t1.recommenduserno_Flag,
            t1.recommendtype_Flag,t1.creator,t1.modifier,t1.credt,t1.moddt,t1.curmonthlevel_Flag,t1.regularsalary_flag)
      values(t2.userid,t2.consname_Flag,t2.citycode_Flag,t2.center_Org_Flag,t2.startdt_Flag,t2.startlevel_Flag,t2.salary_Flag,
            t2.dt3m_Flag,t2.regularlevel_Flag,t2.dt12m_Flag,t2.quitdt_Flag,t2.quitlevel_Flag,t2.worktype_Flag,t2.workstate_Flag
    ,t2.subpositions_Flag,t2.curmonthsalary_Flag,t2.regulardt_Flag ,
            t2.background_Flag,t2.source_Flag,t2.beforepositionage_Flag,t2.recruit_Flag,t2.recommend_Flag,t2.recommenduserno_Flag,
            t2.recommendtype_Flag,'auto','auto',sysdate,sysdate,t2.curmonthlevel_Flag,t2.regularsalary_flag)
  </insert>

  <select id="selectByUseridList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP_MODIFY_FLAG
    where userid in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
        #{item}
    </foreach>
  </select>

</mapper>