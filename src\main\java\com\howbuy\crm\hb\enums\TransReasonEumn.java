/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

/**
 * @description: (划转原因枚举类)
 * <AUTHOR>
 * @since JDK 1.8
 */
public enum TransReasonEumn {
    DAILY_TRANS("日常划转", "2"),
    OTHER("其他", "99"),
    DEPART_TRANS("离职划转", "3"),
    ONLOAD_NEW_IN("在途新人入职", "4"),
    CLOENT_400_CALL("客户400呼入", "5"),
    LEADER_TRANS("Leads分配", "6"),
    COUNTERPART_TRANS("同行分配", "11"),
    TRANS20W_BYHANDS("20w手动划转", "7"),
    DEEP("深潜", "8"),
    OTHER_CUSTOMER("其他（客服）","9"),
    SHRRE_LINK("分享链接", "10");

    private String reason;
    private String code;

    TransReasonEumn(String reason, String code) {
        this.reason = reason;
        this.code = code;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * @description:(根据枚举类型返回code值)
     * @param reason 类型名称
     * @return java.lang.String
     * @author: your name
     * @date: 2023/3/9 14:54
     * @since JDK 1.8
     */
    public static String getEnum(String reason) {
        for(TransReasonEumn transReasonEumn : TransReasonEumn.values()){
            if(transReasonEumn.getReason().equals(reason)){
                return transReasonEumn.getCode();
            }
        }
        return null;
    }
}