package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxProductChannelLog.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxProductChannelLog implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private String fundcode;
	private String channcode;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
}
