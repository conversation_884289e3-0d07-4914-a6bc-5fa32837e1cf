package com.howbuy.crm.hb.domain.prosale;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmCustTransfervol implements Serializable {

	/** 
	 * (变量说明描述)	
	 *
	 * long Prebookmanycallinfo.java serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	private Long id;

	private String idStr;
	
	/**
	 * 转让人客户号
	 */
	private String transferor;

	/**
	 * 转让人客户类型
	 */
	private String invsttype;

	/**
	 * 转让人姓名
	 */
	private String transferorname;
	
	/**
	 * 转让人所属投顾
	 */
	private String transferorcons;
	
	/**
	 * 转让人所属部门
	 */
	private String transferororg;
	
	/**
	 * 受让人客户号
	 */
	private String assignee;
	
	/**
	 * 受让人姓名
	 */
	private String assigneename;
	
	/**
	 * 受让人所属投顾
	 */
	private String assigneecons;
	
	/**
	 * 受让人所属部门
	 */
	private String assigneeorg;
	
	/**
	 * 产品代码
	 */
	private String fundcode;
	
	/**
	 * 产品名称
	 */
	private String fundname;
	
	/**
	 * 转让份额
	 */
	private BigDecimal transfervol;
	
	/**
	 * 转让金额 （历史原因，定义和赋值错乱，已废弃）
	 */
	@Deprecated
	private BigDecimal transferamt;
	
	/**
	 * 预计交易日期
	 */
	private String tradedt;
	
	/**
	 * 确认状态
	 */
	private String checkstat;
	
	/**
	 * 确认状态
	 */
	private String checkstatval;
	
	/**
	 * 确认人
	 */
	private String checkor;
	
	/**
	 * 确认日期
	 */
	private String checkdt;
	
	/**
	 * 创建人
	 */
	private String creator;
	
	/**
	 * 创建人中文名
	 */
	private String creatorval;

	/**
	 * 创建人所属部门
	 */
	private String uporgname;

	/**
	 * 创建人所属部门
	 */
	private String creatororg;
	
	private Date creddt;
	
	private String creddtVal;
	
	private Date upddt;
	
	/**
	 * 转让强减交易id
	 */
	private String tradetransfid;
	
	/**
	 * 受让强制交易id
	 */
	private String tradeassignid;
	
	/**
	 * 分次CALL标识
	 */
	private String fcclflag;
	
	/**
	 * 分次CALL首次实缴预约id
	 */
	private BigDecimal fcclpreid;
	
	/**
	 * 分次CALL类型，1：补预约；2：实缴新增
	 */
	private String fccltype;
	
	/**
	 * 转让人原认缴金额
	 */
	private BigDecimal fccloldtotalamt;
	
	/**
	 * 可转让剩余缴款金额
	 */
	private BigDecimal fccloldamt;
	
	/**
	 * 转让人投顾
	 */
	private String transferorconscode;
	/**
	 * 转让人所属投顾编码
	 */
	private String transferConsCode;
	/**
	 * 转让人所属中心
	 */
	private String transferCenter;
	
	/**
	 * 转让人所属区域
	 */
	private String transferorarea;
	
	/**
	 * 受让人投顾
	 */
	private String assigneeconscode;

	/**
	 * 受让人所属投顾编码
	 */
	private String assigneConsCode;
	/**
	 * 受让人所属中心
	 */
	private String assigneeCenter;
	
	/**
	 * 受让人所属区域
	 */
	private String assigneearea;
	/**
	 * 推介服务费
	 */
	private BigDecimal recommfee;
	
	/**
	 * 转让认缴金额
	 */
	private BigDecimal transfertotalamt;
	
	/**
	 * 受让份额对应实缴金额
	 */
	private BigDecimal fccltransfervolamt;
	
	/**
	 * 确认时录入的转让金额
	 */
	private BigDecimal checktransferamt;

	/**
	 * 产品类型为股权的时候 需要添加转让价格
	 *  （历史原因，定义和赋值错乱，已废弃）
	 */
	@Deprecated
	private BigDecimal transferprice;


	/**
	 * 转让金额
	 */
	private BigDecimal transferAmount;

	/**
	 * 受让剩余缴款金额
	 */
	private BigDecimal fcclTransferRemainPayAmt;

	/**
	 * 已分配金额 ALLOCATED_AMT
	 */
	private BigDecimal allocatedAmt;

}
