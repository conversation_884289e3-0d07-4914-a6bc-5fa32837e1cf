package com.howbuy.crm.hb.service.system.cmconsultantexpserviceimpl;

import com.howbuy.crm.hb.constants.RewardConstants;
import com.howbuy.crm.hb.service.system.impl.CmConsultantExpServiceImpl;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.math.BigDecimal;

import static org.powermock.api.mockito.PowerMockito.when;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2024/11/27 17:41
 * @since JDK 1.8
 */
@PowerMockIgnore("javax.management.*")
@PrepareForTest({CmConsultantExpServiceImpl.class})
public class TestNotEqualCheckBigDecimal extends PowerMockTestCase {

    @InjectMocks
    private CmConsultantExpServiceImpl spy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        spy = PowerMockito.spy(new CmConsultantExpServiceImpl());
    }

    @Test
    public void testNotEqualCheck_NewValueEmpty_OldValueNotEmpty_ShouldReturnTrue() {
        // Arrange
        BigDecimal oldValue = BigDecimal.valueOf(5200);
        BigDecimal newValue = null;

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testNotEqualCheck_NewValueNotEmpty_OldValueEmpty_ShouldReturnFalse() {
        // Arrange
        BigDecimal oldValue = null;
        BigDecimal newValue = BigDecimal.valueOf(5200);

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testNotEqualCheck_BothValuesEqual_ShouldReturnFalse() {
        // Arrange
        BigDecimal oldValue = BigDecimal.valueOf(5200.00);
        BigDecimal newValue = BigDecimal.valueOf(5200);

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertFalse(result);
    }

    @Test
    public void testNotEqualCheck_BothValuesEmpty_ShouldReturnFalse() {
        // Arrange
        BigDecimal oldValue = null;
        BigDecimal newValue = null;

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertFalse(result);
    }

    @Test
    public void testNotEqualCheck_NewValueNotEmpty_OldValueDifferent_ShouldReturnTrue() {
        // Arrange
        BigDecimal oldValue = BigDecimal.valueOf(5200);
        BigDecimal newValue = BigDecimal.valueOf(5201);

        // Act
        boolean result = ReflectionTestUtils.invokeMethod(spy, "notEqualCheck", oldValue, newValue);

        // Assert
        assertTrue(result);
    }
}