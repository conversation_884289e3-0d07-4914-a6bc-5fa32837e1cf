package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.howbuy.crm.hb.domain.custinfo.FdbXtsyl;
import com.howbuy.crm.hb.domain.custinfo.ProductShareBonus;

import crm.howbuy.base.db.CommPageBean;

/**
 * 到期提醒持久层接口
 * 
 * @author: wu.long
 * date: 2019年7月8日 下午1:36:39
 * version: V1.0
 * since: jdk 1.8,tomcat 8.0
 */
public interface RemindOfProductExpirationMapper {
	/**
	 * 查询部门团队列表数据对象
	 * 
	 * author: wu.long
	 * date: 2019年7月10日 下午1:46:50 
	 * @param param
	 * @return
	 */
	List<ProductShareBonus> listTimeEndBonusDept(Map<String, String> param);
	
	/**
	 * 查询列表（分页数据）
	 * 
	 * author: wu.long
	 * date: 2019年7月10日 下午1:47:01 
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<ProductShareBonus> listProductHoldingByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 根据条件查询历史到期查询信息
	 * 
	 * author: wu.long
	 * date: 2019年7月15日 下午6:18:37 
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<FdbXtsyl> selectHistoryExpirationQueryByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	
	
	
	
	
	
}
