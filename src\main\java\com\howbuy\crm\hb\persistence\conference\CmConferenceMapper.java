package com.howbuy.crm.hb.persistence.conference;

import com.howbuy.crm.hb.domain.conference.CmConference;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * 路演会议记录
 * <AUTHOR>
 *
 */
public interface CmConferenceMapper {
	
	/**
	 * 新增
	 * @param cmconference
	 */
	public void insertCmConference(CmConference cmconference);
	
	/**
	 * 删除
	 * @param conferenceid
	 */
	public void deleteCmConference(String conferenceid);
	
	/**
	 * 更新
	 * @param cmconference
	 */
	public void updateCmConference(CmConference cmconference);
	
	/**
	 * 查询
	 * @param conferenceid
	 * @return
	 */
	public CmConference queryCmConferenceInfo(String conferenceid);

	String getCourseId(@Param("conferenceId") String conferenceId);



	/**
	 * 列表查询
	 * @param param
	 * @return
	 */
	public abstract List<CmConference> listCmConference(Map<String, Object> param);
	
	/**
	 * 获取单个
	 * @param param
	 * @return
	 */
	public List<Map<String,Object>> getCmConferenceList(Map<String, String> param);

	/**
	 * 当前总人数和部门可参加人数
	 * @param param
	 * @return
	 */
	public Map<String,Object>  getCmConferenceOrgNum(Map<String, String> param);

	/**
	 * 获当前部门已参加人数取
	 * @param param
	 * @return
	 */
	public Map<String,Object>  getCmConferenceJoinOrgNum(Map<String, String> param);

	/**
	 * 获取已经参加的总人数
	 * @param param
	 * @return
	 */
	public Map<String,Object>  getCmConferenceJoinAllNum(Map<String, String> param);

	/**
	 * 获取已经参加的总人数
	 * @param param
	 * @return
	 */
	Map<String,Object>  getCmConferenceJoinOtherNum(Map<String, String> param);

	/**
	 * 根据ID查询
	 * @param conferenceId
	 * @return
	 */
	CmConference getCmConferenceById(String conferenceId);

}
