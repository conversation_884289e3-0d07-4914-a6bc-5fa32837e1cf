package com.howbuy.crm.hb.domain.fixed;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmFixedIntention implements Serializable{
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private String conscustno;
	private String fundcode;
	private String planrate;
	private String planrateval;
	private String plantotalnum;
	private String planhasnum;
	private BigDecimal planamount;
	private String paytype;
	private String paytypeval;
	private String planstate;
	private String planstateval;
	private String fixedstate;
	private String fixedstateval;
	private String cpacctno;
	private String conscode;
	private String orgcode;
	private String creator;
	private Date creattime;
	private String creattimeval;
	private String modifier;
	private Date modtime;
	private String hboneno;
	private String custname;
	private String uporgname;
	private String outletName;
	private String consname;
	private String fundname;
	private String discountstate;
	private String discountstateval;
	private String doublestate;
	private String doublestateval;
	/**
	 * 折扣方式
	 */
	private String discountWay;
	private String discountWayVal;
	/**
	 * 折扣类型
	 */
	private String discountType;
	private String discountTypeVal;
	/**
	 * 折扣率
	 */
	private BigDecimal discountRate;
	/**
	 * 折扣理由
	 */
	private String discountReason;
	/**
	 * 税后折扣金额
	 */
	private BigDecimal afterTaxAmt;
	/**
	 * 税后折扣金额(人民币)
	 */
	private BigDecimal afterTaxAmtRmb;
	/**
	 * 税前折扣金额
	 */
	private BigDecimal beforetaxamt;
	/**
	 * 税前折扣金额（调整）
	 */
	private BigDecimal befTaxAmtAdjust;
	
	/**
	 * 折扣审核意见
	 */
	private String disremarks;
	/**
	 * 折扣审核人
	 */
	private String dischecker;
	/**
	 * 折扣形式
	 */
	private String discountstyle;
	/**
	 * 员工关系
	 */
	private String staffRelation ;
	
	private String isrefund;

}
