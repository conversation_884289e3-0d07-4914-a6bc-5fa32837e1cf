package com.howbuy.crm.hb.persistence.callout;

import java.util.Map;
import com.howbuy.crm.hb.domain.callout.CsCalloutRec;
import org.apache.ibatis.annotations.Param;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CsCalloutRecMapper {
	/**
	 * 新增呼出流水记录
	 * @param csCalloutRec
	 */
	void insertCsCalloutRec(CsCalloutRec csCalloutRec);
	
	/**
	 * 根据contractNo获取返回id集合（逗号分隔）的流水记录
	 * @param param
	 * @return
	 */
	String getCsCalloutRec(Map<String, String> param);

	/**
	 * 根据tid查询最近一次记录id:
	 * @param tid
	 * @param startTime
	 * @return
	 */
	String getLastRecByTidAndTime(@Param("tid") String tid,@Param("startTime") String startTime);

	/**
	 * 根据属性查询最近一次记录id
	 * @param param
	 * @return
	 */
	String getLastRecByAttr(Map<String, String> param);

	/**
	 * 更新拨号记录:
	 * @param csCalloutRec
	 */
	void updateRecAppSerialNo(CsCalloutRec csCalloutRec);
}
