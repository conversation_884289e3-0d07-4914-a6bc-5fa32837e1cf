package com.howbuy.crm.hb.domain.checkmail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 定制账单配置主表
 * <AUTHOR>
 */
@Data
public class CmCheckMailConMain implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;
    /** 投顾客户号 */
    private String conscustno;
    /** 一账通账号 */
    private String hboneno;
    /** 客户姓名 */
    private String custname;
    /** 收件邮箱,逗号隔开 */
    private String receivemail;
    /** 状态 0无效1有效 */
    private String status;
    /** 创建人 */
    private String creator;
    /** 创建日期 */
    private Date credt;
    /** 创建日期 */
    private String credtstr;
    /** 修改人 */
    private String modifier;
    /** 修改日期 */
    private Date moddt;
    /** 修改日期 */
    private String moddtstr;
}
