package com.howbuy.crm.hb.domain.custinfo;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 实体类CmCustconstant.java
 * @created
 */
@Data
public class CmCustconstant implements Serializable {

    private static final long serialVersionUID = 1L;

    private String custno;

    private String conscode;

    private String startdt;

    private String enddt;
    
    private String beforehisid;

    private String memo;

    private String recstat;

    private String checkflag;

    private String creator;

    private String modifier;

    private String checker;

    private String credt;

    private String moddt;

    private Date binddate;


}
