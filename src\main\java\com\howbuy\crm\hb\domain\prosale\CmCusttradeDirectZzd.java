package com.howbuy.crm.hb.domain.prosale;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 直转代产品的非黑名单客户交易记录
 * @TableName CM_CUSTTRADE_DIRECT_ZZD
 */
@Data
public class CmCusttradeDirectZzd implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    private String appserialno;

    /**
     * 
     */
    private String tradedt;

    /**
     * 
     */
    private String hboneno;

    /**
     * 
     */
    private String fundcode;

    /**
     * 
     */
    private String fundname;

    /**
     * 
     */
    private String currency;

    /**
     * 
     */
    private String busicode;

    /**
     * 
     */
    private BigDecimal appamt;

    /**
     * 
     */
    private BigDecimal appvol;

    /**
     * 
     */
    private BigDecimal ackamt;

    /**
     * 
     */
    private BigDecimal ackvol;

    /**
     * 
     */
    private BigDecimal discrateofcomm;

    /**
     * 
     */
    private BigDecimal nav;

    /**
     * 
     */
    private String divmode;

    /**
     * 
     */
    private BigDecimal fee;

    /**
     * 
     */
    private String applyAppserialno;

    /**
     * 
     */
    private String discode;

    /**
     * 
     */
    private String credt;

    /**
     * 
     */
    private String moddt;

    /**
     * 
     */
    private String bankacct;

    /**
     * 汇率日期
     */
    private String ratedt;

    /**
     * 打款日期
     */
    private String paydt;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CmCusttradeDirectZzd other = (CmCusttradeDirectZzd) that;
        return (this.getAppserialno() == null ? other.getAppserialno() == null : this.getAppserialno().equals(other.getAppserialno()))
            && (this.getTradedt() == null ? other.getTradedt() == null : this.getTradedt().equals(other.getTradedt()))
            && (this.getHboneno() == null ? other.getHboneno() == null : this.getHboneno().equals(other.getHboneno()))
            && (this.getFundcode() == null ? other.getFundcode() == null : this.getFundcode().equals(other.getFundcode()))
            && (this.getFundname() == null ? other.getFundname() == null : this.getFundname().equals(other.getFundname()))
            && (this.getCurrency() == null ? other.getCurrency() == null : this.getCurrency().equals(other.getCurrency()))
            && (this.getBusicode() == null ? other.getBusicode() == null : this.getBusicode().equals(other.getBusicode()))
            && (this.getAppamt() == null ? other.getAppamt() == null : this.getAppamt().equals(other.getAppamt()))
            && (this.getAppvol() == null ? other.getAppvol() == null : this.getAppvol().equals(other.getAppvol()))
            && (this.getAckamt() == null ? other.getAckamt() == null : this.getAckamt().equals(other.getAckamt()))
            && (this.getAckvol() == null ? other.getAckvol() == null : this.getAckvol().equals(other.getAckvol()))
            && (this.getDiscrateofcomm() == null ? other.getDiscrateofcomm() == null : this.getDiscrateofcomm().equals(other.getDiscrateofcomm()))
            && (this.getNav() == null ? other.getNav() == null : this.getNav().equals(other.getNav()))
            && (this.getDivmode() == null ? other.getDivmode() == null : this.getDivmode().equals(other.getDivmode()))
            && (this.getFee() == null ? other.getFee() == null : this.getFee().equals(other.getFee()))
            && (this.getApplyAppserialno() == null ? other.getApplyAppserialno() == null : this.getApplyAppserialno().equals(other.getApplyAppserialno()))
            && (this.getDiscode() == null ? other.getDiscode() == null : this.getDiscode().equals(other.getDiscode()))
            && (this.getCredt() == null ? other.getCredt() == null : this.getCredt().equals(other.getCredt()))
            && (this.getModdt() == null ? other.getModdt() == null : this.getModdt().equals(other.getModdt()))
            && (this.getBankacct() == null ? other.getBankacct() == null : this.getBankacct().equals(other.getBankacct()))
            && (this.getRatedt() == null ? other.getRatedt() == null : this.getRatedt().equals(other.getRatedt()))
            && (this.getPaydt() == null ? other.getPaydt() == null : this.getPaydt().equals(other.getPaydt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAppserialno() == null) ? 0 : getAppserialno().hashCode());
        result = prime * result + ((getTradedt() == null) ? 0 : getTradedt().hashCode());
        result = prime * result + ((getHboneno() == null) ? 0 : getHboneno().hashCode());
        result = prime * result + ((getFundcode() == null) ? 0 : getFundcode().hashCode());
        result = prime * result + ((getFundname() == null) ? 0 : getFundname().hashCode());
        result = prime * result + ((getCurrency() == null) ? 0 : getCurrency().hashCode());
        result = prime * result + ((getBusicode() == null) ? 0 : getBusicode().hashCode());
        result = prime * result + ((getAppamt() == null) ? 0 : getAppamt().hashCode());
        result = prime * result + ((getAppvol() == null) ? 0 : getAppvol().hashCode());
        result = prime * result + ((getAckamt() == null) ? 0 : getAckamt().hashCode());
        result = prime * result + ((getAckvol() == null) ? 0 : getAckvol().hashCode());
        result = prime * result + ((getDiscrateofcomm() == null) ? 0 : getDiscrateofcomm().hashCode());
        result = prime * result + ((getNav() == null) ? 0 : getNav().hashCode());
        result = prime * result + ((getDivmode() == null) ? 0 : getDivmode().hashCode());
        result = prime * result + ((getFee() == null) ? 0 : getFee().hashCode());
        result = prime * result + ((getApplyAppserialno() == null) ? 0 : getApplyAppserialno().hashCode());
        result = prime * result + ((getDiscode() == null) ? 0 : getDiscode().hashCode());
        result = prime * result + ((getCredt() == null) ? 0 : getCredt().hashCode());
        result = prime * result + ((getModdt() == null) ? 0 : getModdt().hashCode());
        result = prime * result + ((getBankacct() == null) ? 0 : getBankacct().hashCode());
        result = prime * result + ((getRatedt() == null) ? 0 : getRatedt().hashCode());
        result = prime * result + ((getPaydt() == null) ? 0 : getPaydt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", appserialno=").append(appserialno);
        sb.append(", tradedt=").append(tradedt);
        sb.append(", hboneno=").append(hboneno);
        sb.append(", fundcode=").append(fundcode);
        sb.append(", fundname=").append(fundname);
        sb.append(", currency=").append(currency);
        sb.append(", busicode=").append(busicode);
        sb.append(", appamt=").append(appamt);
        sb.append(", appvol=").append(appvol);
        sb.append(", ackamt=").append(ackamt);
        sb.append(", ackvol=").append(ackvol);
        sb.append(", discrateofcomm=").append(discrateofcomm);
        sb.append(", nav=").append(nav);
        sb.append(", divmode=").append(divmode);
        sb.append(", fee=").append(fee);
        sb.append(", applyAppserialno=").append(applyAppserialno);
        sb.append(", discode=").append(discode);
        sb.append(", credt=").append(credt);
        sb.append(", moddt=").append(moddt);
        sb.append(", bankacct=").append(bankacct);
        sb.append(", ratedt=").append(ratedt);
        sb.append(", paydt=").append(paydt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}