package com.howbuy.crm.hb.domain.uploadmodule;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

import com.howbuy.crm.hb.domain.insur.PageVo;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CmUploadModule extends PageVo implements Serializable  {
	private static final long serialVersionUID = 1L;
	private String id;
	/**
	 * 主模块
	 */
	private String maintype;
	private String maintypeval;
	/**
	 * 子模块
	 */
	private String subtype  ;
	private String subtypeval  ;
	
	/**
	 * 功能按钮说明
	 */
	private String des;
	
	/**
	 * 查询条件可上传类型
	 */
	private String uptype;
	private String uptypes;
	
	/**
	 * 查询条件可上传格式
	 */
	private String suffix;
	
	private String creator;
	
	/**
	 * 可上传类型的明细
	 */
	private List<CmUploadModuleType> listCmUploadModuleType;
	  
    
    private String  sort;

	private String  order;
	
	/**
	 * 已经选中的类型
	 */
	private List<String> listHasSelectTypes;
	
	/**
	 * 文档已经选中的格式
	 */
	private String hasSelectDocuments;
	
	/**
	 * 图片已经选中的格式
	 */
	private String hasSelectPictures;
	
	/**
	 * 音频已选中的格式
	 */
	private String hasSelectaudios;
	
	/**
	 * 视频已选中的格式
	 */
	private String hasSelectvideos;
	
	/**
	 * 邮件已选中的格式
	 */
	private String hasSelectemails;
	
	
	private BigDecimal documentMaxSize;
	private BigDecimal pictureMaxSize;
	private BigDecimal audioMaxSize;
	private BigDecimal videoMaxSize;
	private BigDecimal emailMaxSize;

}
