package com.howbuy.crm.hb.persistence.callout;

import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CsCalloutMyTaskMapper {

    /**
     * 根据客户的手机号判断对象是否存在
     * @param param
     * @param pageBean
     * @return
     */
    List<Map<String,Object>> isExsitCustByMobileByPage(@Param("p") Map<String, String> param, @Param("page") CommPageBean pageBean);
}
