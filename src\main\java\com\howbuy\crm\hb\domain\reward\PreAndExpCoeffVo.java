package com.howbuy.crm.hb.domain.reward;

import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: 预约与调整系数
 * @reason:
 * @Date: 2020/9/18 18:45
 */
@Data
public class PreAndExpCoeffVo extends Prebookproductinfo {

    private String preId;
    /**
     * 核算产品类型
     */
    private String accountProductType;
    private String accountDt;
    private BigDecimal foldCoeff;
    private BigDecimal commissionRate;
    private BigDecimal annuallySetAward;
    private BigDecimal secondStockCoeff;
    private BigDecimal stockFeeA;
    private BigDecimal stockFeeB;
    private BigDecimal operFoldCoeff;
    private String accountDtModify;
    private String foldCoeffModify;
    private String commissionRateModify;
    private String annuallySetAwardModify;
    private String secondStockCoeffModify;
    private String stockFeeAModify;
    private String stockFeeBModify;
    private String operFoldCoeffModify;
    private String rowModify;
    private String creator;
    private String createTime;
    private String modor;
    private String updateTime;
    /**
     * 汇率
     */
    private String exchangeRate;

    /** 添加客户来源系数相关 */
    /** 来源系数 */
    private BigDecimal sourceCoeff;
    /** 客户折标系数 */
    private BigDecimal zbCoeff;
    /** 管理系数-分总 */
    private BigDecimal manageCoeff;
    /** 管理系数-区副 */
    private BigDecimal manageCoeffRegionalsubtotal;
    /** 管理系数-区总 */
    private BigDecimal manageCoeffRegionaltotal;
    /** 存续A 可选项有：公司资源、投顾资源 */
    private String cxa;
    /** 存续B 可选项有：公司资源、投顾资源 */
    private String cxb;

    /** 是否修改 */
    /** 来源系数 */
    private String sourceCoeffModify;
    /** 客户折标系数 */
    private String zbCoeffModify;
    /** 管理系数 */
    private String manageCoeffModify;
    /** 存续A 可选项有：公司资源、投顾资源 */
    private String cxaModify;
    /** 存续B 可选项有：公司资源、投顾资源 */
    private String cxbModify;
    /**
     * 调整系数
     */
    private String unqualifiedCoeffModify;

    /**
     * 调整系数
     */
    private BigDecimal unqualifiedCoeff;
    /**
     * 折扣金额修改标识
     */
    private String beforeTaxAmtModify;
    /**
     * 管理系数区副修改标识
     */
    private String coeffRegionalsubtotalModify;
    /**
     * 管理系数区总修改标识
     */
    private String coeffRegionaltotalModify;
    /**
     * 权益折扣率修改标识
     */
    private String interestsRateModify;

    /**
     * 到账金额(RMB)
     */
    private BigDecimal realpayamtRmb;
    /**
     * 到账手续费(RMB)
     */
    private BigDecimal feeRmb;
    /**
     * 折扣金额(RMB)
     */
    private BigDecimal beforetaxamtRmb;

    /**
     * 权益级别：1-级别1; 2-级别2; 3-级别3
     */
    private String interestsLevelPre;
    /**
     * 权益折扣率
     */
    private BigDecimal interestsRatePre;
    /**
     * 权益类型CODE: 1-权益折扣；2-MGM权益折扣
     */
    private String interestsTypePre;

    /**
     * 客户权益
     */
    private String interestComment;
    /**
     * 权益折扣率
     */
    private BigDecimal interestsRate;
    /**
     *  预约id 如果是分次call非首次，且投顾和首次一致，则为首次id
     */
    private String coeffPreId;

    /**
     *  如果是分次call非首次，且投顾和首次不一致一致，则为1，否则为0
     */
    private String tansConsManycall;
}
