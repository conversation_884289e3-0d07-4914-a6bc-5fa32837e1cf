/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.birthdaygift;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 生日礼品-物流信息VO
 * @date 2024/9/23 15:47
 * @since JDK 1.8
 */

@Getter
@Setter
public class SmGiftGisVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准状态码
     */
    private Integer status;

    /**
     * 标准状态码名称(UserBirthdayLogisticsStateEnum)
     */
    private String statusName;

    /**
     * 状态描述-详细信息
     */
    private String statusDesc;

    /**
     * 时间
     */
    private String timeDesc;

}