/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.hb.enums;
/**
 * @description:( 佣金结算相关操作  数据类型：
 *        1- 对 佣金ID 操作
 *        2- 对 佣金明细拆单后的明细ID 操作)
 * @param null
 * @return
 * @author: haoran.zhang
 * @date: 2024/10/14 17:49
 * @since JDK 1.8
 */
public enum CommissionOptDataEnum {

	/**
	 * 待结算
	 */
	OPT_COMMISSION("1", "操作佣金ID"),
    /**
     * 已开票
     */
	OPT_SPLIT("2", "操作佣金明细拆单");

	/**
	 * 编码
	 */
	private String code;
	/**
	 * 描述
	 */
	private String description;

	private CommissionOptDataEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CommissionOptDataEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return PreOccupyTypeEnum
	 */
	public static CommissionOptDataEnum getEnum(String code) {
		for(CommissionOptDataEnum statusEnum : CommissionOptDataEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}


	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
