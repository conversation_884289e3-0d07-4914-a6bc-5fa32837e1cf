package com.howbuy.crm.hb.persistence.custinfo;

import java.util.List;
import java.util.Map;
import com.howbuy.crm.hb.domain.custinfo.CmCustfamilyFile;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmCustfamilyFileMapper {
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmCustfamilyFile> listCmCustfamilyFile(Map<String, String> param);
	
	/**
	 * 得到单个数据对象
	 * @param param
	 * @return
	 */
	CmCustfamilyFile getCmCustfamilyFile(Map<String, String> param);

	/**
	 * 单条修改数据对象
	 * @param cmCustfamilyFile
	 */
	void updateCmCustfamilyFile(CmCustfamilyFile cmCustfamilyFile);
	
	/**
	 * 新增数据对象
	 * @param cmCustfamilyFile
	 */
	void insertCmCustfamilyFile(CmCustfamilyFile cmCustfamilyFile);

}
