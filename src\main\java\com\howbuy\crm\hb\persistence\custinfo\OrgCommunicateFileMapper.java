package com.howbuy.crm.hb.persistence.custinfo;

import com.howbuy.crm.hb.domain.custinfo.OrgCommunicateFile;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/6/11 17:57
 */
public interface OrgCommunicateFileMapper {

    void batchInsert(@Param("list") List<OrgCommunicateFile> list);
    
    /**
     * 根据机构客户沟通号查询上传的附件信息
     * @param param
     * @return
     */
    List<OrgCommunicateFile> listOrgCommunicateFile(Map<String,Object> param);
    
    /**
     * 根据id查询上传的附件信息
     * @param param
     * @return
     */
    OrgCommunicateFile getOrgCommunicateFile(Map<String,Object> param);

    /**
     * 批量删除文件记录:
     * @param
    * <AUTHOR>
    * @date 2020/6/16
    */
    void deleteFileByIds(@Param("list") List<String> list);
}
