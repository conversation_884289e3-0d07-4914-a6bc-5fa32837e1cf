package com.howbuy.crm.hb.enums;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Description 选礼物发货状态状态枚举
 * <AUTHOR>
 * @Date 2024/10/16 14:16
 * @Version 1.0.0
 */
@Getter
public enum UserBirthdayShippingStateEnum {

    /**
     * 历史数据或选择投顾寄送
     */
    NO_TRACKING("0", "无需跟踪"),

    /**
     * 选择公司寄送
     */
    PLACED_ORDER("1", "已下单"),

    /**
     * 存在物流单号
     */
    SHIPPED("2", "已发货"),

    /**
     * 轨迹状态为已签收
     */
    SIGNED_OFF("3", "已签收");

    /**
     * 编号
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    public static Map<String, String> getUserBirthdayShippingStateEnumMap() {
        Map<String, String> reportMap = new LinkedHashMap<String, String>();
        UserBirthdayShippingStateEnum[] values = UserBirthdayShippingStateEnum.values();
        for (UserBirthdayShippingStateEnum type : values) {
            reportMap.put(type.getCode(), type.getName());
        }
        return reportMap;
    }

    public static UserBirthdayShippingStateEnum getUserBirthdayShippingStateEnum(String code) {
        for (UserBirthdayShippingStateEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    UserBirthdayShippingStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

}



