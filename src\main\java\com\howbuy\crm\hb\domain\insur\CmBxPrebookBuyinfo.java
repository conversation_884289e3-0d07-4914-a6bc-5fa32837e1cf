package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxPrebookBuyinfo.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxPrebookBuyinfo implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private BigDecimal preid;
	private String fundcode;
	private String fundname;
	private String prodtype;
	private String payyears;
	private String ensureyears;
	private BigDecimal yearamk;
	private BigDecimal insuramk;
	private BigDecimal rate;
	private BigDecimal collamk;
	private BigDecimal collamkSpec;
	/**
	 * 折标系数
	 */
	private BigDecimal procoe;
	private String isdel;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	private String prodproper;
	/**
	 * 销量核算日期
	 */
	private String salesdt;
	/**
	 * 佣金核算日期
	 */
	private String commissiondt;
	/**
	 * 投顾佣金率
	 */
	private BigDecimal commissionratio;
	/**
	 * 投顾佣金
	 */
	private BigDecimal commission;
	/**
	 * 投顾佣金汇率
	 */
	private BigDecimal commissionrate;
	/**
	 * 人工修改的销量核算日期
	 */
	private String expsalesdt;
	/**
	 * 人工修改的佣金核算日期
	 */
	private String expcommissiondt;
	/**
	 * 人工修改的折标系数
	 */
	private BigDecimal expprocoe;
	/**
	 * 人工修改的投顾佣金率
	 */
	private BigDecimal expcommissionratio;
	/**
	 * 到期状态
	 */
	private String expirestat;
	/**
	 * 到期状态
	 */
	private String expirestatval;
	/**
	 * 到期日期
	 */
	private String expiredate;


	/**
	 * 投顾创新方案 1方案一 2方案二
	 */
	private String bxCommissionWay;
	/**
	 * 方案2-佣金
	 */
	private BigDecimal commissionForTwo;
	/**
	 * 方案2-存续D
	 */
	private BigDecimal commissionCxdForTwo;

	/**
	 * 管理系数
	 */
	private String managePoint;
	/**
	 * 人工修改的管理佣金率
	 */
	private BigDecimal expManageCommissionRatio;
	/**
	 * 管理佣金率
	 */
	private BigDecimal manageCommissionRatio;
	/**
	 * 管理佣金
	 */
	private BigDecimal manageCommission;
	/**
	 * 方案2-管理佣金
	 */
	private BigDecimal manageCommissionForTwo;
}
