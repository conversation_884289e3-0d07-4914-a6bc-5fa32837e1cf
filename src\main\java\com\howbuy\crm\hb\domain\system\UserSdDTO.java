/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.system;

import com.howbuy.crm.page.framework.domain.User;

/**
 * @description: 用户数据深度DTO
 * <AUTHOR>
 * @date 2024/9/24 19:11
 * @since JDK 1.8
 */

public class UserSdDTO {


    private User user;


    /**
     * 当前投顾下的客户，信息明文展示
     */
    Boolean myShow = false;

    /**
     * 非当前投顾下的客户，信息明文展示
     */
    Boolean notMyShow = false;

    /**
     * 投顾管理下的客户，信息明文展示
     */
    Boolean mgrShow = false;

    /**
     * 所有客户，信息明文展示
     */
    Boolean allShow = false;

    /**
     * 所有客户，信息加密展示
     */
    Boolean allEncry = false;

    /**
     * 是否可以查看地址
     * 字段逻辑：业务方指定的 部分角色 有查看所有地址的权限，那么该字段为true
     * 部分角色为："ROLE_PS_HEAD", "ROLE_TRAIN_SERVICE", "ROLE_PS_LEADER", "ROLE_PS"
     */
    Boolean canSeeAddressRole = false;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Boolean getMyShow() {
        return myShow;
    }

    public void setMyShow(Boolean myShow) {
        this.myShow = myShow;
    }

    public Boolean getNotMyShow() {
        return notMyShow;
    }

    public void setNotMyShow(Boolean notMyShow) {
        this.notMyShow = notMyShow;
    }

    public Boolean getMgrShow() {
        return mgrShow;
    }

    public void setMgrShow(Boolean mgrShow) {
        this.mgrShow = mgrShow;
    }

    public Boolean getAllShow() {
        return allShow;
    }

    public void setAllShow(Boolean allShow) {
        this.allShow = allShow;
    }

    public Boolean getAllEncry() {
        return allEncry;
    }

    public void setAllEncry(Boolean allEncry) {
        this.allEncry = allEncry;
    }

    public Boolean getCanSeeAddressRole() {
        return canSeeAddressRole;
    }

    public void setCanSeeAddressRole(Boolean canSeeAddressRole) {
        this.canSeeAddressRole = canSeeAddressRole;
    }
}