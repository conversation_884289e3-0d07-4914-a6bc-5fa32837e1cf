package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @Description: 实体类CmNormalCustReview.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
public class CmNormalCustReview implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal id;
	private String conscustno;
	private String conscode;
	private String custtype;
	private String reviewtype;
	private String optor;
	private String optorname;
	private String dealstate;
	private String iswrong;
	private String wrongdes;
	private String isconnect;
	private BigDecimal notconnectnum;
	private String notifymessagedt;
	private String reviewdt;
	private String communicate;

	private String isrealconscode;
	private String dreviewdt2nd;
	private String communicate2nd;
	private String dreviewdt3rd;
	private String communicate3rd;
	private String isfeedback;
	private String feedbackcontent;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	private String uporgname;
	private String orgname;
	private String creatdtstr;
	private String custname;
	private String sszxname;
	private String mobile;
	private String linkmobile;
	private String mobileMask;
	private String linkmobileMask;
	private String mobileCipher;
	private String linkmobileCipher;
	private String encryptionMobile;
	private String encryptionLinkmobile;

	public BigDecimal getId() {
		return id;
	}
	public void setId(BigDecimal id) {
		this.id = id;
	}
	public String getConscustno() {
		return conscustno;
	}
	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	public String getConscode() {
		return conscode;
	}
	public void setConscode(String conscode) {
		this.conscode = conscode;
	}
	public String getCusttype() {
		return custtype;
	}
	public void setCusttype(String custtype) {
		this.custtype = custtype;
	}
	public String getReviewtype() {
		return reviewtype;
	}
	public void setReviewtype(String reviewtype) {
		this.reviewtype = reviewtype;
	}
	public String getOptor() {
		return optor;
	}
	public void setOptor(String optor) {
		this.optor = optor;
	}
	public String getDealstate() {
		return dealstate;
	}
	public void setDealstate(String dealstate) {
		this.dealstate = dealstate;
	}
	public String getIswrong() {
		return iswrong;
	}
	public void setIswrong(String iswrong) {
		this.iswrong = iswrong;
	}
	public String getWrongdes() {
		return wrongdes;
	}
	public void setWrongdes(String wrongdes) {
		this.wrongdes = wrongdes;
	}
	public String getIsconnect() {
		return isconnect;
	}
	public void setIsconnect(String isconnect) {
		this.isconnect = isconnect;
	}
	public BigDecimal getNotconnectnum() {
		return notconnectnum;
	}
	public void setNotconnectnum(BigDecimal notconnectnum) {
		this.notconnectnum = notconnectnum;
	}
	public String getNotifymessagedt() {
		return notifymessagedt;
	}
	public void setNotifymessagedt(String notifymessagedt) {
		this.notifymessagedt = notifymessagedt;
	}
	public String getReviewdt() {
		return reviewdt;
	}
	public void setReviewdt(String reviewdt) {
		this.reviewdt = reviewdt;
	}
	public String getCommunicate() {
		return communicate;
	}
	public void setCommunicate(String communicate) {
		this.communicate = communicate;
	}
	public String getDreviewdt2nd() {
		return dreviewdt2nd;
	}
	public void setDreviewdt2nd(String dreviewdt2nd) {
		this.dreviewdt2nd = dreviewdt2nd;
	}
	public String getCommunicate2nd() {
		return communicate2nd;
	}
	public void setCommunicate2nd(String communicate2nd) {
		this.communicate2nd = communicate2nd;
	}
	public String getDreviewdt3rd() {
		return dreviewdt3rd;
	}
	public void setDreviewdt3rd(String dreviewdt3rd) {
		this.dreviewdt3rd = dreviewdt3rd;
	}
	public String getCommunicate3rd() {
		return communicate3rd;
	}
	public void setCommunicate3rd(String communicate3rd) {
		this.communicate3rd = communicate3rd;
	}
	public String getIsfeedback() {
		return isfeedback;
	}
	public void setIsfeedback(String isfeedback) {
		this.isfeedback = isfeedback;
	}
	public String getFeedbackcontent() {
		return feedbackcontent;
	}
	public void setFeedbackcontent(String feedbackcontent) {
		this.feedbackcontent = feedbackcontent;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public Date getCreatdt() {
		return creatdt;
	}
	public void setCreatdt(Date creatdt) {
		this.creatdt = creatdt;
	}
	public String getModifier() {
		return modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}
	public Date getModifydt() {
		return modifydt;
	}
	public void setModifydt(Date modifydt) {
		this.modifydt = modifydt;
	}
	public String getOrgname() {
		return orgname;
	}
	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}
	public String getCreatdtstr() {
		return creatdtstr;
	}
	public void setCreatdtstr(String creatdtstr) {
		this.creatdtstr = creatdtstr;
	}
	public String getCustname() {
		return custname;
	}
	public void setCustname(String custname) {
		this.custname = custname;
	}
	public String getSszxname() {
		return sszxname;
	}
	public void setSszxname(String sszxname) {
		this.sszxname = sszxname;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getLinkmobile() {
		return linkmobile;
	}
	public void setLinkmobile(String linkmobile) {
		this.linkmobile = linkmobile;
	}
	public String getOptorname() {
		return optorname;
	}
	public void setOptorname(String optorname) {
		this.optorname = optorname;
	}
	public String getEncryptionMobile() {
		return encryptionMobile;
	}
	public void setEncryptionMobile(String encryptionMobile) {
		this.encryptionMobile = encryptionMobile;
	}
	public String getEncryptionLinkmobile() {
		return encryptionLinkmobile;
	}
	public void setEncryptionLinkmobile(String encryptionLinkmobile) {
		this.encryptionLinkmobile = encryptionLinkmobile;
	}

	public String getUporgname() {
		return uporgname;
	}

	public void setUporgname(String uporgname) {
		this.uporgname = uporgname;
	}
	public String getMobileMask() {
		return mobileMask;
	}
	public void setMobileMask(String mobileMask) {
		this.mobileMask = mobileMask;
	}
	public String getLinkmobileMask() {
		return linkmobileMask;
	}
	public void setLinkmobileMask(String linkmobileMask) {
		this.linkmobileMask = linkmobileMask;
	}
	public String getMobileCipher() {
		return mobileCipher;
	}
	public void setMobileCipher(String mobileCipher) {
		this.mobileCipher = mobileCipher;
	}
	public String getLinkmobileCipher() {
		return linkmobileCipher;
	}
	public void setLinkmobileCipher(String linkmobileCipher) {
		this.linkmobileCipher = linkmobileCipher;
	}

	public String getIsrealconscode() {
		return isrealconscode;
	}

	public void setIsrealconscode(String isrealconscode) {
		this.isrealconscode = isrealconscode;
	}
}
