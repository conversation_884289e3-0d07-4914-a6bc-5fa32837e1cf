package com.howbuy.crm.hb.domain.prosale.autocompare;

import lombok.Data;

/**
 * 预约数据自动核对（概览）-前端查询对象
 * <AUTHOR>
 * @date 2022/10/19 19:58
 */
@Data
public class SearchAutoCompareSummaryVo {

    /** 产品代码 */
    private String productCode;
    /** 预约结束日 */
    private String appointEndDtStr;
    /** 开放日 */
    private String openDtStr;
    /** 业务类型 */
    private String convertBusiType;
    /** 产品类别 */
    private String productCategory;
    /** 实际管理人 */
    private String actualMgmtMan;
    /** 汇总核对结果：全部、一致 true、不一致 false */
    private Boolean summaryCompareResult;
}
