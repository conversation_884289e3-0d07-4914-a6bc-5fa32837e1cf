## 测试的类
> com.howbuy.crm.hb.service.custinfo.impl.MergeConscustServiceImpl
## 测试的方法
> handleCmConferenceConscust(List<String> mergeConscustes,String conscustno,String userid)  处理会议预约参会记录方法

遍历预约参会记录放入map：key为会议id,value为会议记录对象集合
循环遍历map
1.一个会议对应一个客户：
    客户号为保留客户：不处理
    非保留客户：更新为保留客户号

2.一个会议对应多个客户：
    包含保留客户：
     1）
            保留客户信息为的最大的实际到场人数和最小参会时间：不处理
             否则：寻找最大的实际到场人数和最小的参会时间,更新到保留客户中
     2） 删除非保留客户
      不包含保留客户：
            保留最大的实际到场人数和最小参会时间的记录，将客户号更新为保留客户号
             删除其他非保留的客户
                
                
                
## 分支伪代码
```java
查询合并客户的所有预约参会记录：实际到场人数降序且签到时间升序排列
遍历参会记录把记录对象放入map,key为会议id,value为会议记录对象集合
for(循环会议记录map){
     取会议id对应的集合
  if(某会议对应一个客户参会记录){
     if(该参会记录的客户号不等于保留客户号){
                   该客户放入待更新客户list中
     }
  }else{
     for(循环该会议id对应的list){
        if(第一次循环){
           if(会议对象客户号不等于保留客户号){
                                 标记 实际到场人数、签到时间
           }else{
                                设置是否包含保留的客户号状态为true
           }
        }else{
           if(会议对象客户号等于保留客户号){
                                 设置是否包含保留的客户号状态为true
                                  更新 标记 实际到场人数、签到时间
           }else{
                                  删除被合并的客户参会会议记录
           }
        }
      }
      if(是否包含保留的客户号状态为true){
         if(保留客户号不等于第一条参会记录客户号){
                             删除该条客户参会会议记录
         }
       }else{
                           将第一条参会记录客户放入待更新客户list中
       }
    }
  }
  
  if(待更新客户list不为空){
          待更新客户list的客户号为保留客户号
  }
}


```

## 测试案例
##### 1、testOne：一个会议id对应一个客户且是保留客户      &&    一个会议id对应一个客户且不是保留客户

##### 2、testTwo：一个会议id对应多个客户且包含保留客户，且第一条是保留客户
##### 3、testThr：一个会议id对应多个客户且包含保留客户，且第一条不是保留客户

##### 4、testFor：一个会议id对应一个客户且是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条是保留客户
##### 5、testFiv：一个会议id对应一个客户且是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
##### 6、testSix：一个会议id对应一个客户且是保留客户     &&    一个会议id对应多个客户且不包含保留客户


##### 7、testSer：一个会议id对应一个客户且不是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条是保留客户
##### 8、testEig：一个会议id对应一个客户且不是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户



##### 9、testNin：一个会议id对应多个客户且包含保留客户，且第一条是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条是保留客户
##### 10、testTen：一个会议id对应多个客户且包含保留客户，且第一条是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
##### 11、testEle：一个会议id对应多个客户且包含保留客户，且第一条是保留客户     &&    一个会议id对应多个客户且不包含保留客户
##### 12、testTwe：一个会议id对应多个客户且包含保留客户，且第一条不是保留客户     &&    一个会议id对应多个客户且包含保留客户，且第一条不是保留客户
##### 13、testThi：一个会议id对应多个客户且包含保留客户，且第一条不是保留客户     &&    一个会议id对应多个客户且不包含保留客户



