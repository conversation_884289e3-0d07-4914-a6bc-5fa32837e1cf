package com.howbuy.crm.hb.domain.conference;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * (路演参会人员)
 * @ClassName: CmConferenceCustKinsfolk
 * 											  
 * 创建日期    		                            修改人员   	        版本	 	     修改内容  
 * -------------------------------------------------  
 * 2017年8月14日 下午5:09:21   yu.zhang     1.0    	初始化创建
 *
 * 修改记录:
 * @since		JDK1.6
 */
public class CmConferenceCustKinsfolk implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**亲属参会表主键*/
	private String kinsfolkid;

	/**参与会议(外键)*/
	private String conferenceid;
	
	/**参与会议(外键)*/
	private String conferencename;
	
	/**投顾客户号*/
	private String conscustno;

	/**投顾客户姓名*/
	private String custname;

	/**所属投顾*/
	private String conscode;

	/**投顾姓名*/
	private String consname;

	/**所属部门*/
	private String uporgname;

	/**所属部门*/
	private String orgcode;

	/**亲属姓名*/
	private String kinsfolkname;

	/**亲属证件类型*/
	private String idtype;

	/**亲属证件类型*/
	private String idtypename;

	/**亲属证件号码*/
	private String idno;
	private String idnoDigest;
	private String idnoMask;
	private String idnoCipher;

	/**去程航班号*/
	private String gotoflight;

	/**去程航班抵达时间*/
	private Timestamp gotoflightdt;

	private String gotoflightdtstr;

	/**返程航班号*/
	private String returnflight;

	/**返程航班抵达时间*/
	private Timestamp returnflightdt;

	private String returnflightdtstr;

	/**创建日期*/
	private Date creatdt;

	/**创建人*/
	private String creater;

	/**修改日期*/
	private Date modifydt;

	/**修改人*/
	private String modifier;

	private String appointmentstype;

	private String appointmentstypename;

	/**参会截止操作时间*/
	private String cutoffdt;
	
	private List<String> conscustnoList;
	
	/**出行方式*/
	private String tripway;
	
	/**去程班次抵达日期*/
	private String gotoflightDate;
	
	/**去程班次抵达时间*/
	private String gotoflightTime;
	
	/**返程班次出发日期*/
	private String returnflightDate;
	
	/**返程班次出发时间*/
	private String returnflightTime;

	public String getConsname() {
		return consname;
	}

	public void setConsname(String consname) {
		this.consname = consname;
	}

	public String getConferencename() {
		return conferencename;
	}

	public void setConferencename(String conferencename) {
		this.conferencename = conferencename;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getConferenceid() {
		return conferenceid;
	}

	public void setConferenceid(String conferenceid) {
		this.conferenceid = conferenceid;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	public String getCreater() {
		return creater;
	}

	public void setCreater(String creater) {
		this.creater = creater;
	}


	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getKinsfolkname() {
		return kinsfolkname;
	}

	public void setKinsfolkname(String kinsfolkname) {
		this.kinsfolkname = kinsfolkname;
	}

	public String getIdtype() {
		return idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}

	public String getGotoflight() {
		return gotoflight;
	}

	public void setGotoflight(String gotoflight) {
		this.gotoflight = gotoflight;
	}

	public String getIdno() {
		return idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}

	public String getReturnflight() {
		return returnflight;
	}

	public void setReturnflight(String returnflight) {
		this.returnflight = returnflight;
	}

	public Date getCreatdt() {
		return creatdt;
	}

	public void setCreatdt(Date creatdt) {
		this.creatdt = creatdt;
	}

	public Date getModifydt() {
		return modifydt;
	}

	public void setModifydt(Date modifydt) {
		this.modifydt = modifydt;
	}

	public String getKinsfolkid() {
		return kinsfolkid;
	}

	public void setKinsfolkid(String kinsfolkid) {
		this.kinsfolkid = kinsfolkid;
	}

	public String getAppointmentstype() {
		return appointmentstype;
	}

	public void setAppointmentstype(String appointmentstype) {
		this.appointmentstype = appointmentstype;
	}

	public String getAppointmentstypename() {
		return appointmentstypename;
	}

	public void setAppointmentstypename(String appointmentstypename) {
		this.appointmentstypename = appointmentstypename;
	}

	public String getCutoffdt() {
		return cutoffdt;
	}

	public void setCutoffdt(String cutoffdt) {
		this.cutoffdt = cutoffdt;
	}

	public String getIdtypename() {
		return idtypename;
	}

	public void setIdtypename(String idtypename) {
		this.idtypename = idtypename;
	}

	public String getGotoflightdtstr() {
		return gotoflightdtstr;
	}

	public void setGotoflightdtstr(String gotoflightdtstr) {
		this.gotoflightdtstr = gotoflightdtstr;
	}

	public String getReturnflightdtstr() {
		return returnflightdtstr;
	}

	public void setReturnflightdtstr(String returnflightdtstr) {
		this.returnflightdtstr = returnflightdtstr;
	}

	public List<String> getConscustnoList() {
		return conscustnoList;
	}

	public void setConscustnoList(List<String> conscustnoList) {
		this.conscustnoList = conscustnoList;
	}

	public String getTripway() {
		return tripway;
	}

	public void setTripway(String tripway) {
		this.tripway = tripway;
	}

	public String getGotoflightDate() {
		return gotoflightDate;
	}

	public void setGotoflightDate(String gotoflightDate) {
		this.gotoflightDate = gotoflightDate;
	}

	public String getGotoflightTime() {
		return gotoflightTime;
	}

	public void setGotoflightTime(String gotoflightTime) {
		this.gotoflightTime = gotoflightTime;
	}

	public String getReturnflightDate() {
		return returnflightDate;
	}

	public void setReturnflightDate(String returnflightDate) {
		this.returnflightDate = returnflightDate;
	}

	public String getReturnflightTime() {
		return returnflightTime;
	}

	public void setReturnflightTime(String returnflightTime) {
		this.returnflightTime = returnflightTime;
	}

	public Timestamp getGotoflightdt() {
		return gotoflightdt;
	}

	public void setGotoflightdt(Timestamp gotoflightdt) {
		this.gotoflightdt = gotoflightdt;
	}

	public Timestamp getReturnflightdt() {
		return returnflightdt;
	}

	public void setReturnflightdt(Timestamp returnflightdt) {
		this.returnflightdt = returnflightdt;
	}

    public String getUporgname() {
        return uporgname;
    }

    public void setUporgname(String uporgname) {
        this.uporgname = uporgname;
    }

	public String getIdnoDigest() {
		return idnoDigest;
	}

	public void setIdnoDigest(String idnoDigest) {
		this.idnoDigest = idnoDigest;
	}

	public String getIdnoMask() {
		return idnoMask;
	}

	public void setIdnoMask(String idnoMask) {
		this.idnoMask = idnoMask;
	}

	public String getIdnoCipher() {
		return idnoCipher;
	}

	public void setIdnoCipher(String idnoCipher) {
		this.idnoCipher = idnoCipher;
	}
}
