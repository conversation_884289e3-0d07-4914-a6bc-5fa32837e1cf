package com.howbuy.crm.hb.domain.lcjzsigndata;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 理财九章线上签到数据
    */
@Data
public class CmLcjzsigndata implements Serializable {
    /**
     * 课程ID
     */
    private String courseid;

    /**
     * 关联课程
     */
    private String joincoursename;

    /**
     * 会议id
     */
    private String conferenceid;

    /**
     * 举办部门
     */
    private String holdorg;

    /**
     * 会议类型
     */
    private String conferencetype;

    /**
     * 报名人一账通
     */
    private String hboneno;

    /**
     * 报名人投顾客户号
     */
    private String conscustno;

    /**
     * 报名人姓名
     */
    private String realname;

    /**
     * 报名时投顾code
     */
    private String conscode;

    /**
     * 课程名称
     */
    private String coursename;

    /**
     * 主办方
     */
    private String sponsor;

    /**
     * 城市
     */
    private String cityname;

    /**
     * 课程日期
     */
    private Date startdate;

    /**
     * 讲师
     */
    private String lecturer;

    /**
     * 课程地点
     */
    private String activityarea;

    /**
     * 课程时间
     */
    private String activitytime;

    /**
     * 课程状态
     */
    private String onlineflag;

    /**
     * 会议名称
     */
    private String conferencename;

    /**
     * 报名数据来源
     */
    private String assignsource;

    /**
     * 报名状态
     */
    private String appointstatus;

    /**
     * 报名时间
     */
    private Date appointtime;

    /**
     * 是否线下签到
     */
    private String ifcheckin;

    /**
     * 签到时间
     */
    private Date checkintime;

    /**
     * 报名人是否是投顾
     */
    private String ifcons;

    /**
     * 客户属性
     */
    private String custproperty;

    /**
     * 报名时客户类型
     */
    private String custclassifyassign;

    /**
     * 当前客户类型
     */
    private String custclassifynow;

    /**
     * 客户一级来源
     */
    private String firstlevelname;

    /**
     * 客户二级来源
     */
    private String secondlevelname;

    /**
     * 客户三级来源
     */
    private String thirdlevelname;

    /**
     * 客户四级来源
     */
    private String fourthlevelname;

    /**
     * 是否有高端持仓
     */
    private String gdholdflag;

    /**
     * 是否有零售持仓
     */
    private String lsholdflag;

    /**
     * 是否有海外持仓
     */
    private String hwflag;

    /**
     * 高端市值
     */
    private BigDecimal gdcap;

    /**
     * 零售市值
     */
    private BigDecimal lscap;

    /**
     * 高端产品首单成交时间
     */
    private Date gdfstackdt;

    /**
     * 报名时是否有投顾
     */
    private String ifwithcons;

    /**
     * 报名时投顾
     */
    private String consname;

    /**
     * 当前投顾
     */
    private String consnamenow;

    /**
     * 当前投顾一级组织
     */
    private String u1name;

    /**
     * 当前投顾二级组织
     */
    private String u2name;

    /**
     * 当前投顾三级组织
     */
    private String u3name;

    /**
     * 是否进产品预约表
     */
    private String ifprdreserv;

    /**
     * 是否客服外呼过
     */
    private String ifcall;

    /**
     * 客服外呼时间
     */
    private Date callhandledate;

    /**
     * 是否成功接听
     */
    private String ifcallsuccess;

    /**
     * 是否客服成功分配真实投顾
     */
    private String ifcallleads;

    /**
     * 30天是否客服外呼过
     */
    private String ifcall30;

    /**
     * 30天客服外呼时间
     */
    private Date callhandledate30;

    /**
     * 是否成功接听_30天
     */
    private String ifcallsuccess30;

    /**
     * 30天是否客服成功分配真实投顾
     */
    private String ifcallleads30;

    /**
     * 外呼过_是否同一天
     */
    private String ifcallsametime;

    /**
     * 会议时间
     */
    private Date conferencetime;

    /**
     * 创建时间
     */
    private Date credt;

    private static final long serialVersionUID = 1L;
}