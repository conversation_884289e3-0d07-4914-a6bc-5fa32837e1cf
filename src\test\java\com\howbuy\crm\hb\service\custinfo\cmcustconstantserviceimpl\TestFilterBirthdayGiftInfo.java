package com.howbuy.crm.hb.service.custinfo.cmcustconstantserviceimpl;

import com.howbuy.crm.hb.service.conscust.impl.CmCustConstantServiceImpl;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.persistence.web.SVIPBirthdayGiftInfo;
import com.howbuy.web.service.business.SVIPBirthdayGiftMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.testng.PowerMockTestCase;
import java.lang.reflect.Method;
import org.testng.Assert;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 单元测试:生日礼品接口信息处理
 * @author: jianyi.tao
 * @create: 2022/06/20 10:12
 * @since: JDK 1.8
 */
@PowerMockIgnore("javax.management.*")
@RunWith(PowerMockRunner.class)
@PrepareForTest({CmCustConstantServiceImpl.class, SVIPBirthdayGiftMessageService.class, Map.class, HashMap.class})
public class TestFilterBirthdayGiftInfo extends PowerMockTestCase {
    @Mock
    private CmCustConstantServiceImpl cmCustConstantServiceImpl = PowerMockito.spy(cmCustConstantServiceImpl = new CmCustConstantServiceImpl());

    // 模拟接口返回数据
    private static SVIPBirthdayGiftInfo bgl = new SVIPBirthdayGiftInfo();

    // 模拟客户投顾关系
    private static Map<String, String> custConstantMap = new HashMap<>();

    // 模拟投顾组织关系
    private static Map<String, String> cons2OutletMap = new HashMap<>();

    // 模拟组织结构层级关系
    private static Map<String, String> allOrgMap = new HashMap<>();

    // 模拟子组织和父组织对应关系
    private static Map<String, String> upOrgMap = new HashMap<>();

    // 模拟所属中心对应关系
    private static Map<String, String> upCenterMap = new HashMap<>();

    // 预设调用返回数据
    static {
        // 模拟接口返回的客户数据
        bgl.setCustName("赵瑛");
        bgl.setConsCustNo("1000536105");
        bgl.setHboneno("8012164865");
        bgl.setBirthday("19500902");
        bgl.setChoiceTime("2021-12-01 10:45:26");
        bgl.setGiftType("礼物2");
        bgl.setGiftCode("00");

        // 模拟客户投顾关系
        custConstantMap.put("1000536105", "lena.jiang");

        // 模拟投顾组织关系
        cons2OutletMap.put("lena.jiang", "2000009993");

        // 模拟组织结构层级关系
        allOrgMap.put("2000009993", "江洁组");
        allOrgMap.put("200000999", "财富一区");
        allOrgMap.put("1", "财富管理中心");

        // 模拟子组织和父组织对应关系
        upOrgMap.put("2000009993", "200000999");

        // 模拟所属中心对应关系
        upCenterMap.put("2000009993", "1");
    }

    /**
     * 1、组织的上级层级为howbuy：则二级目录名称等一级名称
     */
    @Test
    public void test01() throws Exception {
        ConsOrgCache orgCache = PowerMockito.mock(ConsOrgCache.class);

        // 重新模拟子组织和父组织对应关系
        // 原始对应关系：upOrgMap.put("2000009993", "200000999");
        upOrgMap.put("2000009993", "0");

        // mock目标方法中调用方法orgCache.getCons2OutletMap()
        PowerMockito.when(orgCache.getCons2OutletMap()).thenReturn(cons2OutletMap);
        PowerMockito.when(orgCache.getAllOrgMap()).thenReturn(allOrgMap);

        // mock目标方法中调用方法orgCache.getUpOrgMapCache()
        PowerMockito.when(orgCache.getUpOrgMapCache()).thenReturn(upOrgMap);

        // mock目标方法中调用方法orgCache.getUpCenterMapCache()
        PowerMockito.when(orgCache.getUpCenterMapCache()).thenReturn(upCenterMap);

        Method method = PowerMockito.method(CmCustConstantServiceImpl.class, "filterBirthdayGiftInfo",
                SVIPBirthdayGiftInfo.class, null, null, null, null, null);

        // 调用目标方法获取返回结果
        List<String> consCodeList = new ArrayList<>();
        Object result = method.invoke(cmCustConstantServiceImpl, bgl, null, null, orgCache, consCodeList, custConstantMap);

        Assert.assertEquals(((SVIPBirthdayGiftInfo) result).getU2Name(), "江洁组");
    }

    /**
     * 2、组织的上级层级为非howbuy：则二级目录名称为所属区域名称
     */
    @Test
    public void test02() throws Exception {
        ConsOrgCache orgCache = PowerMockito.mock(ConsOrgCache.class);

        // 重新模拟子组织和父组织对应关系
        // 恢复原始对应关系：
        upOrgMap.put("2000009993", "200000999");

        // mock目标方法中调用方法orgCache.getCons2OutletMap()
        PowerMockito.when(orgCache.getCons2OutletMap()).thenReturn(cons2OutletMap);

        PowerMockito.when(orgCache.getAllOrgMap()).thenReturn(allOrgMap);

        // mock目标方法中调用方法orgCache.getUpOrgMapCache()
        PowerMockito.when(orgCache.getUpOrgMapCache()).thenReturn(upOrgMap);

        // mock目标方法中调用方法orgCache.getUpCenterMapCache()
        PowerMockito.when(orgCache.getUpCenterMapCache()).thenReturn(upCenterMap);

        Method method = PowerMockito.method(CmCustConstantServiceImpl.class, "filterBirthdayGiftInfo",
                SVIPBirthdayGiftInfo.class, null, null, null, null, null);

        // 调用目标方法获取返回结果
        List<String> consCodeList = new ArrayList<>();
        Object result = method.invoke(cmCustConstantServiceImpl, bgl, null, null, orgCache, consCodeList, custConstantMap);

        Assert.assertEquals(((SVIPBirthdayGiftInfo) result).getU2Name(), "财富一区");
    }

    /**
     * 3、模拟条件中按客户名称进行搜索
     */
    @Test
    public void test03() throws Exception {
        ConsOrgCache orgCache = PowerMockito.mock(ConsOrgCache.class);

        // mock目标方法中调用方法orgCache.getCons2OutletMap()
        PowerMockito.when(orgCache.getCons2OutletMap()).thenReturn(cons2OutletMap);

        PowerMockito.when(orgCache.getAllOrgMap()).thenReturn(allOrgMap);

        // mock目标方法中调用方法orgCache.getUpOrgMapCache()
        PowerMockito.when(orgCache.getUpOrgMapCache()).thenReturn(upOrgMap);

        // mock目标方法中调用方法orgCache.getUpCenterMapCache()
        PowerMockito.when(orgCache.getUpCenterMapCache()).thenReturn(upCenterMap);

        Method method = PowerMockito.method(CmCustConstantServiceImpl.class, "filterBirthdayGiftInfo",
                SVIPBirthdayGiftInfo.class, null, null, null, null, null);

        // 调用目标方法获取返回结果（模拟搜索条件，设置客户名称为：张三）
        String custName = "张三";
        List<String> consCodeList = new ArrayList<>();
        Object result = method.invoke(cmCustConstantServiceImpl, bgl, custName, null, orgCache, consCodeList, custConstantMap);

        Assert.assertEquals(result, null);
    }

    /**
     * 4、模拟条件中按投顾编码进行搜索
     */
    @Test
    public void test04() throws Exception {
        ConsOrgCache orgCache = PowerMockito.mock(ConsOrgCache.class);

        // mock目标方法中调用方法orgCache.getCons2OutletMap()
        PowerMockito.when(orgCache.getCons2OutletMap()).thenReturn(cons2OutletMap);

        PowerMockito.when(orgCache.getAllOrgMap()).thenReturn(allOrgMap);

        // mock目标方法中调用方法orgCache.getUpOrgMapCache()
        PowerMockito.when(orgCache.getUpOrgMapCache()).thenReturn(upOrgMap);

        // mock目标方法中调用方法orgCache.getUpCenterMapCache()
        PowerMockito.when(orgCache.getUpCenterMapCache()).thenReturn(upCenterMap);

        Method method = PowerMockito.method(CmCustConstantServiceImpl.class, "filterBirthdayGiftInfo",
                SVIPBirthdayGiftInfo.class, null, null, null, null, null);

        // 调用目标方法获取返回结果（模拟搜索条件，设置投顾为：jianyi.tao）
        List<String> consCodeList = new ArrayList<>();
        consCodeList.add("jianyi.tao");
        Object result = method.invoke(cmCustConstantServiceImpl, bgl, null, null, orgCache, consCodeList, custConstantMap);

        Assert.assertEquals(result, null);
    }

    /**
     * 5、模拟条件中按客户生日月份进行搜索
     */
    @Test
    public void test05() throws Exception {
        ConsOrgCache orgCache = PowerMockito.mock(ConsOrgCache.class);

        // mock目标方法中调用方法orgCache.getCons2OutletMap()
        PowerMockito.when(orgCache.getCons2OutletMap()).thenReturn(cons2OutletMap);

        PowerMockito.when(orgCache.getAllOrgMap()).thenReturn(allOrgMap);

        // mock目标方法中调用方法orgCache.getUpOrgMapCache()
        PowerMockito.when(orgCache.getUpOrgMapCache()).thenReturn(upOrgMap);

        // mock目标方法中调用方法orgCache.getUpCenterMapCache()
        PowerMockito.when(orgCache.getUpCenterMapCache()).thenReturn(upCenterMap);

        Method method = PowerMockito.method(CmCustConstantServiceImpl.class, "filterBirthdayGiftInfo",
                SVIPBirthdayGiftInfo.class, null, null, null, null, null);

        // 调用目标方法获取返回结果（模拟搜索条件，设置投顾为：jianyi.tao）
        String birthMonth = "12";
        List<String> consCodeList = new ArrayList<>();
        Object result = method.invoke(cmCustConstantServiceImpl, bgl, null, birthMonth, orgCache, consCodeList, custConstantMap);

        Assert.assertEquals(result, null);
    }

    /**
     * 6、模拟正常搜索
     */
    @Test
    public void test06() throws Exception {
        ConsOrgCache orgCache = PowerMockito.mock(ConsOrgCache.class);

        // mock目标方法中调用方法orgCache.getCons2OutletMap()
        PowerMockito.when(orgCache.getCons2OutletMap()).thenReturn(cons2OutletMap);

        PowerMockito.when(orgCache.getAllOrgMap()).thenReturn(allOrgMap);

        // mock目标方法中调用方法orgCache.getUpOrgMapCache()
        PowerMockito.when(orgCache.getUpOrgMapCache()).thenReturn(upOrgMap);

        // mock目标方法中调用方法orgCache.getUpCenterMapCache()
        PowerMockito.when(orgCache.getUpCenterMapCache()).thenReturn(upCenterMap);

        Method method = PowerMockito.method(CmCustConstantServiceImpl.class, "filterBirthdayGiftInfo",
                SVIPBirthdayGiftInfo.class, null, null, null, null, null);

        // 调用目标方法获取返回结果（模拟搜索条件，设置客户名称：赵瑛，生日月份：09，投顾为：lena.jiang）
        String custName = "赵瑛";
        String birthMonth = "09";
        List<String> consCodeList = new ArrayList<>();
        consCodeList.add("lena.jiang");
        Object result = method.invoke(cmCustConstantServiceImpl, bgl, custName, birthMonth, orgCache, consCodeList, custConstantMap);

        Assert.assertEquals(((SVIPBirthdayGiftInfo)result).getCustName(), "赵瑛");
    }

}
