package com.howbuy.crm.hb.domain.manage;

import java.io.Serializable;

/**
 * @Description: 实体类CmAnnounce.java
 * <AUTHOR>
 * @version 1.0
 * @created
 */
public class CmAnnounce implements Serializable {

	private static final long serialVersionUID = 1L;

	private long announcedid;

	/** 标题 */
	private String subject;

	/** 内容 */
	private String content;

	/** 生效日期 */
	private String validatedt;

	/** 失效日期 */
	private String invalidatedt;

	/** 发布人 */
	private String publisher;

	/** 记录状态  */
	private int recstat;

	/** 复核标志 */
	private int checkflag;

	private String creator;

	private String modifier;

	private String checker;

	private String credt;

	private String moddt;

	/** 所属模块 */
	private String moduleFlag;

	/** 公告所属类型 */
	private int noticeType;

	/** 复核时间 */
	private String checkdt;

	public String getSubject() {
		return this.subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getValidatedt() {
		return this.validatedt;
	}

	public void setValidatedt(String validatedt) {
		this.validatedt = validatedt;
	}

	public String getInvalidatedt() {
		return this.invalidatedt;
	}

	public void setInvalidatedt(String invalidatedt) {
		this.invalidatedt = invalidatedt;
	}

	public String getPublisher() {
		return this.publisher;
	}

	public void setPublisher(String publisher) {
		this.publisher = publisher;
	}

	public int getRecstat() {
		return recstat;
	}

	public void setRecstat(int recstat) {
		this.recstat = recstat;
	}

	public int getCheckflag() {
		return checkflag;
	}

	public void setCheckflag(int checkflag) {
		this.checkflag = checkflag;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public String getCredt() {
		return this.credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return this.moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public long getAnnouncedid() {
		return announcedid;
	}

	public void setAnnouncedid(long announcedid) {
		this.announcedid = announcedid;
	}

	public String getModuleFlag() {
		return moduleFlag;
	}

	public void setModuleFlag(String moduleFlag) {
		this.moduleFlag = moduleFlag;
	}

	public int getNoticeType() {
		return noticeType;
	}

	public void setNoticeType(int noticeType) {
		this.noticeType = noticeType;
	}

	public String getCheckdt() {
		return checkdt;
	}

	public void setCheckdt(String checkdt) {
		this.checkdt = checkdt;
	}
	
	

}
