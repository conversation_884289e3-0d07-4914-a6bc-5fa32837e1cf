package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.util.List;

/**
 * 预约单查询Vo
 * <AUTHOR>
 * NOTICE: 替换 {mybatis :id=listPrebookproductinfoByPage} 参数
 */
public class PreBookWithSaleControlVo extends PreBookProductVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 预约单是否在期间内  0-否 1-是s
	 */
    private String intoControl;

	/**
	 * 额度占用查询-占用方式occupyType	 *
	 */
	private String occupyType;


	/**
	 * 占位状态：0-无需占位，1-未占位，2-已占位
	 */
	private String  occupyStatus;



	/**
	 * 发放名额 状态： 0-未发送  1-已发送  2-发送后取消
	 */
	private String sendQuota;
	
	/**
	 * 是否定投  0-否 1-是s
	 */
	private String fixedflag;

	/**
	 * 产品广度信息
	 */
	private String topCpData;

	/**
	 * 母子基金代码列表
	 */
	private List<String> feederFundCodeList;

	/**
	 * 上报TA状态上报状态 0-未上报、1-上报中、2-上报成功、3-需重新上报、4-撤回上报、5-无需上报
	 */
	private String submitStatus;

	/**
	 * 香港交易账号
	 */
	private String hkTxAcctNo;

	/**
	 * 销售机构(产品分销渠道)
	 */
	private String xsjg;

	public List<String> getFeederFundCodeList() {
		return feederFundCodeList;
	}

	public void setFeederFundCodeList(List<String> feederFundCodeList) {
		this.feederFundCodeList = feederFundCodeList;
	}

	public String getIntoControl() {
		return intoControl;
	}

	public void setIntoControl(String intoControl) {
		this.intoControl = intoControl;
	}

	public String getOccupyType() {
		return occupyType;
	}


	public void setOccupyType(String occupyType) {
		this.occupyType = occupyType;
	}



	public String getSendQuota() {
		return sendQuota;
	}

	public void setSendQuota(String sendQuota) {
		this.sendQuota = sendQuota;
	}

	public String getFixedflag() {
		return fixedflag;
	}

	public void setFixedflag(String fixedflag) {
		this.fixedflag = fixedflag;
	}

	public String getOccupyStatus() {
		return occupyStatus;
	}

	public void setOccupyStatus(String occupyStatus) {
		this.occupyStatus = occupyStatus;
	}

	public String getTopCpData() {
		return topCpData;
	}

	public void setTopCpData(String topCpData) {
		this.topCpData = topCpData;
	}

	public String getSubmitStatus() {
		return submitStatus;
	}

	public void setSubmitStatus(String submitStatus) {
		this.submitStatus = submitStatus;
	}

	public String getHkTxAcctNo() {
		return hkTxAcctNo;
	}

	public void setHkTxAcctNo(String hkTxAcctNo) {
		this.hkTxAcctNo = hkTxAcctNo;
	}

	public String getXsjg() {
		return xsjg;
	}

	public void setXsjg(String xsjg) {
		this.xsjg = xsjg;
	}
}
