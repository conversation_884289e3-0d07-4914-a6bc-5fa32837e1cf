package com.howbuy.crm.hb.domain.doubletrade;

import com.alibaba.fastjson.JSON;
import crm.howbuy.base.utils.StringUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 双录质检接口返回数据的实体
 *
 * 示例
 * { "code": 0, "msg": "ok", "data": { "asrsl": [ { "fileid": "1c7fa828-1375-4cc8-bcd8-2b614c075ba0.wav", "filename": "20211231151747_1179119302_何佩玲_SNL688_1780612_26.wav", "is_legal": 0, "legal_result": { "0": {"ts": -1, "reply": "", "offset": -1, "is_interrupt": false },"1": [], "2": [{ "ts": "02:13", "q_idx": 6, "reply": "客户未确认资产证明等真实有效", "offset": 608 } ],"3": [] },"origin_asr_raw": "嗯嗯嗯嗯你好你好，请问是何⼥⼠吗？喂喂你好，我是豪迈客户服务中⼼售后部员⼯，根据私募投资基⾦募集⾏为管理办法的相关规 "modify_asr_raw": "嗯嗯嗯嗯你好你好，请问是何⼥⼠吗？喂喂你好，我是好买客户服务中⼼售后部员⼯，根据私募投资基⾦募集⾏为管理办法的相关规 },{ "fileid": "f37a243f-a95c-46d4-b398-9d6c72a50c8a.wav", "filename": "20211201155905_1177896335_⽅孝秋_M06548_1709054_37.wav", "is_legal": 0, "legal_result": { "0": {
 * "ts": -1, "reply": "", "offset": -1, "is_interrupt": false },"1": [], "2": [{ "ts": "00:27", "q_idx": 0, "reply": "客户未确认产品购买信息或证件信息", "offset": 112 } ],"3": [] },"origin_asr_raw": "嗯他就他要想着这样的。喂你好，是⽅秀邱先⽣对吗？对。您好，这边市场买进客户服务中⼼⾸批员⼯信封，根据私募投资募资⾦管 "modify_asr_raw": "嗯他就他要想着这样的。喂你好，是⽅秀邱先⽣对吗？对。您好，这边市场买进客户服务中⼼⾸批员⼯信封，根据私募投资募资⾦管 },{ "fileid": "18691756-969f-430e-88cf-65217de49237.wav", "filename": "20211208120638_1179245155_潘涌_M06548_1735858_83.wav", "is_legal": 1, "legal_result": { "0": {"ts": -1, "reply": "", "offset": -1, "is_interrupt": false },"1": [], "2": [], "3": [] },"origin_asr_raw": "嗯嗯嗯嗯嗯嗯嗯嗯嗯你好。唉你好你好，请问是潘勇先⽣吗？啊对啊嗯你好。我是豪豪买卖客户服务中⼼售后部员⼯，根据中天、期 "modify_asr_raw": "嗯嗯嗯嗯嗯嗯嗯嗯嗯你好。唉你好你好，请问是潘涌先⽣吗？啊对啊嗯你好。我是豪豪买卖客户服务中⼼售后部员⼯，根据中天、期 } ] } }
 *
 * @date 2022/10/10 16:26
 */
@Data
public class DtQualityResult implements Serializable {
    private static final long serialVersionUID = 1L;

    private String code;

    private String msg;

    private Data data;

    @lombok.Data
    public static class Data{
        private List<AsrSl> asrsl;
    }

    @lombok.Data
    public static class AsrSl{

        private String fileId;

        private String fileName;

        /**
         * 是否合规 1-合规 0-不合格
         */
        private String is_legal;

        /**
         * 质检结果明细
         */
        private Map<String, String> legal_result;

        /**
         * 质检结果明细
         */
        private List<LegalResult> legalResultList;

        /**
         * 原始文本
         */
        private String origin_asr_raw;

    }

    @lombok.Data
    public static class LegalResult{

        /**
         * 发生时间点
         */
        private String ts;

        /**
         * 原因
         */
        private String reply;

        /**
         * 类型
         */
        private String typeText;
    }

    public boolean isSuccess(){
        return "0".equals(this.getCode()) && this.getData() != null;
    }

    public List<LegalResult> getLegalResultList(AsrSl asrSl){
        List<LegalResult> legalResultList = asrSl.getLegalResultList();
        if(legalResultList != null){
            return legalResultList;
        }
        legalResultList = new ArrayList<>();
        asrSl.setLegalResultList(legalResultList);
        Map<String, String> legalResultMap = asrSl.legal_result;
        String field0 = legalResultMap.get("0");
        String field1 = legalResultMap.get("1");
        String field2 = legalResultMap.get("2");
        String field3 = legalResultMap.get("3");
        String field4 = legalResultMap.get("4");
        if(field0 != null){
            LegalResult legalResult = JSON.parseObject(field0, LegalResult.class);
            if(StringUtil.isNotNullStr(legalResult.reply)) {
                legalResult.typeText = "回访电话中断";
                legalResultList.add(legalResult);
            }
        }
        if(field1 != null){
            setLegalResultList(field1, legalResultList, "客服遗漏回访信息");
        }
        if(field2 != null){
            setLegalResultList(field2, legalResultList, "客户没有明确确认");
        }
        if(field3 != null){
            setLegalResultList(field3, legalResultList, "禁词");
        }
        if(field4 != null){
        	LegalResult legalResult = new LegalResult();
        	legalResult.setTs("00:00");
        	legalResult.setTypeText("信托/非信托");
        	legalResult.setReply(field4);
        	legalResultList.add(legalResult);
        }
        return legalResultList;
    }

    public static String getReplyStr(List<LegalResult> legalResults){
        StringBuilder stringBuilder = new StringBuilder();
        if(legalResults != null) {
            for (LegalResult legalResult : legalResults) {
                stringBuilder.append("," + legalResult.reply);
            }
        }
        return stringBuilder.length() > 1 ? stringBuilder.substring(1) : "";
    }

    private void setLegalResultList(String fieldStr, List<LegalResult> list, String text){
        List<LegalResult> legalResultList1 = JSON.parseArray(fieldStr, LegalResult.class);
        for(LegalResult legalResult : legalResultList1){
            if(StringUtil.isNotNullStr(legalResult.reply)) {
                legalResult.setTypeText(text);
                list.add(legalResult);
            }
        }
    }


}
