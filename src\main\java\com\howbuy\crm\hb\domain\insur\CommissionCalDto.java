package com.howbuy.crm.hb.domain.insur;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: (佣金明细 计算 对象)
 * <AUTHOR>
 * @date 2024/9/26 15:33
 * @since JDK 1.8
 */

/**
 * 创新产品预约佣金拆单明细表
 */
@Data
public class CommissionCalDto {

    /**
    * 佣金明细表ID
    */
    private BigDecimal commissionId;


    /**
    * 应收佣金-内
    */
    private BigDecimal gatherCommIn;

    /**
    * 应收佣金-外
    */
    private BigDecimal gatherCommOut;


    /**
    * 实收佣金
    */
    private BigDecimal realCommission;


    /**
     * 结算汇率
     */
    private BigDecimal settleRate;


    /**
    * 应收佣金-内(结算外币)
    */
    private BigDecimal settleGatherCommIn;

    /**
    * 应收佣金-外(结算外币)
    */
    private BigDecimal settleGatherCommOut;

    /**
    * 实收佣金(结算外币)
    */
    private BigDecimal settleRealCommission;


    /**
     * 佣金率 占比
     */
    private BigDecimal percentNum;


}