package com.howbuy.crm.hb.domain.custinfo;


import lombok.Data;
import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 实体类CmCustconstanthis.java
 * @created
 */
@Data
public class CmCustconstanthis implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String custconshisid;

    private String custno;

    private String conscode;

    private String startdt;
    
    private String enddt;

    private String memo;

    private String recstat;
    
    private String checkflag;

    private String creator;

    private String modifier;

    private String checker;

    private String credt;

    private String moddt;

    private String pcustid;
    
    private Date binddate;
    
    private String beforehisid;
    
    private Date unbunddate;

}
