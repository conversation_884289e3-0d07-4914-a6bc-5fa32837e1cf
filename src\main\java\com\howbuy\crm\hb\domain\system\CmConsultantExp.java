package com.howbuy.crm.hb.domain.system;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CmConsultantExp implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "员工编码")
    private String conscode;
    @ExcelProperty(value = "姓名")
    private String consname;
    /**
     * 员工状态
     */
    @ExcelProperty(value = "员工状态")
    private String worktype;

    /**
     * 在职状态
     */
    @ExcelProperty(value = "在职状态")
    private String workstate;
    @ExcelProperty(value = "工号")
    private String userno;
    @ExcelProperty(value = "电子邮箱")
    private String email;
    private String mobile;
    @ExcelProperty(value = "所属中心")
    private String centerName;
    /**
     * 业务中心
     */
    @ExcelProperty(value = "业务中心")
    private String centerOrg;
    private String areacode;
    @ExcelProperty(value = "区域")
    private String areaname;
    /**
     * 区域总
     */
    @ExcelProperty(value = "区域总")
    private String arealeader;
    /**
     * 区域副总
     */
    @ExcelProperty(value = "区域执行副总")
    private String areafleader;
    private String provcode;
    private String citycode;
    @ExcelProperty(value = "省市区")
    private String cityname;
    private String outletcode;
    @ExcelProperty(value = "分公司")
    private String orgname;
    /**
     * 分总
     */
    @ExcelProperty(value = "分总")
    private String outletleader;

    private String teamcode;
    @ExcelProperty(value = "所属团队")
    private String teamname;
    /**
     * 团队长
     */
    @ExcelProperty(value = "团队长")
    private String teamleader;
    @ExcelProperty(value = "层级")
    private String userlevel;
    @ExcelProperty(value = "当月职级")
    private String curmonthlevel;
    /**
     * 当月薪资
     */
    private BigDecimal curmonthsalary;
    @ExcelProperty(value = "当月薪资")
    private String curmonthsalaryStr;
    /**
     * 副职
     */
    @ExcelProperty(value = "副职")
    private String subpositions;
    @ExcelProperty(value = "入职日期")
    private String startdt;
    @ExcelProperty(value = "入职职级")
    private String startlevel;
    /**
     * 入职档级
     */
    @ExcelProperty(value = "入职档级")
    private String joinRank;
    private BigDecimal salary;
    @ExcelProperty(value = "入职薪资")
    private String salaryStr;
    private String telno;
    /**
     * 入职社保基数
     */
    @ExcelProperty(value = "入职社保基数")
    private String joinSsb;
    /**
     * 试用截止日期
     */
    @ExcelProperty(value = "试用截止日期")
    private String probationenddt;
    private String userid;
    /**
     * 下次考核周期
     */
    @ExcelProperty(value = "下次考核周期")
    private String nextTestPeriod;
    /**
     * 下次考核截止日期
     */
    @ExcelProperty(value = "下次考核截止日期")
    private String nexttestdate;
    /**
     * 创新方案
     */
    @ExcelProperty(value = "创新方案")
    private String bxcommissionway;
    /**
     *3M日期
     */
    @ExcelProperty(value = "3M日期")
    private String dt3m;
    /**
     * 试用3M考核结果
     */
    @ExcelProperty(value = "试用3M考核结果")
    private String probationresult3m;
    /**
     * 试用3M考核职级
     */
    @ExcelProperty(value = "试用3M考核职级")
    private String probationLevel3m;
    /**
     * 试用3M档级
     */
    @ExcelProperty(value = "试用3M档级")
    private String probationRank3m;
    /**
     * 试用3M薪资
     */
    private BigDecimal probationSalary3m;
    @ExcelProperty(value = "试用3M薪资")
    private String probationSalary3mStr;
    /**
     * 试用3M社保基数
     */
    @ExcelProperty(value = "试用3M社保基数")
    private String probationSsb3m;
    /**
     * 转正日期
     */
    @ExcelProperty(value = "转正日期")
    private String regulardt;
    /**
     * 试用6M考核结果
     */
    @ExcelProperty(value = "转正结果")
    private String probationresult6m;
    /**
     * 转正职级
     */
    @ExcelProperty(value = "转正职级")
    private String regularlevel;
    /**
     * 转正档级
     */
    @ExcelProperty(value = "转正档级")
    private String regularRank;
    /**
     * 转正薪资
     */
    private BigDecimal regularsalary;
    @ExcelProperty(value = "转正薪资")
    private String regularsalaryStr;
    /**
     * 转正社保基数
     */
    @ExcelProperty(value = "转正社保基数")
    private String regularSsb;
    /**
     *12M日期
     */
    @ExcelProperty(value = "12M日期")
    private String dt12m;
    /**
     * 12M考核结果
     */
    @ExcelProperty(value = "12M考核结果")
    private String probationResult12m;
    /**
     * 12M考核职级
     */
    @ExcelProperty(value = "12M考核职级")
    private String testLevel12m;
    /**
     * 12M档级
     */
    @ExcelProperty(value = "12M档级")
    private String rank12m;
    /**
     * 12M薪资
     */
    private BigDecimal salary12m;
    @ExcelProperty(value = "12M薪资")
    private String salary12mStr;
    /**
     * 12M社保基数
     */
    @ExcelProperty(value = "12M社保基数")
    private String ssb12m;
    /**
     * 晋升日期
     */
    @ExcelProperty(value = "管理日期")
    private String promotedate;
    @ExcelProperty(value = "好买司龄(月)")
    private BigDecimal servingage;
    /**
     * 调整司龄(月)
     */
    @ExcelProperty(value = "调整司龄(月)")
    private String adjustServingMonth;
    /**
     * 调整管理司龄(月)
     */
    @ExcelProperty(value = "调整管理司龄(月)")
    private String adjustManageServingMonth;
    /**
     * 离职日期
     */
    @ExcelProperty(value = "离职日期")
    private String quitdt;
    /**
     * 离职职级
     */
    @ExcelProperty(value = "离职职级")
    private String quitlevel;
    /**
     * 离职薪资
     */
    private BigDecimal quitsalary;
    @ExcelProperty(value = "离职薪资")
    private String quitsalaryStr;
    /**
     * 离职原因
     */
    @ExcelProperty(value = "离职原因")
    private String quitreason;
    /**
     * 离职去向
     */
    @ExcelProperty(value = "离职去向")
    private String quitInfo;
    /**
     * 基金从业资格编码
     */
    @ExcelProperty(value = "基金从业资格编码")
    private String jjcardno;
    /**
     * 是否挂靠
     */
    @ExcelProperty(value = "是否挂靠")
    private String attachtype;
    /**
     * 背景
     */
    @ExcelProperty(value = "上家公司")
    private String background;
    /**
     * 来源
     */
    @ExcelProperty(value = "背景来源")
    private String source;
    @ExcelProperty(value = "性别")
    private String gender;
    @ExcelProperty(value = "出生日期")
    private String birthday;
    @ExcelProperty(value = "学历")
    private String edulevel;
    @ExcelProperty(value = "年龄")
    private String age;
    /**
     * 入职好买前职位类型
     */
    @ExcelProperty(value = "入职好买前职位类型")
    private String beforepositiontype;
    /**
     * 入职好买前工作年限
     */
    @ExcelProperty(value = "上家工作月份数")
    private String beforepositionage;
    /**
     * 招聘经理
     */
    @ExcelProperty(value = "招聘经理")
    private String recruit;
    /**
     * 推荐人
     */
    @ExcelProperty(value = "推荐人")
    private String recommend;
    /**
     * 推荐人工号
     */
    @ExcelProperty(value = "推荐人工号")
    private String recommenduserno;
    /**
     * 推荐类型
     */
    @ExcelProperty(value = "招聘渠道")
    private String recommendtype;
    /**
     * 在途备注
     */
    @ExcelProperty(value = "在途备注")
    private String inTransitRemark;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    private String checkflag;
    @ExcelProperty(value = "审核状态")
    private String checkflagval;
    @ExcelProperty(value = "最近修改人")
    private String modor;
    /**
     * 北森ID
     */
    @ExcelProperty(value = "北森ID")
    private String beisenid;

    /**
     * 转正日期人工修改标识1是0否
     */
    private String regulardtFlag;
    private String checkor;
    private String creator;
    private Date creatdt;
    private String ssOrgInfos;
    /**
     * 下次考核截止日期人工修改标识1是0否
     */
    private String nexttestdateFlag;
    /**
     * 当月职级info
     *
     * @return
     */
    private String curmonthlevelinfo;










    /**
     * 用于回显机构代码
     */
    private String orgCode;
    /**
     * 修改日期
     */
    private Date moddt;




    /**
     *3M日期人工修改标识1是0否
     */
    private String dt3mFlag;

    /**
     *12M日期人工修改标识1是0否
     */
    private String dt12mFlag;





    /**
     * 区code
     */
    private String countyCode;


}
