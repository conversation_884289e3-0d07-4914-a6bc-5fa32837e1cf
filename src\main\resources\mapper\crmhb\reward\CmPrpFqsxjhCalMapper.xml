<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.reward.CmPrpFqsxjhCalMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>
	<insert id="insertCmPrpFqsxjhCal" parameterType="CmPrpFqsxjhCal">
		INSERT INTO CM_PRP_FQSXJH_CAL (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="quarter != null">quarter,</if>
			<if test="preid != null">preid,</if>
			<if test="conscode != null">conscode,</if>
			<if test="accountProductType != null">account_Product_Type,</if>
			<if test="ackAmtRmb != null">ack_Amt_Rmb,</if>
			<if test="subscribeAmtRmb != null">subscribe_Amt_Rmb,</if>
			<if test="ackFeeRmb != null">ack_Fee_Rmb,</if>
			<if test="discountFee != null">discount_Fee,</if>
			<if test="adjustDiscountFee != null">adjust_Discount_Fee,</if>
			<if test="discountType != null">discount_Type,</if>
			<if test="discountReason != null">discount_Reason,</if>
			<if test="tradeNum != null">trade_Num,</if>
			<if test="sourceType != null">source_Type,</if>
			<if test="sourceCoeff != null">source_Coeff,</if>
			<if test="foldCoeff != null">fold_Coeff,</if>
			<if test="commissionRate != null">commission_Rate,</if>
			<if test="foldPayamtRmb != null">fold_Payamt_Rmb,</if>
			<if test="quarterTotalAmtRmb != null">quarter_Total_Amt_Rmb,</if>
			<if test="unqualifiedCoeff != null">unqualified_Coeff,</if>
			<if test="achieveCoeff != null">achieve_Coeff,</if>
			<if test="extraCoeff != null">extra_Coeff,</if>
			<if test="totalAchieve != null">total_Achieve,</if>
			<if test="creator != null">creator,</if>
			<if test="modor != null">modor,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="expecttradedt != null">expecttradedt,</if>
			<if test="paystate != null">paystate,</if>
		</trim>
		) VALUES (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="quarter != null">#{quarter},</if>
			<if test="preid != null">#{preid},</if>
			<if test="conscode != null">#{conscode},</if>
			<if test="accountProductType != null">#{accountProductType},</if>
			<if test="ackAmtRmb != null">#{ack_Amt_Rmb},</if>
			<if test="subscribeAmtRmb != null">#{subscribeAmtRmb},</if>
			<if test="ackFeeRmb != null">#{ackFeeRmb},</if>
			<if test="discountFee != null">#{discountFee},</if>
			<if test="adjustDiscountFee != null">#{adjustDiscountFee},</if>
			<if test="discountType != null">#{discountType},</if>
			<if test="discountReason != null">#{discountReason},</if>
			<if test="tradeNum != null">#{tradeNum},</if>
			<if test="sourceType != null">#{sourceType},</if>
			<if test="sourceCoeff != null">#{sourceCoeff},</if>
			<if test="foldCoeff != null">#{foldCoeff},</if>
			<if test="commissionRate != null">#{commissionRate},</if>
			<if test="foldPayamtRmb != null">#{foldPayamtRmb},</if>
			<if test="quarterTotalAmtRmb != null">#{quarterTotalAmtRmb},</if>
			<if test="unqualifiedCoeff != null">#{unqualifiedCoeff},</if>
			<if test="achieveCoeff != null">#{achieveCoeff},</if>
			<if test="extraCoeff != null">#{extraCoeff},</if>
			<if test="totalAchieve != null">#{totalAchieve},</if>
			<if test="creator != null">#{creator},</if>
			<if test="modor != null">#{modor},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="expecttradedt != null">#{expecttradedt},</if>
			<if test="paystate != null">#{paystate},</if>
		</trim>
		)
	</insert>
	
	<update id="updateCmPrpFqsxjhCal" parameterType="CmPrpFqsxjhCal" >
	    UPDATE CM_PRP_FQSXJH_CAL	    
	    <set>        
        	<if test="quarter != null">quarter=#{quarter},</if>
			<if test="preid != null">preid=#{preid},</if>
			<if test="conscode != null">conscode=#{conscode},</if>
			<if test="accountProductType != null">account_Product_Type=#{accountProductType},</if>
			<if test="ackAmtRmb != null">ack_Amt_Rmb=#{ackAmtRmb},</if>
			<if test="subscribeAmtRmb != null">subscribe_Amt_Rmb=#{subscribeAmtRmb},</if>
			<if test="ackFeeRmb != null">ack_Fee_Rmb=#{ackFeeRmb},</if>
			<if test="discountFee != null">discount_Fee=#{discountFee},</if>
			<if test="adjustDiscountFee != null">adjust_Discount_Fee=#{adjustDiscountFee},</if>
			<if test="discountType != null">discount_Type=#{discountType},</if>
			<if test="discountReason != null">discount_Reason=#{discountReason},</if>
			<if test="tradeNum != null">trade_Num=#{tradeNum},</if>
			<if test="sourceType != null">source_Type=#{sourceType},</if>
			<if test="sourceCoeff != null">source_Coeff=#{sourceCoeff},</if>
			<if test="foldCoeff != null">fold_Coeff=#{foldCoeff},</if>
			<if test="commissionRate != null">commission_Rate=#{commissionRate},</if>
			<if test="foldPayamtRmb != null">fold_Payamt_Rmb=#{foldPayamtRmb},</if>
			<if test="quarterTotalAmtRmb != null">quarter_Total_Amt_Rmb=#{quarterTotalAmtRmb},</if>
			<if test="unqualifiedCoeff != null">unqualified_Coeff=#{unqualifiedCoeff},</if>
			<if test="achieveCoeff != null">achieve_Coeff=#{achieveCoeff},</if>
			<if test="extraCoeff != null">extra_Coeff=#{extraCoeff},</if>
			<if test="totalAchieve != null">total_Achieve=#{totalAchieve},</if>
			<if test="creator != null">creator=#{creator},</if>
			<if test="modor != null">modor=#{modor},</if>    
			<if test="expecttradedt != null">expecttradedt=#{expecttradedt},</if>
			<if test="paystate != null">paystate = #{paystate},</if>
			update_time = sysdate,
         </set>
          where id = #{id}
	  </update>
	  
	  <delete id="delCmPrpFqsxjhCal" parameterType="BigDecimal">
	    delete from CM_PRP_FQSXJH_CAL where id = #{id}
	  </delete>
	  
	  <select id="listCmPrpFqsxjhCalByPage" parameterType="Map" resultType="CmPrpFqsxjhCal" useCache="false">
	    SELECT id id,
				quarter quarter,
				preid preid,
				conscode conscode,
				account_product_type accountProductType,
				ack_amt_rmb ackAmtRmb,
				subscribe_amt_rmb subscribeAmtRmb,
				ack_fee_rmb ackFeeRmb,
				discount_fee discountFee,
				adjust_discount_fee adjustDiscountFee,
				discount_type discountType,
				discount_reason discountReason,
				trade_num tradeNum,
				source_type sourceType,
				source_coeff sourceCoeff,
				fold_coeff foldCoeff,
				commission_rate commissionRate,
				fold_payamt_rmb foldPayamtRmb,
				quarter_total_amt_rmb quarterTotalAmtRmb,
				unqualified_coeff unqualifiedCoeff,
				achieve_coeff achieveCoeff,
				extra_coeff extraCoeff,
				total_achieve totalAchieve,
				creator creator,
				create_time createTime,
				modor modor,
				update_time updateTime
		 FROM CM_PRP_FQSXJH_CAL 
	    where 1=1   
            <if test="param.id != null"> AND id = #{param.id} </if>    
            <if test="param.accountProductType != null"> AND account_product_type = #{param.accountProductType} </if>             
         order by create_time desc
      </select>
      
      <select id="getCmPrpFqsxjhCal" parameterType="Map" resultType="CmPrpFqsxjhCal" useCache="false">
	    SELECT id id,
				quarter quarter,
				preid preid,
				conscode conscode,
				account_product_type accountProductType,
				ack_amt_rmb ackAmtRmb,
				subscribe_amt_rmb subscribeAmtRmb,
				ack_fee_rmb ackFeeRmb,
				discount_fee discountFee,
				adjust_discount_fee adjustDiscountFee,
				discount_type discountType,
				discount_reason discountReason,
				trade_num tradeNum,
				source_type sourceType,
				source_coeff sourceCoeff,
				fold_coeff foldCoeff,
				commission_rate commissionRate,
				fold_payamt_rmb foldPayamtRmb,
				quarter_total_amt_rmb quarterTotalAmtRmb,
				unqualified_coeff unqualifiedCoeff,
				achieve_coeff achieveCoeff,
				extra_coeff extraCoeff,
				total_achieve totalAchieve,
				creator creator,
				create_time createTime,
				modor modor,
				update_time updateTime
		 FROM CM_PRP_FQSXJH_CAL
	     where 1=1  
         <if test="id != null"> AND id = #{id} </if>  
         <if test="accountProductType != null"> AND account_product_type = #{accountProductType} </if>                            
  	  </select>

	<!--核算25年前-->
      <select id="listCmPrpFqsxjhCalQuery" parameterType="Map" resultType="CmPrpFqsxjhCal" useCache="false">
	    SELECT T.PREID,
		       T.TRADE_DT TRADEDT,
		       DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) ACCOUNTDT,
		       T2.CONSCUSTNO,
		       T2.CONSCUSTNAME CUSTNAME,
		       T2.CREATOR CONSCODE,
		       T2.PCODE FUNDCODE,
		       JJXX.JJJC FUNDNAME,
		       T5.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
		       T.REALPAY_AMT_RMB ACKAMTRMB,
		       T.FEE_RMB ACKFEERMB,
		       CASE
		         WHEN (T3.DISCOUNT_TYPE = '5' AND T3.DISCOUNT_STATE IN ('3', '5')) OR
		              (T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') OR
		              (T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')) THEN
		          T3.DISCOUNT_TYPE
		         ELSE
		          CASE
		            WHEN T3.DISCOUNT_TYPE IS NOT NULL THEN
		             '4'
		            ELSE
		             ''
		          END
		       END DISCOUNTTYPE,
		       CASE
		         WHEN T2.CURRENCY != '156' THEN
		          NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT) * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
		         ELSE
		          NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT)
		       END DISCOUNTFEE,
		       CASE
		         WHEN T2.CURRENCY != '156' THEN
		          T3.BEF_TAX_AMT_ADJUST * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
		         ELSE
		          T3.BEF_TAX_AMT_ADJUST
		       END ADJUSTDISCOUNTFEE,
		       T3.DISCOUNT_REASON DISCOUNTREASON,
		       T.TRADE_NUM TRADENUM,
		       T2.SOURCETYPE,
		       CASE
				  WHEN T3.ID IS NOT NULL AND ((T3.DISCOUNT_TYPE = '6' and T3.DISCOUNT_STATE = '5')
				  								or (T3.DISCOUNT_TYPE = '8' and T3.DISCOUNT_STATE IN ('3', '5'))) THEN
		          1
		         ELSE
		          (CASE
		            WHEN T.MANYCALL_FLAG = '2' THEN
		             NVL(CFCOEFF.SOURCECOEFF, 1)
		            ELSE
		             NVL(T1.CUST_SORCE_COEFF, T7.SOURCE_COEFF)
		          END)
		       END AS SOURCECOEFF,
		       NVL(T1.COMMISSION_RATE,
		           (CASE
		             WHEN T.MANYCALL_FLAG = '2' THEN
		              CFCOEFF.COMMISSIONRATE
		             ELSE
		              (CASE
		                WHEN (T3.DISCOUNT_TYPE = '5' AND T3.DISCOUNT_STATE IN ('3', '5')) OR
		                     (T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') OR
		                     (T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')) THEN
		                 (CASE
		                   WHEN T3.DISCOUNT_TYPE = '5' THEN
		                    NVL(ORDERCOEFF.COMMISSION_RATE, 0)
		                   WHEN T3.DISCOUNT_TYPE = '6' THEN
		                    NVL(ORDERCOEFF.COMMISSION_RATE, 0)
		                   WHEN T3.DISCOUNT_TYPE = '8' THEN
		                    NVL(ORDERCOEFF.COMMISSION_RATE, 0.1)
		                   ELSE
		                    NULL
		                 END)
		                ELSE
		                 T5.COMMISSION_RATE
		              END)
		           END)) AS COMMISSIONRATE,
		       T1.MANAGE_COEFF manageCoeff,
			   T1.MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
			   T1.MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
			   CASE WHEN t.MANYCALL_FLAG = '2' AND T2.creator != firstPre.creator then '1'
			  		else '0' end
			   as tansConsManycall,
			  DECODE(T1.ACCOUNT_DT, NULL, NULL, '1') accountDtFlag,
			  DECODE(T1.COMMISSION_RATE, NULL, NULL, '1') commissionRateFlag,
			  T.SUBSCRIBE_AMT_RMB subscribeAmtRmb,
			  T.REALPREID realpreId,
			  T2.EXPECTTRADEDT expectTradeDt,
			  T2.PAYSTATE payState
		  FROM CM_PRP_TRADE_NUM T
		  LEFT JOIN CM_PRP_PREID_EXP_COEFF T1
		    ON T.PREID = T1.PREID
		  LEFT JOIN CM_PREBOOKPRODUCTINFO T2
		    ON T.REALPREID = T2.ID
		  LEFT JOIN CM_DISCOUNTAPP T3
		    ON T.PREID = T3.PREBOOKID
		   AND T3.DISCOUNT_STATE != '7'
		  left join CM_PREBOOKPRODUCTINFO firstPre
		  on T.REALPREID = firstPre.ID
		  LEFT JOIN CM_CONSULTANT T4
		    ON T2.CREATOR = T4.CONSCODE
		  LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT T5
		    ON T2.PCODE = T5.FUNDCODE
		   AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) >= T5.START_DT
		   AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) &lt;= T5.End_Dt
		  LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
		    ON T2.SOURCETYPE = CPCSC.SOURCE_TYPE
		   AND T.TRADE_DT >= CPCSC.START_DT
		   AND T.TRADE_DT &lt;= NVL(CPCSC.END_DT, '********')
		  LEFT JOIN CM_PRP_SOURCE_COEFFICIENT T7
		    ON CPCSC.START_POINT = T7.START_POINT
		   AND DECODE(T.TRADE_NUM, 0, '0', 1, '1', 2, '2', 3, '3', 4, '4', '99') =
		       T7.TRADE_NUM
		  LEFT JOIN JJXX1 JJXX
		    ON T2.PCODE = JJXX.JJDM
		  LEFT JOIN RMBHLZJJ RMBZJJ
		    ON T2.CURRENCY = RMBZJJ.DHBZ
		   AND T.TRADE_DT = RMBZJJ.JZRQ
		   AND T2.CURRENCY != '156'
		  LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
		    ON T3.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
		   AND T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
		   AND T.TRADE_DT >= ORDERCOEFF.START_DT
		   AND T.TRADE_DT &lt;= NVL(ORDERCOEFF.END_DT, '********')
		   AND ORDERCOEFF.OLD_FOLD_COEFF = T5.FOLD_COEFFICIENT
		   AND ORDERCOEFF.OLD_COMMISSION_RATE = T5.COMMISSION_RATE
		  LEFT JOIN (SELECT PREBOOK.ID,
		                    NVL(EXPCOEFF.COMMISSION_RATE,
		                        (CASE
		                          WHEN (DISCOUNT.DISCOUNT_TYPE = '5' AND
		                               DISCOUNT.DISCOUNT_STATE IN ('3', '5')) OR
		                               (DISCOUNT.DISCOUNT_TYPE = '6' AND
		                               DISCOUNT.DISCOUNT_STATE = '5') OR
		                               (DISCOUNT.DISCOUNT_TYPE = '8' AND
		                               DISCOUNT.DISCOUNT_STATE IN ('3', '5')) THEN
		                           (CASE
		                             WHEN DISCOUNT.DISCOUNT_TYPE = '5' THEN
		                              NVL(ORDERCOEFF.COMMISSION_RATE, 0)
		                             WHEN DISCOUNT.DISCOUNT_TYPE = '6' THEN
		                              NVL(ORDERCOEFF.COMMISSION_RATE, 0)
		                             WHEN DISCOUNT.DISCOUNT_TYPE = '8' THEN
		                              NVL(ORDERCOEFF.COMMISSION_RATE, 0.1)
		                             ELSE
		                              NULL
		                           END)
		                          ELSE
		                           PRODUCTCOEFF.COMMISSION_RATE
		                        END)) AS COMMISSIONRATE,
		                    NVL(EXPCOEFF.CUST_SORCE_COEFF, CPSC.SOURCE_COEFF) AS SOURCECOEFF,
		                    NVL(EXPCOEFF.MANAGE_COEFF, CPCSC.MANAGE_COEFF) AS MANAGECOEFF
		               FROM CM_PREBOOKPRODUCTINFO PREBOOK
		               LEFT JOIN CM_DISCOUNTAPP DISCOUNT
		                 ON PREBOOK.ID = DISCOUNT.PREBOOKID
		                AND DISCOUNT.DISCOUNT_STATE != '7'
		              INNER JOIN CM_PRP_TRADE_NUM CPTN
		                 ON CPTN.PREID = PREBOOK.ID
		               LEFT JOIN CM_PRP_PREID_EXP_COEFF EXPCOEFF
		                 ON PREBOOK.ID = EXPCOEFF.PREID
		              INNER JOIN CM_CONSULTANT CONS
		                 ON PREBOOK.CREATOR = CONS.CONSCODE
		               LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT PRODUCTCOEFF
		                 ON PREBOOK.PCODE = PRODUCTCOEFF.FUNDCODE
		  				AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) >= PRODUCTCOEFF.START_DT
		  				AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) &lt;= PRODUCTCOEFF.End_Dt
		               LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
		                 ON DISCOUNT.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
		                AND PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE =
		                    ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
		                AND CPTN.TRADE_DT >= ORDERCOEFF.START_DT
		                AND CPTN.TRADE_DT &lt;= NVL(ORDERCOEFF.END_DT, '********')
		                AND ORDERCOEFF.OLD_FOLD_COEFF =
		                    PRODUCTCOEFF.FOLD_COEFFICIENT
		                AND ORDERCOEFF.OLD_COMMISSION_RATE =
		                    PRODUCTCOEFF.COMMISSION_RATE
		               LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
		                 ON PREBOOK.SOURCETYPE = CPCSC.SOURCE_TYPE
		                AND CPTN.TRADE_DT >= CPCSC.START_DT
		                AND CPTN.TRADE_DT &lt;= NVL(CPCSC.END_DT, '********')
		               LEFT JOIN CM_PRP_SOURCE_COEFFICIENT CPSC
		                 ON DECODE(CPTN.TRADE_NUM,
		                           0,
		                           '0',
		                           1,
		                           '1',
		                           2,
		                           '2',
		                           3,
		                           '3',
		                           4,
		                           '4',
		                           '99') = CPSC.TRADE_NUM
		                AND CPSC.START_POINT = CPCSC.START_POINT
		              WHERE CPTN.MANYCALL_FLAG = '1') CFCOEFF
		    ON T.REALPREID = CFCOEFF.ID
		 WHERE (T5.ACCOUNT_PRODUCT_TYPE not in ('8','6') OR T5.ACCOUNT_PRODUCT_TYPE IS NULL)
         <if test="startDate != null"> AND T.TRADE_DT &gt;= #{startDate} </if>
         <if test="endDate != null"> AND T.TRADE_DT &lt;= #{endDate} </if>
         order by T.TRADE_DT DESC
      </select>

	<!--核算-->
	<select id="listCmPrpFqsxjhCalQueryNew" resultType="CmPrpFqsxjhCal" useCache="false">
		<![CDATA[
		SELECT T.PRE_ID,
       T.TRADE_DT TRADEDT,
       DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) ACCOUNTDT,
       T2.CONSCUSTNO,
       T2.CONSCUSTNAME CUSTNAME,
       preAct.CREATOR CONSCODE,
       T2.PCODE FUNDCODE,
       JJXX.JJJC FUNDNAME,
       T5.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
       nvl(T.REALPAY_AMT_RMB, nvl(preAct.realpayamt_rmb, preAct.realpayamt)) ACKAMTRMB,
       CASE
           WHEN preAct.CURRENCY <> '156' THEN
                   preAct.FEE *
                   NVL(RMBZJJ.ZJJ,
                       (SELECT ZZJJ.ZJJ
                        FROM RMBHLZJJ ZZJJ
                        WHERE ZZJJ.DHBZ = preAct.CURRENCY
                          AND ZZJJ.JZRQ IN
                              (SELECT MAX(ZJJ.JZRQ)
                               FROM RMBHLZJJ ZJJ
                               WHERE ZJJ.DHBZ = preAct.CURRENCY
                                 AND ZJJ.JZRQ <= preAct.EXPECTTRADEDT)))
			   ELSE
				   preAct.FEE
			   END ACKFEERMB,
		   CASE
			   WHEN (T3.DISCOUNT_TYPE = '5' AND T3.DISCOUNT_STATE IN ('3', '5')) OR
					(T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') OR
					(T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')) THEN
				   T3.DISCOUNT_TYPE
			   ELSE
				   CASE
					   WHEN T3.DISCOUNT_TYPE IS NOT NULL THEN
						   '4'
					   ELSE
						   ''
					   END
			   END DISCOUNTTYPE,
		   CASE
			   WHEN T2.CURRENCY != '156' THEN
					   NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT) * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
			   ELSE
				   NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT)
			   END DISCOUNTFEE,
		   CASE
			   WHEN T2.CURRENCY != '156' THEN
					   T3.BEF_TAX_AMT_ADJUST * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
			   ELSE
				   T3.BEF_TAX_AMT_ADJUST
			   END ADJUSTDISCOUNTFEE,
		   T3.DISCOUNT_REASON DISCOUNTREASON,
		   T.TRADE_NUM TRADENUM,
		   T2.SOURCETYPE,
		   CASE
			   WHEN T3.ID IS NOT NULL AND ((T3.DISCOUNT_TYPE = '6' and T3.DISCOUNT_STATE = '5')
			   								or (T3.DISCOUNT_TYPE = '8' and T3.DISCOUNT_STATE IN ('3', '5'))) THEN
				   1
			   ELSE
				   (CASE
						WHEN T.MANY_CALL_FLAG = '2' THEN
							NVL(CFCOEFF.SOURCECOEFF, 1)
						ELSE
							NVL(T1.CUST_SORCE_COEFF, T7.SOURCE_COEFF)
					   END)
			   END AS SOURCECOEFF,

		NVL(T1.COMMISSION_RATE,
		(CASE
		WHEN T.MANY_CALL_FLAG = '2' THEN
		-- 后续call预约的佣金率，如果换投顾，则取0.1（********惠真提的）
		(case when CFCOEFF.COMMISSIONRATE is null and (t.REAL_PRE_ID = T.PRE_ID or t.cons_code != T2.creator) then 0.1 else CFCOEFF.COMMISSIONRATE end)
		ELSE
			(CASE
			WHEN
			(T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') THEN
				NVL(ORDERCOEFF.COMMISSION_RATE, 0)
			ELSE
			T5.COMMISSION_RATE
			END)
		END)) AS COMMISSIONRATE,
		T1.MANAGE_COEFF manageCoeff,
		T1.MANAGE_COEFF_REGIONALSUBTOTAL AS manageCoeffRegionalsubtotal,
		T1.MANAGE_COEFF_REGIONALTOTAL AS manageCoeffRegionaltotal,
		CASE WHEN T.MANY_CALL_FLAG = '2' AND (t.REAL_PRE_ID = T.PRE_ID or t.cons_code != T2.creator) then '1'
			 else '0'
		end  as tansConsManycall,
		CASE WHEN t.MANY_CALL_FLAG = '2' AND t.cons_code = T2.creator then T2.id else preAct.id end  as coeffPreId,
		CASE WHEN t.MANY_CALL_FLAG = '2' AND t.cons_code = T2.creator then T2.credt else preAct.credt end as creDt,
		DECODE(T1.ACCOUNT_DT, NULL, NULL, '1') accountDtFlag,
		DECODE(T1.COMMISSION_RATE, NULL, NULL, '1') commissionRateFlag,
		CASE WHEN T2.CURRENCY != '156' THEN
				manycall.TOTALAMT *
						NVL(RMBZJJ.ZJJ,
							(SELECT ZZJJ.ZJJ FROM RMBHLZJJ ZZJJ WHERE ZZJJ.DHBZ = T2.CURRENCY
								AND ZZJJ.JZRQ IN (SELECT MAX(ZJJ.JZRQ) FROM RMBHLZJJ ZJJ WHERE ZJJ.DHBZ = T2.CURRENCY
													AND ZJJ.JZRQ <= T2.EXPECTTRADEDT
												  )
							)
						)
		ELSE
			manycall.TOTALAMT
		END SUBSCRIBE_AMT_RMB,
		T.REAL_PRE_ID realpreId,
		T2.EXPECTTRADEDT expectTradeDt,
		T2.PAYSTATE payState
		FROM CM_TRADE_NUM_SUMMARY T
         LEFT JOIN CM_PRP_PREID_EXP_COEFF T1
                   ON T.PRE_ID = T1.PREID
         LEFT JOIN CM_PREBOOKPRODUCTINFO T2
                   ON T.REAL_PRE_ID = T2.ID
         LEFT JOIN CM_PREBOOKPRODUCTINFO preAct
                   ON T.PRE_ID = preAct.ID
		left join cm_prebook_manycall manycall
				   on T.REAL_PRE_ID = manycall.FIRSTPREID
         LEFT JOIN CM_DISCOUNTAPP T3
                   ON T.PRE_ID = T3.PREBOOKID
                       AND T3.DISCOUNT_STATE != '7'
         LEFT JOIN CM_CONSULTANT T4
                   ON T2.CREATOR = T4.CONSCODE
         LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT T5
                   ON T2.PCODE = T5.FUNDCODE
                       AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) >= T5.START_DT
                       AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) <= T5.End_Dt
         LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
                   ON T2.SOURCETYPE = CPCSC.SOURCE_TYPE
                       AND T.TRADE_DT >= CPCSC.START_DT
                       AND T.TRADE_DT <= NVL(CPCSC.END_DT, '********')
         LEFT JOIN CM_PRP_SOURCE_COEFFICIENT T7
                   ON CPCSC.START_POINT = T7.START_POINT
                       AND DECODE(T.TRADE_NUM, 0, '0', 1, '1', 2, '2', 3, '3', 4, '4', '99') =
                           T7.TRADE_NUM
         LEFT JOIN JJXX1 JJXX
                   ON T2.PCODE = JJXX.JJDM
         LEFT JOIN RMBHLZJJ RMBZJJ
                   ON T2.CURRENCY = RMBZJJ.DHBZ
                       AND T.TRADE_DT = RMBZJJ.JZRQ
                       AND T2.CURRENCY != '156'
         LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
                   ON T3.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
                       AND T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
                		and T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.Big_Order_Product_Type
                       AND T.TRADE_DT >= ORDERCOEFF.START_DT
                       AND T.TRADE_DT <= NVL(ORDERCOEFF.END_DT, '********')
                       and t5.STOCK_FEE_A >= nvl(ORDERCOEFF.stock_fee_d_min,0) and T5.STOCK_FEE_A < nvl(ORDERCOEFF.stock_fee_d_max,10000)
         LEFT JOIN (SELECT PREBOOK.ID,
                           NVL(EXPCOEFF.UNQUALIFIED_COEFF, 1) AS UNQUALIFIEDCOEFF,
                           NVL(EXPCOEFF.COMMISSION_RATE,
								(CASE
								WHEN
								(DISCOUNT.DISCOUNT_TYPE = '6' AND
									DISCOUNT.DISCOUNT_STATE = '5')  THEN
								NVL(ORDERCOEFF.COMMISSION_RATE, 0)
								ELSE
								PRODUCTCOEFF.COMMISSION_RATE
								END)) AS COMMISSIONRATE,

                           NVL(EXPCOEFF.CUST_SORCE_COEFF, CPSC.SOURCE_COEFF) AS SOURCECOEFF,
                           NVL(EXPCOEFF.MANAGE_COEFF, CPCSC.MANAGE_COEFF) AS MANAGECOEFF
                    FROM CM_PREBOOKPRODUCTINFO PREBOOK
                             LEFT JOIN CM_DISCOUNTAPP DISCOUNT
                                       ON PREBOOK.ID = DISCOUNT.PREBOOKID
                                           AND DISCOUNT.DISCOUNT_STATE != '7'
                             INNER JOIN (
		select PREID as PRE_ID,trade_dt as TRADE_DT,TRADE_NUM,MANYCALL_FLAG as MANY_CALL_FLAG,REALPREID from CM_PRP_TRADE_NUM
		union
		select PRE_ID,TRADE_DT,TRADE_NUM,MANY_CALL_FLAG,REAL_PRE_ID as REALPREID from CM_TRADE_NUM_SUMMARY) CPTN
                                        ON CPTN.PRE_ID = PREBOOK.ID
                             LEFT JOIN CM_PRP_PREID_EXP_COEFF EXPCOEFF
                                       ON PREBOOK.ID = EXPCOEFF.PREID
                             INNER JOIN CM_CONSULTANT CONS
                                        ON PREBOOK.CREATOR = CONS.CONSCODE
                             LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT PRODUCTCOEFF
                                       ON PREBOOK.PCODE = PRODUCTCOEFF.FUNDCODE
                                           AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) >= PRODUCTCOEFF.START_DT
                                           AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) <= PRODUCTCOEFF.End_Dt
                             LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
                                       ON DISCOUNT.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
                                           AND PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE =
                                               ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
                							and PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.Big_Order_Product_Type
                                           AND CPTN.TRADE_DT >= ORDERCOEFF.START_DT
                                           AND CPTN.TRADE_DT <= NVL(ORDERCOEFF.END_DT, '********')
                                           and PRODUCTCOEFF.STOCK_FEE_A >= nvl(ORDERCOEFF.stock_fee_d_min,0) and PRODUCTCOEFF.STOCK_FEE_A < nvl(ORDERCOEFF.stock_fee_d_max,10000)
                             LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
                                       ON PREBOOK.SOURCETYPE = CPCSC.SOURCE_TYPE
                                           AND CPTN.TRADE_DT >= CPCSC.START_DT
                                           AND CPTN.TRADE_DT <= NVL(CPCSC.END_DT, '********')
                             LEFT JOIN CM_PRP_SOURCE_COEFFICIENT CPSC
                                       ON DECODE(CPTN.TRADE_NUM,
                                                 0,
                                                 '0',
                                                 1,
                                                 '1',
                                                 2,
                                                 '2',
                                                 3,
                                                 '3',
                                                 4,
                                                 '4',
                                                 '99') = CPSC.TRADE_NUM
                                           AND CPSC.START_POINT = CPCSC.START_POINT
                    WHERE CPTN.MANY_CALL_FLAG = '1') CFCOEFF
                   ON T.REAL_PRE_ID = CFCOEFF.ID
			WHERE
			t.trade_num_type in ('1','2')
			and (T5.ACCOUNT_PRODUCT_TYPE not in ('8','12','13','14','15') OR T5.ACCOUNT_PRODUCT_TYPE IS NULL)
		]]>
  		 <if test="startDate != null"> AND T.TRADE_DT &gt;= #{startDate} </if>
         <if test="endDate != null"> AND T.TRADE_DT &lt;= #{endDate} </if>
		order by T.TRADE_DT DESC
	</select>
      
      <select id="excuteCmPrpTradeNum" statementType="CALLABLE"   useCache="false" parameterType="Map" resultType="Map">
		<![CDATA[
		{call PRO_REWARD_STAT_TRADE_NUM(
			#{RESULTCODE,mode=OUT,jdbcType=VARCHAR},
			#{MESSAGE,mode=OUT,jdbcType=VARCHAR} )}
		]]>
	</select>
	
	<insert id="saveCmPrpFqsxjhCal" parameterType="Map">
		INSERT INTO CM_PRP_FQSXJH_CAL
		  (ID,
		   QUARTER,
		   PREID,
		   ACCOUNT_DT,
		   CONSCODE,
		   ACCOUNT_PRODUCT_TYPE,
		   ACK_AMT_RMB,
		   ACK_FEE_RMB,
		   DISCOUNT_FEE,
		   ADJUST_DISCOUNT_FEE,
		   DISCOUNT_TYPE,
		   DISCOUNT_REASON,
		   TRADE_NUM,
		   SOURCE_TYPE,
		   SOURCE_COEFF,
		   COMMISSION_RATE,
		   CREATOR,
		   ACCOUNT_DT_FLAG,
		   COMMISSION_RATE_FLAG,
		   SUBSCRIBE_AMT_RMB,
		   REALPREID,
			manage_coeff,
			EXPECTTRADEDT,
			PAYSTATE
		   )
		  SELECT  SEQ_REWARD_ID.NEXTVAL,
			       #{yyyyq},
			       T.PREID,
			       DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) ACCOUNTDT,
			       T2.CREATOR CONSCODE,
			       T5.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
			       T.REALPAY_AMT_RMB ACKAMTRMB,
			       T.FEE_RMB ACKFEERMB,
			       CASE
			         WHEN T2.CURRENCY != '156' THEN
			          NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT) * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
			         ELSE
			          NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT)
			       END DISCOUNTFEE,
			       CASE
			         WHEN T2.CURRENCY != '156' THEN
			          T3.BEF_TAX_AMT_ADJUST * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
			         ELSE
			          T3.BEF_TAX_AMT_ADJUST
			       END ADJUSTDISCOUNTFEE,
			       CASE
			         WHEN (T3.DISCOUNT_TYPE = '5' AND T3.DISCOUNT_STATE IN ('3', '5')) OR
			              (T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') OR
			              (T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')) THEN
			          T3.DISCOUNT_TYPE
			         ELSE
			          CASE
			            WHEN T3.DISCOUNT_TYPE IS NOT NULL THEN
			             '4'
			            ELSE
			             ''
			          END
			       END DISCOUNTTYPE,
			       T3.DISCOUNT_REASON DISCOUNTREASON,
			       T.TRADE_NUM TRADENUM,
			       T2.SOURCETYPE,
			       CASE
					WHEN T3.ID IS NOT NULL AND ((T3.DISCOUNT_TYPE = '6' and T3.DISCOUNT_STATE = '5')
												or (T3.DISCOUNT_TYPE = '8' and T3.DISCOUNT_STATE IN ('3', '5'))) THEN
			          1
			         ELSE
			          (CASE
			            WHEN T.MANYCALL_FLAG = '2' THEN
			             NVL(CFCOEFF.SOURCECOEFF, 1)
			            ELSE
			             NVL(T1.CUST_SORCE_COEFF, T7.SOURCE_COEFF)
			          END)
			       END AS SOURCECOEFF,
			       NVL(T1.COMMISSION_RATE,
			           (CASE
			             WHEN T.MANYCALL_FLAG = '2' THEN
			              CFCOEFF.COMMISSIONRATE
			             ELSE
			              (CASE
			                WHEN (T3.DISCOUNT_TYPE = '5' AND T3.DISCOUNT_STATE IN ('3', '5')) OR
			                     (T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') OR
			                     (T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')) THEN
			                 (CASE
			                   WHEN T3.DISCOUNT_TYPE = '5' THEN
			                    NVL(ORDERCOEFF.COMMISSION_RATE, 0)
			                   WHEN T3.DISCOUNT_TYPE = '6' THEN
			                    NVL(ORDERCOEFF.COMMISSION_RATE, 0)
			                   WHEN T3.DISCOUNT_TYPE = '8' THEN
			                    NVL(ORDERCOEFF.COMMISSION_RATE, 0.1)
			                   ELSE
			                    NULL
			                 END)
			                ELSE
			                 T5.COMMISSION_RATE
			              END)
			           END)) AS COMMISSIONRATE,
			       #{creator},
			       DECODE(T1.ACCOUNT_DT, NULL, NULL, '1') ACCOUNT_DT_FLAG,
			       DECODE(T1.COMMISSION_RATE, NULL, NULL, '1') COMMISSION_RATE_FLAG,
			       T.SUBSCRIBE_AMT_RMB,
			       T.REALPREID,
			       NVL(T1.MANAGE_COEFF,
			           CASE
			             WHEN T.MANYCALL_FLAG = '2' THEN
			              CFCOEFF.MANAGECOEFF
			             ELSE
			              CPCSC.MANAGE_COEFF
			           END) AS MANAGECOEFF,
			       T2.EXPECTTRADEDT,
			       T2.PAYSTATE
			  FROM CM_PRP_TRADE_NUM T
			  LEFT JOIN CM_PRP_PREID_EXP_COEFF T1
			    ON T.REALPREID = T1.PREID
			  LEFT JOIN CM_PREBOOKPRODUCTINFO T2
			    ON T.REALPREID = T2.ID
			  LEFT JOIN CM_DISCOUNTAPP T3
			    ON T.PREID = T3.PREBOOKID
			   AND T3.DISCOUNT_STATE != '7'
			  LEFT JOIN CM_CONSULTANT T4
			    ON T2.CREATOR = T4.CONSCODE
			  LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT T5
			    ON T2.PCODE = T5.FUNDCODE
			   AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) >= T5.START_DT
			   AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) &lt;= T5.End_Dt
			  LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
			    ON T2.SOURCETYPE = CPCSC.SOURCE_TYPE
			   AND T.TRADE_DT >= CPCSC.START_DT
			   AND T.TRADE_DT &lt;= NVL(CPCSC.END_DT, '********')
			  LEFT JOIN CM_PRP_SOURCE_COEFFICIENT T7
			    ON CPCSC.START_POINT = T7.START_POINT
			   AND DECODE(T.TRADE_NUM, 0, '0', 1, '1', 2, '2', 3, '3', 4, '4', '99') =
			       T7.TRADE_NUM
			  LEFT JOIN JJXX1 JJXX
			    ON T2.PCODE = JJXX.JJDM
			  LEFT JOIN RMBHLZJJ RMBZJJ
			    ON T2.CURRENCY = RMBZJJ.DHBZ
			   AND T.TRADE_DT = RMBZJJ.JZRQ
			   AND T2.CURRENCY != '156'
			  LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
			    ON T3.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
			   AND T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
			   AND T.TRADE_DT >= ORDERCOEFF.START_DT
			   AND T.TRADE_DT &lt;= NVL(ORDERCOEFF.END_DT, '********')
			   AND ORDERCOEFF.OLD_FOLD_COEFF = T5.FOLD_COEFFICIENT
			   AND ORDERCOEFF.OLD_COMMISSION_RATE = T5.COMMISSION_RATE
			  LEFT JOIN (SELECT PREBOOK.ID,
			                    NVL(EXPCOEFF.COMMISSION_RATE,
			                        (CASE
			                          WHEN (DISCOUNT.DISCOUNT_TYPE = '5' AND
			                               DISCOUNT.DISCOUNT_STATE IN ('3', '5')) OR
			                               (DISCOUNT.DISCOUNT_TYPE = '6' AND
			                               DISCOUNT.DISCOUNT_STATE = '5') OR
			                               (DISCOUNT.DISCOUNT_TYPE = '8' AND
			                               DISCOUNT.DISCOUNT_STATE IN ('3', '5')) THEN
			                           (CASE
			                             WHEN DISCOUNT.DISCOUNT_TYPE = '5' THEN
			                              NVL(ORDERCOEFF.COMMISSION_RATE, 0)
			                             WHEN DISCOUNT.DISCOUNT_TYPE = '6' THEN
			                              NVL(ORDERCOEFF.COMMISSION_RATE, 0)
			                             WHEN DISCOUNT.DISCOUNT_TYPE = '8' THEN
			                              NVL(ORDERCOEFF.COMMISSION_RATE, 0.1)
			                             ELSE
			                              NULL
			                           END)
			                          ELSE
			                           PRODUCTCOEFF.COMMISSION_RATE
			                        END)) AS COMMISSIONRATE,
			                    NVL(EXPCOEFF.CUST_SORCE_COEFF, CPSC.SOURCE_COEFF) AS SOURCECOEFF,
			                    NVL(EXPCOEFF.MANAGE_COEFF, CPCSC.MANAGE_COEFF) AS MANAGECOEFF
			               FROM CM_PREBOOKPRODUCTINFO PREBOOK
			               LEFT JOIN CM_DISCOUNTAPP DISCOUNT
			                 ON PREBOOK.ID = DISCOUNT.PREBOOKID
			                AND DISCOUNT.DISCOUNT_STATE != '7'
			              INNER JOIN CM_PRP_TRADE_NUM CPTN
			                 ON CPTN.PREID = PREBOOK.ID
			               LEFT JOIN CM_PRP_PREID_EXP_COEFF EXPCOEFF
			                 ON PREBOOK.ID = EXPCOEFF.PREID
			              INNER JOIN CM_CONSULTANT CONS
			                 ON PREBOOK.CREATOR = CONS.CONSCODE
			               LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT PRODUCTCOEFF
			                 ON PREBOOK.PCODE = PRODUCTCOEFF.FUNDCODE
							AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) >= PRODUCTCOEFF.START_DT
							AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) &lt;= PRODUCTCOEFF.End_Dt
			               LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
			                 ON DISCOUNT.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
			                AND PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE =
			                    ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
			                AND CPTN.TRADE_DT >= ORDERCOEFF.START_DT
			                AND CPTN.TRADE_DT &lt;= NVL(ORDERCOEFF.END_DT, '********')
			                AND ORDERCOEFF.OLD_FOLD_COEFF =
			                    PRODUCTCOEFF.FOLD_COEFFICIENT
			                AND ORDERCOEFF.OLD_COMMISSION_RATE =
			                    PRODUCTCOEFF.COMMISSION_RATE
			               LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
			                 ON PREBOOK.SOURCETYPE = CPCSC.SOURCE_TYPE
			                AND CPTN.TRADE_DT >= CPCSC.START_DT
			                AND CPTN.TRADE_DT &lt;= NVL(CPCSC.END_DT, '********')
			               LEFT JOIN CM_PRP_SOURCE_COEFFICIENT CPSC
			                 ON DECODE(CPTN.TRADE_NUM,
			                           0,
			                           '0',
			                           1,
			                           '1',
			                           2,
			                           '2',
			                           3,
			                           '3',
			                           4,
			                           '4',
			                           '99') = CPSC.TRADE_NUM
			                AND CPSC.START_POINT = CPCSC.START_POINT
			              WHERE CPTN.MANYCALL_FLAG = '1') CFCOEFF
			    ON T.REALPREID = CFCOEFF.ID
			 WHERE (T5.ACCOUNT_PRODUCT_TYPE not in ('8','6') OR T5.ACCOUNT_PRODUCT_TYPE IS NULL)
		   AND T.TRADE_DT &gt;= #{startDate}
         AND T.TRADE_DT &lt;= #{endDate}
	</insert>

	<!-- 保存 -->
	<insert id="saveCmPrpFqsxjhCalNew" parameterType="Map">
		INSERT INTO CM_PRP_FQSXJH_CAL
		(ID,
		QUARTER,
		PREID,
		ACCOUNT_DT,
		CONSCODE,
		ACCOUNT_PRODUCT_TYPE,
		ACK_AMT_RMB,
		ACK_FEE_RMB,
		DISCOUNT_FEE,
		ADJUST_DISCOUNT_FEE,
		DISCOUNT_TYPE,
		DISCOUNT_REASON,
		TRADE_NUM,
		SOURCE_TYPE,
		SOURCE_COEFF,
		COMMISSION_RATE,
		CREATOR,
		ACCOUNT_DT_FLAG,
		COMMISSION_RATE_FLAG,
		SUBSCRIBE_AMT_RMB,
		REALPREID,
		manage_coeff,
		EXPECTTRADEDT,
		PAYSTATE
		)
		SELECT  SEQ_REWARD_ID.NEXTVAL,
		#{yyyyq},
		T.PRE_ID,
		DECODE(expAct.ACCOUNT_DT, NULL, T.TRADE_DT, expAct.ACCOUNT_DT) ACCOUNTDT,
		preAct.CREATOR CONSCODE,
		T5.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
		nvl(T.REALPAY_AMT_RMB, nvl(preAct.realpayamt_rmb, preAct.realpayamt)) ACKAMTRMB,
		CASE
		WHEN preAct.CURRENCY != '156' THEN
		preAct.FEE *
		NVL(RMBZJJ.ZJJ,
		(SELECT ZZJJ.ZJJ
		FROM RMBHLZJJ ZZJJ
		WHERE ZZJJ.DHBZ = preAct.CURRENCY
		AND ZZJJ.JZRQ IN
		(SELECT MAX(ZJJ.JZRQ)
		FROM RMBHLZJJ ZJJ
		WHERE ZJJ.DHBZ = preAct.CURRENCY
		AND ZJJ.JZRQ &lt;= preAct.EXPECTTRADEDT)))
		ELSE
		preAct.FEE
		END ACKFEERMB,
		CASE
		WHEN T2.CURRENCY != '156' THEN
		NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT) * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
		ELSE
		NVL(T1.BEFORE_TAX_AMT, T3.BEFORE_TAX_AMT)
		END DISCOUNTFEE,
		CASE
		WHEN T2.CURRENCY != '156' THEN
		T3.BEF_TAX_AMT_ADJUST * NVL(RMBZJJ.ZJJ, F_GET_NEARHLZJJ(T2.CURRENCY,T.TRADE_DT))
		ELSE
		T3.BEF_TAX_AMT_ADJUST
		END ADJUSTDISCOUNTFEE,
		CASE
		WHEN (T3.DISCOUNT_TYPE = '5' AND T3.DISCOUNT_STATE IN ('3', '5')) OR
		(T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') OR
		(T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')) THEN
		T3.DISCOUNT_TYPE
		ELSE
		CASE
		WHEN T3.DISCOUNT_TYPE IS NOT NULL THEN
		'4'
		ELSE
		''
		END
		END DISCOUNTTYPE,
		T3.DISCOUNT_REASON DISCOUNTREASON,
		T.TRADE_NUM TRADENUM,
		T2.SOURCETYPE,
		CASE
		WHEN T3.ID IS NOT NULL AND ((T3.DISCOUNT_TYPE = '6' and T3.DISCOUNT_STATE = '5')
									or (T3.DISCOUNT_TYPE = '8' and T3.DISCOUNT_STATE IN ('3', '5'))) THEN
		1
		ELSE
		(CASE
		WHEN T.MANY_CALL_FLAG = '2' THEN
		NVL(CFCOEFF.SOURCECOEFF, 1)
		ELSE
		NVL(T1.CUST_SORCE_COEFF, T7.SOURCE_COEFF)
		END)
		END AS SOURCECOEFF,
		NVL(T1.COMMISSION_RATE,
		(CASE
		WHEN T.MANY_CALL_FLAG = '2' THEN
		-- 后续call预约的佣金率，如果换投顾，则取0.1（********惠真提的）
		(case when CFCOEFF.COMMISSIONRATE is null and (t.REAL_PRE_ID = T.PRE_ID or t.cons_code != T2.creator) then 0.1 else CFCOEFF.COMMISSIONRATE end)
		ELSE
			(CASE
			WHEN
			(T3.DISCOUNT_TYPE = '6' AND T3.DISCOUNT_STATE = '5') THEN
				NVL(ORDERCOEFF.COMMISSION_RATE, 0)
			ELSE
			T5.COMMISSION_RATE
			END)
		END)) AS COMMISSIONRATE,

		#{creator},
		DECODE(T1.ACCOUNT_DT, NULL, NULL, '1') ACCOUNT_DT_FLAG,
		DECODE(T1.COMMISSION_RATE, NULL, NULL, '1') COMMISSION_RATE_FLAG,

		CASE
		WHEN T2.CURRENCY != '156' THEN
		manycall.TOTALAMT *
		NVL(RMBZJJ.ZJJ,
		(SELECT ZZJJ.ZJJ
		FROM RMBHLZJJ ZZJJ
		WHERE ZZJJ.DHBZ = T2.CURRENCY
		AND ZZJJ.JZRQ IN
		(SELECT MAX(ZJJ.JZRQ)
		FROM RMBHLZJJ ZJJ
		WHERE ZJJ.DHBZ = T2.CURRENCY
		AND ZJJ.JZRQ &lt;= T2.EXPECTTRADEDT)))
		ELSE
		manycall.TOTALAMT
		END SUBSCRIBE_AMT_RMB,

		T.REAL_PRE_ID,
		NVL(T1.MANAGE_COEFF,
		CASE
		WHEN T.MANY_CALL_FLAG = '2' THEN
		-- 后续call预约的管理系数，如果换投顾，则取1（********惠真提的）
		(case when CFCOEFF.COMMISSIONRATE is null and (t.REAL_PRE_ID = T.PRE_ID or t.cons_code != T2.creator) then 1 else CFCOEFF.MANAGECOEFF end)
		ELSE
		CPCSC.MANAGE_COEFF
		END) AS MANAGECOEFF,
		T2.EXPECTTRADEDT,
		T2.PAYSTATE
		FROM CM_TRADE_NUM_SUMMARY T
		LEFT JOIN CM_PRP_PREID_EXP_COEFF T1
		ON T.REAL_PRE_ID = T1.PREID
		LEFT JOIN CM_PRP_PREID_EXP_COEFF expAct
		ON T.PRE_ID = expAct.PREID
		LEFT JOIN CM_PREBOOKPRODUCTINFO T2
		ON T.REAL_PRE_ID = T2.ID
		LEFT JOIN CM_PREBOOKPRODUCTINFO preAct
		ON T.PRE_ID = preAct.ID
		LEFT JOIN CM_DISCOUNTAPP T3
		ON T.PRE_ID = T3.PREBOOKID
		AND T3.DISCOUNT_STATE != '7'
		left join cm_prebook_manycall manycall
		on REAL_PRE_ID = manycall.FIRSTPREID
		LEFT JOIN CM_CONSULTANT T4
		ON T2.CREATOR = T4.CONSCODE
		LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT T5
		ON T2.PCODE = T5.FUNDCODE
		AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) >= T5.START_DT
		AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) &lt;= T5.End_Dt
		LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
		ON T2.SOURCETYPE = CPCSC.SOURCE_TYPE
		AND T.TRADE_DT >= CPCSC.START_DT
		AND T.TRADE_DT &lt;= NVL(CPCSC.END_DT, '********')
		LEFT JOIN CM_PRP_SOURCE_COEFFICIENT T7
		ON CPCSC.START_POINT = T7.START_POINT
		AND DECODE(T.TRADE_NUM, 0, '0', 1, '1', 2, '2', 3, '3', 4, '4', '99') =
		T7.TRADE_NUM
		LEFT JOIN JJXX1 JJXX
		ON T2.PCODE = JJXX.JJDM
		LEFT JOIN RMBHLZJJ RMBZJJ
		ON T2.CURRENCY = RMBZJJ.DHBZ
		AND T.TRADE_DT = RMBZJJ.JZRQ
		AND T2.CURRENCY != '156'
		LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
		ON T3.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
		AND T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
		and T5.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.Big_Order_Product_Type
		AND T.TRADE_DT >= ORDERCOEFF.START_DT
		AND T.TRADE_DT &lt;= NVL(ORDERCOEFF.END_DT, '********')
		and T5.STOCK_FEE_A >= nvl(ORDERCOEFF.stock_fee_d_min,0) and T5.STOCK_FEE_A &lt; nvl(ORDERCOEFF.stock_fee_d_max,10000)
		LEFT JOIN (SELECT PREBOOK.ID,
		NVL(EXPCOEFF.UNQUALIFIED_COEFF, 1) AS UNQUALIFIEDCOEFF,
		NVL(EXPCOEFF.COMMISSION_RATE,
		(CASE
		WHEN
		(DISCOUNT.DISCOUNT_TYPE = '6' AND
			DISCOUNT.DISCOUNT_STATE = '5')  THEN
		NVL(ORDERCOEFF.COMMISSION_RATE, 0)
		ELSE
		PRODUCTCOEFF.COMMISSION_RATE
		END)) AS COMMISSIONRATE,
		NVL(EXPCOEFF.FOLD_COEFF,
		(CASE
		WHEN (DISCOUNT.DISCOUNT_TYPE = '5' AND
		DISCOUNT.DISCOUNT_STATE IN ('3', '5')) OR
		(DISCOUNT.DISCOUNT_TYPE = '6' AND
		DISCOUNT.DISCOUNT_STATE = '5') OR
		(DISCOUNT.DISCOUNT_TYPE = '8' AND
		DISCOUNT.DISCOUNT_STATE IN ('3', '5')) THEN
		(CASE
		WHEN DISCOUNT.DISCOUNT_TYPE = '5' THEN
		NVL(ORDERCOEFF.FOLD_COEFF,
		PRODUCTCOEFF.FOLD_COEFFICIENT)
		WHEN DISCOUNT.DISCOUNT_TYPE = '6' THEN
		NVL(ORDERCOEFF.FOLD_COEFF, 1)
		WHEN DISCOUNT.DISCOUNT_TYPE = '8' THEN
		NVL(ORDERCOEFF.FOLD_COEFF, 1)
		ELSE
		NULL
		END)
		ELSE
		PRODUCTCOEFF.FOLD_COEFFICIENT
		END)) AS FOLDCOEFF,
		NVL(EXPCOEFF.CUST_SORCE_COEFF, CPSC.SOURCE_COEFF) AS SOURCECOEFF,
		NVL(EXPCOEFF.CUST_FOLD_COEFF, CPCSC.ZB_COEFF) AS ZBCOEFF,
		NVL(EXPCOEFF.MANAGE_COEFF, CPCSC.MANAGE_COEFF) AS MANAGECOEFF
		FROM CM_PREBOOKPRODUCTINFO PREBOOK
		LEFT JOIN CM_DISCOUNTAPP DISCOUNT
		ON PREBOOK.ID = DISCOUNT.PREBOOKID
		AND DISCOUNT.DISCOUNT_STATE != '7'
		INNER JOIN (
		select PREID as PRE_ID,trade_dt as TRADE_DT,TRADE_NUM,MANYCALL_FLAG as MANY_CALL_FLAG,REALPREID from CM_PRP_TRADE_NUM
		union
		select PRE_ID,TRADE_DT,TRADE_NUM,MANY_CALL_FLAG,REAL_PRE_ID as REALPREID from CM_TRADE_NUM_SUMMARY) CPTN
		ON CPTN.PRE_ID = PREBOOK.ID
		LEFT JOIN CM_PRP_PREID_EXP_COEFF EXPCOEFF
		ON PREBOOK.ID = EXPCOEFF.PREID
		INNER JOIN CM_CONSULTANT CONS
		ON PREBOOK.CREATOR = CONS.CONSCODE
		LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT PRODUCTCOEFF
		ON PREBOOK.PCODE = PRODUCTCOEFF.FUNDCODE
		AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) >= PRODUCTCOEFF.START_DT
		AND NVL(EXPCOEFF.ACCOUNT_DT, CPTN.TRADE_DT) &lt;= PRODUCTCOEFF.End_Dt
		LEFT JOIN CM_PRP_ORDERINFO_TYPE_COEFF ORDERCOEFF
		ON DISCOUNT.DISCOUNT_TYPE = ORDERCOEFF.ORDER_INFO_TYPE
		AND PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE =
		ORDERCOEFF.ACCOUNT_PRODUCT_TYPE
		and PRODUCTCOEFF.ACCOUNT_PRODUCT_TYPE = ORDERCOEFF.Big_Order_Product_Type
		AND CPTN.TRADE_DT >= ORDERCOEFF.START_DT
		AND CPTN.TRADE_DT &lt;= NVL(ORDERCOEFF.END_DT, '********')
		and PRODUCTCOEFF.STOCK_FEE_A >= nvl(ORDERCOEFF.stock_fee_d_min,0) and PRODUCTCOEFF.STOCK_FEE_A &lt; nvl(ORDERCOEFF.stock_fee_d_max,10000)
		LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
		ON PREBOOK.SOURCETYPE = CPCSC.SOURCE_TYPE
		AND CPTN.TRADE_DT >= CPCSC.START_DT
		AND CPTN.TRADE_DT &lt;= NVL(CPCSC.END_DT, '********')
		LEFT JOIN CM_PRP_SOURCE_COEFFICIENT CPSC
		ON DECODE(CPTN.TRADE_NUM,
		0,
		'0',
		1,
		'1',
		2,
		'2',
		3,
		'3',
		4,
		'4',
		'99') = CPSC.TRADE_NUM
		AND CPSC.START_POINT = CPCSC.START_POINT
		WHERE CPTN.MANY_CALL_FLAG = '1') CFCOEFF
		ON T.REAL_PRE_ID = CFCOEFF.ID
		WHERE
		t.trade_num_type in ('1','2')
		and (T5.ACCOUNT_PRODUCT_TYPE not in ('8','12','13','14','15') OR T5.ACCOUNT_PRODUCT_TYPE IS NULL)
		AND T.TRADE_DT &gt;= #{startDate}
		AND T.TRADE_DT &lt;= #{endDate}
	</insert>

	<insert id="saveCmPrpFqsxjhBigOrder" parameterType="Map">
		INSERT INTO CM_PRP_FQSXJH_BIG_ORDER
		(ID,
		PRE_ID,
		CONS_CUST_NO,
		FUND_CODE,
		CONS_CODE,
		ACCOUNT_PRODUCT_TYPE,
		BIG_ORDER_PRODUCT_TYPE,
		SETTLEMENT_STATUS,
		<!--COMMISSION_RATE,-->
		<!--ACHIEVE_COEFF,-->
		CREATOR,
		create_time,
		TRADE_DT,
		SETTLEMENT_DT
		)
		SELECT  SEQ_REWARD_ID.NEXTVAL,
		T.PRE_ID,
		T.CONS_CUST_NO,
		T.PRODUCT_CODE,
		T2.CREATOR CONSCODE,
		T5.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
		T5.ACCOUNT_PRODUCT_TYPE big_order_product_type,
		'0',
		#{creator},
		sysdate,
		T.TRADE_DT,
		<!-- 结算日期为交易日期加一年减一天 -->
		to_char(ADD_MONTHS(to_date(T.TRADE_DT,'yyyymmdd'), 12) -1,'yyyymmdd')
		FROM CM_TRADE_NUM_SUMMARY T
		left join CM_PRP_FQSXJH_CAL_EXP calExp
		on t.PRE_ID = calExp.PREID
		LEFT JOIN
		CM_PRP_PREID_EXP_COEFF T1
		ON T.REAL_PRE_ID = T1.PREID
		LEFT JOIN
		CM_PREBOOKPRODUCTINFO T2
		ON T.REAL_PRE_ID = T2.ID
		LEFT JOIN
		CM_DISCOUNTAPP T3
		ON T.PRE_ID = T3.PREBOOKID
		AND T3.DISCOUNT_STATE != '7'
		LEFT JOIN
		CM_CONSULTANT T4
		ON T2.CREATOR = T4.CONSCODE
		LEFT JOIN
		CM_PRP_PRODUCT_COEFFICIENT T5
		ON T2.PCODE = T5.FUNDCODE
		AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) >= T5.START_DT
		AND DECODE(T1.ACCOUNT_DT, NULL, T.TRADE_DT, T1.ACCOUNT_DT) &lt;= T5.End_Dt
		LEFT JOIN CM_PRP_CUST_SOURCE_COEFF CPCSC
		ON T2.SOURCETYPE = CPCSC.SOURCE_TYPE
		AND T.TRADE_DT >= CPCSC.START_DT
		AND T.TRADE_DT &lt;= NVL(CPCSC.END_DT, '********')
		WHERE
		t.trade_num_type in ('1','2')
		and (T5.ACCOUNT_PRODUCT_TYPE not in ('8','12','13','14','15') OR T5.ACCOUNT_PRODUCT_TYPE IS NULL)
		AND T.TRADE_DT &gt;= #{startDate}
		AND T.TRADE_DT &lt;= #{endDate}
		and T3.DISCOUNT_TYPE = '8' AND T3.DISCOUNT_STATE IN ('3', '5')
		and (T.MANY_CALL_FLAG != '2' or T.MANY_CALL_FLAG is null)
		-- 已存在的不重复添加 （已结算的由于不删除，所以不再添加）
		and not exists(select 1 from CM_PRP_FQSXJH_BIG_ORDER bg where bg.PRE_ID = T.PRE_ID and SETTLEMENT_STATUS = '1')
	</insert>

	<!--复核查询-->
	<select id="listCmPrpFqsxjhCal" parameterType="Map" resultType="CmPrpFqsxjhCal" useCache="false">
	    SELECT T.ID ID,
		       T.ACCOUNT_DT ACCOUNTDT,
		       T4.EXPECTTRADEDT TRADEDT,
		       T4.CONSCUSTNO,
		       T4.CONSCUSTNAME CUSTNAME,
		       T4.PCODE FUNDCODE,
		       T5.JJJC FUNDNAME,
		       T.QUARTER QUARTER,
		       T.PREID PREID,
		       T.CONSCODE CONSCODE,
		       T.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
		       T.ACK_AMT_RMB ACKAMTRMB,
		       T.SUBSCRIBE_AMT_RMB SUBSCRIBEAMTRMB,
		       T.ACK_FEE_RMB ACKFEERMB,
		       T.DISCOUNT_FEE DISCOUNTFEE,
		       T.ADJUST_DISCOUNT_FEE ADJUSTDISCOUNTFEE,
		       T.DISCOUNT_TYPE DISCOUNTTYPE,
		       T.DISCOUNT_REASON DISCOUNTREASON,
		       T.TRADE_NUM TRADENUM,
		       T.SOURCE_TYPE SOURCETYPE,
		       DECODE(T1.SOURCE_COEFF, NULL, T.SOURCE_COEFF, T1.SOURCE_COEFF) SOURCECOEFF,
		       T.COMMISSION_RATE COMMISSIONRATE,
		       DECODE(T1.EXTRA_COEFF, NULL, NVL(T.EXTRA_COEFF, 0), T1.EXTRA_COEFF) EXTRACOEFF,
			   CASE WHEN MANYCALL.MANYCALLFLAG = '1' THEN
						T.SUBSCRIBE_AMT_RMB || ''
			   WHEN MANYCALL.MANYCALLFLAG IS NOT NULL AND MANYCALL.MANYCALLFLAG != '1' AND
					 MANYCALL.RN IS NOT NULL AND MANYCALL.RN != 1 THEN
						'第' || MANYCALL.RN || '次call'
			   ELSE
					'-'
			   END SUBSCRIBEAMTRMBVAL,
			   --核算类型不为股权类，折扣类型 是“大单定制”和“活动竞赛” ,折扣方式=直接少汇：业绩系数=到账手续费(RMB)* 佣金率
		       case when t.account_product_type != '3' and t.discount_type in ('5','8') and t6.discount_way ='1' then
		            	t.ack_fee_rmb * t.commission_rate
					--核算类型不为股权类，折扣类型 是“大单定制”和“活动竞赛” ,折扣方式=好买返回：业绩系数= (到账手续费(RMB)-税前折扣金额(RMB)) * 佣金率
					when t.account_product_type != '3' and t.discount_type in ('5','8') and t6.discount_way ='2' then
						(t.ack_fee_rmb - t6.before_tax_amt) * t.commission_rate
					-- 核算类型不为股权类，折扣类型为正常折扣，有客户权益，实际折扣率等于权益折扣率，业绩系数=到账金额（RMB）*佣金率*0.01*佣金系数,权益折扣率有人工调整，取人工调整后的数据
					when t.account_product_type != '3' and t.discount_type ='4' and t7.interests_rate != null
						 and t6.discount_rate = decode(t7.interests_rate, null, t8.interests_rate, t7.interests_rate) then
						 t.ack_amt_rmb * t.commission_rate * 0.01 * decode(t1.source_coeff, null, t.source_coeff, t1.source_coeff)
					-- 核算类型不为股权类，折扣类型为正常折扣，有客户权益，实际折扣率小于权益折扣率，业绩系数=到账手续费（RMB）*佣金率*佣金系数,权益折扣率有人工调整，取人工调整后的数据
					when t.account_product_type != '3' and t.discount_type ='4' and t7.interests_level != null
						 and t6.discount_rate &lt; decode(t8.interests_rate, null, t7.interests_rate, t8.interests_rate) then
						 t.ack_fee_rmb * t.commission_rate * decode(t1.source_coeff, null, t.source_coeff, t1.source_coeff)
					--核算类型不为股权类， 折扣类型 是“员工及其亲属福利”，业绩系数= 到账手续费（RMB）*佣金率 若计算结果为负数，按0显示（Java中处理）
					when t.account_product_type != '3' and t.discount_type ='6' then
						 t.ack_fee_rmb * t.commission_rate
					--其他情况，业绩系数=到账金额（RMB）*佣金率*0.01*佣金系数 - 手续费折扣（RMB）
					else
					     t.ack_amt_rmb * t.commission_rate * 0.01 * decode(t1.source_coeff, null, t.source_coeff, t1.source_coeff) - nvl(t.discount_fee, 0)
			   end achievecoeff,
		       T1.TOTAL_ACHIEVE TOTALACHIEVE,
		       T.CREATOR CREATOR,
		       T.CREATE_TIME CREATETIME,
		       T.MODOR MODOR,
		       T.UPDATE_TIME UPDATETIME,
		       T.ACCOUNT_DT_FLAG ACCOUNTDTFLAG,
		       T.FOLD_COEFF_FLAG FOLDCOEFFFLAG,
		       T.COMMISSION_RATE_FLAG COMMISSIONRATEFLAG,
		       DECODE(T1.SOURCE_COEFF, NULL, NULL, '1') SOURCECOEFFFLAG,
		       DECODE(T1.EXTRA_COEFF, NULL, NULL, '1') EXTRACOEFFFLAG,
		       DECODE(T1.TOTAL_ACHIEVE, NULL, NULL, '1') TOTALACHIEVEFLAG,
		       T1.MODOR LASTMODOR,
			   MANYCALL.MANYCALLFLAG MANYCALLFLAG,
			   MANYCALL.RN MANYCALLSORT,
		       T.manage_coeff MANAGECOEFF,
		       T.manage_coeff_regionalsubtotal manageCoeffRegionalsubtotal,
		       t.manage_coeff_regionaltotal manageCoeffRegionaltotal,
			   t7.interests_level as interestsLevelPre,
			   t7.interests_rate as interestsRatePre,
			   t7.interests_type as interestsTypePre,
			   t8.interests_rate as interestsRateMan,
			   t6.discount_rate as discountRate,
			   t6.discount_way as discountWay,
			   t6.before_tax_amt as beforeTaxAmt,
			  T1.adjust_manage_coeff_commission adjustManageCoeffCommission,
			  t1.manage_coeff_regsubtotal_comm adjManageCoeffRegsubtotalComm,
			  t1.manage_coeff_regtotal_comm adjManageCoeffRegtotalComm,
		      T.REALPREID realpreId
		FROM CM_PRP_FQSXJH_CAL T
		  LEFT JOIN CM_PRP_FQSXJH_CAL_EXP T1
		    ON T.PREID = T1.PREID
		  LEFT JOIN CM_CONSULTANT T3
		    ON T.CONSCODE = T3.CONSCODE
		  LEFT JOIN CM_PREBOOKPRODUCTINFO T4
		    ON T.PREID = T4.ID
		  LEFT JOIN JJXX1 T5
		    ON T4.PCODE = T5.JJDM
		left join CM_DISCOUNTAPP t6 on t.preid = t6.prebookid
		left join cm_discountapp_extend t7 on t6.id = t7.discount_id
		left join cm_prp_preid_exp_coeff t8 on t.preid = t8.preid
		LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT productCoeff
		ON T4.PCODE = productCoeff.FUNDCODE
		AND T.ACCOUNT_DT >= productCoeff.START_DT
		AND T.ACCOUNT_DT &lt;= productCoeff.End_Dt
		LEFT JOIN (SELECT CMR.PREID,
		CPM.FIRSTPREID,
		CASE
		WHEN CPM.FIRSTPREID = CMR.PREID THEN
		'1'
		ELSE
		'2'
		END MANYCALLFLAG,
		CPM.TOTALAMT,
		ROW_NUMBER() OVER(PARTITION BY CMR.CALLID ORDER BY CMR.CREDDT ASC) AS RN
		FROM CM_PREBOOK_MANYCALL CPM
		LEFT JOIN CM_MANYCALL_RELATION CMR
		ON CPM.ID = CMR.CALLID) MANYCALL
		ON T4.id = MANYCALL.PREID

		 WHERE t.quarter = #{quarter}
		-- 债券A按小集合算法核算，这里要剔除债券A类产品
		AND (t.account_product_type not in ('6','12','13','14','15') or t.account_product_type is null)
		<if test="accountProductType != null"> AND t.account_product_type = #{accountProductType} </if>
         <if test="discountType != null"> AND t.DISCOUNT_TYPE = #{discountType} </if>
         <if test="preId != null"> AND T.PREID = #{preId} </if>
         <if test="fundcode != null"> AND t4.pcode = #{fundcode} </if>
         <if test="custname != null"> and t4.conscustName like '%'||#{custname ,jdbcType=VARCHAR}||'%' </if>
         <if test="conscode != null"> and T.CONSCODE = #{conscode,jdbcType=VARCHAR} </if>
	     <if test="conscode == null and orgcode != null"> 
	     	and ( T3.TEAMCODE = #{orgcode} OR
	     		T3.OUTLETCODE in (
	     			SELECT HO.ORGCODE
					  FROM HB_ORGANIZATION HO
					 WHERE HO.STATUS = '0'
					 START WITH HO.ORGCODE = #{orgcode}
					CONNECT BY PRIOR ORGCODE = PARENTORGCODE
	     		)
	     	)
	     	
	     </if>
      </select>
	
	<delete id="delCmPrpFqsxjhCalByParam" parameterType="Map">
	    delete from CM_PRP_FQSXJH_CAL where QUARTER = #{yyyyq}
	  </delete>

	<delete id="delCmPrpFqsxjhBigOrderByParam" parameterType="Map">
		delete from CM_PRP_FQSXJH_BIG_ORDER
		where trade_dt >= #{startDate} and trade_dt &lt;= #{endDate}
		and SETTLEMENT_STATUS = '0'
	</delete>
	  
	<insert id="saveCmPrpFqsxjhFinal" parameterType="Map">
		INSERT INTO CM_PRP_FQSXJH_FINAL
		  (ID,
	       QUARTER,
	       PREID,
	       CONSCODE,
	       ACCOUNT_PRODUCT_TYPE,
	       ACK_AMT_RMB,
	       SUBSCRIBE_AMT_RMB,
	       SUBSCRIBE_AMT_RMB_VAL,
	       ACK_FEE_RMB,
	       DISCOUNT_FEE,
	       ADJUST_DISCOUNT_FEE,
	       DISCOUNT_TYPE,
	       DISCOUNT_REASON,
	       TRADE_NUM,
	       SOURCE_TYPE,
	       SOURCE_COEFF,
	       COMMISSION_RATE,
	       ACHIEVE_COEFF,
	       EXTRA_COEFF,
	       TOTAL_ACHIEVE,
	       ACCOUNT_DT,
	       ACCOUNT_DT_FLAG,
	       COMMISSION_RATE_FLAG,
	       SOURCE_COEFF_FLAG,
	       EXTRA_COEFF_FLAG,
	       CREATOR,
	       REALPREID,
		TOTAL_ACHIEVE_FLAG,
		manage_coeff,
		manage_coeff_commission,
		ADJUST_MANAGE_COEFF_COMMISSION
		   )
		   SELECT SEQ_REWARD_ID.NEXTVAL,
			       #{quarter},
			       T.PREID,
			       T.CONSCODE CONSCODE,
			       T.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
			       T.ACK_AMT_RMB ACKAMTRMB,
			       T.SUBSCRIBE_AMT_RMB SUBSCRIBEAMTRMB,
			       CASE
			         WHEN MANYCALL.MANYCALLFLAG = '1' THEN
			          T.SUBSCRIBE_AMT_RMB || ''
			         WHEN MANYCALL.MANYCALLFLAG IS NOT NULL AND MANYCALL.MANYCALLFLAG != '1' AND
		MANYCALL.RN IS NOT NULL AND MANYCALL.RN != 1 THEN
			          '第' || MANYCALL.RN || '次call'
			         ELSE
			          '-'
			       END SUBSCRIBEAMTRMBVAL,
			       T.ACK_FEE_RMB ACKFEERMB,
			       T.DISCOUNT_FEE DISCOUNTFEE,
			       T.ADJUST_DISCOUNT_FEE ADJUSTDISCOUNTFEE,
			       T.DISCOUNT_TYPE DISCOUNTTYPE,
			       T.DISCOUNT_REASON DISCOUNTREASON,
			       T.TRADE_NUM TRADENUM,
			       T.SOURCE_TYPE SOURCETYPE,
			       DECODE(T1.SOURCE_COEFF, NULL, T.SOURCE_COEFF, T1.SOURCE_COEFF) SOURCECOEFF,
			       T.COMMISSION_RATE COMMISSIONRATE,
		case
		--新算法 非股权且大单定制或活动竞赛情况  取到账手续费(RMB)」* 原佣金率
		when #{newPrpCalYear} = '1' and T.account_product_type != '3' and T.DISCOUNT_TYPE in ('5','8') then
		T.ACK_FEE_RMB * T.COMMISSION_RATE
		--债券C类产品不按季度佣金计算，默认0
		when T.account_product_type = '11' then 0
		when T.DISCOUNT_TYPE in('5','6','8') then T.ACK_AMT_RMB * T.COMMISSION_RATE / 100.0
		when MANYCALL.MANYCALLFLAG = '2' then  T.ACK_AMT_RMB * T.COMMISSION_RATE / 100.0 *
		DECODE(T1.SOURCE_COEFF, NULL, T.SOURCE_COEFF, T1.SOURCE_COEFF) *
		DECODE(T1.UNQUALIFIED_COEFF,NULL,NVL(T.UNQUALIFIED_COEFF, 1),T1.UNQUALIFIED_COEFF)
		else
		T.ACK_AMT_RMB * T.COMMISSION_RATE / 100.0 *
		DECODE(T1.SOURCE_COEFF, NULL, T.SOURCE_COEFF, T1.SOURCE_COEFF) -
		NVL(T.DISCOUNT_FEE, 0) end ACHIEVECOEFF,

		DECODE(T1.EXTRA_COEFF, NULL, NVL(T.EXTRA_COEFF, 0), T1.EXTRA_COEFF) EXTRACOEFF,

		NVL(T1.TOTAL_ACHIEVE,
		case
		--新算法 非股权且大单定制或活动竞赛情况  取到账手续费(RMB)」* 原佣金率
		when #{newPrpCalYear} = '1' and T.account_product_type != '3' and T.DISCOUNT_TYPE in ('5','8') then
		T.ACK_FEE_RMB * T.COMMISSION_RATE
		--债券C类产品不按季度佣金计算，默认0
		when T.account_product_type = '11' then 0
		when T.DISCOUNT_TYPE in('5','6','8') then T.ACK_AMT_RMB * T.COMMISSION_RATE / 100.0
		when MANYCALL.MANYCALLFLAG = '2' then  T.ACK_AMT_RMB * T.COMMISSION_RATE / 100.0 *
		DECODE(T1.SOURCE_COEFF, NULL, T.SOURCE_COEFF, T1.SOURCE_COEFF) *
		DECODE(T1.UNQUALIFIED_COEFF,NULL,NVL(T.UNQUALIFIED_COEFF, 1),T1.UNQUALIFIED_COEFF)
		else
		T.ACK_AMT_RMB * T.COMMISSION_RATE / 100.0 *
		DECODE(T1.SOURCE_COEFF, NULL, T.SOURCE_COEFF, T1.SOURCE_COEFF) -
		NVL(T.DISCOUNT_FEE, 0) end) TOTALACHIEVE,

		T.ACCOUNT_DT ACCOUNTDT,
		   T.ACCOUNT_DT_FLAG,
		   T.COMMISSION_RATE_FLAG,
		   DECODE(T1.SOURCE_COEFF, NULL, NULL, '1') SOURCECOEFFFLAG,
		   DECODE(T1.EXTRA_COEFF, NULL, NULL, '1') EXTRACOEFFFLAG ,
		   #{userid},
		   T.REALPREID,
		DECODE(T1.TOTAL_ACHIEVE, NULL, NULL, '1') TOTALACHIEVEFLAG,
		T.manage_coeff MANAGECOEFF,

		case
		--新算法 非股权且大单定制或活动竞赛情况  取到账手续费(RMB)」* 原佣金率 / 100 * 管理系数
		when #{newPrpCalYear} = '1' and T.account_product_type != '3' and T.DISCOUNT_TYPE in ('5','8') then
		T.ACK_FEE_RMB * productCoeff.COMMISSION_RATE  * T.manage_coeff
		--其他情况计算不变
		when T.ACCOUNT_PRODUCT_TYPE = '4' then 0
		when (T.ACCOUNT_PRODUCT_TYPE is null or T.ACCOUNT_PRODUCT_TYPE != '4')
		and DISCOUNT_TYPE in ('5','6','8')
		then T.ACK_AMT_RMB * T.COMMISSION_RATE/ 100.0 * T.manage_coeff *
		DECODE(T1.UNQUALIFIED_COEFF,NULL,NVL(T.UNQUALIFIED_COEFF, 1),T1.UNQUALIFIED_COEFF)
		else  (T.ACK_AMT_RMB * T.COMMISSION_RATE/100 - nvl(DISCOUNT_FEE,0)) * T.manage_coeff *
		DECODE(T1.UNQUALIFIED_COEFF,NULL,NVL(T.UNQUALIFIED_COEFF, 1),T1.UNQUALIFIED_COEFF) end as MANAGECOEFFCOMMISSION,
		T1.adjust_manage_coeff_commission ADJUSTMANAGECOEFFCOMMISSION

		  FROM CM_PRP_FQSXJH_CAL T
		  LEFT JOIN CM_PRP_FQSXJH_CAL_EXP T1
		    ON T.PREID = T1.PREID
		  LEFT JOIN CM_CONSULTANT T3
		    ON T.CONSCODE = T3.CONSCODE
		  LEFT JOIN CM_PREBOOKPRODUCTINFO T4
		    ON T.PREID = T4.ID
		  LEFT JOIN JJXX1 T5
		    ON T4.PCODE = T5.JJDM
		LEFT JOIN CM_PRP_PRODUCT_COEFFICIENT productCoeff
		ON T4.PCODE = productCoeff.FUNDCODE
		AND T.ACCOUNT_DT >= productCoeff.START_DT
		AND T.ACCOUNT_DT &lt;= productCoeff.End_Dt
		LEFT JOIN (SELECT CMR.PREID,
			CPM.FIRSTPREID,
			CASE
			WHEN CPM.FIRSTPREID = CMR.PREID THEN
			'1'
			ELSE
			'2'
			END MANYCALLFLAG,
			CPM.TOTALAMT,
			ROW_NUMBER() OVER(PARTITION BY CMR.CALLID ORDER BY CMR.CREDDT ASC) AS RN
			FROM CM_PREBOOK_MANYCALL CPM
			LEFT JOIN CM_MANYCALL_RELATION CMR
			ON CPM.ID = CMR.CALLID) MANYCALL
			ON T4.id = MANYCALL.PREID

		WHERE t.quarter = #{quarter}
		-- 债券A按小集合算法核算，这里要剔除债券A类产品
		AND (t.account_product_type not in ('6','12','13','14','15') or t.account_product_type is null)
         <if test="accountProductType != null"> AND t.account_product_type = #{accountProductType} </if>
		<if test="discountType != null"> AND t.DISCOUNT_TYPE = #{discountType} </if>
		<if test="preId != null"> AND T.PREID = #{preId} </if>
         <if test="fundcode != null"> AND t4.pcode = #{fundcode} </if>
         <if test="custname != null"> and t4.conscustName like '%'||#{custname ,jdbcType=VARCHAR}||'%' </if>
         <if test="conscode != null"> and T.CONSCODE = #{conscode,jdbcType=VARCHAR} </if>
	     <if test="conscode == null and orgcode != null"> 
	     	and ( T3.TEAMCODE = #{orgcode} OR
	     		T3.OUTLETCODE in (
	     			SELECT HO.ORGCODE
					  FROM HB_ORGANIZATION HO
					 WHERE HO.STATUS = '0'
					 START WITH HO.ORGCODE = #{orgcode}
					CONNECT BY PRIOR ORGCODE = PARENTORGCODE
	     		)
	     	)
	     	
	     </if>
	</insert>
	<delete id="delCmPrpFqsxjhFinalByParam" parameterType="Map">
	    delete from CM_PRP_FQSXJH_Final where QUARTER = #{quarter}
	</delete>
      
    <select id="listRepeatCmPrpFqsxjh" parameterType="Map" resultType="CmPrpFqsxjhCal" useCache="false">
    	SELECT T.PREID, T.QUARTER
		  FROM CM_PRP_FQSXJH_FINAL T
		 WHERE T.PREID IN (SELECT T1.PREID
		                     FROM CM_PRP_FQSXJH_CAL T1
		                    WHERE T1.QUARTER = #{quarter})
		   AND T.QUARTER != #{quarter}
    </select>
    
    <update id="initCmPrpFqsxjhCalExp" parameterType="Map" >
	    UPDATE CM_PRP_FQSXJH_CAL_EXP
	    <set>
			<if test="initSourceCoeff != null"> source_Coeff = null, </if>
			<if test="initUnqualifiedCoeff != null"> unqualified_Coeff = null, </if>
			<if test="initExtraCoeff != null"> extra_Coeff = null, </if>
			modor=#{modor},                     
            update_time = sysdate,           
         </set>
          where preid = #{preid}
	  </update>
	  
	  <update id="mergeCmPrpFqsxjhCalExp" parameterType="Map" >
	    MERGE INTO CM_PRP_FQSXJH_CAL_EXP t
		using (select #{preid} as preid from dual) t1
		on (t.preid = t1.preid)
		when not matched then
		INSERT
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="preid != null"> preid, </if>
			<if test="editSourceCoeffVal != null"> source_Coeff, </if>
			<if test="editUnqualifiedCoeffVal != null"> unqualified_Coeff, </if>
			<if test="editExtraCoeffVal != null"> extra_Coeff, </if>
			<if test="editTotalAchieve != null"> total_Achieve, </if>
			<if test="adjustManageCoeffCommission != null"> adjust_manage_coeff_commission, </if>
			<if test="adjManageCoeffRegsubtotalComm != null"> manage_coeff_regsubtotal_comm, </if>
			<if test="adjManageCoeffRegtotalComm != null"> manage_coeff_regtotal_comm, </if>
			<if test="modor != null"> CREATOR, </if>
			<if test="modor != null"> MODOR, </if>
		</trim>
		VALUES
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="preid != null"> #{preid}, </if>
			<if test="editSourceCoeffVal != null"> #{editSourceCoeffVal}, </if>
			<if test="editUnqualifiedCoeffVal != null"> #{editUnqualifiedCoeffVal}, </if>
			<if test="editExtraCoeffVal != null"> #{editExtraCoeffVal}, </if>
			<if test="editTotalAchieve != null"> #{editTotalAchieve}, </if>
			<if test="adjustManageCoeffCommission != null"> #{adjustManageCoeffCommission}, </if>
			<if test="adjManageCoeffRegsubtotalComm != null"> #{adjManageCoeffRegsubtotalComm}, </if>
			<if test="adjManageCoeffRegtotalComm != null"> #{adjManageCoeffRegtotalComm}, </if>
			<if test="modor != null"> #{modor}, </if>
			<if test="modor != null"> #{modor}, </if>
		</trim>
		WHEN matched THEN
		update
		<set>
			<if test="editSourceCoeffVal != null"> source_Coeff=#{editSourceCoeffVal}, </if>
			<if test="editUnqualifiedCoeffVal != null"> unqualified_Coeff=#{editUnqualifiedCoeffVal}, </if>
			<if test="editExtraCoeffVal != null"> extra_Coeff=#{editExtraCoeffVal}, </if>
			<if test="editTotalAchieve != null"> total_Achieve=#{editTotalAchieve}, </if>
			<if test="adjustManageCoeffCommission != null"> adjust_manage_coeff_commission=#{adjustManageCoeffCommission}, </if>
			<if test="adjManageCoeffRegsubtotalComm != null"> manage_coeff_regsubtotal_comm = #{adjManageCoeffRegsubtotalComm}, </if>
			<if test="adjManageCoeffRegtotalComm != null"> manage_coeff_regtotal_comm = #{adjManageCoeffRegtotalComm}, </if>
			<if test="modor != null"> MODOR=#{modor}, </if>
			update_time = sysdate,
		</set>
		where t.preid = #{preid}
	  </update>

	<select id="listCmPrpFqsxjhCalResult" resultType="CmPrpFqsxjhCal" useCache="false">
	    SELECT T.ID ID,
	           T.ACCOUNT_DT ACCOUNTDT,
	           T4.EXPECTTRADEDT TRADEDT,
	           T4.CONSCUSTNO,
	           T4.CONSCUSTNAME CUSTNAME,
	           T4.PCODE FUNDCODE,
	           T5.JJJC FUNDNAME,
	           T.QUARTER QUARTER,
	           T.PREID PREID,
	           T.CONSCODE CONSCODE,
	           T.ACCOUNT_PRODUCT_TYPE ACCOUNTPRODUCTTYPE,
	           T.ACK_AMT_RMB ACKAMTRMB,
	           T.SUBSCRIBE_AMT_RMB_VAL SUBSCRIBEAMTRMBVAL,
	           T.ACK_FEE_RMB ACKFEERMB,
	           T.DISCOUNT_FEE DISCOUNTFEE,
	           T.ADJUST_DISCOUNT_FEE ADJUSTDISCOUNTFEE,
	           T.DISCOUNT_TYPE DISCOUNTTYPE,
	           T.DISCOUNT_REASON DISCOUNTREASON,
	           T.TRADE_NUM TRADENUM,
	           T.SOURCE_TYPE SOURCETYPE,
	           T.SOURCE_COEFF SOURCECOEFF,
	           T.COMMISSION_RATE COMMISSIONRATE,
	           NVL(T.EXTRA_COEFF, 0) EXTRACOEFF,
	           T.ACHIEVE_COEFF ACHIEVECOEFF,
	           T.TOTAL_ACHIEVE TOTALACHIEVE,
	           T.CREATOR CREATOR,
	           T.CREATE_TIME CREATETIME,
	           T.MODOR MODOR,
	           T.UPDATE_TIME UPDATETIME,
	           T.ACCOUNT_DT_FLAG ACCOUNTDTFLAG,
	           T.COMMISSION_RATE_FLAG COMMISSIONRATEFLAG,
	           T.SOURCE_COEFF_FLAG SOURCECOEFFFLAG,
	           T.EXTRA_COEFF_FLAG EXTRACOEFFFLAG,
	           T.TOTAL_ACHIEVE_FLAG TOTALACHIEVEFLAG,
	           T1.MODOR LASTMODOR,

			   T.manage_coeff managecoeff,
			   T.manage_coeff_commission managecoeffcommission,
			   t.manage_coeff_regionalsubtotal manageCoeffRegionalsubtotal,
			   t.manage_coeff_regionaltotal manageCoeffRegionaltotal,
			   t.manage_coeff_regsubtotal_comm manageCoeffRegsubtotalComm,
			   t.manage_coeff_regtotal_comm manageCoeffRegtotalComm
		FROM cm_prp_fqsxjh_final T
	      LEFT JOIN CM_PRP_FQSXJH_CAL_EXP T1
	        ON T.PREID = T1.PREID
	      LEFT JOIN CM_CONSULTANT T3
	        ON T.CONSCODE = T3.CONSCODE
	      LEFT JOIN CM_PREBOOKPRODUCTINFO T4
	        ON T.PREID = T4.ID
	      LEFT JOIN JJXX1 T5
	        ON T4.PCODE = T5.JJDM
		 WHERE t.quarter = #{param.quarter}
         <if test="param.accountProductType != null"> AND t.account_product_type = #{param.accountProductType} </if>
         <if test="param.fundcode != null"> AND t4.pcode = #{param.fundcode} </if>
         <if test="param.custname != null"> and t4.conscustName like '%'||#{param.custname ,jdbcType=VARCHAR}||'%' </if>
         <if test="param.conscode != null"> and T.CONSCODE = #{param.conscode,jdbcType=VARCHAR} </if>
	     <if test="param.conscode == null and param.orgcode != null">
	     	and ( T3.TEAMCODE = #{param.orgcode} OR
	     		T3.OUTLETCODE in (
	     			SELECT HO.ORGCODE
					  FROM HB_ORGANIZATION HO
					 WHERE HO.STATUS = '0'
					 START WITH HO.ORGCODE = #{param.orgcode}
					CONNECT BY PRIOR ORGCODE = PARENTORGCODE
	     		)
	     	)
	     	
	     </if>
		<if test="disOrganList != null and disOrganList.size()>0">
			and (
			<foreach collection="disOrganList" item="disOrgan" separator=" or ">
				<if test="disOrgan == '1'.toString() ">
					T5.xsjg = '03'
				</if>
				<if test="disOrgan == '2'.toString() ">
					T5.xsjg = '05'
				</if>
				<if test="disOrgan == '3'.toString() ">
					T5.xsjg = '01'
				</if>
				<if test="disOrgan == '4'.toString() ">
					(T5.xsjg is null or T5.xsjg not in ('01','03','05'))
				</if>
			</foreach>
			)
		</if>
	     ORDER BY T3.OUTLETCODE , T3.CONSNAME
      </select>

	<insert id="saveCmPrpFqsxjhCalAll" parameterType="list">
	begin
	<foreach collection="list" item="item" index="index" separator=";">
		INSERT INTO CM_PRP_FQSXJH_CAL
		(ID,
		QUARTER,
		PREID,
		ACCOUNT_DT,
		CONSCODE,
		ACCOUNT_PRODUCT_TYPE,
		ACK_AMT_RMB,
		ACK_FEE_RMB,
		DISCOUNT_FEE,
		ADJUST_DISCOUNT_FEE,
		DISCOUNT_TYPE,
		DISCOUNT_REASON,
		TRADE_NUM,
		SOURCE_TYPE,
		SOURCE_COEFF,
		COMMISSION_RATE,
		CREATOR,
		ACCOUNT_DT_FLAG,
		COMMISSION_RATE_FLAG,
		SUBSCRIBE_AMT_RMB,
		REALPREID,
		manage_coeff,
		manage_coeff_regionalsubtotal,
		manage_coeff_regionaltotal,
		EXPECTTRADEDT,
		PAYSTATE
		)
		SELECT  SEQ_REWARD_ID.NEXTVAL,
			#{item.quarter,jdbcType=VARCHAR},
			#{item.preid,jdbcType=DECIMAL},
			#{item.accountDt,jdbcType=VARCHAR},
			#{item.conscode,jdbcType=VARCHAR},
			#{item.accountProductType,jdbcType=VARCHAR},
			#{item.ackAmtRmb,jdbcType=DECIMAL},
			#{item.ackFeeRmb,jdbcType=DECIMAL},
			#{item.discountFee,jdbcType=DECIMAL},
			#{item.adjustDiscountFee,jdbcType=DECIMAL},
			#{item.discountType,jdbcType=VARCHAR},
			#{item.discountReason,jdbcType=VARCHAR},
			#{item.tradeNum,jdbcType=DECIMAL},
			#{item.sourceType,jdbcType=VARCHAR},
			#{item.sourceCoeff,jdbcType=DECIMAL},
			#{item.commissionRate,jdbcType=DECIMAL},
			#{item.creator,jdbcType=VARCHAR},
			#{item.accountDtFlag,jdbcType=VARCHAR},
			#{item.commissionRateFlag,jdbcType=VARCHAR},
			#{item.subscribeAmtRmb,jdbcType=DECIMAL},
			#{item.realpreId,jdbcType=DECIMAL},
			#{item.manageCoeff,jdbcType=DECIMAL},
			#{item.manageCoeffRegionalsubtotal,jdbcType=DECIMAL},
			#{item.manageCoeffRegionaltotal,jdbcType=DECIMAL},
			#{item.expecttradedt,jdbcType=VARCHAR},
			#{item.paystate,jdbcType=VARCHAR}
		FROM DUAL
	</foreach>
	;end;
	</insert>


	<insert id="saveCmPrpFqsxjhFinalNew" parameterType="list">
		begin
		<foreach collection="list" item="item" index="index" separator=";">
		INSERT INTO CM_PRP_FQSXJH_FINAL
		(id,
		quarter,
		preid,
		conscode,
		account_product_type,
		ack_amt_rmb,
		subscribe_amt_rmb,
		subscribe_amt_rmb_val,
		ack_fee_rmb,
		discount_fee,
		adjust_discount_fee,
		discount_type,
		discount_reason,
		trade_num,
		source_type,
		source_coeff,
		commission_rate,
		achieve_coeff,
		extra_coeff,
		total_achieve,
		account_dt,
		account_dt_flag,
		commission_rate_flag,
		source_coeff_flag,
		extra_coeff_flag,
		creator,
		realpreid,
		total_achieve_flag,
		manage_coeff,
		manage_coeff_commission,
		manage_coeff_regionalsubtotal,
		manage_coeff_regionaltotal,
		manage_coeff_regsubtotal_comm,
		manage_coeff_regtotal_comm
		)
		VALUES
		(SEQ_REWARD_ID.NEXTVAL,
			#{item.quarter,jdbcType=VARCHAR},
			#{item.preid,jdbcType=DECIMAL},
			#{item.conscode,jdbcType=VARCHAR},
			#{item.accountProductType,jdbcType=VARCHAR},
			#{item.ackAmtRmb,jdbcType=DECIMAL},
			#{item.subscribeAmtRmb,jdbcType=DECIMAL},
			#{item.subscribeAmtRmbVal,jdbcType=VARCHAR},
			#{item.ackFeeRmb,jdbcType=DECIMAL},
			#{item.discountFee,jdbcType=DECIMAL},
			#{item.adjustDiscountFee,jdbcType=DECIMAL},
			#{item.discountType,jdbcType=VARCHAR},
			#{item.discountReason,jdbcType=VARCHAR},
			#{item.tradeNum,jdbcType=DECIMAL},
			#{item.sourceType,jdbcType=VARCHAR},
			#{item.sourceCoeff,jdbcType=DECIMAL},
			#{item.commissionRate,jdbcType=DECIMAL},
			#{item.achieveCoeff,jdbcType=DECIMAL},
			#{item.extraCoeff,jdbcType=DECIMAL},
			#{item.totalAchieve,jdbcType=DECIMAL},
			#{item.accountDt,jdbcType=VARCHAR},
			#{item.accountDtFlag,jdbcType=VARCHAR},
			#{item.commissionRateFlag,jdbcType=VARCHAR},
			#{item.sourceCoeffFlag,jdbcType=VARCHAR},
			#{item.extraCoeffFlag,jdbcType=VARCHAR},
			#{item.creator,jdbcType=VARCHAR},
			#{item.realpreId,jdbcType=DECIMAL},
			#{item.totalAchieveFlag,jdbcType=VARCHAR},
			#{item.manageCoeff,jdbcType=DECIMAL},
			#{item.manageCoeffCommission,jdbcType=DECIMAL},
			#{item.manageCoeffRegionalsubtotal,jdbcType=DECIMAL},
			#{item.manageCoeffRegionaltotal,jdbcType=DECIMAL},
			#{item.manageCoeffRegsubtotalComm,jdbcType=DECIMAL},
			#{item.manageCoeffRegtotalComm,jdbcType=DECIMAL})
		</foreach>
		;end;
	</insert>


</mapper>