package com.howbuy.crm.hb.domain.reward;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: shuai.zhang
 * @Date: 2021/9/1
 * @Description:配置-存续系数
 */
@Data
public class CmPrpCxCoeff implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键id */
    private Long id;
    /** 存续类型 存续A、存续B */
    private String cxType;
    /** 来源类型 公司资源、投顾资源 */
    private String sourceType;
    /** 二级系数 */
    private BigDecimal ejCoeff;
    /** 股权系数 */
    private BigDecimal gqCoeff;
    /**
     * 起始日期
     */
    private String startDt;
    /**
     * 结束日期
     */
    private String endDt;

    private String creator;

    private Date createTime;

    private String modor;

    private Date updateTime;

}
