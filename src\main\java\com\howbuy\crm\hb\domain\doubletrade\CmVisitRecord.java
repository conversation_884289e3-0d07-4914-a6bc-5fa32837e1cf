package com.howbuy.crm.hb.domain.doubletrade;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 新规回访记录
 * @reason:
 * @Date: 2020/4/28 13:34
 */
@Data
public class CmVisitRecord implements Serializable{
    private static final long serialVersionUID = 1L;
    private Long id;
    private String tradeId;
    private String conscustno;
    private String conscustname;
    private String consName;
    private String orgCode;
    private String orgName;
    private String upOrgName;
    /** 客户一级来源 */
    private String sourceName;
    private String fundCode;
    private String fundName;
    /** 购买金额 */
    private String appAmt;
    /** 打款确认日期 */
    private String pmtDt;
    /** 回访时间 */
    private Date visitDt;
    /** 回访时间开始（查询条件） */
    private Date visitDtBegin;
    /** 回访时间结束（查询条件） */
    private Date visitDtEnd;
    /** 第二次时间 */
    private Date secondDt;
    /** 第三次时间 */
    private Date thirdDt;
    /** 回访结果 */
    private String visitResult;
    /** 回访完成时间 */
    private String visitResultDt;
    /** 存在问题 */
    private String visitProblem;
    /** 沟通记录 */
    private String commRecord;
    /** 回访人 */
    private String visitMan;
    private String remark;
    private Date createTime;
    /** 是否上传录音 */
    private String isUpload;
    /** 历史沟通记录id列表(逗号分隔) */
    private String commRecordIds;
    /** 所属中心 */
    private String partOrgName;
    /** 删除标识：0-正常；1-已删除 */
    private String recstate;
    /** 数据入库时间 */
    private String insertdbdt;

    /**  任务分配日期 */
    private String assignTime;

    /**
     *新规回访方式类型 ：1-电话回访 2-邮件回访 3-问卷回访
     */
    private String feedBackType;
}
