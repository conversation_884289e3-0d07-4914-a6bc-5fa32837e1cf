package com.howbuy.crm.hb.domain.wage;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class CrmWageQuaterPerformance implements Serializable {

    private static final long serialVersionUID = -6430037841855553488L;

    private long id;

    private String orgCode;

    private BigDecimal quaterPerformanceMin;

    private BigDecimal quaterPerformanceMax;

    private BigDecimal performanceRatio;

    private String startdt;

    private String enddt;

    private String creator;

    private String  modifier;

    private Timestamp credt;

    private Timestamp moddt;
}
