package com.howbuy.crm.hb.persistence.insur;

import com.howbuy.crm.hb.domain.insur.CmBxPrebookCommission;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxPrebookCommissionMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
    CmBxPrebookCommission getCmBxPrebookCommission(Map<String, Object> param);


	/**
	 * 根据 佣金明细ID 获取数据对象
	 * @param commissionId
	 * @return
	 */
	CmBxPrebookCommission getCommissionById(@Param("commissionId") BigDecimal commissionId);


	/**
	 * 获取佣金信息列表.不包括动态计算佣金相关属性
	 * @param commissionIdList
	 * @return
	 */
	List<CmBxPrebookCommission> getCommissionList(@Param("commissionIdList") List<BigDecimal> commissionIdList);
    
     /**
      * 新增数据对象
      * @param cmBxPrebookCommission
      */
	void insertCmBxPrebookCommission(CmBxPrebookCommission cmBxPrebookCommission);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxPrebookCommission
	 */
	int updateCmBxPrebookCommission(CmBxPrebookCommission cmBxPrebookCommission);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxPrebookCommission> listCmBxPrebookCommission(Map<String, Object> param);
	
	/**
	 * 删除佣金明细
	 * @param param
	 */
	void deleteCmBxPrebookCommission(Map<String, Object> param);
	
	/**
	 * 更新人工输入的公司佣金数据
	 * @param updateCommission
	 */
	void updateExpCommission(CmBxPrebookCommission updateCommission);



	/**
	 * @description:(更新备忘)
	 * @param commissionId	佣金明细Id
	 * @param settleRemark 备忘
	 * @return void
	 * @author: haoran.zhang
	 * @date: 2024/9/24 9:21
	 * @since JDK 1.8
	 */
	void updateSettleRemark(@Param("commissionId") BigDecimal commissionId, @Param("settleRemark") String settleRemark);
}
