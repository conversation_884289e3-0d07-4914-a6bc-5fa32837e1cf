package com.howbuy.crm.hb.domain.insur;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @Description: 实体类CmBxProductCoeff.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxProductCoeffRatio implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private BigDecimal coeffid;
	private BigDecimal ratio;
	private String isdel;
	private String creator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	private String ratiotype;
	private BigDecimal year;
	private BigDecimal ratioone;
	private BigDecimal ratiotwo;
	private BigDecimal gsratio;
	private BigDecimal tgratio;
}
