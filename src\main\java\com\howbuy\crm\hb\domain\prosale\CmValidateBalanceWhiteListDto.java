package com.howbuy.crm.hb.domain.prosale;

import com.google.common.collect.Lists;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.prosale.dto.CmPreSaleForControlCalInfo;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *销控--校验白名单-校验金额-dto
 * <AUTHOR>
 * @time 2022-1-7 13:46:31
 */
@Data
public class CmValidateBalanceWhiteListDto  implements Serializable {
	private static final long serialVersionUID = 1L;


	public CmValidateBalanceWhiteListDto() {
	}

	/**
	 *
	 * @param usedLimit 金额-限额
	 * @param validatePreList  追加的数据  也就是 待验证的数据
	 * @param calculatePreList 当前统计内的 数据
	 */
	public CmValidateBalanceWhiteListDto(BigDecimal usedLimit, List<CmPreForWhiteValidateDto> validatePreList, List<CmPreSaleForControlCalInfo> calculatePreList) {
		this.usedLimit = usedLimit;
		this.validatePreList = CollectionUtils.isEmpty(validatePreList)?Lists.newArrayList():validatePreList;
		this.calculatePreList = CollectionUtils.isEmpty(calculatePreList)?Lists.newArrayList():calculatePreList;
	}

	/**
     * 日历Id
	 */
	private String calenderId;

    /**
     * 日历的 总额度-金额
	 */
	private BigDecimal totalBalanceLimit;


    /**
     * 参数配置的-IC额度
	 */
	private BigDecimal configIcBalanceLimit;

	/**
	 * 参数配置的-HBC额度
	 */
	private BigDecimal configHbcBalanceLimit;


	/**
	 * 校验数据使用的 额度控制
	 */
	private BigDecimal usedLimit;

	/**
	 * 追加的数据  也就是 待验证的数据
	 */
	List<CmPreForWhiteValidateDto> validatePreList= Lists.newArrayList();

	/**
	 * 当前统计内的 数据
	 */
	List<CmPreSaleForControlCalInfo>  calculatePreList=Lists.newArrayList();




	/**
	 * 验证金额
	 * @return
	 */
	public ReturnMessageDto<String> validate(){
		// 校验本期已预约金额+本次预约金额  是否达到预约控制参数“预约金额达本期额度”
		if(usedLimit==null){
			return ReturnMessageDto.ok();
		}
		if(CollectionUtils.isEmpty(getValidatePreList())){
			return ReturnMessageDto.ok();
		}
        //追加 金额
		BigDecimal newAddBalance=getValidatePreList().stream().map(CmPreForWhiteValidateDto::getBuyAmt).reduce(BigDecimal::add)
				.orElse(BigDecimal.ZERO);
		//统计内已有 金额
		BigDecimal calculateBalance=getCalculatePreList().stream().map(CmPreSaleForControlCalInfo::getBuyAmt).reduce(BigDecimal::add)
				.orElse(BigDecimal.ZERO);

		BigDecimal combineAmt=calculateBalance.add(newAddBalance);
		if(combineAmt.compareTo(usedLimit)>0){
			return ReturnMessageDto.fail("添加后白名单内的预约金额超过本期总额度，请重新选择!");
		}
		return ReturnMessageDto.ok();
	}




}
