package com.howbuy.crm.hb.domain.custinfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 实体类CmBirthdayGiftInfo.java
 * @created
 * @version 1.0
 */
public class CmBirthdayGiftInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String custName;
    private String consCustNo;
    private String hboneno;
    private String consName;
    private String consDept;
    private String birthday;
    private String choiceTime;
    private String giftType;
    private String giftCode;
    private Date dataYear;
    private String pushFlag;
    private String u1Name;
    private String u2Name;
    private String u3Name;
    private String conscode;
	private BigDecimal premiumAmount;


    /**
     * 收货人姓名
     */
    private String deliveryName;
    /**
     * 收货人手机号掩码
     */
    private String deliveryMobileMask;

    /**
     * 收货人手机号 - 密文
     */
    private String phoneEncrypt;

    /**
     * 能否查看 收货人手机号-明文
     */
    private Boolean mobileViewFlag;

    /**
     * 收货地区(省市区空格分隔)
     */
    private String deliveryArea;
    /**
     * 收货地址
     */
    private String deliveryAddr;
    /**
     * 寄送方式(1:公司寄送,2:人工寄送)
     */
    private String deliveryMethod;

    /**
     * 寄送方式(1:好买财富统一寄送,2:投顾联系寄送)
     */
    private String deliveryMethodName;

    /**
     * id字符串
     */
    private String idString;

    /**
     * 物流单号
     */
    private String waybillNo;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 发货状态
     */
    private String shippingStatus;

    /**
     * 发货状态名称
     */
    private String shippingStatusName;

    /**
     * 是否添加企微且绑定,1:是,0:否
     */
    private String addWeChatAndBind;


    /**
     * 是否添加企微且绑定,是/否
     */
    private String addWeChatAndBindStr;

    public String getAddWeChatAndBindStr() {
        return addWeChatAndBindStr;
    }

    public void setAddWeChatAndBindStr(String addWeChatAndBindStr) {
        this.addWeChatAndBindStr = addWeChatAndBindStr;
    }

    public String getAddWeChatAndBind() {
        return addWeChatAndBind;
    }

    public void setAddWeChatAndBind(String addWeChatAndBind) {
        this.addWeChatAndBind = addWeChatAndBind;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getConsCustNo() {
        return consCustNo;
    }

    public void setConsCustNo(String consCustNo) {
        this.consCustNo = consCustNo;
    }

    public String getHboneno() {
        return hboneno;
    }

    public void setHboneno(String hboneno) {
        this.hboneno = hboneno;
    }

    public String getConsName() {
        return consName;
    }

    public void setConsName(String consName) {
        this.consName = consName;
    }

    public String getConsDept() {
        return consDept;
    }

    public void setConsDept(String consDept) {
        this.consDept = consDept;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getChoiceTime() {
        return choiceTime;
    }

    public void setChoiceTime(String choiceTime) {
        this.choiceTime = choiceTime;
    }

    public String getGiftType() {
        return giftType;
    }

    public void setGiftType(String giftType) {
        this.giftType = giftType;
    }

    public String getGiftCode() {
        return giftCode;
    }

    public void setGiftCode(String giftCode) {
        this.giftCode = giftCode;
    }

    public Date getDataYear() {
        return dataYear;
    }

    public void setDataYear(Date dataYear) {
        this.dataYear = dataYear;
    }

    public String getPushFlag() {
        return pushFlag;
    }

    public void setPushFlag(String pushFlag) {
        this.pushFlag = pushFlag;
    }

    public String getU1Name() {
        return u1Name;
    }

    public void setU1Name(String u1Name) {
        this.u1Name = u1Name;
    }

    public String getU2Name() {
        return u2Name;
    }

    public void setU2Name(String u2Name) {
        this.u2Name = u2Name;
    }

    public String getU3Name() {
        return u3Name;
    }

    public void setU3Name(String u3Name) {
        this.u3Name = u3Name;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public BigDecimal getPremiumAmount() {
        return premiumAmount;
    }

    public void setPremiumAmount(BigDecimal premiumAmount) {
        this.premiumAmount = premiumAmount;
    }



    public String getDeliveryName() {
        return deliveryName;
    }

    public void setDeliveryName(String deliveryName) {
        this.deliveryName = deliveryName;
    }

    public String getDeliveryMobileMask() {
        return deliveryMobileMask;
    }

    public void setDeliveryMobileMask(String deliveryMobileMask) {
        this.deliveryMobileMask = deliveryMobileMask;
    }

    public String getPhoneEncrypt() {
        return phoneEncrypt;
    }

    public void setPhoneEncrypt(String phoneEncrypt) {
        this.phoneEncrypt = phoneEncrypt;
    }

    public Boolean getMobileViewFlag() {
        return mobileViewFlag;
    }

    public void setMobileViewFlag(Boolean mobileViewFlag) {
        this.mobileViewFlag = mobileViewFlag;
    }

    public String getDeliveryArea() {
        return deliveryArea;
    }

    public void setDeliveryArea(String deliveryArea) {
        this.deliveryArea = deliveryArea;
    }

    public String getDeliveryAddr() {
        return deliveryAddr;
    }

    public void setDeliveryAddr(String deliveryAddr) {
        this.deliveryAddr = deliveryAddr;
    }

    public String getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(String deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryMethodName() {
        return deliveryMethodName;
    }

    public void setDeliveryMethodName(String deliveryMethodName) {
        this.deliveryMethodName = deliveryMethodName;
    }

    public String getIdString() {
        return idString;
    }

    public void setIdString(String idString) {
        this.idString = idString;
    }

    public String getWaybillNo() {
        return waybillNo;
    }

    public void setWaybillNo(String waybillNo) {
        this.waybillNo = waybillNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getShippingStatus() {
        return shippingStatus;
    }

    public void setShippingStatus(String shippingStatus) {
        this.shippingStatus = shippingStatus;
    }

    public String getShippingStatusName() {
        return shippingStatusName;
    }

    public void setShippingStatusName(String shippingStatusName) {
        this.shippingStatusName = shippingStatusName;
    }
}
