/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.beisen;

import lombok.Data;

import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/4 9:33
 * @since JDK 1.8
 */
@Data
public class CmBeisenOrgConfigPO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 架构ID（北森）
     */
    private String orgIdBeisen;

    /**
     * 架构名称（北森）
     */
    private String orgNameBeisen;

    /**
     * 所属部门
     */
    private String orgCode;

    /**
     * 业务中心
     */
    private String centerOrg;

    /**
     * 起始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date credt;

    /**
     * 修改时间
     */
    private Date moddt;
}