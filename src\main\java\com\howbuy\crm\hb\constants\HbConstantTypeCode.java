/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.constants;

/**
 * @description: 常量类别定义
 * <AUTHOR>
 * @date 2023/12/14 10:26
 * @since JDK 1.8
 */
public class HbConstantTypeCode {

    /**
     * 身份类型
     */
    public static final String ID_TYPE = "idtype";
    /**
     * 投资类型
     */
    public static final String INVST_TYPES = "InvstTypes";
    /**
     * 性别类型
     */
    public static final String HRGENDER  = "hrgender";
    /**
     * 员工状态
     */
    public static final String HRQUERYWORKTYPE = "hrqueryworktype";
    /**
     * 在职状态
     */
    public static final String HRWORKSTATE = "hrworkstate";
    /**
     * 学历
     */
    public static final String HREDULEVEL = "hredulevel";
    /**
     * 职级
     */
    public static final String HRPOSITIONSLEVEL = "hrpositionslevel";

}