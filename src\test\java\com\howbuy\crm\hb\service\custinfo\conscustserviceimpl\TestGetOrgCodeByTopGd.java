package com.howbuy.crm.hb.service.custinfo.conscustserviceimpl;

import com.howbuy.crm.hb.service.custinfo.impl.ConscustServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import java.lang.reflect.Method;

/**
 * @description: 单元测试:根据广度信息获取对应组织编码
 * @author: jianyi.tao
 * @create: 2022/09/29 14:43
 * @since: JDK 1.8
 */
@PowerMockIgnore("javax.management.*")
@RunWith(PowerMockRunner.class)
@PrepareForTest({ConscustServiceImpl.class})
public class TestGetOrgCodeByTopGd extends PowerMockTestCase {
    @Mock
    private ConscustServiceImpl conscustServiceImpl = PowerMockito.spy(conscustServiceImpl = new ConscustServiceImpl());

    /**
     * 1、模拟广度配置值的为：11-所有客户（包括未分配），则返回查看的组织结构范围为:0-全部
     */
    @Test
    public void test01() throws Exception {
        Method method = PowerMockito.method(ConscustServiceImpl.class, "getOrgCodeByTopGd",
                null, null, null, null);

        // 调用目标方法获取返回结果
        Object result = method.invoke(conscustServiceImpl, "11", "100000025", "200000999", "100000001");

        Assert.assertEquals((String) result, "0");
    }

    /**
     * 2、模拟广度配置值的为：12-所有客户（不包括未分配）
     */
    @Test
    public void test02() throws Exception {
        Method method = PowerMockito.method(ConscustServiceImpl.class, "getOrgCodeByTopGd",
                null, null, null, null);

        // 调用目标方法获取返回结果
        Object result = method.invoke(conscustServiceImpl, "12", "100000025", "200000999", "100000001");

        Assert.assertEquals((String) result, "0");
    }

    /**
     * 3、模拟广度配置值的为：13-所属组织架构及下属子部门客户
     */
    @Test
    public void test03() throws Exception {
        Method method = PowerMockito.method(ConscustServiceImpl.class, "getOrgCodeByTopGd",
                null, null, null, null);

        // 调用目标方法获取返回结果
        Object result = method.invoke(conscustServiceImpl, "13", "100000025", "200000999", "100000001");

        Assert.assertEquals((String) result, "100000025");
    }

    /**
     * 4、模拟广度配置值的为：14-所属团队客户
     */
    @Test
    public void test04() throws Exception {
        Method method = PowerMockito.method(ConscustServiceImpl.class, "getOrgCodeByTopGd",
                null, null, null, null);

        // 调用目标方法获取返回结果
        Object result = method.invoke(conscustServiceImpl, "14", "100000025", "200000999", "100000001");

        Assert.assertEquals((String) result, "200000999");
    }

    /**
     * 5、模拟广度配置值的为：15-分配给自己客户
     */
    @Test
    public void test05() throws Exception {
        Method method = PowerMockito.method(ConscustServiceImpl.class, "getOrgCodeByTopGd",
                null, null, null, null);

        // 调用目标方法获取返回结果
        Object result = method.invoke(conscustServiceImpl, "15", "100000025", "200000999", "100000001");

        Assert.assertEquals((String) result, "100000001");
    }
}
