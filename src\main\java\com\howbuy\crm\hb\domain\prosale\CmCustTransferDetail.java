package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 实体类CmCustTransferDetail.java
 * <AUTHOR>
 * @version 1.0
 */
public class CmCustTransferDetail implements Serializable {

	private static final long serialVersionUID = 1L;

	private String depositSid;

	private String contractNo;

	private String hboneNo;

	private String vouchDt;

	private Date vouchStimestamp;

	private BigDecimal occurBalance;

	/**
	 * 客户余额
	 */
	private BigDecimal depositAvailBalance;
	
	private String summaryInfo;
	
	private String depositMatchStatus;

	private String thisBankAcct;

	private String thatBankAcct;

	private String thatBankAcctName;
	
	private Date stimestamp;
	
	private Date updateStimestamp;
	
	private String custname;
	
	private String isRelatedAcct;

	public String getDepositSid() {
		return depositSid;
	}

	public void setDepositSid(String depositSid) {
		this.depositSid = depositSid;
	}

	public String getContractNo() {
		return contractNo;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}

	public String getHboneNo() {
		return hboneNo;
	}

	public void setHboneNo(String hboneNo) {
		this.hboneNo = hboneNo;
	}

	public String getVouchDt() {
		return vouchDt;
	}

	public void setVouchDt(String vouchDt) {
		this.vouchDt = vouchDt;
	}

	public Date getVouchStimestamp() {
		return vouchStimestamp;
	}

	public void setVouchStimestamp(Date vouchStimestamp) {
		this.vouchStimestamp = vouchStimestamp;
	}

	public BigDecimal getOccurBalance() {
		return occurBalance;
	}

	public void setOccurBalance(BigDecimal occurBalance) {
		this.occurBalance = occurBalance;
	}

	public String getSummaryInfo() {
		return summaryInfo;
	}

	public void setSummaryInfo(String summaryInfo) {
		this.summaryInfo = summaryInfo;
	}

	public String getDepositMatchStatus() {
		return depositMatchStatus;
	}

	public void setDepositMatchStatus(String depositMatchStatus) {
		this.depositMatchStatus = depositMatchStatus;
	}

	public String getThisBankAcct() {
		return thisBankAcct;
	}

	public void setThisBankAcct(String thisBankAcct) {
		this.thisBankAcct = thisBankAcct;
	}

	public String getThatBankAcct() {
		return thatBankAcct;
	}

	public void setThatBankAcct(String thatBankAcct) {
		this.thatBankAcct = thatBankAcct;
	}

	public String getThatBankAcctName() {
		return thatBankAcctName;
	}

	public void setThatBankAcctName(String thatBankAcctName) {
		this.thatBankAcctName = thatBankAcctName;
	}

	public Date getStimestamp() {
		return stimestamp;
	}

	public void setStimestamp(Date stimestamp) {
		this.stimestamp = stimestamp;
	}

	public Date getUpdateStimestamp() {
		return updateStimestamp;
	}

	public void setUpdateStimestamp(Date updateStimestamp) {
		this.updateStimestamp = updateStimestamp;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public BigDecimal getDepositAvailBalance() {
		return depositAvailBalance;
	}

	public void setDepositAvailBalance(BigDecimal depositAvailBalance) {
		this.depositAvailBalance = depositAvailBalance;
	}

	public String getIsRelatedAcct() {
		return isRelatedAcct;
	}

	public void setIsRelatedAcct(String isRelatedAcct) {
		this.isRelatedAcct = isRelatedAcct;
	}
	
}
