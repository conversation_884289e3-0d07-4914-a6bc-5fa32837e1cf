package com.howbuy.crm.hb.domain.insur;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/4/10 15:20
 */
@Data
public class BxBalanceDetail implements Serializable{

    private static final long serialVersionUID = 1L;
    /** 受保人姓名 */
    private String insurname;

    private String fundcode;

    private String fundname;
    /** 产品类型1储蓄型、2重疾险、3寿险、4意外险、5医疗险 */
    private String prodtype;
    /** 币种 */
    private String currency;
    /** 缴费年限 */
    private String payyears;
    /** 年缴保费 */
    private BigDecimal yearamk;
    /** 保障年限 */
    private String ensureyears;
    /** 保额 */
    private BigDecimal insuramk;
    /** 到期状态（1：正常；0：终止） */
    private String expirestat;
    /** 合作渠道 */
    private String channname;
    /** 下次缴费日期 */
    private String nextPayDt;
    /** 已缴费年限 */
    private String payNums;
}
