/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (消息角色枚举)
 * <AUTHOR>
 * @date 2024/10/31 15:17
 * @since JDK 1.8
 */
public enum MessageRoleEnum {
    MESSAGE_ROLE_1("ROLE_IC_KPI_NEW", "中心绩效负责人"),
    MESSAGE_ROLE_2("ROLE_IC_KPI_A", "总部绩效岗A角"),
    MESSAGE_ROLE_3("ROLE_SALE_CONSRELATION", "中心员工关系岗"),
    MESSAGE_ROLE_4("ROLE_IC_KPI_B", "总部绩效岗B角");

    private String code;
    private String name;

    MessageRoleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * @description:(返回所有枚举数据)
     * @param
     * @return java.util.List<java.lang.String>
     * @author: shijie.wang
     * @date: 2024/11/14 9:52
     * @since JDK 1.8
     */
    public static List<String> getMessageRoleCode(){
        return Arrays.stream(MessageRoleEnum.values()).map(MessageRoleEnum::getCode).collect(Collectors.toList());
    }

}