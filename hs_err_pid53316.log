#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_SINGLE_STEP (0x80000004) at pc=0x00007ff96d9a1033, pid=53316, tid=64772
#
# JRE version:  (17.0.9+7) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.9+7-b1000.46, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  [KERNELBASE.dll+0x31033]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Password for 'http://<EMAIL>': 

Host: 11th Gen Intel(R) Core(TM) i7-1165G7 @ 2.80GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5198)
Time: Wed Jan 22 14:40:37 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5198) elapsed time: 0.063048 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread is native thread

Stack: [0x0000000f61600000,0x0000000f61700000],  sp=0x0000000f616fed90,  free space=1019k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [KERNELBASE.dll+0x31033]
V  [jvm.dll+0x6a9c7c]
V  [jvm.dll+0x6a8fb3]
V  [jvm.dll+0x6aa1bd]
V  [jvm.dll+0x6a8f07]
V  [jvm.dll+0x6a8abe]
V  [jvm.dll+0x372217]
V  [jvm.dll+0x7f12b3]
V  [jvm.dll+0x3f5d4f]
V  [jvm.dll+0x3f7981]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


siginfo: EXCEPTION_SINGLE_STEP (0x80000004)


Registers:
RAX=0x0000000000000001, RBX=0x000000000000001c, RCX=0x00000000000002d8, RDX=0x0000018d5eb5f910
RSP=0x0000000f616fed90, RBP=0x0000000f616fee39, RSI=0x0000018d5eb5f900, RDI=0x0000000000000000
R8 =0x000000000000001c, R9 =0x0000018d5eb5f910, R10=0x0000018d5c253230, R11=0x0000000f616fed90
R12=0x00000000001f001f, R13=0x0000000000020000, R14=0x0000018d5c253230, R15=0x00000000000f001f
RIP=0x00007ff96d9a1033, EFLAGS=0x0000000000000202


Top of Stack: (sp=0x0000000f616fed90)
0x0000000f616fed90:   000000000000001c 00007ff8cdc59c7c
0x0000000f616feda0:   000000000000001c 0000018d5eb5f900
0x0000000f616fedb0:   0000dd42b00fcaf0 0000018d5eb5fa40
0x0000000f616fedc0:   0000000f616fee00 0000018d00000000
0x0000000f616fedd0:   0000018d00000000 0000000000000000
0x0000000f616fede0:   0000000000000000 00007ff900000000
0x0000000f616fedf0:   0000000f616fee10 0000018d5c200000
0x0000000f616fee00:   000000000000002c 00000000000002d8
0x0000000f616fee10:   0000018d5eb71010 0000018d5eb71190
0x0000000f616fee20:   0000018d5c2594d0 0000050000000000
0x0000000f616fee30:   0000010000000000 0000018d5c2536b0
0x0000000f616fee40:   0000018d001f01ff 0000018d5eb71190
0x0000000f616fee50:   0000018d001f01ff 0000018d5eb71010
0x0000000f616fee60:   00000000000200a9 00004a0ccd17813a
0x0000000f616fee70:   0000000000010000 0000018d5c2587c0
0x0000000f616fee80:   0000018d5c253500 0000018d5c223b00
0x0000000f616fee90:   0000000f616fef19 00007ff8cdc58fb3
0x0000000f616feea0:   0000018d5eb71270 0000018d5c2587c0
0x0000000f616feeb0:   0000000000000000 0000018d5eb70bb0
0x0000000f616feec0:   0000018d5c253500 0000018d5c223b00
0x0000000f616feed0:   0000018d5eb70bb0 00007ff9702747b1
0x0000000f616feee0:   0000000000000000 0000018d5c200000
0x0000000f616feef0:   0000000000010000 0000000000000000
0x0000000f616fef00:   0000000000000000 0000000000000000
0x0000000f616fef10:   0000000000000000 00007ff96e16f05b
0x0000000f616fef20:   0000018d5eb70bb0 00004a0ccd17823a
0x0000000f616fef30:   0000dd42b00fced0 00000000000002d8
0x0000000f616fef40:   0000000000000000 0000018d5c2587c0
0x0000000f616fef50:   0000018d5eb70bb0 0000018d5c253500
0x0000000f616fef60:   0000018d5c223b00 0000000000000000
0x0000000f616fef70:   0000018d5eb73ed0 00007ff8cdc5a1bd
0x0000000f616fef80:   0000018d5eb73ed0 0000018d5c2587c0 

Instructions: (pc=0x00007ff96d9a1033)
0x00007ff96d9a0f33:   ff 15 b7 b1 1b 00 0f 1f 44 00 00 e9 ef fe ff ff
0x00007ff96d9a0f43:   65 48 8b 0c 25 60 00 00 00 33 d2 4c 8b 44 24 40
0x00007ff96d9a0f53:   48 8b 49 30 48 ff 15 3a c0 1b 00 0f 1f 44 00 00
0x00007ff96d9a0f63:   b8 01 00 00 00 e9 bc fd ff ff 0f b7 09 48 ff 15
0x00007ff96d9a0f73:   51 ab 1b 00 0f 1f 44 00 00 66 41 2b c6 66 83 f8
0x00007ff96d9a0f83:   19 0f 87 05 fd ff ff 66 83 7f 02 3a 0f 85 fa fc
0x00007ff96d9a0f93:   ff ff 4d 8b cc 48 8d 4d d0 4c 8b c7 ba 04 01 00
0x00007ff96d9a0fa3:   00 48 ff 15 e5 b0 1b 00 0f 1f 44 00 00 48 8d 75
0x00007ff96d9a0fb3:   d0 c7 45 d4 5c 00 00 00 e9 cf fc ff ff 8b df e9
0x00007ff96d9a0fc3:   21 ff ff ff 83 f8 12 74 f4 83 f8 14 74 ef 83 f8
0x00007ff96d9a0fd3:   24 0f 85 0e ff ff ff e9 07 f1 09 00 cc cc cc cc
0x00007ff96d9a0fe3:   cc cc cc cc cc cc cc cc cc cc cc cc cc 48 83 ec
0x00007ff96d9a0ff3:   38 48 8b 44 24 60 48 89 44 24 20 48 ff 15 8b aa
0x00007ff96d9a1003:   1b 00 0f 1f 44 00 00 85 c0 78 0b b8 01 00 00 00
0x00007ff96d9a1013:   48 83 c4 38 c3 cc 8b c8 e8 30 1b 00 00 33 c0 eb
0x00007ff96d9a1023:   ef cc cc cc cc cc cc cc cc cc cc cc cc e9 a3 f1
0x00007ff96d9a1033:   92 c2 cc 48 8b d9 83 f9 f4 73 57 41 b9 04 00 00
0x00007ff96d9a1043:   00 4c 8d 05 fd a0 10 00 b9 ab ab ab ab 41 8d 51
0x00007ff96d9a1053:   fd 48 ff 15 25 aa 1b 00 0f 1f 44 00 00 48 85 c0
0x00007ff96d9a1063:   74 06 ff 15 cd bf 1b 00 48 8b cb 48 ff 15 7b b0
0x00007ff96d9a1073:   1b 00 0f 1f 44 00 00 85 c0 78 0c b8 01 00 00 00
0x00007ff96d9a1083:   48 83 c4 20 5b c3 cc 8b c8 e8 bf 1a 00 00 33 c0
0x00007ff96d9a1093:   eb ee 83 fb f6 77 a4 e9 63 f0 09 00 cc cc cc cc
0x00007ff96d9a10a3:   cc cc cc cc cc 40 53 55 56 57 41 54 41 55 41 56
0x00007ff96d9a10b3:   41 57 48 81 ec 98 00 00 00 48 8b 05 dd dd 28 00
0x00007ff96d9a10c3:   48 33 c4 48 89 84 24 88 00 00 00 45 33 ed 41 8b
0x00007ff96d9a10d3:   e8 49 8b f1 48 8b da 4c 8b e1 bf 01 00 00 00 48
0x00007ff96d9a10e3:   85 d2 74 08 3b ef 72 04 66 44 89 2a 66 0f 6f 05
0x00007ff96d9a10f3:   39 ce 1e 00 48 8d 44 24 48 44 89 6c 24 28 33 d2
0x00007ff96d9a1103:   48 89 44 24 20 b8 03 00 00 00 44 8b c8 c7 44 24
0x00007ff96d9a1113:   48 20 00 00 00 44 8b c0 c7 44 24 4c 80 00 00 00
0x00007ff96d9a1123:   48 c7 44 24 50 00 00 20 02 f3 0f 7f 44 24 58 e8 



---------------  P R O C E S S  ---------------

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000000000000, size: 0 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Not initialized>

Dll operation events (0 events):
No events

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff716330000 - 0x00007ff71633a000 	D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\java.exe
0x00007ff970250000 - 0x00007ff970448000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff96fa80000 - 0x00007ff96fb42000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff96d970000 - 0x00007ff96dc6f000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff96e160000 - 0x00007ff96e260000 	C:\Windows\System32\ucrtbase.dll
0x00007ff8d8280000 - 0x00007ff8d8297000 	D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\jli.dll
0x00007ff953a60000 - 0x00007ff953a7b000 	D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\VCRUNTIME140.dll
0x00007ff96f690000 - 0x00007ff96f82d000 	C:\Windows\System32\USER32.dll
0x00007ff96dc70000 - 0x00007ff96dc92000 	C:\Windows\System32\win32u.dll
0x00007ff945670000 - 0x00007ff94590a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ff96e2f0000 - 0x00007ff96e31b000 	C:\Windows\System32\GDI32.dll
0x00007ff96dea0000 - 0x00007ff96dfb7000 	C:\Windows\System32\gdi32full.dll
0x00007ff96ed00000 - 0x00007ff96ed9e000 	C:\Windows\System32\msvcrt.dll
0x00007ff96dca0000 - 0x00007ff96dd3d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff96eca0000 - 0x00007ff96eccf000 	C:\Windows\System32\IMM32.DLL
0x00007ff93e370000 - 0x00007ff93e3e2000 	C:\Windows\LVUAAgentInstBaseRoot\system32\Vozokopot.dll
0x00007ff964a10000 - 0x00007ff964a1c000 	D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\vcruntime140_1.dll
0x00007ff94e880000 - 0x00007ff94e90d000 	D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\msvcp140.dll
0x00007ff8cd5b0000 - 0x00007ff8ce233000 	D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\server\jvm.dll
0x00007ff96f8a0000 - 0x00007ff96f951000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff970170000 - 0x00007ff97020f000 	C:\Windows\System32\sechost.dll
0x00007ff96eb70000 - 0x00007ff96ec93000 	C:\Windows\System32\RPCRT4.dll
0x00007ff96e130000 - 0x00007ff96e157000 	C:\Windows\System32\bcrypt.dll
0x00007ff93a120000 - 0x00007ff93a129000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff96f830000 - 0x00007ff96f89b000 	C:\Windows\System32\WS2_32.dll
0x00007ff96ce70000 - 0x00007ff96cebb000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff961d60000 - 0x00007ff961d6a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff95fa00000 - 0x00007ff95fa27000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff96cce0000 - 0x00007ff96ccf2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff93a2d0000 - 0x00007ff93a691000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathCore.dll
0x00007ff96ea90000 - 0x00007ff96eb6a000 	C:\Windows\System32\COMDLG32.dll
0x00007ff96ee20000 - 0x00007ff96f175000 	C:\Windows\System32\combase.dll
0x00007ff9688b0000 - 0x00007ff9688c4000 	C:\Windows\SYSTEM32\WTSAPI32.dll
0x00007ff96f250000 - 0x00007ff96f2fd000 	C:\Windows\System32\shcore.dll
0x00007ff96cd00000 - 0x00007ff96cd3b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ff963c50000 - 0x00007ff963f8c000 	C:\Windows\SYSTEM32\msi.dll
0x00007ff96fc90000 - 0x00007ff96fce5000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff96e320000 - 0x00007ff96ea8e000 	C:\Windows\System32\SHELL32.dll
0x00007ff95f7a0000 - 0x00007ff95f7bd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff96cd40000 - 0x00007ff96ce0a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ff96fb60000 - 0x00007ff96fc8b000 	C:\Windows\System32\ole32.dll
0x00007ff96f180000 - 0x00007ff96f24d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff945410000 - 0x00007ff9454b4000 	C:\Windows\SYSTEM32\WINSPOOL.DRV
0x00007ff96ecd0000 - 0x00007ff96ecd8000 	C:\Windows\System32\NSI.dll
0x00007ff96c000000 - 0x00007ff96c012000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff93a060000 - 0x00007ff93a08f000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathOM.dll
0x00007ff958f60000 - 0x00007ff958f67000 	C:\Windows\SYSTEM32\MSIMG32.dll
0x00007ff93e1f0000 - 0x00007ff93e1fa000 	D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\jimage.dll
0x00007ff939980000 - 0x00007ff9399ae000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathFw.dll
0x00007ff96b460000 - 0x00007ff96b644000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff92cc50000 - 0x00007ff92cc9e000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathBolo2.dll
0x00007ff9583f0000 - 0x00007ff958424000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff92cc20000 - 0x00007ff92cc4d000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathProtect.dll
0x00007ff96d8e0000 - 0x00007ff96d962000 	C:\Windows\System32\bcryptPrimitives.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Windows\LVUAAgentInstBaseRoot\system32;D:\DevelopTool\IntelliJ IDEA 2023.2.5\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Password for 'http://<EMAIL>': 
java_class_path (initial): D:/DevelopTool/IntelliJ IDEA 2023.2.5/plugins/vcs-git/lib/git4idea-rt.jar;D:/DevelopTool/IntelliJ IDEA 2023.2.5/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532559488                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8520951808                                {product} {ergonomic}
   size_t MinHeapSize                              = 6815736                                   {product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\DevelopTool\jdk1.8.0_271
CLASSPATH=D:\DevelopTool\jdk1.8.0_271\lib
PATH=D:/Program Files/Git/mingw64/libexec/git-core;D:/Program Files/Git/mingw64/libexec/git-core;D:\Program Files\Git\mingw64\bin;D:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\DevelopTool\Microsoft VS Code\bin;D:\DevelopTool\apache-maven-3.3.9\bin;D:\DevelopTool\jdk1.8.0_271\bin;D:\DevelopTool\jdk1.8.0_271\jre\bin;d:\Program Files\Git\cmd;D:\DevelopTool\TortoiseSVN\bin;C:\Program Files\nodeis\node_global;C:\Program Files\Apache Software Foundation\apache-tomcat-7.0.11\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=shijie.wang
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

OOME stack traces (most recent first):
Classloader memory used:
Not available (crashed in non-Java thread)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5198)
OS uptime: 5 days 17:55 hours

CPU: total 8 (initial active 8) 
Processor Information for processor 0
  Max Mhz: 2803, Current Mhz: 2803, Mhz Limit: 2803
Processor Information for processor 1
  Max Mhz: 2803, Current Mhz: 1501, Mhz Limit: 2803
Processor Information for processor 2
  Max Mhz: 2803, Current Mhz: 2803, Mhz Limit: 2803
Processor Information for processor 3
  Max Mhz: 2803, Current Mhz: 2803, Mhz Limit: 2803
Processor Information for processor 4
  Max Mhz: 2803, Current Mhz: 2803, Mhz Limit: 2803
Processor Information for processor 5
  Max Mhz: 2803, Current Mhz: 2803, Mhz Limit: 2803
Processor Information for processor 6
  Max Mhz: 2803, Current Mhz: 1501, Mhz Limit: 2803
Processor Information for processor 7
  Max Mhz: 2803, Current Mhz: 1501, Mhz Limit: 2803

Memory: 4k page, system-wide physical 32504M (14020M free)
TotalPageFile size 37368M (AvailPageFile size 12800M)
current process WorkingSet (physical memory assigned to process): 14M, peak: 14M
current process commit charge ("private bytes"): 5M, peak: 5M

vm_info: OpenJDK 64-Bit Server VM (17.0.9+7-b1000.46) for windows-amd64 JRE (17.0.9+7-b1000.46), built on 2023-10-27 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
