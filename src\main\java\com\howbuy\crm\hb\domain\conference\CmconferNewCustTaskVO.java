/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.domain.conference;

import com.howbuy.crm.hb.domain.insur.PageVo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (分配任务单请求类)
 * @date 2023/11/14 09:07
 * @since JDK 1.8
 */
@Data
public class CmconferNewCustTaskVO  extends PageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 会议ID
     */
    private String conferenceId;
    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 申请人
     */
    private String applyer;

    /**
     * 当前状态
     */
    private String currentStatus;

    /**
     * 审核意见
     */
    private String remark;


}