package com.howbuy.crm.hb.domain.conscust;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Description: 实体类Vconscust.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class Vconscust implements Serializable {

private static final long serialVersionUID = 1L;

	private String conscustno;
	
	private String conscustlvl;
	
	private Integer conscustgrade;
	
	private String conscuststatus;
	
	private String idtype;
	
	private String idno;
	
	private String custname;
	
	private String provcode;
	
	private String citycode;
	
	private String edulevel;
	
	private String vocation;
	
	private String inclevel;
	
	private String birthday;
	
	private String gender;
	
	private String married;
	
	private String pincome;
	
	private String fincome;
	
	private String decisionflag;
	
	private String interests;
	
	private String familycondition;
	
	private String contacttime;
	
	private String contactmethod;
	
	private String sendinfoflag;
	
	private String recvtelflag;
	
	private String recvemailflag;
	
	private String recvmsgflag;
	
	private String company;
	
	private String risklevel;
	
	private String selfrisklevel;
	
	private String addr;
	
	private String postcode;
	
	private String mobile;
	
	private String mobileMask;
	
	private String telno;
	
	private String fax;
	
	private String email;
	
	private String emailMask;
	
	private String hometelno;
	
	private String officetelno;
	
	private String actcode;
	
	private String intrcustno;
	
	private String source;
	
	private String sourcename;
	
	private String knowchan;
	
	private String otherchan;
	
	private String otherinvest;
	
	private String salon;
	
	private String beforeinvest;
	
	private String im;
	
	private String msn;
	
	private String ainvestamt;
	
	private String ainvestfamt;
	
	private String selfdefflag;
	
	private String visitfqcy;
	
	private String devdirection;
	
	private String saledirection;
	
	private String subsource;
	
	private String subsourcetype;
	
	private String saleprocess;
	
	private String mergedconscust;
	
	private String addr2;
	
	private String postcode2;
	
	private String mobile2;
	
	private String email2;
	
	private String knowhowbuy;
	
	private String subknow;
	
	private String subknowtype;
	
	private String buyingprod;
	
	private String buyedprod;
	
	private String freeprod;
	
	private String specialflag;
	
	private String dlvymode;
	
	private String remark;
	
	private String regdt;
	
	private String uddt;
	
	private String pririsklevel;
	
	private String linkman;
	
	private String linktel;
	
	private String linkmobile;
	
	private String linkemail;
	
	private String linkpostcode;
	
	private String linkaddr;
	
	private String capacity;
	
	private String activityno;
	
	private String partnerno;
	
	private String gpsinvestlevel;
	
	private String gpsrisklevel;
	
	private String isboss;
	
	private String financeneed;
	
	private String isjoinclub;
	
	private String tmpBeforeinvest;
	
	private String tmpOtherinvest;
	
	private String isrisktip;
	
	private BigDecimal totalmarketvalue;
	
	private String assignconsdt;
	
	private String conscode;
	
	private String consname;
	
	private String visittime;
	
	private String visitsummary;
	
	private String visittype;
	
	private String custcallintime;
	
	private BigDecimal pmarketvalue;
	
	private String firsttradedt;
	
	private String seniormgrcode;
	
	private String seniormgrname;
	
	private String joinclubdt;
	
	private String surveydt;
	
	private String pubcustno;
	
	private String pinvsttype;
	
	private String pidtype;
	
	private String pidno;
	
	private String prisklevel;
	
	private String pubregdt;
	
	private String newtradedt;
	
	private BigDecimal pmarketamt;
	
	private BigDecimal pmarketamtlevel;
	
	private String caneditflag;
	
	private String latesttradedt;
	
	private String newsourceno; //3.5.3 来源重构
	
	private String newsourcename; //3.5.3 来源重构
	
	private String newsubsourcename;//3.5.3 来源重构
	
	private String newsubsourcetypename;//3.5.3 来源重构
	
	private String invsttype;//3.5.5增加证件类型区分
	
	private String orgname;//3.6.4增加客户对应的投顾所在组织架构名称

	private String teamname;//3.8.8增加客户对应的投顾所在组织架构名称

	private BigDecimal retailamount;//3.6.9增加零售资产合计
	
	private BigDecimal premiumamount;//3.6.9增加高端资产合计
	
	private String markcode; //打标代码
	
	private String markvalue;//打标名称
	
	private String zjllabel; //资金量标签
	
	private String khyxlabel;//客户意向标签
	
	private String lxqklabel;//联系情况标签
	
	private String custlabelval;//客户标签值
	
	private String labeldt;//打标日期
	
	private String canseelabel;//是否可以看到打标情况
	
	private String istrans;//是否在待划转表中

}
