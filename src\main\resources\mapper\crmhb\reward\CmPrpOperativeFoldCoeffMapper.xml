<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.reward.CmPrpOperativeFoldCoeffMapper">
    <cache type="org.mybatis.caches.oscache.OSCache"/>
	<insert id="insertCmPrpOperativeFoldCoeff" parameterType="CmPrpOperativeFoldCoeff">
		INSERT INTO CM_PRP_OPERATIVE_FOLD_COEFF (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="accountProductType != null">account_product_type,</if>
			<if test="operFoldCoeff != null">oper_fold_coeff,</if>
			<if test="startDt != null">start_dt,</if>
			<if test="endDt != null">end_dt,</if>
			<if test="creator != null">creator,</if>
			<if test="modor != null">modor,</if>
			<if test="updateTime != null">update_time,</if>
		</trim>
		) VALUES (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="accountProductType != null">#{accountProductType},</if>
			<if test="operFoldCoeff != null">#{operFoldCoeff},</if>
			<if test="startDt != null">#{startDt},</if>
			<if test="endDt != null">#{endDt},</if>
			<if test="creator != null">#{creator},</if>
			<if test="modor != null">#{modor},</if>
			<if test="updateTime != null">#{updateTime},</if>
		</trim>
		)
	</insert>
	
	<update id="updateCmPrpOperativeFoldCoeff" parameterType="CmPrpOperativeFoldCoeff" >
	    UPDATE CM_PRP_OPERATIVE_FOLD_COEFF	    
	    <set>        
        	<if test="accountProductType != null"> account_product_type = #{accountProductType}, </if>             
            <if test="operFoldCoeff != null">oper_fold_coeff = #{operFoldCoeff},</if>
			<if test="startDt != null">start_dt = #{startDt },</if>
			<if test="endDt != null">end_dt = #{endDt },</if>
			<if test="creator != null">creator = #{creator},</if>
			<if test="modor != null">modor = #{modor},</if>                           
            update_time = sysdate,           
         </set>
          where id = #{id}
	  </update>
	  
	  <delete id="delCmPrpOperativeFoldCoeff" parameterType="BigDecimal">
	    delete from CM_PRP_OPERATIVE_FOLD_COEFF where id = #{id}
	  </delete>
	  
	  <select id="listCmPrpOperativeFoldCoeffByPage" parameterType="Map" resultType="CmPrpOperativeFoldCoeff" useCache="false">
	    SELECT id,
	           account_product_type accountProductType,
	           oper_fold_coeff operFoldCoeff,
	    	   start_dt startDt,
	    	   end_dt endDt,
	    	   creator,
	    	   create_time createTime,
	    	   modor,
	    	   update_time updateTime
		 FROM CM_PRP_OPERATIVE_FOLD_COEFF 
	    where 1=1   
            <if test="param.id != null"> AND id = #{param.id} </if>    
            <if test="param.accountProductType != null"> AND account_product_type = #{param.accountProductType} </if>             
         order by create_time desc
      </select>
      
      <select id="getCmPrpOperativeFoldCoeff" parameterType="Map" resultType="CmPrpOperativeFoldCoeff" useCache="false">
	    SELECT id,
	           account_product_type accountProductType,
	           oper_fold_coeff operFoldCoeff,
	    	   start_dt startDt,
	    	   end_dt endDt,
	    	   creator,
	    	   create_time createTime,
	    	   modor,
	    	   update_time updateTime
		 FROM CM_PRP_OPERATIVE_FOLD_COEFF
	     where 1=1  
         <if test="id != null"> AND id = #{id} </if>  
         <if test="accountProductType != null"> AND account_product_type = #{accountProductType} </if> 
         <if test="operFoldCoeff != null"> AND oper_fold_coeff = #{operFoldCoeff} </if>            
         <if test="startDt != null"> AND start_dt = #{startDt} </if>             
         <if test="endDt != null"> AND end_dt = #{endDt} </if>                             
  	  </select>
  	  
  	  <select id="listCmPrpOperativeFoldCoeff" parameterType="Map" resultType="CmPrpOperativeFoldCoeff" useCache="false">
	    SELECT id,
	           account_product_type accountProductType,
	           oper_fold_coeff operFoldCoeff,
	    	   start_dt startDt,
	    	   end_dt endDt,
	    	   creator,
	    	   create_time createTime,
	    	   modor,
	    	   update_time updateTime
		 FROM CM_PRP_OPERATIVE_FOLD_COEFF
	     where 1=1  
         <if test="id != null"> AND id = #{id} </if>  
         <if test="accountProductType != null"> AND account_product_type = #{accountProductType} </if>
         <if test="operFoldCoeff != null"> AND oper_fold_coeff = #{operFoldCoeff} </if>            
         <if test="startDt != null"> AND start_dt = #{startDt} </if>             
         <if test="endDt != null"> AND end_dt = #{endDt} </if>      
         <if test="checkStartDt != null">  
         AND ( #{checkStartDt} BETWEEN START_DT AND NVL(END_DT, '********') 
         	   OR #{checkEndDt} BETWEEN START_DT AND NVL(END_DT, '********')
         	   OR START_DT BETWEEN #{checkStartDt} AND #{checkEndDt}
         	   OR NVL(END_DT, '********') BETWEEN #{checkStartDt} AND #{checkEndDt}
         	  ) 
         </if>
         <if test="notId != null"> AND id != #{notId} </if>            
      </select>
      
</mapper>