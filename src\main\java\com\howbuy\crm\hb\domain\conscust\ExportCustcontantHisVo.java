package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

/**
 * 客户历史分配记录导出bean
 * <AUTHOR>
 *
 */
public class ExportCustcontantHisVo implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    /**
     * 投顾客户号
     */
    private String custNo;

    /**
     * 姓名
     */
    private String custName;

    /**
     * 原投顾
     */
    private String fromConsName;

    /**
     * 原区域
     */
    private String fromUporgName;

    /**
     * 原部门
     */
    private String fromOrgName;

    /**
     * 分配投顾
     */
    private String toConsName;

    /**
     * 分配区域
     */
    private String toUporgName;

    /**
     * 分配部门
     */
    private String toOrgName;

    /**
     * 操作人
     */
    private String creator;
    
    
    /**
     * 操作日期
     */
    private String credt;

    /**
     * 分配原因
     */
    private String reason;

    /**
     * 客户状态
     */
    private String custTradeType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 地区
     */
    private String area;

    /**
     * 无线渠道
     */
    private String wifiChannel;

    /**
     * 客户来源
     */
    private String custSource;

    /**
     * 处理类型
     */
    private String dealType;

    /**
     * 客户类型
     */
    private String custType;
    
    /**
     * 省
     */
    private String provCode;

    /**
     * 市
     */
    private String cityCode;

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getFromConsName() {
		return fromConsName;
	}

	public void setFromConsName(String fromConsName) {
		this.fromConsName = fromConsName;
	}

	public String getFromUporgName() {
		return fromUporgName;
	}

	public void setFromUporgName(String fromUporgName) {
		this.fromUporgName = fromUporgName;
	}

	public String getFromOrgName() {
		return fromOrgName;
	}

	public void setFromOrgName(String fromOrgName) {
		this.fromOrgName = fromOrgName;
	}

	public String getToConsName() {
		return toConsName;
	}

	public void setToConsName(String toConsName) {
		this.toConsName = toConsName;
	}

	public String getToUporgName() {
		return toUporgName;
	}

	public void setToUporgName(String toUporgName) {
		this.toUporgName = toUporgName;
	}

	public String getToOrgName() {
		return toOrgName;
	}

	public void setToOrgName(String toOrgName) {
		this.toOrgName = toOrgName;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCredt() {
		return credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getCustTradeType() {
		return custTradeType;
	}

	public void setCustTradeType(String custTradeType) {
		this.custTradeType = custTradeType;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getWifiChannel() {
		return wifiChannel;
	}

	public void setWifiChannel(String wifiChannel) {
		this.wifiChannel = wifiChannel;
	}

	public String getCustSource() {
		return custSource;
	}

	public void setCustSource(String custSource) {
		this.custSource = custSource;
	}

	public String getDealType() {
		return dealType;
	}

	public void setDealType(String dealType) {
		this.dealType = dealType;
	}

	public String getCustType() {
		return custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

	public String getProvCode() {
		return provCode;
	}

	public void setProvCode(String provCode) {
		this.provCode = provCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}
}
