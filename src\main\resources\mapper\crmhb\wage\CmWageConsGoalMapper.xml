<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.wage.CmWageConsGoalMapper">
	<cache type="org.mybatis.caches.oscache.OSCache" />

	<insert id="saveOrUpdateConslevel"  parameterType="Map">
		<selectKey keyProperty="count" resultType="int" order="BEFORE">
			select count(*) AS COUNTS from CM_WAGE_BASE_CONSLEVEL where CONSCODE = #{conscode}
			AND SAVE_YEAR =#{year}  and SAVE_MONTH =#{month}
		</selectKey>
		<if test="count > 0">
			UPDATE CM_WAGE_BASE_CONSLEVEL
			<set>
				<trim suffix="" suffixOverrides=",">
					<if test="conslevel!= null"> CONSLEVEL=#{conslevel}, </if>
				</trim>
			</set>
			<where>
				conscode = #{conscode} AND SAVE_YEAR =#{year}  and SAVE_MONTH =#{month}
			</where>
		</if>
		<if test="count==0">
			INSERT INTO CM_WAGE_BASE_CONSLEVEL (
			<trim suffix="" suffixOverrides=",">
				<if test="conscode!= null"> conscode, </if>
				<if test="conslevel!= null"> CONSLEVEL, </if>
				<if test="year!= null"> SAVE_YEAR, </if>
				<if test="month!= null"> SAVE_MONTH, </if>
			</trim>
			) values (
			<trim suffix="" suffixOverrides=",">
				<if test="conscode!= null"> #{conscode}, </if>
				<if test="conslevel!= null"> #{conslevel}, </if>
				<if test="year!= null"> #{year}, </if>
				<if test="month!= null"> #{month}, </if>
			</trim>
			)
		</if>
	</insert>
	
	<insert id="saveOrUpdateConslevelDes"  parameterType="Map">
		<selectKey keyProperty="count" resultType="int" order="BEFORE">
			select count(*) AS COUNTS from CM_WAGE_BASE_CONSLEVEL_DES where CONSCODE = #{conscode}
			AND SAVE_YEAR =#{year}
		</selectKey>
		<if test="count > 0">
			UPDATE CM_WAGE_BASE_CONSLEVEL_DES SET DES = #{des}
			where conscode = #{conscode} AND SAVE_YEAR =#{year}
		</if>
		<if test="count==0">
			INSERT INTO CM_WAGE_BASE_CONSLEVEL_DES (
			<trim suffix="" suffixOverrides=",">
				<if test="conscode!= null"> conscode, </if>
				<if test="des!= null"> des, </if>
				<if test="year!= null"> SAVE_YEAR, </if>
			</trim>
			) values (
			<trim suffix="" suffixOverrides=",">
				<if test="conscode!= null"> #{conscode}, </if>
				<if test="des!= null"> #{des}, </if>
				<if test="year!= null"> #{year}, </if>
			</trim>
			)
		</if>
	</insert>
	
	<delete id="delDelWageRecord" parameterType="Map" >
		DELETE FROM CM_WAGE_DEL_RECORD
 		 WHERE  1=1
		 <if test="id!=null">and ID = #{id} </if>
 		   AND START_DT = #{startDt}
           AND END_DT = #{endDt} 
	</delete>
	
	<insert id="insertDelWageRecord" parameterType="Map" >
		INSERT INTO CM_WAGE_DEL_RECORD (ID, START_DT, END_DT) VALUES (#{id},#{startDt},#{endDt})
	</insert>
	
	<delete id="delCoffWageRecord" parameterType="Map" >
		DELETE FROM cm_WAGE_coff_record
 		 WHERE 1=1
		<if test="id!=null">and ID = #{id} </if>
   		   AND START_DT = #{startDt} 
           AND END_DT = #{endDt} 
	</delete>

	<delete id="delAllWageRecord" parameterType="Map" >
		<if test="type=='DELRECORD'">
			DELETE FROM CM_WAGE_DEL_RECORD
		</if>
		<if test="type=='COFFRECORD'">
			DELETE FROM cm_WAGE_coff_record
		</if>
	</delete>
	
	<insert id="insertCoffWageRecord" parameterType="Map" >
		INSERT INTO cm_WAGE_coff_record (ID, START_DT, END_DT,coff_value) VALUES (#{id},#{startDt},#{endDt},#{coffValue})
	</insert>
	
	<update id="updateCoffWageRecord" parameterType="Map" >
	    update cm_WAGE_coff_record
	       set COFF_VALUE = #{coffValue}
 		 WHERE ID = #{id} 
   		   AND START_DT = #{startDt} 
           AND END_DT = #{endDt} 
	</update>

	<insert id="saveOrUpdateCoffWageRecord"  parameterType="Map">
		<selectKey keyProperty="count" resultType="int" order="BEFORE">
			select count(*) AS COUNTS from cm_WAGE_coff_record
		    WHERE ID = #{id}
		</selectKey>
		<if test="count > 0">
			update cm_WAGE_coff_record
			  set COFF_VALUE = #{coffValue},
			      UPTOR = #{uptor},
			      UPTTIME = sysdate
			WHERE ID = #{id}
		</if>
		<if test="count==0">
			INSERT INTO cm_WAGE_coff_record (ID, START_DT, END_DT,coff_value) VALUES (#{id},#{startDt},#{endDt},#{coffValue})
		</if>
	</insert>


	<delete id="initDataBaseCoeff" parameterType="Map">
		delete  from CM_WAGE_COFF_RECORD
		 where START_DT  = #{startDt}
		   and END_DT =#{endDt}
	</delete>

	<delete id="initDataBaseLevel" parameterType="Map">
		delete from CM_WAGE_BASE_CONSLEVEL
		where SAVE_YEAR = #{saveYear}
	</delete>
	
	<delete id="initDataBaseLevelDes" parameterType="Map">
		delete from CM_WAGE_BASE_CONSLEVEL_DES
		where SAVE_YEAR = #{saveYear}
	</delete>

	<insert id="saveOrUpdateExtra"  parameterType="Map">
		<selectKey keyProperty="count" resultType="int" order="BEFORE">
			select count(*) AS COUNTS from CM_WAGE_PERMANCE_EXTRA where ID = #{id}
		</selectKey>
		<if test="count > 0">
			UPDATE CM_WAGE_PERMANCE_EXTRA
			<set >
				<trim suffix="" suffixOverrides=",">
					<if test="extraNumber!= null"> EXTRA_NUMBER=#{extraNumber}, </if>
					<if test="extraRemark!= null"> EXTRA_REMARK=#{extraRemark}, </if>

					<if test="sourceCoe!= null"> SOURCE_COE=#{sourceCoe}, </if>
					<if test="proRatio!= null"> PRO_RATIO=#{proRatio}, </if>
					<if test="percoeValue!= null"> PERCOE_VALUE=#{percoeValue}, </if>
				</trim>
			</set>
			<where>
				ID = #{id}
			</where>
		</if>
		<if test="count==0">
			INSERT INTO CM_WAGE_PERMANCE_EXTRA (
			<trim suffix="" suffixOverrides=",">
				<if test="id!= null"> ID, </if>
				<if test="extraNumber!= null"> EXTRA_NUMBER, </if>
				<if test="extraRemark!= null"> EXTRA_REMARK, </if>
				<if test="sourceCoe!= null"> SOURCE_COE, </if>
				<if test="proRatio!= null"> PRO_RATIO, </if>
				<if test="percoeValue!= null"> PERCOE_VALUE, </if>
			</trim>
			) values (
			<trim suffix="" suffixOverrides=",">
				<if test="id!= null"> #{id}, </if>
				<if test="extraNumber!= null">#{extraNumber}, </if>
				<if test="extraRemark!= null"> #{extraRemark}, </if>
				<if test="sourceCoe!= null"> #{sourceCoe}, </if>
				<if test="proRatio!= null"> #{proRatio}, </if>
				<if test="percoeValue!= null"> #{percoeValue}, </if>
			</trim>
			)
		</if>
	</insert>



	<delete id="delQuaterRecord" parameterType="Map" >
		DELETE FROM CM_WAGE_quater_RECORD
		WHERE  1=1
		<if test="id!=null">and ID = #{id} </if>
	</delete>

	<insert id="insertQuaterRecord" parameterType="Map" >
		INSERT INTO CM_WAGE_quater_RECORD (ID, START_DT, END_DT) VALUES (#{id},#{startDt},#{endDt})
	</insert>
	
	<select id="listCmWageBaseConslevel" parameterType="Map" resultType="Map" useCache="false">
	  SELECT conscode,save_year,save_month,conslevel FROM CM_WAGE_BASE_CONSLEVEL WHERE save_year=#{P_YEAR}
	</select>

    <update id="initQuaterCountData" parameterType="Map" >
		DELETE FROM CM_WAGE_PERMANCE_EXTRA T
		 WHERE T.ID IN
		       (SELECT A.ID
		          FROM CM_WAGE_QUATER_COLLECT A
		          LEFT JOIN CM_CONSULTANT C
		            ON A.CONSCODE = C.CONSCODE
		         WHERE (A.ORGCODE IN (SELECT ORGCODE
		                                FROM HB_ORGANIZATION HJ
		                              CONNECT BY PRIOR HJ.ORGCODE = HJ.PARENTORGCODE
		                               START WITH ORGCODE = #{orgcode}) OR C.TEAMCODE = #{orgcode})
		           AND A.YEAR_MONTH = #{yearMonth}
		           <if test="conscode != null"> AND A.CONSCODE = #{conscode} </if>
		           <if test="prodtype != null"> AND A.PRO_TYPE = #{prodtype} </if>
		           <if test="custname != null"> AND A.CONSCUSTNAME LIKE '%'||#{custname}||'%' </if>
		           <if test="pcode != null"> AND A.PCODE = #{pcode} </if>
		           )
	</update>

</mapper>



