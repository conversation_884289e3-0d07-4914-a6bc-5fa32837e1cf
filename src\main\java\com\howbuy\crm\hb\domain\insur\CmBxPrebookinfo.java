package com.howbuy.crm.hb.domain.insur;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @Description: 实体类CmBxPrebookinfo.java
 * <AUTHOR> @version 1.0
 * @created 
 */
@Data
public class CmBxPrebookinfo implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private String fundcode;
	private String conscustno;
	private String idtype;
	private String idno;
	private String idnoDigest;
	private String idnoMask;
	private String idnoCipher;
	private Integer age;
	private String insurname;
	private Integer insurage;
	private String insuridtype;
	private String insuridno;
	private String insuridnoDigest;
	private String insuridnoMask;
	private String insuridnoCipher;
	private String relation;
	private String otherrelation;
	private String expectsigndt;
	private String channcode;
	private String busitype;
	private String busisource;
	private String busisourceval;
	private String compcode;
	private String currency;
	private String consremark;
	private String checkremark;
	private String confirmremark;
	private String prestate;
	private String prestateval;
	private String insurstate;
	private String insurstateval;
	private String checkstate;
	/**
	 * 复核状态中文值
	 */
	private String checkStateVal;
	private String finstate;
	private String ischangereturn;
	private String creator;
	private String realcreator;
	private Date creatdt;
	private String modifier;
	private Date modifydt;
	private String edittims;
	private String outletcode;
	private String yearno;
	private String sfsyxbx;
	private Boolean isEdit;
	/**
	 * 购买明细id
	 */
	private BigDecimal buyid;
	/**
	 * 购买产品代码
	 */
	private String buyfundcode;
	/**
	 * 录入时间，精确到时分秒
	 */
	private String credt;
	private String signdt;
	private String orgcode;
	private String uporgname;
	private String conscode;
	private String custname;
	/**
	 * 保险公司名称
	 */
	private String compname;
	/**
	 * 合作渠道名称
	 */
	private String channname;
	/**
	 * 产品类型
	 */
	private String prodtype;
	/**
	 * 产品类型枚举值
	 */
	private String prodtypeVal;
	private String fundname;
	/**
	 * 缴费年限
	 */
	private String payyears;
	/**
	 * 缴费年数
	 */
	private String payyears2;
	/**
	 * 保障年限
	 */
	private String ensureyears;
	/**
	 * 年缴保费
	 */
	private BigDecimal yearamk;
	private String insurid;
	/**
	 * 核保通过日期
	 */
	private String passdt;
	/**
	 * 缴费状态
	 */
	private String paystate;
	private String paystateval;

	/**
	 * 回访状态
	 */
	private String visitstate;
	private String visitstateval;

	/**
	 * 保费缴清日
	 */
	private String paydt;
	/**
	 * 冷静期
	 */
	private String caltime;
	/**
	 * 是否朱预约的记录
	 */
	private String ismain;
	/**
	 * 折标系数
	 */
	private BigDecimal procoe;
	/**
	 * 汇率
	 */
	private BigDecimal rate;
	/**
	 * 折标销量
	 */
	private BigDecimal collamk;

	/**
	 * 按preId汇总的折标销量和
	 */
	private BigDecimal evaluateCollAmk;
	/**
	 * 折标销量特殊
	 */
	private BigDecimal collamkSpec;
	/**
	 * 保单备注
	 */
	private String insurremark;
	/**
	 * 次年保费日
	 */
	private String nextyearpaydt;
	/**
	 * 退保/拒保日期
	 */
	private String cancelsurdt;
	/**
	 * 佣金结算日
	 */
	private String ratiodt;
	/**
	 * 保额
	 */
	private BigDecimal insuramk;
	/**
	 * 佣金核算日期
	 */
	private String commissiondt;
	/**
	 * 投顾佣金率
	 */
	private BigDecimal commissionratio;
	/**
	 * 管理佣金率
	 */
	private BigDecimal manageCommissionRatio;
	/**
	 * 佣金汇率
	 */
	private BigDecimal commissionrate;
	/**
	 * 投顾佣金
	 */
	private BigDecimal commission;
	private String commissionStr;
	/**
	 * 应收单id
	 */
	private BigDecimal commid;
	/**
	 * 收款日期
	 */
	private String skdt;
	private String gatheringdt;
	/**
	 * 公司佣金率
	 */
	private BigDecimal comcommissionratio;
	private BigDecimal gathercommin;
	private BigDecimal gathercommout;
	private String ticketdt;
	private BigDecimal realcommission;
	private String accountdt;
	private String commstate;
	private BigDecimal expgathercommout;
	private String expticketdt;
	private BigDecimal exprealcommission;
	private String expaccountdt;
	private String istpcheck;
	
	private String nowconscode;
	
	private String insurrenewalstate;
	
	private String insurrenewalstateval;
	
	private String realpaydt;
	
	private BigDecimal paylistid;

	//总保费≥40万    是 /否
	private String isMoreThenForty;
	//投顾创新方案 1方案一 2方案二
	private String bxcommissionway;
	/**
	 * 方案2-佣金
	 */
	private BigDecimal commissionfortwo;
	private String commissionfortwoStr;
	/**
	 * 方案2-存续D
	 */
	private BigDecimal commissioncxdfortwo;
	private String commissioncxdfortwoStr;
	/**
	 * 正常存续D
	 */
	private BigDecimal commissionCxd;
	private String commissionCxdStr;

	/**
	 * 佣金总和（方案1佣金+方案2佣金） 创新绩效需求新增字段
	 */
	private String totalCommission;
	/**
	 * 管理系数-分总
	 */
	private String managePoint;
	/**
	 * 管理系数-区副
	 */
	private BigDecimal manageCoeffRegionalsubtotal;
	/**
	 * 管理系数-区总
	 */
	private BigDecimal manageCoeffRegionaltotal;
	/**
	 * 管理奖金基数-分总（totalCommission * managePoint） 创新绩效需求新增字段
	 */
	private String pointCommission;
	/**
	 * 管理奖金基数-区副
	 */
	private BigDecimal manageCoeffRegsubtotalComm;
	/**
	 * 管理奖金基数-区总
	 */
	private BigDecimal manageCoeffRegtotalComm;
	/**
	 * 总保费（yearamk * payyears * rate 保留两位）  创新绩效需求新增字段
	 */
	private String totalPremium;
	/**
	 * 投顾姓名
	 */
	private String consName;
	/**
	 * 中心名称(预约时)
	 */
	private String preCenterName;
	/**
	 * 核算数据修改人
	 */
	private String expCreator;

	/**
	 * 投顾当前部门,根据nowconscode 从缓存获取名称
	 */
	private String presentorgname;

	/**
	 * 投顾当前名称,根据nowconscode 从缓存获取名称
	 */
	private String presentconsname;

	/**
	 * 总保费 （RMB） 因为导出类使用到这个类，需要添加字段，但在数据库中不存在
	 */
	private String newTotalPremium;

	/**
	 * 是否海外投资,因为导出类使用到这个类，需要添加字段，但在数据库中不存在
	 */
	private String isInvestment;
	/**
	 * 管理层的佣金总和， 只是计算中间变量
	 */
	private String manageTotalCommission;
}
