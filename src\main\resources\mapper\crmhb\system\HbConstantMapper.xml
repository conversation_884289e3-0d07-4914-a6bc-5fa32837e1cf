<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.hb.persistence.system.HbConstantMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.hb.domain.system.HbConstant">
    <!--@mbg.generated-->
    <!--@Table HB_CONSTANT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TYPECODE" jdbcType="VARCHAR" property="typecode" />
    <result column="TYPEDESC" jdbcType="VARCHAR" property="typedesc" />
    <result column="CODEDESC" jdbcType="VARCHAR" property="codedesc" />
    <result column="CONSTCODE" jdbcType="VARCHAR" property="constcode" />
    <result column="CONSTDESC" jdbcType="VARCHAR" property="constdesc" />
    <result column="CONSTLEVEL" jdbcType="DECIMAL" property="constlevel" />
    <result column="ISVALID" jdbcType="CHAR" property="isvalid" />
    <result column="CONSTALIAS" jdbcType="VARCHAR" property="constalias" />
    <result column="CONSTEXT1" jdbcType="VARCHAR" property="constext1" />
    <result column="CONSTEXT2" jdbcType="VARCHAR" property="constext2" />
    <result column="CONSTEXT3" jdbcType="VARCHAR" property="constext3" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDATE" jdbcType="TIMESTAMP" property="credate" />
    <result column="MODDATE" jdbcType="TIMESTAMP" property="moddate" />
    <result column="CHECKDATE" jdbcType="TIMESTAMP" property="checkdate" />
    <result column="UPDATEID" jdbcType="VARCHAR" property="updateid" />
    <result column="RESOURCEID" jdbcType="VARCHAR" property="resourceid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TYPECODE, TYPEDESC, CODEDESC, CONSTCODE, CONSTDESC, CONSTLEVEL, ISVALID, CONSTALIAS, 
    CONSTEXT1, CONSTEXT2, CONSTEXT3, CREATOR, MODIFIER, CHECKER, CREDATE, MODDATE, CHECKDATE, 
    UPDATEID, RESOURCEID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from HB_CONSTANT
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <select id="listByTypeCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from HB_CONSTANT
    where TYPECODE = #{typecode,jdbcType=VARCHAR}
  </select>

  <select id="listConstantCodeByConsText2" parameterType="string" resultType="string">
    select constcode from HB_CONSTANT
    where constext2 = #{consText2,jdbcType=VARCHAR}
    and TYPECODE = #{typeCode,jdbcType=VARCHAR}
  </select>

  <select id="getConsText2ConstDescByCode" resultType="string" parameterType="string">
    select constdesc from HB_CONSTANT
    where constcode = (select constext2 from HB_CONSTANT
                    where constcode = #{constCode,jdbcType=VARCHAR}
                            and TYPECODE = #{typeCode,jdbcType=VARCHAR})
    and TYPECODE = #{textTypeCode,jdbcType=VARCHAR}
  </select>
</mapper>