package com.howbuy.crm.hb.domain.custinfo;


import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 实体类CmConscusthis.java
 * @created
 */
@Data
public class CmConscusthis implements Serializable {

    private static final long serialVersionUID = 1L;

    private String appserialno;

    private String txcode;

    private String txchkflag;

    private String conscustno;

    private String conscustlvl;

    private Integer conscustgrade;

    private String conscuststatus;

    private String idtype;

    private String idno;

    private String custname;

    private String provcode;

    private String citycode;

    private String edulevel;

    private String vocation;

    private String inclevel;

    private String birthday;

    private String gender;

    private String married;

    private String pincome;

    private String fincome;

    private String decisionflag;

    private String interests;

    private String familycondition;

    private String contacttime;

    private String contactmethod;

    private String sendinfoflag;

    private String recvtelflag;

    private String recvemailflag;

    private String recvmsgflag;

    private String company;

    private String risklevel;

    private String selfrisklevel;

    private String addr;

    private String postcode;

    private String mobile;

    private String telno;

    private String fax;

    private String email;

    private String hometelno;

    private String officetelno;

    private String actcode;

    private String intrcustno;

    private String source;

    private String knowchan;

    private String otherchan;

    private String otherinvest;

    private String salon;

    private String beforeinvest;

    private String im2;

    private String msn;

    private String ainvestamt;

    private String ainvestfamt;

    private String selfdefflag;

    private String visitfqcy;

    private String saledirection;

    private String devdirection;

    private String subsource;

    private String subsourcetype;

    private String saleprocess;

    private String mergedconscust;

    private String remark;

    private String addr2;

    private String postcode2;

    private String mobile2;

    private String email2;

    private String knowhowbuy;

    private String subknow;

    private String subknowtype;

    private String buyingprod;

    private String buyedprod;

    private String freeprod;

    private String specialflag;

    private String dlvymode;

    private String specialreason;

    private String creator;

    private String checker;

    private Date stimestamp;

    private String pririsklevel;

    private String linkman;

    private String linktel;

    private String linkmobile;

    private String linkemail;

    private String linkpostcode;

    private String linkaddr;

    private String capacity;

    private String activityno;

    private String partnerno;

    private String gpsinvestlevel;

    private String gpsrisklevel;

    private String isboss;

    private String financeneed;

    private String isjoinclub;

    private String explanation;

    private String tmpBeforeinvest;

    private String tmpOtherinvest;

    private String iswritebook;

    private String invsttype;

    private String hbvipUsername;

    /** 3.5.1 新增微信号字段 */
    private String wechatcode;

    /**  3.5.3 新来源 */
    private String newsourceno;

    /** 3.6.2 客户期望交易类型 added by jingya.xu */
    private String hopetradetype;

    private String hboneno;

    /** 3.7.1证件有限期 */
    private String validity;

    /** 3.7.1证件有限期日期 */
    private String validitydt;

    /** 3.7.1性质 */
    private String nature;

    /** 3.7.1资质 */
    private String aptitude;

    /** 3.7.1经营范围 */
    private String scopebusiness;


    public String getIdno() {
        return this.idno;
    }

    public void setIdno(String idno) {
        if (StringUtils.isNotEmpty(idno)) {
            this.idno = idno.trim();
        } else {
            this.idno = null;
        }
    }

    public String getCustname() {
        return this.custname;
    }

    public void setCustname(String custname) {

        if (StringUtils.isNotEmpty(custname)) {
            this.custname = custname.trim();
        } else {
            this.custname = null;
        }
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        if (StringUtils.isNotEmpty(mobile)) {
            this.mobile = mobile.trim();
        } else {
            this.mobile = null;
        }
    }

}
