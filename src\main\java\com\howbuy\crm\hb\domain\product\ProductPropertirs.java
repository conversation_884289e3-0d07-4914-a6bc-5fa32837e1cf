package com.howbuy.crm.hb.domain.product;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class ProductPropertirs implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String pcode;
	
	private String pname;
	
	private String jjpy;
	
	private String isElecContract;
	
	private String creator;
	
	private String modifier;
	
	private String credt;
	
	private String moddt;
	
	private String checkflag;
	
	private String checkman;
	
	private String latestBackdateCom;
	
	private String latestBackdateSa;
	
	private String element;
	
	private String iscaltradenum;

	public String getPcode() {
		return pcode;
	}

	public void setPcode(String pcode) {
		this.pcode = pcode;
	}

	

	public String getIsElecContract() {
		return isElecContract;
	}

	public void setIsElecContract(String isElecContract) {
		this.isElecContract = isElecContract;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public String getCheckflag() {
		return checkflag;
	}

	public void setCheckflag(String checkflag) {
		this.checkflag = checkflag;
	}

	public String getCheckman() {
		return checkman;
	}

	public void setCheckman(String checkman) {
		this.checkman = checkman;
	}

	public String getLatestBackdateCom() {
		return latestBackdateCom;
	}

	public void setLatestBackdateCom(String latestBackdateCom) {
		this.latestBackdateCom = latestBackdateCom;
	}

	public String getLatestBackdateSa() {
		return latestBackdateSa;
	}

	public void setLatestBackdateSa(String latestBackdateSa) {
		this.latestBackdateSa = latestBackdateSa;
	}

	public String getElement() {
		return element;
	}

	public void setElement(String element) {
		this.element = element;
	}

	public String getIscaltradenum() {
		return iscaltradenum;
	}

	public void setIscaltradenum(String iscaltradenum) {
		this.iscaltradenum = iscaltradenum;
	}
	
	public String getPname() {
		return pname;
	}

	public void setPname(String pname) {
		this.pname = pname;
	}

	public String getJjpy() {
		return jjpy;
	}

	public void setJjpy(String jjpy) {
		this.jjpy = jjpy;
	}

	public ProductPropertirs() {
	
	}
	
}
