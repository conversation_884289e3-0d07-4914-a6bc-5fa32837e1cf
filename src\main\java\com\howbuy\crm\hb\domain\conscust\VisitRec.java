package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;
/**
 * 
 * <AUTHOR>
 *
 */
public class VisitRec implements Serializable {

    /** identifier field */
    private String appSerialNo = "";

    /** nullable persistent field */
    private String tradeDt = "";

    /** nullable persistent field */
    private String appCode = "";

    /** nullable persistent field */
    private String txCode = "";

    /** nullable persistent field */
    private String txAppFlag = "";

    /** nullable persistent field */
    private String txChkFlag = "";

    /** nullable persistent field */
    private String tradeChan = "";

    /** nullable persistent field */
    private String regionCode = "";

    /** nullable persistent field */
    private String outletCode = "";

    /** nullable persistent field */
    private String appDt = "";

    /** nullable persistent field */
    private String appTm = "";

    /** nullable persistent field */
    private String custNo = "";

    /** nullable persistent field */
    private String PCustID = "";

    /** nullable persistent field */
    private String contactStaff = "";

    /** nullable persistent field */
    private String howLong = "";

    /** nullable persistent field */
    private String visitRs = "";

    /** nullable persistent field */
    private String visitCust = "";

    /** nullable persistent field */
    private String visitTime = "";

    /** nullable persistent field */
    private String visitType = "";
    
    /**
     * 该属性只用作获取界面的请求参数，
     * 并向 VisitRecNewest 中的对应属性赋值
     * VisitRec实体中该列无对应hbm映射和表字段，
     */
    private String visitClassify = "";


    public String getvisitClassify() {
		return visitClassify;
	}

	public void setvisitClassify(String visitClassify) {
		this.visitClassify = visitClassify;
	}

	/** nullable persistent field */
    private String visitSummary = "";

    /** nullable persistent field */
    private Double visitFee;

    /** nullable persistent field */
    private String feePurpose = "";

    /** nullable persistent field */
    private String servStaff = "";

    /** nullable persistent field */
    private String memo = "";

    /** nullable persistent field */
    private String retCode = "";

    /** nullable persistent field */
    private String retMsg = "";

    /** nullable persistent field */
    private String creator = "";

    /** nullable persistent field */
    private String checker = "";

    private String consCustNo;
    private String nextDt;
    
    private String nextSummary;
    
    private String consBookingID;
    
    /** nullable persistent field */
    private Date STimeStamp = new Timestamp(System.currentTimeMillis());

   
    /** default constructor */
    public VisitRec() {
    }

    public String getAppSerialNo() {
        return this.appSerialNo;
    }

    public void setAppSerialNo(String appSerialNo) {
        this.appSerialNo = appSerialNo;
    }

    public String getTradeDt() {
        return this.tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getAppCode() {
        return this.appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getTxCode() {
        return this.txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getTxAppFlag() {
        return this.txAppFlag;
    }

    public void setTxAppFlag(String txAppFlag) {
        this.txAppFlag = txAppFlag;
    }

    public String getTxChkFlag() {
        return this.txChkFlag;
    }

    public void setTxChkFlag(String txChkFlag) {
        this.txChkFlag = txChkFlag;
    }

    public String getTradeChan() {
        return this.tradeChan;
    }

    public void setTradeChan(String tradeChan) {
        this.tradeChan = tradeChan;
    }

    public String getRegionCode() {
        return this.regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getOutletCode() {
        return this.outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getAppDt() {
        return this.appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return this.appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getCustNo() {
        return this.custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getPCustID() {
        return this.PCustID;
    }

    public void setPCustID(String PCustID) {
        this.PCustID = PCustID;
    }

    public String getContactStaff() {
        return this.contactStaff;
    }

    public void setContactStaff(String contactStaff) {
        this.contactStaff = contactStaff;
    }

    public String getHowLong() {
        return this.howLong;
    }

    public void setHowLong(String howLong) {
        this.howLong = howLong;
    }

    public String getVisitRs() {
        return this.visitRs;
    }

    public void setVisitRs(String visitRs) {
        this.visitRs = visitRs;
    }

    public String getVisitCust() {
        return this.visitCust;
    }

    public void setVisitCust(String visitCust) {
        this.visitCust = visitCust;
    }

    public String getVisitTime() {
        return this.visitTime;
    }

    public void setVisitTime(String visitTime) {
        this.visitTime = visitTime;
    }

    public String getVisitType() {
        return this.visitType;
    }

    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }

    public String getVisitSummary() {
        return this.visitSummary;
    }

    public void setVisitSummary(String visitSummary) {
        this.visitSummary = visitSummary;
    }

    public Double getVisitFee() {
        return this.visitFee;
    }

    public void setVisitFee(Double visitFee) {
        this.visitFee = visitFee;
    }

    public String getFeePurpose() {
        return this.feePurpose;
    }

    public void setFeePurpose(String feePurpose) {
        this.feePurpose = feePurpose;
    }

    public String getServStaff() {
        return this.servStaff;
    }

    public void setServStaff(String servStaff) {
        this.servStaff = servStaff;
    }

    public String getMemo() {
        return this.memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRetCode() {
        return this.retCode;
    }

    public void setRetCode(String retCode) {
        this.retCode = retCode;
    }

    public String getRetMsg() {
        return this.retMsg;
    }

    public void setRetMsg(String retMsg) {
        this.retMsg = retMsg;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getChecker() {
        return this.checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public Date getSTimeStamp() {
        return this.STimeStamp;
    }

    public void setSTimeStamp(Date STimeStamp) {
        this.STimeStamp = STimeStamp;
    }

    public String getNextDt() {
		return nextDt;
	}

	public void setNextDt(String nextDt) {
		this.nextDt = nextDt;
	}

	public String getNextSummary() {
		return nextSummary;
	}

	public void setNextSummary(String nextSummary) {
		this.nextSummary = nextSummary;
	}

	public String getConsBookingID() {
		return consBookingID;
	}

	public void setConsBookingID(String consBookingID) {
		this.consBookingID = consBookingID;
	}

	@Override
	public String toString() {
        return new ToStringBuilder(this)
            .append("appSerialNo", getAppSerialNo())
            .toString();
    }

	@Override
    public boolean equals(Object other) {
        if ( !(other instanceof VisitRec) ){
        	return false;
        }
        VisitRec castOther = (VisitRec) other;
        return new EqualsBuilder()
            .append(this.getAppSerialNo(), castOther.getAppSerialNo())
            .isEquals();
    }

	@Override
    public int hashCode() {
        return new HashCodeBuilder()
            .append(getAppSerialNo())
            .toHashCode();
    }

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

}
