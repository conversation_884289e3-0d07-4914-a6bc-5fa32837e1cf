package com.howbuy.crm.hb.persistence.conference;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.hb.domain.conference.CmConferenceConscust;
import com.howbuy.crm.hb.domain.conference.CmOrgCodeWithNum;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.method.P;

/**
 * 路演参会人员
 * <AUTHOR>
 *
 */
public interface CmConferenceConscustMapper {

	/**
	 * 新增
	 * @param cmConferenceConscust
	 */
	void insertCmConferenceConscust(CmConferenceConscust cmConferenceConscust);

	/**
	 * 批量插入
	 * @param cmConferenceConscust
	 */
	void batchInsert(@Param("list") List<CmConferenceConscust> cmConferenceConscust);
	/**
	 * 修改
	 * @param cmConferenceConscust
	 */
	void updateCmConferenceConscust(CmConferenceConscust cmConferenceConscust);

	/**
	 * @description:(批量更新)
	 * @param cmConferenceConscusts
	 * @return void
	 * @author: xufanchao
	 * @date: 2023/11/13 17:42
	 * @since JDK 1.8
	 */
	void updateCmConferenceConscustBatch(@Param("list") List<CmConferenceConscust> cmConferenceConscusts);

	/**
	 *删除
	 * @param conferenceid
	 */
	void deleteCmConferenceConscust(String conferenceid);
	
	/**
	 * 删除
	 * @param param
	 */
	void deleteConferenceConscust(Map<String, String> param);


	/**
	 * @description:(请在此添加描述)
	 * @param conferenceId 会议ID
	 * @param consCode 投顾code值
	 * @param orgCode 部门code值
	 * @param conscustNo 投顾客户号
	 * @return void
	 * @author: xufanchao
	 * @date: 2023/11/20 09:03
	 * @since JDK 1.8
	 */
	void updateCmConferenceConscustByConscode(@Param("conferenceId") String conferenceId, @Param("consCode") String consCode,
											  @Param("orgCode") String orgCode, @Param("conscustNo") String conscustNo);
	
	/**
	 * 分页查询
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmConferenceConscust> listCmConferenceConscustByPage(@Param("param") Map<String, Object> param,
															  @Param("page") CommPageBean pageBean);
	
	/**
	 * 查询列表
	 * @param param
	 * @return
	 */
	List<CmConferenceConscust> listCmConferenceConscust(Map<String, Object> param);

	/**
	 * 查询
	 * @param param
	 * @return
	 */
	List<CmConferenceConscust> listCmConferenceConscustForClub(Map<String, String> param);

	/**
	 * 查询
	 * @param param
	 * @return
	 */
	List<CmConferenceConscust> listCmConferenceConscustById(Map<String, String> param);
	
	/**
	 * 获取单个
	 * @param param
	 * @return
	 */
	CmConferenceConscust getConferenceConscustinfo(Map<String, String> param);

	/**
	 * @description:(根据会议id获取到对应的会议类型)
	 * @param conferenceIdList
	 * @return java.util.List<com.howbuy.crm.hb.domain.conference.CmConferenceConscust>
	 * @author: xufanchao
	 * @date: 2023/11/11 09:09
	 * @since JDK 1.8
	 */
	List<CmConferenceConscust> listCmConferenceByIds(@Param("idList") List<String> conferenceIdList);


	/**
	 * @description:(根据会议id获取到对应的最大配置人数)
	 * @param conferenceIdList
	 * @return java.util.List<com.howbuy.crm.hb.domain.conference.CmConferenceConscust>
	 * @author: xufanchao
	 * @date: 2023/11/18 11:54
	 * @since JDK 1.8
	 */
	List<CmConferenceConscust> getMaxNumByConferenceId(@Param("idList") List<String> conferenceIdList);
	
	/**
	 * 更新
	 * @param cmConferenceConscust
	 */
	void updateCmConferenceConscustByConcustnos(CmConferenceConscust cmConferenceConscust);

	/**
	 * 查询
	 * @param map
	 * @return
	 */
	List<CmOrgCodeWithNum> listOrgCodeNumByList(Map<String,Object> map);

	/**
	 * 查询
	 * @param conferenceid
	 * @return
	 */
	List<CmOrgCodeWithNum> listOrgCodeNumByDepartment(@Param("conferenceid") String conferenceid);

	/**
	 * 获取单个
	 * @param map
	 * @return
	 */
	Integer getOrgCodeNumByCode(Map<String,Object> map);

	/**
	 * @param null
	 * @return
	 * @description:(获取对应的会议类型的总数)
	 * @author: xufanchao
	 * @date: 2023/11/13 14:43
	 * @since JDK 1.8
	 */
	List<CmConferenceConscust> getConferenceBySource(@Param("list") List<String> list);


	/**
	 * 根据投顾客户号
	 * @param conscustno
	 * @return
	 */
	String queryGdcjlabelByConsCustNo(String conscustno);
	
	/**
	 * 查询列表
	 * @param conscustnoList
	 * @return
	 */
	List<CmConferenceConscust> listCmConferenceConscustByConscustnoList(@Param("conscustnoList") List<String> conscustnoList);
	
	/**
	 * 分页查询签到统计
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmConferenceConscust> listCmConferenceConscustStatByPage(@Param("param") Map<String, Object> param,
															  @Param("page") CommPageBean pageBean);
	
	/**
	 * 查询询签到统计记录
	 * @param param
	 * @return
	 */
	List<CmConferenceConscust> listCmConferenceConscustStat(Map<String, Object> param);


	/**
	 * 查询参会客户的投顾信息
	 * @param conscustno
	 * @return
	 */
	CmConferenceConscust getConscustOrg(String conscustno);
}
