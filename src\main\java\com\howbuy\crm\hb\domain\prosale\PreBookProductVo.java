package com.howbuy.crm.hb.domain.prosale;

import com.howbuy.crm.hb.domain.insur.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约单查询Vo
 * <AUTHOR>
 * NOTICE: 替换 {mybatis :id=listPrebookproductinfoByPage} 参数
 */
@Data
public class PreBookProductVo extends PageVo implements Serializable {

	private static final long serialVersionUID = 1L;

	private String  isOrdered;


	/**
	 * true 字符串
	 */
	private String hascp;

	private String  custname;

	private String  crtBeginDt;

	private String  crtEndDt;

	private String  realpayamtbegdt;

	private String  realpayamtenddt;

	private String  credt;

	private String  expecttradebegdt;

	private String  expecttradeenddt;

	private String  pcode;

	private String  tradeTypes;

	private String  prehbzl;

	private String  prebookStates;

	private String  payStates;

	private String  tradeStates;

	private String  conscode;

	private String  orgcode;

	private String  discountstate;

	private String  discountType;

	private String  discountstyle;

	private String  isstat;

	private String  isrepeatbuy;

	private String  pretype;

	private String  disfccl;
	private String  conscustno;

	private String  hboneno;
	private String  preid;

	private String  refundbegdt;
	private String  refundenddt;

	private String  tradeTypesStr;
	private String  sort;

	private String  order;

	/**
	 * 是否资金匹配  1-是 0-否
	 */
	private String finMatched;


	/**
	 * 线上签约标识 0-不支持;1-支持;2-待确定
	 */
	private String onlineSignFlag;


	/**
	 * 签约状态 0-未签约；1-已签约
	 */
	private String signFlag;

	/**
	 * 签约日期时间 -起始  yyyyMMdd
	 */
	private String signFlagBegDt;


	/**
	 * 签约日期时间 -结束  yyyyMMdd
	 */
	private String signFlagEndDt;

	/**
	 * 是否香港:1-是，0-否
	 */
	@Deprecated
	private String sfxg;

	/**
	 * 产品分销 1-好买 2-好臻 3-海外
	 */
	private String cpfx;

	/**
	 * 售前留痕材料状态(0-无需上传/1-未上传/2-已上传/3-审核通过/4审核不通过)
	 */
	private String legaldocStat;

	/**
	 * 	售前留痕材料上传方式 0-自主上传资料、1-自动发送客户邮箱
	 */
	private String legalDocUploadMethod;

}
