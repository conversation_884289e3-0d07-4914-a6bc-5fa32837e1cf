package com.howbuy.crm.hb.persistence.insur;

import com.howbuy.crm.hb.domain.insur.CmBxPrebookEndpayList;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmBxPrebookEndpayListMapper {

     /**
      * 得到单个数据对象
      * @param param
      * @return
      */
	CmBxPrebookEndpayList getCmBxPrebookEndpayList(Map<String, Object> param);
    
     /**
      * 新增数据对象
      * @param cmBxPrebookEndpayList
      */
	void insertCmBxPrebookEndpayList(CmBxPrebookEndpayList cmBxPrebookEndpayList);
	
	/**
	 * 单条修改数据对象
	 * @param cmBxPrebookEndpayList
	 */
	void updateCmBxPrebookEndpayList(CmBxPrebookEndpayList cmBxPrebookEndpayList);
	
	/**
	 * 查询列表数据对象
	 * @param param
	 * @return
	 */
	List<CmBxPrebookEndpayList> listCmBxPrebookEndpayList(Map<String, Object> param);
	
	/**
	 * 批量更新预约单缴款计划明细表（删除现有的明细）
	 * @param param
	 */
	void batchUpdateCmBxPrebookEndpayList(Map<String, Object> param);
	
	/**
	 * 查询列表（分页数据）
	 * @param param
	 * @param pageBean
	 * @return
	 */
	List<CmBxPrebookEndpayList> listCmBxPrebookEndpayListByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);
	
	/**
	 * 缴费状态=未缴费时，如当前系统日期大于截止缴款日期且小于等于一个月，则更新为“未缴费（宽限期）”
	 * @param param
	 */
	void updateInsurPaystateExtend(Map<String, Object> param);
	
	/**
	 * 缴费状态=未缴费（宽限期）时，如当前系统日期已超过截止缴款日期一个月且小于等于2年，则更新为“未缴费（逾期）”
	 * @param param
	 */
	void updateInsurPaystateDue(Map<String, Object> param);
	
	/**
	 * 缴费状态=未缴费（逾期）时，如当前系统日期已超过截止缴款日期2年，则更新为“未缴费（终止）”
	 * @param param
	 */
	void updateInsurPaystateEnd(Map<String, Object> param);

	/**
	 * 处理产品到期正常的
	 * @param param
	 */
	void updateInsurProductExpireNormal(Map<String, Object> param);
	
	/**
	 * 处理产品到期暂停的
	 * @param param
	 */
	void updateInsurProductExpireStop(Map<String, Object> param);
	
	/**
	 * 处理产品到期终止的
	 * @param param
	 */
	void updateInsurProductExpireEnd(Map<String, Object> param);
	
	/**
	 * 处理由于时间过期的产品到期终止的
	 * @param param
	 */
	void updateInsurProductExpireEndByDt(Map<String, Object> param);
	
	/**
	  * 导出缴费计划
	  * @param param
	  * @return
	  */
	List<CmBxPrebookEndpayList> listCmBxPrebookEndpayListByExp(Map<String, String> param);

	/**
	 * 根据传入条件查询所有的缴款计划
	 */
	List<CmBxPrebookEndpayList> listCmBxPrebookEndpayListByidOrState(@Param("param") Map<String, String> param);

	/**
	 * 当核保状态为 延保/拒保、退保（冷静期内）的时候 更新缴款计划
	 */
	int updateCmBxPrebookEndpayListById(List<CmBxPrebookEndpayList> cmBxPrebookEndpayLists);

	/**
	 * 更新佣金
	 * @param commissionDt 佣金核算日期
	 * @param commissionRatio 佣金核算率
	 * @param commission 佣金
	 * @param endPayId 缴款Id
	 * @param modifier 修改人
	 * @return
	 */
	int updateCalculatedCommison(@Param("commissionDt") String commissionDt,
								 @Param("commissionRatio") BigDecimal commissionRatio,
								 @Param("commission") BigDecimal commission,
								 @Param("commissionForTwo") BigDecimal commissionForTwo,
								 @Param("endPayId") BigDecimal endPayId,
								 @Param("modifier") String modifier);

	/**
	 * @description:(更新缴款计划中的投顾佣金汇率)
	 * @param endPayId
	 * @param commissionRate
	 * @param modifier
	 * @return int
	 * @author: shucheng.luo
	 * @date: 2023/4/21 22:08
	 * @since JDK 1.8
	 */
	int updateCommissionRate(@Param("endPayId") BigDecimal endPayId, @Param("commissionRate") BigDecimal commissionRate, @Param("modifier") String modifier);

	/**
	 * @param endPayIdList       待更新 缴款计划Id
	 * @param expcommissionDt    人工指定佣金核算日期
	 * @param expcommissionRatio 人工指定投顾佣金率
	 * @param managePoint        管理系数-分总
	 * @param manageCoeffRegionalsubtotal 管理系数-区副
	 * @param manageCoeffRegionaltotal 管理系数-区总
	 * @return
	 * @description 人工调整核算数据[佣金核算日期、投顾佣金率]
	 * <AUTHOR>
	 * @date 2023/9/5 下午4:54
	 * @since JDK 1.8
	 */
	int updateExpCommisonInfo(@Param("endPayIdList") List<BigDecimal> endPayIdList,
							  @Param("expcommissionRatio") BigDecimal expcommissionRatio,
							  @Param("managePoint") String managePoint,
							  @Param("userId") String userId,
							  @Param("manageCoeffRegionalsubtotal") String manageCoeffRegionalsubtotal,
							  @Param("manageCoeffRegionaltotal") String manageCoeffRegionaltotal);

	/**
	 * @description 根据缴费id查币种
	 * @param endPayId
	 * @return java.lang.String
	 * @author: jianjian.yang
	 * @date: 2024/5/8 15:26
	 * @since JDK 1.8
	 */
	String getCurrencyByEndPayId(@Param("endPayId") BigDecimal endPayId);
}
