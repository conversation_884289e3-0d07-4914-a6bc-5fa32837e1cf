package com.howbuy.crm.hb.domain.custinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/5/28 18:10
 */
@Data
public class CmOrgLinkman implements Serializable{

    private static final long serialVersionUID = 1L;

    private Long id;

    /** 投顾客户号 */
    private String custNo;

    /** 姓名 */
    private String linkmanName;

    /** 部门 */
    private String partName;

    /** 性别 */
    private String sex;

    /** 生日 */
    private String birthday;

    /** 手机 */
    private String mobile;

    /** 座机 */
    private String tel;

    /** 邮箱 */
    private String email;

    /** 地址 */
    private String address;

    /** 职位 */
    private String positionName;

    /** 对接人部门 */
    private String counterpartOrgCode;

    /** 对接人 */
    private String counterpartMan;

    /** 是否删除 */
    private String isdel;
}
