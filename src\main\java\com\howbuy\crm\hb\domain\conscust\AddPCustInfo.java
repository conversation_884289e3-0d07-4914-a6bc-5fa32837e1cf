package com.howbuy.crm.hb.domain.conscust;

import java.io.Serializable;

import com.alibaba.druid.util.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class AddPCustInfo implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 客户名称.
	 */
	private String custName = "";

	/** 潜在客户评分 */
	private Integer PGrade;

	private String PGradeStr;

	/** 电子邮箱 */
	private String email = "";
	/** 投资顾问代码 */
	private String consCode = "";
	/** 客户来源 */
	private String source = "";
	/** 地址 */
	private String addr = "";

	/** 邮政编码 */
	private String postCode = "";
	/** 投资者手机区号 */
	private String mobileAreaCode = "";
	/** 投资者手机号码 */
	private String mobile = "";
	/** 联系电话 */
	private String telNo = "";
	/**
	 * 来源细分。
	 */
	private String subSource;
	
	/** 客户来源 */
	private String newSourceNo = "";
	/**
	 * 备注（拜访记录）
	 */
	private String memo;
	

	/**
	 * @return the custName
	 */
	public String getCustName() {
		return custName;
	}
	/**
	 * @param custName
	 *            the custName to set
	 */
	public void setCustName(String custName) {
		this.custName = custName;
	}
	/**
	 * @return the pGrade
	 */
	public Integer getPGrade() {

		return StringUtils.isEmpty(this.getPGradeStr()) ? 0 : Integer
				.parseInt(this.getPGradeStr());

	}

	/**
	 * @return the pGradeStr
	 */
	public String getPGradeStr() {
		return PGradeStr;
	}
	/**
	 * @param pGradeStr
	 *            the pGradeStr to set
	 */
	public void setPGradeStr(String pGradeStr) {
		PGradeStr = pGradeStr;
	}
	/**
	 * @return the email
	 */
	public String getEmail() {
		return email;
	}
	/**
	 * @param email
	 *            the email to set
	 */
	public void setEmail(String email) {
		this.email = email;
	}
	
	/**
	 * @return the consCode
	 */
	public String getConsCode() {
		return consCode;
	}
	/**
	 * @param consCode
	 *            the consCode to set
	 */
	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}
	/**
	 * @param source
	 *            the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}
	/**
	 * @return the addr
	 */
	public String getAddr() {
		return addr;
	}
	/**
	 * @param addr
	 *            the addr to set
	 */
	public void setAddr(String addr) {
		this.addr = addr;
	}
	/**
	 * @return the postCode
	 */
	public String getPostCode() {
		return postCode;
	}
	/**
	 * @param postCode
	 *            the postCode to set
	 */
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	/**
	 * @return the mobile
	 */
	public String getMobile() {
		return mobile;
	}
	/**
	 * @param mobile
	 *            the mobile to set
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	/**
	 * @return the telNo
	 */
	public String getTelNo() {
		return telNo;
	}
	/**
	 * @param telNo
	 *            the telNo to set
	 */
	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}
	public String getSubSource() {
		return subSource;
	}
	public void setSubSource(String subSource) {
		this.subSource = subSource;
	}
	public String getNewSourceNo() {
		return newSourceNo;
	}
	public void setNewSourceNo(String newSourceNo) {
		this.newSourceNo = newSourceNo;
	}
	public void setPGrade(Integer grade) {
		PGrade = grade;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getMobileAreaCode() {
		return mobileAreaCode;
	}

	public void setMobileAreaCode(String mobileAreaCode) {
		this.mobileAreaCode = mobileAreaCode;
	}
}
