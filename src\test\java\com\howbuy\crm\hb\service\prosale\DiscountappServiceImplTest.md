## 测试的类
> com.howbuy.crm.hb.service.prosale.impl.DiscountappServiceImpl
## 测试的方法
> cancelDiscount(String preBookId,String userId,String type)

## 分支伪代码
```java
if (根据预约id没有查询到折扣信息) {
    返回，提示"查询不到折扣信息"
} 
if (非定投意向单的折扣撤销) {
	if(根据预约id能查询到对应的预约){
		if(正常预约在非审核页面撤销){
			if(审核状态是初审通过或者审核状态是终审通过的){
				返回，提示"不符合撤销条件，不允许撤销"
			}
			if(预约中台已下单且折扣方式是直接少汇){
				返回，提示"不符合撤销条件，不允许撤销"
			}
		}
	}else{
		返回，提示"查询不到预约信息"
	}
}

```

## 测试案例
##### 1、testNotFindDiscount：没有查询到折扣信息的情况
##### 2、testFixedCancelDiscount：定投意向单折扣撤销情况
##### 3、testPreCancelDiscountNotPre：预约取消折扣未查到预约情况
##### 4、testPreCancelDiscountAtAudit：预约取消折扣在审核页面撤销
##### 5、testPreCancelDiscountNotAuditCsPassd：预约取消初审通过折扣在非审核页面撤销
##### 6、testPreCancelDiscountNotAuditZsPassd：预约取消终审通过折扣在非审核页面撤销
##### 7、testPreCancelDiscountNotAuditHasZtNotSh：预约取消非审核通过的中台已经下单的非直接少汇的折扣在非审核页面撤销
##### 8、testPreCancelDiscountNotAuditHasZtAndSh：预约取消非审核通过的中台已经下单的直接少汇的折扣在非审核页面撤销
##### 9、testPreCancelDiscountNotAuditNotZt：预约取消非审核通过的中台未下单的折扣在非审核页面撤销