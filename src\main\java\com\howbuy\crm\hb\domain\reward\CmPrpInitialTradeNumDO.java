package com.howbuy.crm.hb.domain.reward;

import com.alibaba.excel.annotation.ExcelProperty;
import com.howbuy.crm.util.validation.AddGroup;
import com.howbuy.crm.util.validation.UpdateGroup;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2020/9/15 11:10
 * @Description 配置初始交易次数实体类
 * @Version 1.0
 */
@Data
public class CmPrpInitialTradeNumDO implements Serializable {
    /**
    * 主键id
    */
    @NotNull(message = "主键id不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private Long id;

    /**
    * 客户号
    */
    @NotBlank(message ="客户号不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String conscustno;

    /**
    * 投顾号
    */
    @NotBlank(message ="投顾号不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String conscode;

    /**
    * 初始交易次数
    */
    @ExcelProperty(value = "初始交易次数",index =5)
    @NotBlank(message = "初始交易次数不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String initialNum;

    /**
    * 生效状态(1有效，0无效)
    */
    @NotBlank(message ="生效状态不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String isValid;
    /**
    * 备注
    */
    private String remark;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private String createTime;

    /**
    * 修改人
    */
    private String modor;
    /**
     * 修改人姓名
     */
    private String modorname;

    /**
    * 修改时间
    */
    private String updateTime;

    /**
     * 所属部门
     */
    private String orgname;

    /**
     * 客户姓名
     */
    private String conscustname;

    /**
     * 投顾姓名
     */
    private String consname;

    /**
     * 部门id
     */
    private String orgcode;


    private static final long serialVersionUID = 1L;
}