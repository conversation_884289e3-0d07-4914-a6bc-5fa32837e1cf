## 测试的类
> com.howbuy.crm.hb.service.conscust.impl.CmCustConstantServiceImpl
## 测试的方法 
> choiceGiftByDate(SVIPBirthdayGiftInfo bgl,
                   String choiceGift,
                   ConsOrgCache orgCache,
                   List<String> consCodeList,
                   Map<String, String> custConstantMap)

## 分支伪代码
``` java
验证过程 {
    if (组织的上级层级为howbuy) {
        返回 二级目录名称等一级名称
    }
    if (组织的上级层级为非howbuy) {
       返回 二级目录名称为所属区域名称
    }
    if (是否选择礼品条件为空&&接口数据为已选择礼品) {
        返回 null
    }
    if (是否选择礼品条件为否&&接口数据为未选择礼品) {
        返回 未选择礼品数据
    }
    if (是否选择礼品条件为是&&接口数据为未选择礼品) {
        返回 null
    }
    if (是否选择礼品条件为是&&接口数据为已选择礼品) {
        返回 已选择礼品数据
    }
    
    返回 推送时间范围内且指定生日月份的结果数据
}


## 测试案例
1、组织的上级层级为howbuy：则二级目录名称等一级名称
### test01
2、组织的上级层级为非howbuy：则二级目录名称为所属区域名称
### test02
3、是否选择礼品条件为空
### test03
4、是否选择礼品条件为否&&接口数据为未选择礼品
### test04
5、是否选择礼品条件为否&&接口数据为未选择礼品
### test05
6、是否选择礼品条件为是&&接口数据为未选择礼品
### test06
7、是否选择礼品条件为是&&接口数据为已选择礼品
### test07