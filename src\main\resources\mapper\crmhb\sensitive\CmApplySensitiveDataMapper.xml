<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.hb.persistence.sensitive.CmApplySensitiveDataMapper">
  <cache type="org.mybatis.caches.oscache.OSCache"/>
  <insert id="insertCmApplySensitiveData" parameterType="CmApplySensitiveData">
		INSERT INTO CM_APPLY_SENSITIVE_DATA (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="oaid != null">oaid,</if>
			<if test="title != null">title,</if>
			<if test="sourcetype != null">sourcetype,</if>
			<if test="infotype != null">infotype,</if>
			<if test="applyor != null">applyor,</if>
			<if test="creator != null">creator,</if>
			
		</trim>
		) VALUES (
		<trim suffix="" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="oaid != null">#{oaid},</if>
			<if test="title != null">#{title},</if>
			<if test="sourcetype != null">#{sourcetype},</if>
			<if test="infotype != null">#{infotype},</if>
			<if test="applyor != null">#{applyor},</if>
			<if test="creator != null">#{creator},</if>
		</trim>
		)
	</insert>
	
	<select id="listCmApplySensitiveData" parameterType="Map" resultType="CmApplySensitiveData" useCache="false">
		SELECT * FROM CM_APPLY_SENSITIVE_DATA
		WHERE 1=1
		<if test="id != null">AND id = #{id}</if>
		<if test="oaid != null">AND oaid = #{oaid}</if>
		<if test="loadflag != null">AND loadflag = #{loadflag}</if>
		<if test="applyor != null">AND applyor = #{applyor}</if>
	</select>
	
	<update id="updateCmApplySensitiveData" parameterType="CmApplySensitiveData">
		UPDATE CM_APPLY_SENSITIVE_DATA
		<set>
			<if test="loadflag != null">loadflag = #{loadflag},</if>
			<if test="modifier != null">modifier = #{modifier},</if>
			<if test="modifydt != null">modifydt = #{modifydt},</if>
		</set>
		WHERE id = #{id}
	</update>
	
	<select id="getCmApplySensitiveData" parameterType="Map" resultType="CmApplySensitiveData" useCache="false">
		SELECT * FROM CM_APPLY_SENSITIVE_DATA
		WHERE 1=1
		<if test="id != null">AND id = #{id}</if>
	</select>
	
	<select id="listSensitiveDownDataByCustno" parameterType="Map" resultType="SensitiveDownData" useCache="false">
		SELECT T.CONSCUSTNO, T.CUSTNAME_CIPHER CUSTNAME, T.IDNO_CIPHER IDNO, T.MOBILE_CIPHER MOBILE, T.TELNO_CIPHER TELNO, T.EMAIL_CIPHER EMAIL, T.ADDR_CIPHER ADDR
  		FROM CM_CONSCUST_CIPHER T
		WHERE ${sqlins}
	</select>
  
</mapper>