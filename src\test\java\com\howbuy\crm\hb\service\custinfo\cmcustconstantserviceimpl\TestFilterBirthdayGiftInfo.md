## 测试的类
> com.howbuy.crm.hb.service.conscust.impl.CmCustConstantServiceImpl
## 测试的方法 
> filterBirthdayGiftInfo(SVIPBirthdayGiftInfo bgl,
                         String custName,
                         String birthMonth,
                         ConsOrgCache orgCache,
                         List<String> consCodeList,
                         Map<String, String> custConstantMap)

## 分支伪代码
``` java
验证过程 {
    if (组织的上级层级为howbuy) {
        返回 二级目录名称等一级名称
    }
    if (组织的上级层级为非howbuy) {
       返回 二级目录名称为所属区域名称
    }    
    if (条件中输入了客户名称) {
        返回 按客户名称进行搜索结果
    }
    if (条件中选择了投顾编码) {
        返回 按投顾编码进行搜索结果
    }
    if (条件中选择了客户生日月份) {
        返回 按客户生日月份进行搜索结果
    }
    if (模拟正常搜索) {
        返回 正常搜索条件下的结果数据
    }
    
    返回 默认日期返回内的结果数据（默认接口支持按日期范围搜索）
}


## 测试案例
1、组织的上级层级为howbuy：则二级目录名称等一级名称
### test01
2、组织的上级层级为非howbuy：则二级目录名称为所属区域名称
### test02
3、模拟条件中按客户名称进行搜索
### test03
4、模拟条件中按投顾编码进行搜索
### test04
5、模拟条件中按客户生日月份进行搜索
### test05
6、模拟正常搜索
### test06