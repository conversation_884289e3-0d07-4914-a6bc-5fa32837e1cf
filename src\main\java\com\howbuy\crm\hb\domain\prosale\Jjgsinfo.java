package com.howbuy.crm.hb.domain.prosale;

import java.io.Serializable;


/**
 * 产品信息:
* <AUTHOR>
* @date 2022/9/19
*/
public class Jjgsinfo implements Serializable {

	private static final long serialVersionUID = 1L;

	private String gsqc;

	private String gsjc;

	private String summary;

	private String clrq;

	private String frdb;

	private String zczb;

	private String qxjl;

	private String gljj;

	private String cxjj;

	private String bahm;

	private String glgm;

	public String getGsqc() {
		return gsqc;
	}

	public void setGsqc(String gsqc) {
		this.gsqc = gsqc;
	}

	public String getGsjc() {
		return gsjc;
	}

	public void setGsjc(String gsjc) {
		this.gsjc = gsjc;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public String getClrq() {
		return clrq;
	}

	public void setClrq(String clrq) {
		this.clrq = clrq;
	}

	public String getFrdb() {
		return frdb;
	}

	public void setFrdb(String frdb) {
		this.frdb = frdb;
	}

	public String getZczb() {
		return zczb;
	}

	public void setZczb(String zczb) {
		this.zczb = zczb;
	}

	public String getQxjl() {
		return qxjl;
	}

	public void setQxjl(String qxjl) {
		this.qxjl = qxjl;
	}

	public String getGljj() {
		return gljj;
	}

	public void setGljj(String gljj) {
		this.gljj = gljj;
	}

	public String getCxjj() {
		return cxjj;
	}

	public void setCxjj(String cxjj) {
		this.cxjj = cxjj;
	}

	public String getBahm() {
		return bahm;
	}

	public void setBahm(String bahm) {
		this.bahm = bahm;
	}

	public String getGlgm() {
		return glgm;
	}

	public void setGlgm(String glgm) {
		this.glgm = glgm;
	}

	
}
